## 系统各模块介绍【开发人员每新增一个模块都要在此处维护模块目录，并递增分配一个异常码】
>* `aps-compensation（01）` 服务补偿
>    - `aps-compensation-impl` 服务补偿所有业务实现
>    - `aps-compensation-model` 服务补偿所有的实体
>    - `aps-compensation-remote-api` 服务补偿本身不会使用,提供给其他服务调用的api接口，和impl中需要暴露ctrl接口意一一对应
>* `aps-compensation-bff（01）` 服务补偿，bff聚合层，采集各子服务的功能，统一暴露给前端的接口，某些和服务物无关的数据组装、映射，在该层实现
>* `aps-component（02）` 公共组件,非服务，工具类（openfeign异常响应处理、数据权限……）
>* `aps-droolsrule-bff（02）` droolsrule规则的，bff聚合层，采集各子服务的功能，统一暴露给前端的接口，某些和服务无关的数据组装、映射，在该层实现
>* `aps-filght-passenger（03）` 航班旅客信息
>    - `aps-filght-passenger-impl` 航班旅客的查询业务实现
>    - `aps-filght-passenger-model` 航班旅客的实体
>    - `aps-filght-passenger-remote-api` 提供给其他服务调用的api接口，和impl中需要暴露ctrl接口一一对应
>* `aps-filghtpassenger-bff（03）` 航班旅客，bff聚合层，采集各子服务的功能，统一暴露给前端的接口，某些和服务无关的数据组装、映射，在该层实现
>* `aps-gateway（04）` 网关
>* `aps-share（05）` 公共服务，各种非业务的ctrl-service-dao
>* `aps-user-center（06）` 基础框架uc
>    - `aps-user-center-impl` 具体业务实现，包含传统的service和dao
>    - `aps-user-center-model` 用户中心实体
>    - `aps-user-center-remote-api` 提供给其他服务调用的api接口，和impl中需要暴露ctrl接口一一对应
>    - `aps-user-center-service` 对面接口，相当于ctrl
>    - `aps-user-center-service-starter` 对aps-user-center-service的装配
>    - `aps-user-center-starter` 对aps-user-center-impl的装配
>*  ~~【`aps-cpc（07）` 】~~ ~~审核流对接~~由workflow进行替代
>    -  ~~【`aps-cpc-impl`】~~ ~~cpc对接的业务逻辑实现~~
>    -  ~~【`aps-cpc-model`】~~ ~~cpc对接中的实体~~
>    -  ~~【`aps-cpc-remote-api`】~~ ~~提供给其他服务调用的api接口~~
>* ~~【`aps-cpc-bff（07）`】~~ ~~bff聚合层，采集各子服务的功能，统一暴露给前端的接口~~
>* ~~【`aps-apply（08）`】~~ ~~申领单,对应的代码迁移到compensation中~~
 >    - ~~【`aps-apply-impl`】~~ ~~申领单所有业务实现~~
 >    - ~~【`aps-apply-model` 】~~ ~~申领单所有的实体~~
 >    - ~~【`aps-apply-remote-api`】~~ ~~申领单本身不会使用,提供给其他服务调用的api接口，和impl中需要暴露ctrl接口意一一对应~~
>* `aps-apply-bff（08）` 申领单，bff聚合层，采集各子服务的功能，统一暴露
>* `aps-staff-bff（09）` 工作人员端服务，bff聚合层，采集各子服务的功能，统一暴露
>* `aps-workflow（10）` 工作流
  >    - `aps-workflow-impl` 工作流业务实现
  >    - `aps-workflow-model` 工作流的实体
  >    - `aps-workflow-remote-api` 工作流对外提供的接口，和impl中需要暴露ctrl接口意一一对应
>* `aps-pay（11）` 支付
>* 'aps-coordinate-api (12)' 供协同中心调用的接口，需要采用不同的验证方法进行验证

## 系统异常码分配规则
> aps 异常代码由5位数字组成
>- 第一位: 3 固定值，3代表aps项目;(比如前端用的是6，基础框架用的是5)
>- 第二和三位：给予具体子服务，每个服务对应哪个我们标注在服务模块上，如：aps-compensation 01
>- 第四和五位：开发人员自定义

例如：整个异常码为：30111

## 注意事项

><b>日志打印规范</b><br/>
>//切记要用中文【】包裹，不要用英文[]，不然会和json序列化数组的[]干扰<br/>
>LOGGER.debug("【aps-component】当前子服务返回的不是json字符串，服务路径【{}】，返回内容【{}】", url, res); 

><b>发布规范</b><br/>
>aps项目有两层含义，一层是作为业务基础层，提供给航司直接使用业务逻辑；还有一层是aps自己本身作为项目进行启动，所以在发布上面一定要对版本号进行把控，尤其是各模块的版本

`versions:set -DnewVersion=1.0.3 // 修改所有pom中版本号
 versions:revert //回滚
versions:commit //提交`
## 常用资源访问地址
- https://conf.sw/pages/viewpage.action?pageId=22717455 [开发必看]

## 和前端的约定
- Schemas全部已DTO/VO结束（Schemas后缀确定后不要变动），尽量少嵌套。每个字段需要：注释、类型、是否必填，可选：默认值
- get查询的params，全部放在 ? 后面
- url名称尽量完整（即不简写，大模块除外），url名称中不带get、post等HTTP method关键字（delete可除外）
- 数组提交采用：（见下图）
![img_3.png](img_3.png)

## 常用请求
- 登录，访问地址  http://localhost:8085/api/aps-user-center/oauth/token?username=test&password=123456&grant_type=password&client_id=password_auth_mode&client_secret=123456
- 禁用验证码：在数据库访问sys_config_info表， 修改熟悉sys.login.captcha.enable =1 > 0
- swagger访问，token格式：Bearer 1a62aba3-4fd2-412d-aeab-154da06ac1c8

## 常用命令
>-- 查找所有运行的端口
netstat -ano

>-- 查看被占用端口对应的 PID
netstat -aon|findstr "8081"

>java -jar aps-provider.jar --server.port=8888

## 系统版本一览
* springboot ——> 2.5.0
* springcloud ——> Hoxton.SR12 
* springcloudalibaba ——> 2.2.6.RELEASE

## 微服务的组件或功能
微服务：
注册中心 k8s  nacos
配置中心 nacos-config
网关 gateway
负载均衡  k8s
服务调用 openFeign
链路追踪 skywalking
服务保护 Sentinel   

其他：
MQ\REDIS\MYSQL

已实现：nacos注册中心、nacos配置中心、gateway网关、gateway动态路由、负载均衡、服务调用
![img.png](img.png)
![img_1.png](img_1.png)
![img_2.png](img_2.png)
[服务通信、熔断、限流、链路追踪、日志集中管理]
## spring cloud 、 alibaba和spring boot的对应关系：

> spring cloud目前版本地址：https://spring.io/projects/spring-cloud#learn

> spring boot目前版本地址：https://spring.io/projects/spring-boot#learn

> 查看对应关系的网址（一定要用火狐访问）：https://start.spring.io/actuator/info

> 查看spring历史版本的：https://docs.spring.io/spring-framework/docs/

> 查看spring-boot历史版本的：https://docs.spring.io/spring-boot/docs/

> 查看spring-cloud历史版本： https://docs.spring.io/spring-cloud/docs/

> spring-cloud-alibaba与cloud的版本对应关系：https://github.com/alibaba/spring-cloud-alibaba/wiki/%E7%89%88%E6%9C%AC%E8%AF%B4%E6%98%8E
