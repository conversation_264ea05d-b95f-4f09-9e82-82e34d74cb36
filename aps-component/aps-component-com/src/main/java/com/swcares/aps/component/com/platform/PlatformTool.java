package com.swcares.aps.component.com.platform;

import com.swcares.aps.cpe.coordinate.model.enums.CustomerCategoryEnum;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;

/**
 * @ClassName：PlateTenantTypeConfig
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/9/10 13:12
 * @version： v1.0
 */
@Configuration
@Slf4j
public class PlatformTool {

    private static final String OPERATOR_SYSTEM="system";

    @Value("${platform.tenantType:Airline}")
    private String tenantType;

    @PostConstruct
    public void initPlatformTool(){
        log.info("initPlatformTool,tenantType:{}",tenantType);
    }

    /**
     * 是否为航司平台
     * @return
     */
    public boolean isAirlinePlatform(){
        return CustomerCategoryEnum.AIRLINE.getCode().equals(tenantType);
    }

    /**
     * 是否为机场平台
     * @return
     */
    public boolean isAirportPlatform(){
        return CustomerCategoryEnum.AIRPORT.getCode().equals(tenantType);
    }

    /**
     * 是否为供应商平台
     * @return
     */
    public boolean isSupplierPlatform(){
        return  CustomerCategoryEnum.SUPPLIER.getCode().equals(tenantType);
    }


    public String getTenantType(){
        return tenantType;
    }

    public String getCurrentTenantCode(){
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return null;
        }
        return user.getTenantCode();
    }

    public Long getCurrentTenantId(){
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return TenantHolder.getTenant();
        }
        return user.getTenantId();
    }

    public String getCurrentTenantName() {
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return null;
        }
        return user.getTenantName();
    }

    public String getCurrentOperator(){
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return OPERATOR_SYSTEM;
        }
        if(StringUtils.isNotEmpty(user.getEmployeeName())){
            if(StringUtils.isNotEmpty(user.getJobNumber())){
                return user.getEmployeeName()+user.getJobNumber();
            }
            return user.getEmployeeName()+user.getUsername();
        }
        return user.getUsername();
    }

    public String getCurrentOperatorUserName(){
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return OPERATOR_SYSTEM;
        }
        return user.getUsername();
    }


    public String getCurrentOperatorUserId(){
        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return OPERATOR_SYSTEM;
        }
        return String.valueOf(user.getId());
    }

    public String getCurrentOperatorOrganizationId() {

        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return null;
        }

        return String.valueOf(user.getOrganizationId());
    }

    public String getCurrentOperatorOrganizationName() {

        LoginUserDetails user = UserContext.getUser();
        if(user == null){
            return null;
        }

        return user.getOrganizationName();
    }

    public CurrentUserTenantTypeInfo getCurrentUserTenantTypeInfo(){
        return new CurrentUserTenantTypeInfo(this.tenantType,
                this.getCurrentTenantCode(),
                this.getCurrentTenantName(),
                this.getCurrentTenantId(),
                this.getCurrentOperator(),
                this.getCurrentOperatorUserName(),
                this.getCurrentOperatorUserId(),
                this.getCurrentOperatorOrganizationId(),
                this.getCurrentOperatorOrganizationName());
    }
}
