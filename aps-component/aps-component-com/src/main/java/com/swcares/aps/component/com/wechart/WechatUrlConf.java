package com.swcares.aps.component.com.wechart;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：WechatUrlConf <br>
 * Package：com.swcares.aps.apply.model.conf <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 11月25日 14:57 <br>
 * @version v1.0 <br>
 */
public class WechatUrlConf {
    /**
     * 根据code获取openid（适用于公众号开发获取openid）@see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/Wechat_webpage_authorization.html
     */
    public static String AUTH_OPENID = "https://api.weixin.qq.com/sns/oauth2/access_token?";

    /**
     * 根据code获取openid（适用于小程序开发获取openid） @see https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/login/auth.code2Session.html
     */
    public static String MINI_PROGRAM_AUTH_OPENID = "https://api.weixin.qq.com/sns/jscode2session?";

    /**
     * 公众号和小程序服务消息推送 @see https://developers.weixin.qq.com/miniprogram/dev/OpenApiDoc/mp-message-management/uniform-message/sendUniformMessage.html
     * 本来都是这一个的，但是这里面文档说小程序已经（（小程序模板消息已下线，不用传此节点））下线了，所以后面公众号可以用这个
     */
    public static String UNIFORM_MESSAGE = "https://api.weixin.qq.com/cgi-bin/message/wxopen/template/uniform_send?";

    /**
     * 小程序服务消息推送 @see https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/subscribe-message/subscribeMessage.send.html
     * 次数限制：开通支付能力的是3kw/日，没开通的是1kw/日。
     */
    public static String MINI_PROGRAM_SEND_MESSAGE = "https://api.weixin.qq.com/cgi-bin/message/subscribe/send?";

    /**
     * 获取小程序全局唯一后台接口调用凭据 @see https://developers.weixin.qq.com/miniprogram/dev/api-backend/open-api/access-token/auth.getAccessToken.html
     */
        public static String MINI_PROGRAM_ACCESS_TOKEN = "https://api.weixin.qq.com/cgi-bin/token?";


    /**
     * 获取jsapi_ticket @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62
     */
    public static final String MINI_PROGRAM_JSAPI_TICKET = "https://api.weixin.qq.com/cgi-bin/ticket/getticket?type=jsapi";


}
