package com.swcares.aps.component.com.msg;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.component.com.msg.dto.SubscribeMessageDto;
import com.swcares.aps.component.com.msg.dto.UniformSendDto;
import com.swcares.aps.component.com.wechart.WechatUrlConf;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.io.UnsupportedEncodingException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Formatter;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * @ClassName：MiniProgramMsg
 * @Description：下发小程序和公众号统一的服务消息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 夏阳
 * @Date： 2022/7/21 9:03
 * @version： v1.0
 */
@Slf4j
public class WechatMsgUtil {

    // 存储在redis中小程序发送消息的的token
    private static final String REDIS_TOKEN_PREFIX = "WECHRT:MINIPROGRAM:ACCESSTOKEN";
    private static final String REDIS_JSAPITICKET_PREFIX = "WECHRT:MINIPROGRAM:JSAPITICKET";

    private final static Object lock = new Object();

    /**
     * @title uniformSend
     * @description 下发小程序和公众号统一的服务消息（目前只有公众号）
     * @param accessToken  接口请求凭证
     * @param uniformSendDto 发送消息体
     * <AUTHOR>
     * @date 2022/7/22 9:23
     * @return java.lang.String
     */
    public static String uniformSend(String accessToken, UniformSendDto uniformSendDto){
        StringBuilder url = new StringBuilder(WechatUrlConf.UNIFORM_MESSAGE)
                .append("access_token=" + accessToken);

        String reqBody = JSONUtil.toJsonStr(uniformSendDto);
        log.info("微信公众号发送服务消息的请求为：accessToken【{}】,请求体：【{}】" , accessToken, reqBody);
        String resBody = HttpRequest.post(url.toString()).body(reqBody).execute().body();
        log.info("微信公众号发送服务消息的响应为：accessToken【{}】,反馈结果：【{}】" , accessToken, resBody);

        return resBody;
    }

    /**
     * @title subscribeMessage
     * @description 微信小程序推送服务消息
     * <AUTHOR>
     * @date 2022/7/25 10:29
     * @param accessToken
     * @param subscribeMessageDto
     * @return java.lang.String
     */
    public static String subscribeMessage(String accessToken, SubscribeMessageDto subscribeMessageDto){
        StringBuilder url = new StringBuilder(WechatUrlConf.MINI_PROGRAM_SEND_MESSAGE)
                .append("access_token=" + accessToken);

        String reqBody = JSONUtil.toJsonStr(subscribeMessageDto);
        log.info("微信小程序发送服务消息的请求为：accessToken【{}】,请求体：【{}】" , accessToken, reqBody);
        String resBody = HttpRequest.post(url.toString()).body(reqBody).execute().body();
        log.info("微信小程序发送服务消息的响应为：accessToken【{}】,反馈结果：【{}】" , accessToken, resBody);

        return resBody;
    }


    /**
     * @title getMiniProgramAccessToken
     * @description 获取token,首先从redis拿，没有加锁去更新
     * <AUTHOR>
     * @date 2022/7/22 14:04
     * @param appId
     * @param appSecret
     * @return java.lang.String
     */
    public static String getMiniProgramAccessToken(String appId, String appSecret){
        RedisUtil redisUtil = SpringUtils.getBean("redisUtil");
        String accessToken = redisUtil.get(REDIS_TOKEN_PREFIX+":"+appId);
        if(StringUtils.isEmpty(accessToken)){
            synchronized (lock){
                return getAccessToken(appId, appSecret);
            }
        }
        return accessToken;
    }

    public static void main(String[] args) {
        refreshAccessToken("wx56dd62dfbc79a46f","6dfb9c1a93c4bcae3149ca8210141e6b");
    }

    /**
     * @title refreshAccessToken
     * @description 额外提供刷新接口，用于特殊情况处理，这种情况可以不走redis缓存获取
     * <AUTHOR>
     * @date 2022/7/22 14:08
     * @param appId
     * @param appSecret
     * @return java.lang.String
     */
    public static String refreshAccessToken(String appId, String appSecret){
        return getAccessToken(appId, appSecret);
    }

    /**
     * @title getAccessToken
     * @description 获取小程序token；此方法调用需要加锁，应该统一的服务负责调用更新，否则各服务新旧token会相互覆盖
     * <AUTHOR>
     * @date 2022/7/22 14:00
     * @param appId
     * @param appSecret
     * @return java.lang.String
     */
    private static String getAccessToken(String appId, String appSecret){
        StringBuilder url = new StringBuilder(WechatUrlConf.MINI_PROGRAM_ACCESS_TOKEN)
                .append("grant_type=client_credential").append("&appid=" + appId).append("&secret=" + appSecret);

        log.info("微信小程序access_token的请求为：appId【{}】,appSecret：【{}】" , appId, appSecret);
        String resBody = HttpRequest.get(url.toString()).execute().body();
        log.info("微信小程序access_token的响应为：appId【{}】,appSecret：【{}】,响应结果【{}】" , appId, appSecret, resBody);

        JSONObject jsonObject = JSONUtil.parseObj(resBody);
        if(resBody.indexOf("access_token") > -1){
            String accessToken = jsonObject.getStr("access_token");
            int expiresIn = Integer.parseInt(jsonObject.getStr("expires_in"));

            RedisUtil redisUtil = SpringUtils.getBean("redisUtil");
            //提前一分钟更新
            redisUtil.set(REDIS_TOKEN_PREFIX+":"+appId, accessToken, expiresIn - 60);

            return accessToken;
        }else{
            log.error("微信小程序获取accessToken失败：appId【{}】,appSecret：【{}】,响应结果【{}】", appId, appSecret, resBody);
            log.error("40001->AppSecret 错误或者 AppSecret 不属于这个小程序，请开发者确认 AppSecret 的正确性;40002->请确保 grant_type 字段值为 client_credential;" +
                    "40013->不合法的 AppID，请开发者检查 AppID 的正确性，避免异常字符，注意大小写");
            return null;
        }
    }


    private static String getJsapiTicket(String appId, String appSecret){
        String accessToken = getMiniProgramAccessToken(appId, appSecret);
        StringBuilder url = new StringBuilder(WechatUrlConf.MINI_PROGRAM_JSAPI_TICKET)
                .append("&access_token=" + accessToken);
        log.info("微信小程序jsapi_ticket的请求为：appId【{}】,appSecret：【{}】,url:【{}】" , appId, appSecret,url);
        String resBody = HttpRequest.get(url.toString()).execute().body();
        log.info("微信小程序jsapi_ticket的响应为：appId【{}】,appSecret：【{}】,url:【{}】,响应结果【{}】" , appId, appSecret,url, resBody);

        JSONObject jsonObject = JSONUtil.parseObj(resBody);
        if(resBody.indexOf("ticket") > -1){
            String ticket = jsonObject.getStr("ticket");
            int expiresIn = Integer.parseInt(jsonObject.getStr("expires_in"));

            RedisUtil redisUtil = SpringUtils.getBean("redisUtil");
//            提前一分钟更新
            redisUtil.set(REDIS_JSAPITICKET_PREFIX+":"+appId, ticket, expiresIn - 60);

            return ticket;
        }else{
            log.error("微信小程序获取JsapiTicket失败：appId【{}】,appSecret：【{}】,url:【{}】,响应结果【{}】", appId, appSecret, url,resBody);
            return null;
        }
    }

    public static String getMiniProgramJsapiTicket(String appId, String appSecret){
        RedisUtil redisUtil = SpringUtils.getBean("redisUtil");
        String ticket =  redisUtil.get(REDIS_JSAPITICKET_PREFIX+":"+appId);
        if(StringUtils.isEmpty(ticket)){
            synchronized (lock){
                return getJsapiTicket(appId, appSecret);
            }
        }
        return ticket;
    }

    /**
     * JS-SDK使用权限签名算法 @see https://developers.weixin.qq.com/doc/offiaccount/OA_Web_Apps/JS-SDK.html#62
     * @param appId
     * @param appSecret
     * @param url
     * @return
     */
    public static Map<String, String> takeJsapiSign(String appId, String appSecret, String url){
        String jsapiTicket = getMiniProgramJsapiTicket(appId, appSecret);
        Map<String, String> result = jsapiSign(jsapiTicket, url);
        log.info("JsapiSign获取结果：【{}】,【{}】",appId,result);
        return result;
    }

    private static Map<String, String> jsapiSign(String jsapi_ticket, String url) {
        Map<String, String> ret = new HashMap<String, String>();
        String nonce_str = create_nonce_str();
        String timestamp = create_timestamp();
        String string1;
        String signature = "";

        //注意这里参数名必须全部小写，且必须有序
        string1 = "jsapi_ticket=" + jsapi_ticket +
                "&noncestr=" + nonce_str +
                "&timestamp=" + timestamp +
                "&url=" + url;
        log.info("jsapi签名原始字符串，【{}】",string1);

        try{
            MessageDigest crypt = MessageDigest.getInstance("SHA-1");
            crypt.reset();
            crypt.update(string1.getBytes("UTF-8"));
            signature = byteToHex(crypt.digest());
        }catch (NoSuchAlgorithmException e){
            e.printStackTrace();
        }catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        ret.put("url", url);
        ret.put("jsapi_ticket", jsapi_ticket);
        ret.put("nonceStr", nonce_str);
        ret.put("timestamp", timestamp);
        ret.put("signature", signature);
        log.info("jsapi签名结果为：【{}】,【{}】",string1,ret);
        return ret;
    }

    private static String byteToHex(final byte[] hash) {
        Formatter formatter = new Formatter();
        for (byte b : hash) {formatter.format("%02x", b);}
        String result = formatter.toString();
        formatter.close();
        return result;
    }

    private static String create_nonce_str() {
        return UUID.randomUUID().toString();
    }

    private static String create_timestamp() {
        return Long.toString(System.currentTimeMillis() / 1000);
    }
}
