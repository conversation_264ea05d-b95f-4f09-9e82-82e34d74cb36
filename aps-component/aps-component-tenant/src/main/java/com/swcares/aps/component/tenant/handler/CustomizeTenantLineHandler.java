package com.swcares.aps.component.tenant.handler;

import com.baomidou.mybatisplus.extension.plugins.handler.TenantLineHandler;
import com.swcares.baseframe.common.tenant.TenantHolder;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.LongValue;

import java.util.Arrays;
import java.util.Locale;
import java.util.stream.Collectors;

/**
 * @ClassName：CustomizeTenantLineHandler
 * @Description：租户的自定义实现
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/4/18 10:24
 * @version： v1.0
 */
public class CustomizeTenantLineHandler implements TenantLineHandler {

    private String[] ignoreTableConfig = new String[]{"sys_config_info", "data_permission_model", "bd_airport_info", "bd_area_info", "uc_user_manage_organization",
            "sys_config_value", "sys_dictionary_type", "sys_dictionary_data", "sys_audit_log", "sys_tenant", "sys_feature_resource", "sys_job", "sys_job_log",
            "uc_user_work_for_organization", "uc_user_role_join", "uc_role_feature_resource_join", "uc_user_role_join", "uc_group_manager_resource_join",
            "uc_auth_group_manager_join", "sys_tenant_resource_join", "sys_account", "uc_auth_group_resource_join", "cooperative_customer","bd_airline_info","bd_airport_info","compensation_cabin_config","data_push_record","passenger_category_configure","compensation_rule_config","compensation_data_config"};

    @Override
    public Expression getTenantId() {
        //从基础框架获取到当前登录用户的租户id
        return new LongValue(TenantHolder.getTenant());
    }

    @Override
    public boolean ignoreTable(String tableName) {
        // 例如：表明包含common或者sys开头的表，则不加入租户条件
        // 现在默认都需要加，直接return false
        if (Arrays.stream(ignoreTableConfig).collect(Collectors.toSet()).contains(tableName.toLowerCase(Locale.ROOT))) {
            return true;
        }
        //消息的表
       // if(tableName.startsWith("msg")) return true;
        // 这个是为了解决with_as开头的表，因为with_as开头的表，会报错，所以需要忽略
        return tableName.startsWith("with_as") || tableName.toLowerCase().startsWith("flt_") || tableName.toLowerCase().startsWith("foc_")
                || tableName.toLowerCase().startsWith("act_")  || tableName.toLowerCase().startsWith("workflow_");
    }

    // 租户字段名，默认的。
    @Override
    public String getTenantIdColumn() {
        return TenantLineHandler.super.getTenantIdColumn();
    }
}
