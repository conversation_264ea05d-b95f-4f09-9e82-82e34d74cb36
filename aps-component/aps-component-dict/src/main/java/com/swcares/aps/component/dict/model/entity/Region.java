package com.swcares.aps.component.dict.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

@Data
@TableName("region")
public class Region {

    @TableId
    private Long id;

    private Long pid;

    private Integer deep;

    private String name;

    private String pinyinPrefix;

    private String extName;
}
