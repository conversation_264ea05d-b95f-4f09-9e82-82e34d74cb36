import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.chinapay.secss.SecssUtil;
import com.swcares.aps.component.pay.pay.util.chinapay.Base64;
import com.swcares.aps.component.pay.pay.util.chinapay.Encryptor;
import com.swcares.aps.component.pay.pay.util.chinapay.StringUtil;
import lombok.extern.slf4j.Slf4j;
import net.sf.json.JSONObject;
import org.apache.http.client.methods.CloseableHttpResponse;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @ClassName：UnionValid1004
 * @Description：实名认证测试类（为啥取这个名字，因为银联发给我接口demo就是这个）
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2025/4/27 10:26
 * @version： v1.0
 */
@Slf4j
public class UnionValid1004 {

    public static void main(String[] args) throws Exception {
        SecssUtil secssUtil = new SecssUtil();
        secssUtil.init("/Users/<USER>/code/git_workspace/aps/aps-component/aps-component-pay/src/main/resources/chinapay/luggage/000092504174707.properties");

        //1.风险域
        Map<String, String> riskRateInfo = new HashMap<>();
        riskRateInfo.put("appName", "00 补偿通-西藏机场集团");
        //04：IPV4  06：IPV6
        riskRateInfo.put("ipType", "04");
        riskRateInfo.put("sourceIp", "*********");
        String riskRateInfoStr = new String(Base64.encode(JSONUtil.toJsonStr(riskRateInfo).getBytes(StandardCharsets.UTF_8)));

        //2.敏感域map
        Map<String, String> cardInfoMap = new HashMap<>();
        cardInfoMap.put("riskRateInfo", riskRateInfoStr);
        cardInfoMap.put("cardNo", "6212266767676767676");
        cardInfoMap.put("certType", "01");
        cardInfoMap.put("certNo", "513822199205085796");
        cardInfoMap.put("usrName", "夏阳");
        secssUtil.encryptData(JSONUtil.toJsonStr(cardInfoMap));

        //3.最终请求map
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("protocolVersion", "用户授权书2025版");
        String orderId = System.currentTimeMillis()+"";
        reqMap.put("protocolNo", orderId);
        reqMap.put("authFlag", "1");
        reqMap.put("merNo", "000092504174707");
        reqMap.put("busiType", "1004");
        reqMap.put("orderDate", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN));
        reqMap.put("orderId", orderId);
        System.out.println(orderId);
        reqMap.put("sensData", secssUtil.getEncValue());

        String reqDataStr = new String(Base64.encode(JSONUtil.toJsonStr(reqMap).getBytes(StandardCharsets.UTF_8)));
        // 计算摘要
        String encReqDataStr = Encryptor.encode(reqDataStr, "SHA-512");
        log.info("摘要加密（SHA-512）encReqDataStr=[" + encReqDataStr + "];");

        //商户签名（签名根据摘要（请求内容）计算而来）
        Map<String, String> reqDataMap = new HashMap<String,String>();
        reqDataMap.put("reqData", encReqDataStr);
        secssUtil.sign(reqDataMap);
        String signature = secssUtil.getSign();
        log.debug("signature-->"+signature);

        Map<String, Object> sendMap = new HashMap<String,Object>();
        sendMap.put("reqData", reqDataStr);
        sendMap.put("merNo", "000092504174707");
        sendMap.put("signature", signature);

        String authApi = "https://vas-test.chinapay.com/VASAP/vasap/business.htm";
        int timeout =  50 * 1000;
        String sendMsg = getURLParam(sendMap, false, null);


        String res = HttpRequest.post(authApi).header("Content-Encoding", "UTF-8")
                //.header("Content-Type", "application/x-www-form-urlencoded")
                .setConnectionTimeout(timeout).setReadTimeout(timeout).body(sendMsg)
                .execute().body();
        Map<String, String> resultMap1 = StringUtil.paserStrtoMap(res);
        String respStrDecode1 = new String(Base64.decode(resultMap1.get("respData").toCharArray()), "UTF-8");
        JSONObject jsonRes1 = JSONObject.fromObject(respStrDecode1);


    }

    public static String getURLParam(Map map, boolean isSort, Set removeKey) throws UnsupportedEncodingException {
        StringBuffer param = new StringBuffer();
        List msgList = new ArrayList();
        for (Iterator it = map.keySet().iterator(); it.hasNext();) {
            String key = (String) it.next();
            String value = (String) map.get(key);
            if (removeKey != null && removeKey.contains(key)) {
                continue;
            }
            msgList.add(key + "=" + URLEncoder.encode(value, "UTF-8"));
        }

        if (isSort) {
            // 排序
            Collections.sort(msgList);
        }

        for (int i = 0; i < msgList.size(); i++) {
            String msg = (String) msgList.get(i);
            if (i > 0) {
                param.append("&");
            }
            param.append(msg);
        }

        return param.toString();
    }
}
