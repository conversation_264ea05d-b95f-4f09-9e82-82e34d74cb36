package com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model;

import com.google.gson.annotations.SerializedName;
import com.wechat.pay.java.core.cipher.Encryption;
import org.apache.commons.lang3.StringUtils;

import java.util.function.UnaryOperator;

/**
 * @ClassName：MchTransferDetailEntity
 * @Description：商家转账明细
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/3/5 13:10
 * @version： v1.0
 */
public class MchTransferDetailEntity {
    /** 商户号 说明：微信支付分配的商户号 */
    @SerializedName("mchid")
    private String mchid;

    /**【商户单号】 商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一*/
    @SerializedName("out_bill_no")
    private String outBillNo;

    /**【商家转账订单号】 商家转账订单的主键，唯一定义此资源的标识*/
    @SerializedName("transfer_bill_no")
    private String transferBillNo;

    /** 商户appid 说明：申请商户号的appid或商户号绑定的appid（企业号corpid即为此appid） */
    @SerializedName("appid")
    private String appid;

    /** 【单据状态】
     * ACCEPTED: 转账已受理
     * PROCESSING: 转账锁定资金中。如果一直停留在该状态，建议检查账户余额是否足够，如余额不足，可充值后再原单重试。
     * WAIT_USER_CONFIRM: 待收款用户确认，可拉起微信收款确认页面进行收款确认
     * TRANSFERING: 转账中，可拉起微信收款确认页面再次重试确认收款
     * SUCCESS: 转账成功
     * FAIL: 转账失败
     * CANCELING: 商户撤销请求受理成功，该笔转账正在撤销中
     * CANCELLED: 转账撤销完成
     * */
    @SerializedName("state")
    private String state;

    /** 【转账金额】 转账金额单位为“分” */
    @SerializedName("transfer_amount")
    private Long transferAmount;

    /**
     * 【转账备注】 单条转账备注（微信用户会收到该备注），UTF8编码，最多允许32个字符
     * */
    @SerializedName("transfer_remark")
    private String transferRemark;

    /** 【失败原因】 订单已失败或者已退资金时 */
    @SerializedName("fail_reason")
    private String failReason;

    /** 【收款用户OpenID】 用户在商户appid下的唯一标识。*/
    @SerializedName("openid")
    private String openid;

    /** 【收款用户姓名】 收款方真实姓名。支持标准RSA算法和国密算法，公钥由微信侧提供转账金额 >= 2,000元时，该笔明细必须填写若商户传入收款用户姓名，微信支付会校验用户OpenID与姓名是否一致，并提供电子回单 */
    @SerializedName("user_name")
    @Encryption
    private String userName;

    /** 【单据创建时间】 单据受理成功时返回，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE*/
    @SerializedName("create_time")
    private String createTime;

    /** 【最后一次状态变更时间】 单据最后更新时间，按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE */
    @SerializedName("update_time")
    private String updateTime;

    public String getMchid() {
        return mchid;
    }

    public MchTransferDetailEntity setMchid(String mchid) {
        this.mchid = mchid;
        return this;
    }

    public String getOutBillNo() {
        return outBillNo;
    }

    public MchTransferDetailEntity setOutBillNo(String outBillNo) {
        this.outBillNo = outBillNo;
        return this;
    }

    public String getTransferBillNo() {
        return transferBillNo;
    }

    public MchTransferDetailEntity setTransferBillNo(String transferBillNo) {
        this.transferBillNo = transferBillNo;
        return this;
    }

    public String getAppid() {
        return appid;
    }

    public MchTransferDetailEntity setAppid(String appid) {
        this.appid = appid;
        return this;
    }

    public String getState() {
        return state;
    }

    public MchTransferDetailEntity setState(String state) {
        this.state = state;
        return this;
    }

    public Long getTransferAmount() {
        return transferAmount;
    }

    public MchTransferDetailEntity setTransferAmount(Long transferAmount) {
        this.transferAmount = transferAmount;
        return this;
    }

    public String getTransferRemark() {
        return transferRemark;
    }

    public MchTransferDetailEntity setTransferRemark(String transferRemark) {
        this.transferRemark = transferRemark;
        return this;
    }

    public String getFailReason() {
        return failReason;
    }

    public MchTransferDetailEntity setFailReason(String failReason) {
        this.failReason = failReason;
        return this;
    }

    public String getOpenid() {
        return openid;
    }

    public MchTransferDetailEntity setOpenid(String openid) {
        this.openid = openid;
        return this;
    }

    public String getUserName() {
        return userName;
    }

    public MchTransferDetailEntity setUserName(String userName) {
        this.userName = userName;
        return this;
    }

    public String getCreateTime() {
        return createTime;
    }

    public MchTransferDetailEntity setCreateTime(String createTime) {
        this.createTime = createTime;
        return this;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public MchTransferDetailEntity setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
        return this;
    }

    public MchTransferDetailEntity cloneWithCipher(UnaryOperator<String> s) {
        MchTransferDetailEntity clone = new MchTransferDetailEntity();
        clone.mchid = mchid;
        clone.outBillNo = outBillNo;
        clone.transferBillNo = transferBillNo;
        clone.appid = appid;
        clone.state = state;
        clone.transferAmount = transferAmount;
        clone.transferRemark = transferRemark;
        clone.failReason = failReason;
        clone.openid = openid;
        if(StringUtils.isNotBlank(userName)){
            clone.userName = s.apply(userName);
        }
        clone.createTime = createTime;
        clone.updateTime = updateTime;
        return clone;
    }

    @Override
    public String toString() {
        return "MchTransferDetailEntity{" +
                "mchid='" + mchid + '\'' +
                ", outBillNo='" + outBillNo + '\'' +
                ", transferBillNo='" + transferBillNo + '\'' +
                ", appid='" + appid + '\'' +
                ", state='" + state + '\'' +
                ", transferAmount=" + transferAmount +
                ", transferRemark='" + transferRemark + '\'' +
                ", failReason='" + failReason + '\'' +
                ", openid='" + openid + '\'' +
                ", userName='" + userName + '\'' +
                ", createTime='" + createTime + '\'' +
                ", updateTime='" + updateTime + '\'' +
                '}';
    }
}
