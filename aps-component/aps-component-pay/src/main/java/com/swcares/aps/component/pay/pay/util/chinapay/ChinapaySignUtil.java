package com.swcares.aps.component.pay.pay.util.chinapay;

import com.chinapay.secss.SecssUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.system.ApplicationHome;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.Map;
import java.util.Properties;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：银联签名工具 <br>
 * Package： <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020-09 -28 09：59 <br>
 * @version v1.0 <br>
 */
@Deprecated
@Slf4j
public class ChinapaySignUtil {
    private static SecssUtil secssUtil;
    private static Properties p;

    static {
        /*
         * 初始化security.properties属性文件
         *
         */
        secssUtil = new SecssUtil();//原通道
        log.info("----------->>原通道配置相关properties！！！");
        String propPath = getResourcePath("chinapay/common_auth/security_451121911158001.properties");
        Properties p = new Properties();
        try {
            p.load(new FileInputStream(propPath));
        } catch (IOException e) {
            log.error("加载授权实名认证接口签名错误：", e);
        }
        String signFilePath = propPath.substring(0, propPath.lastIndexOf("/")) + "/" + p.getProperty("sign.file");
        String verifyFilePath = propPath.substring(0, propPath.lastIndexOf("/")) + "/" + p.getProperty("verify.file");
        p.setProperty("sign.file", signFilePath);
        p.setProperty("verify.file", verifyFilePath);
        secssUtil.init(p);
    }

    public static String sign(Map signMap) {
        secssUtil = new SecssUtil();
        secssUtil.init();
        secssUtil.sign(signMap);
        log.info(secssUtil.getErrCode());
        log.info(secssUtil.getErrMsg());
        return secssUtil.getSign();
    }

    public static String sign(String merId, String signData) {
        return null;
    }

    public static boolean verify(Map map) {
        secssUtil.verify(map);
        if ("00".equals(secssUtil.getErrCode()))
            return true;
        return false;
    }

    public static String decode(String merId, String decData) {

        return null;
    }

    public static String decryptData(String encData) {
        secssUtil.decryptData(encData);
        return secssUtil.getSign();
    }

    public static String encryptData(String encData) {
        secssUtil.encryptData(encData);
        return secssUtil.getEncValue();
    }


    public static String getResourcePath(String pathRelative) {
        ApplicationHome applicationHome = new ApplicationHome(ChinapaySignUtil.class);
        //项目打包成jar包所在的根路径
        String rootPath = applicationHome.getSource().getParentFile().toString();
        log.info("单元测试会错误，此处新增日子查看rootPath是什么{}",rootPath);
        int index = rootPath.indexOf("\\");
        if (index == -1) {
            index = rootPath.indexOf("/");
        }
        String filePath = rootPath;
        if (index > -1) {
            filePath = rootPath.substring(0, index);
        }
        filePath += pathRelative.replace("\\", "/");
        return filePath;
    }

}
