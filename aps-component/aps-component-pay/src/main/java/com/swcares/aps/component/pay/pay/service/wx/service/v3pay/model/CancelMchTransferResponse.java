// Copyright 2021 Tencent Inc. All rights reserved.
//
// 运营工具-商家转账
// 取消转账接口响应体
// * 场景及业务流程： 商家转账支持微信商户向微信用户转账，为商户提供免费、安全的转账服务。支持集成到商家自有业务系统，需有研发能力可接入。商户需在用户收款流程中，拉起微信官方页面，由用户确认收款方式后方可成功转账，资金实时到账，转账成功的资金不支持退回。。
// https://pay.weixin.qq.com/doc/v3/merchant/**********
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific languagegoverning permissions and
package com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model;

import com.google.gson.annotations.SerializedName;

/** CancelMchTransferResponse */
public class CancelMchTransferResponse {
  /**【商户单号】必填 string(32)商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一*/
  @SerializedName("out_bill_no")
  private String outBillNo;

  /**【微信转账单号】必填 string(64) 微信转账单号，微信商家转账系统返回的唯一标识*/
  @SerializedName("transfer_bill_no")
  private String transferBillNo;

  /**
   * 【单据状态】 商家转账订单状态 必填 string
   * ACCEPTED: 转账已受理
   * PROCESSING: 转账锁定资金中。如果一直停留在该状态，建议检查账户余额是否足够，如余额不足，可充值后再原单重试。
   * WAIT_USER_CONFIRM: 待收款用户确认，可拉起微信收款确认页面进行收款确认
   * TRANSFERING: 转账中，可拉起微信收款确认页面再次重试确认收款
   * SUCCESS: 转账成功
   * FAIL: 转账失败
   * CANCELING: 商户撤销请求受理成功，该笔转账正在撤销中
   * CANCELLED: 转账撤销完成
   * */
  @SerializedName("state")
  private String state;


  /**
   * 【最后一次单据状态变更时间】 按照使用rfc3339所定义的格式，格式为yyyy-MM-DDThh:mm:ss+TIMEZONE
   */
  @SerializedName("update_time")
  private String updateTime;

  public String getOutBillNo() {
    return outBillNo;
  }

  public CancelMchTransferResponse setOutBillNo(String outBillNo) {
    this.outBillNo = outBillNo;
    return this;
  }

  public String getTransferBillNo() {
    return transferBillNo;
  }

  public CancelMchTransferResponse setTransferBillNo(String transferBillNo) {
    this.transferBillNo = transferBillNo;
    return this;
  }

  public String getState() {
    return state;
  }

  public CancelMchTransferResponse setState(String state) {
    this.state = state;
    return this;
  }

  public String getUpdateTime() {
    return updateTime;
  }

  public CancelMchTransferResponse setUpdateTime(String updateTime) {
    this.updateTime = updateTime;
    return this;
  }

  @Override
  public String toString() {
    return "CancelMchTransferResponse{" +
            "outBillNo='" + outBillNo + '\'' +
            ", transferBillNo='" + transferBillNo + '\'' +
            ", state='" + state + '\'' +
            ", updateTime='" + updateTime + '\'' +
            '}';
  }
}
