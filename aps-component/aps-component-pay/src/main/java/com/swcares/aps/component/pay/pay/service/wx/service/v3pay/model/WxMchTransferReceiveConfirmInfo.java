package com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model;

/**
 * @ClassName：WxMchTransferReceiveConfirmInfo
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/3/6 15:55
 * @version： v1.0
 */
public class WxMchTransferReceiveConfirmInfo {
    /**
     *【商户号】商户号，由微信支付生成并下发
     */
    private String mchId;

    /**
     * 【商户AppID】商户绑定的AppID（企业号corpid即为此AppID），由微信生成，可在公众号后台查看
     */
    private String appId;
    /**
     * 【跳转页面的package信息】商家转账付款单跳转收款页package信息,商家转账付款单受理成功时返回给商户
     */
    private String packageInfo;

    public String getMchId() {
        return mchId;
    }

    public WxMchTransferReceiveConfirmInfo setMchId(String mchId) {
        this.mchId = mchId;
        return this;
    }

    public String getAppId() {
        return appId;
    }

    public WxMchTransferReceiveConfirmInfo setAppId(String appId) {
        this.appId = appId;
        return this;
    }

    public String getPackageInfo() {
        return packageInfo;
    }

    public WxMchTransferReceiveConfirmInfo setPackageInfo(String packageInfo) {
        this.packageInfo = packageInfo;
        return this;
    }

    @Override
    public String toString() {
        return "WxMchTransferReceiveConfirmInfo{" +
                "mchId='" + mchId + '\'' +
                ", appId='" + appId + '\'' +
                ", packageInfo='" + packageInfo + '\'' +
                '}';
    }
}
