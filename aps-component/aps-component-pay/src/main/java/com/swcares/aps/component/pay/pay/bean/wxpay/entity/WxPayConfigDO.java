package com.swcares.aps.component.pay.pay.bean.wxpay.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @ClassName：WxPayConfigDO
 * @Description：存储微信支付、查询、获取openid、实名认证的实体类
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/16 15:47
 * @version： v1.0
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("COMPENSATION_WXPAY_CONFIG")
public class WxPayConfigDO extends BaseEntity {

    private long tenantId;

    /**
     * 账号中文名
     */
    private String accountName;

    private String responsiblePerson;//责任人

    private String appId;

    private String mchId;//微信支付分配的子商户号

    private String appSecret;// 服务号的应用密钥

    private String apiKey;//密钥

    private String notifyUrl;//异步回调地址

    private String signType;//签名方式

    private String inputCharset = "UTF-8";//utf-8

    private byte[] cert;//微信支付证书

    private String spbillCreateIp;//终端IP

    private String authRedirectUri;//授权回调域名

    private String businessType;//不正常航班，异常行李，客票补差

    private String remark;

    private String status;//状态 0禁用， 1启用

    private String payType;//支付类型

    private String capitalPoolId; //资金池id

    private String payTypeApiVersion; //支付类型的接口版本

    private String mchSerialNumber; //商户API证书序列号

    private String apiV3Key; //支付APIV3的key

    private String mchPrivateKey; //商户API证书私钥

    private String mchTrsSceneId; //商家转账场景ID

    private String mchTrsRemark;  //商家转账remark

    private String mchTrsSceneRptType; //商家转账RptType

    private String mchTrsSceneRptContent; //商家转账RptContent
}
