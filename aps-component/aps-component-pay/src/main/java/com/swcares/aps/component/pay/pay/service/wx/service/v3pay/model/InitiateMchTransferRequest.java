// Copyright 2021 Tencent Inc. All rights reserved.
//
// 运营工具-商家转账
// 商家转账接口请求体
//
// * 场景及业务流程： 商家转账支持微信商户向微信用户转账，为商户提供免费、安全的转账服务。支持集成到商家自有业务系统，需有研发能力可接入。商户需在用户收款流程中，拉起微信官方页面，由用户确认收款方式后方可成功转账，资金实时到账，转账成功的资金不支持退回。。
// https://pay.weixin.qq.com/doc/v3/merchant/4012711988

package com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model;

import com.google.gson.annotations.SerializedName;
import com.wechat.pay.java.core.cipher.Encryption;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.function.UnaryOperator;

/** InitiateMchTransferRequest */
public class InitiateMchTransferRequest {
  /**【商户AppID】必填 string(32)是微信开放平台和微信公众平台为开发者的应用程序(APP、小程序、公众号、企业号corpid即为此AppID)提供的一个唯一标识。此处，可以填写这四种类型中的任意一种APPID，但请确保该appid与商户号有绑定关系。 */
  @SerializedName("appid")
  private String appid;

  /**【商户单号】必填 string(32)商户系统内部的商家单号，要求此参数只能由数字、大小写字母组成，在商户系统内部唯一*/
  @SerializedName("out_bill_no")
  private String outBillNo;

  /**【转账场景ID】 必填 string(36)该笔转账使用的转账场景，可前往“商户平台-产品中心-商家转账”中申请。如：1001-现金营销*/
  @SerializedName("transfer_scene_id")
  private String transferSceneId;

  /**【收款用户OpenID】 用户在商户appid下的唯一标识。发起转账前需获取到用户的OpenID */
  @SerializedName("openid")
  private String openid;

  /**【收款用户姓名】 选填 string 收款方真实姓名。需要加密传入，支持标准RSA算法和国密算法，公钥由微信侧提供。
   转账金额 >= 2,000元时，该笔明细必须填写
   若商户传入收款用户姓名，微信支付会校验收款用户与输入姓名是否一致，并提供电子回单 */
  @SerializedName("user_name")
  @Encryption
  private String userName;

  /**【转账金额】必填 integer 转账金额单位为“分”。*/
  @SerializedName("transfer_amount")
  private Integer transferAmount;

  /**【转账备注】必填 string(32) 转账备注，用户收款时可见该备注信息，UTF8编码，最多允许32个字符*/
  @SerializedName("transfer_remark")
  private String transferRemark;

  @SerializedName("transfer_scene_report_infos")
  private List<TransferSceneReportInfo> transferSceneReportInfos = new ArrayList<TransferSceneReportInfo>();

  public InitiateMchTransferRequest cloneWithCipher(UnaryOperator<String> s){
    InitiateMchTransferRequest clone = new InitiateMchTransferRequest();
    clone.appid = appid;
    clone.outBillNo = outBillNo;
    clone.transferSceneId = transferSceneId;
    clone.openid = openid;
    clone.transferAmount = transferAmount;
    clone.transferRemark = transferRemark;
    clone.userName = s.apply(userName);
    clone.transferSceneReportInfos.addAll(transferSceneReportInfos);
    return clone;
  }

  public void addTransferSceneReportInfo(String infoType, String infoContent){
    this.transferSceneReportInfos.add(new TransferSceneReportInfo().setInfoType(infoType).setInfoContent(infoContent));
  }

  public String getAppid() {
    return appid;
  }

  public InitiateMchTransferRequest setAppid(String appid) {
    this.appid = appid;
    return this;
  }

  public String getOutBillNo() {
    return outBillNo;
  }

  public InitiateMchTransferRequest setOutBillNo(String outBillNo) {
    this.outBillNo = outBillNo;
    return this;
  }

  public String getTransferSceneId() {
    return transferSceneId;
  }

  public InitiateMchTransferRequest setTransferSceneId(String transferSceneId) {
    this.transferSceneId = transferSceneId;
    return this;
  }

  public String getOpenid() {
    return openid;
  }

  public InitiateMchTransferRequest setOpenid(String openid) {
    this.openid = openid;
    return this;
  }

  public String getUserName() {
    return userName;
  }

  public InitiateMchTransferRequest setUserName(String userName) {
    this.userName = userName;
    return this;
  }

  public Integer getTransferAmount() {
    return transferAmount;
  }

  public InitiateMchTransferRequest setTransferAmount(Integer transferAmount) {
    this.transferAmount = transferAmount;
    return this;
  }

  public String getTransferRemark() {
    return transferRemark;
  }

  public InitiateMchTransferRequest setTransferRemark(String transferRemark) {
    this.transferRemark = transferRemark;
    return this;
  }

  public List<TransferSceneReportInfo> getTransferSceneReportInfos() {
    return transferSceneReportInfos;
  }

  public InitiateMchTransferRequest setTransferSceneReportInfos(List<TransferSceneReportInfo> transferSceneReportInfos) {
    this.transferSceneReportInfos = transferSceneReportInfos;
    return this;
  }

  @Override
  public String toString() {
    return "InitiateMchTransferRequest{" +
            "appid='" + appid + '\'' +
            ", outBillNo='" + outBillNo + '\'' +
            ", transferSceneId='" + transferSceneId + '\'' +
            ", openid='" + openid + '\'' +
            ", userName='" + userName + '\'' +
            ", transferAmount=" + transferAmount +
            ", transferRemark='" + transferRemark + '\'' +
            ", transferSceneReportInfos=" + transferSceneReportInfos +
            '}';
  }
}
