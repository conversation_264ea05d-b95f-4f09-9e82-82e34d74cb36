package com.swcares.aps.component.pay.pay.service.wx;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.component.pay.decoder.PayErrorException;
import com.swcares.aps.component.pay.pay.api.BasePayService;
import com.swcares.aps.component.pay.pay.api.TransactionType;
import com.swcares.aps.component.pay.pay.bean.Authentication;
import com.swcares.aps.component.pay.pay.bean.PayConstant;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxAuthentication;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxPayConfigStorage;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxTransferOrder;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.WxMchTransferService;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.InitiateMchTransferRequest;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.InitiateMchTransferResponse;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.MchTransferDetailEntity;
import com.swcares.aps.component.pay.pay.util.Utils;
import com.swcares.aps.component.pay.pay.util.wx.HttpClientUtil;
import com.swcares.aps.component.pay.pay.util.wx.WXPayConstants;
import com.swcares.aps.component.pay.pay.util.wx.WXPayUtil;
import com.swcares.aps.component.pay.utils.PayErrors;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.RSAAutoCertificateConfig;
import com.wechat.pay.java.core.exception.HttpException;
import com.wechat.pay.java.core.exception.MalformedMessageException;
import com.wechat.pay.java.core.exception.ServiceException;
import com.wechat.pay.java.core.util.IOUtil;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLContexts;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyStore;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;

/**
 * ClassName：com.swcares.psi.service.wxpay.WxPayService <br>
 * Description：微信-支付处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Slf4j
public class WxPayService extends BasePayService<WxTransferOrder> {

	/** 请求超时*/
	private final static int REQ_TIME_OUT = 10000;
	/** 连接超时*/
	private final static int CON_TIME_OUT = 10000;
	/** 写超时*/
	private final static int WRITE_TIME_OUT = 10000;

	private WxMchTransferService wxMchTransferService;

	@Getter
	private WxPayConfigStorage wxPayConfigStorage;

	private CloseableHttpClient httpClient;

    private CloseableHttpResponse response;

	private String appId;

	/** 设置请求和传输超时时间*/
	private static final RequestConfig requestConfig =
			RequestConfig.custom().setSocketTimeout(REQ_TIME_OUT).setConnectTimeout(CON_TIME_OUT).build();


	public WxPayService(WxPayConfigStorage wxPayConfigStorageIrr) {
		this.wxPayConfigStorage = wxPayConfigStorageIrr;
		isOpenConnection();
		initMchTransV3ApiClient();
	}

	private void initMchTransV3ApiClient() {
		if(!StringUtils.equalsIgnoreCase(PayConstant.PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3, wxPayConfigStorage.getPayTypeApiVersion())){
			log.info("当前接口版本为:{},{}跳过商家转账V3接口桩初始化",  wxPayConfigStorage.getPayTypeApiVersion(),wxPayConfigStorage.getMchId());
			return;
		}

		try{
			String privateKey = wxPayConfigStorage.getMchPrivateKey();
			if(StringUtils.isBlank(wxPayConfigStorage.getMchSerialNumber()) ||
					StringUtils.isBlank(wxPayConfigStorage.getApiV3Key()) ||
					StringUtils.isBlank(privateKey) ||
					StringUtils.isBlank(wxPayConfigStorage.getMchTrsSceneId()) ||
					StringUtils.isBlank(wxPayConfigStorage.getMchTrsSceneRptType()) ||
					StringUtils.isBlank(wxPayConfigStorage.getMchTrsSceneRptContent())){
				log.info("微信商户转账V3接口桩初始化失败,必填参数为空,{},{}",wxPayConfigStorage.getPayTypeApiVersion(),wxPayConfigStorage.getMchId());
				return;
			}
			// 初始化商户配置
			Config config =
					new RSAAutoCertificateConfig.Builder() //自动下载平台证书
							.merchantId(wxPayConfigStorage.getMchId())
							//使用 com.wechat.pay.java.core.util
							.privateKey(privateKey)
							.merchantSerialNumber(wxPayConfigStorage.getMchSerialNumber())
							.apiV3Key(wxPayConfigStorage.getApiV3Key())
							.build();

			// 初始化服务
			this.wxMchTransferService = new WxMchTransferService.Builder()
					.config(config)
					.readTimeoutMs(REQ_TIME_OUT)
					.writeTimeoutMs(WRITE_TIME_OUT)
					.connectTimeoutMs(CON_TIME_OUT)
					.build();
			log.info("微信商户转账V3接口桩初始化成功,{},{},{}",wxPayConfigStorage.getPayTypeApiVersion(),wxPayConfigStorage.getMchId(),wxPayConfigStorage.getMchTrsSceneId());
		}catch (Exception e){
			log.error("商家转账V3接口桩初始化失败,{},{}",wxPayConfigStorage.getPayTypeApiVersion(),wxPayConfigStorage.getMchId(),e);
		}

	}

	/**
	 * Title：isOpenConnection <br>
	 * Description： 打开带证书的httpClient<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:37 <br>
	 * @param
	 * @return
	 */
	private void isOpenConnection(){
		if(ObjectUtils.isNotEmpty(wxPayConfigStorage.getCert())){
			try {
				openConnection();
			} catch (Exception e) {
				log.error("微信—创建http链接失败!",e);
				// 创建失败的时候，不用抛出异常，测试环境是没有支付配置的
				// throw new PayErrorException(CommonErrors.CONFIG_DATA_NULL);
			}
		}
	}

	/**
	 * 创建链接{证书}
	 * @throws Exception
	 */
	private void openConnection() throws Exception {
        KeyStore ks = KeyStore.getInstance("PKCS12");
//		// log.info("微信支付加载证书文件路径：" + path);
		//InputStream stream = new FileInputStream("/Users/<USER>/code/git_workspace/aps/aps-component/aps-component-pay/src/main/resources/wxpay/irrflight/cert/apiclient_cert.p12");
		InputStream stream  = wxPayConfigStorage.getCert();
	  	ks.load(stream, wxPayConfigStorage.getMchId().toCharArray());
        SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(ks, wxPayConfigStorage.getMchId().toCharArray()).build();
        Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
                .register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sslcontext)).build();
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
        httpClient = HttpClients.custom().setConnectionManager(connManager).setConnectionManagerShared(true).build();
    }

	/**
	 * Title：httpostBeltCert <br>
	 * Description： 发起带证书的请求<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:36 <br>
	 * @param  url 请求地址, data 参数xml
	 * @return java.lang.String
	 */
	private String httpostBeltCert(String url,String data) throws IOException{
		try {
			HttpPost httpost = new HttpPost(url);
			httpost.setConfig(requestConfig);
			httpost.setEntity(new StringEntity(data, "UTF-8"));
			response = httpClient.execute(httpost);
			try {
				HttpEntity entity = response.getEntity();
				String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
				EntityUtils.consume(entity);
				return jsonStr;
			} finally {
				httpost.releaseConnection();
			}
		} finally {
			if (response != null) {
				response.close();
			}
		}
	}

	/**
	 * Title：getPublicParameters <br>
	 * Description： 获取公共参数 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.util.Map<java.lang.String,java.lang.Object>
	 */
	private Map<String, Object> getPublicParameters() {
        Map<String, Object> parameters = new TreeMap<>();
		this.appId = wxPayConfigStorage.getAppId();
        parameters.put(WXPayConstants.APPID, wxPayConfigStorage.getAppId());
        parameters.put(WXPayConstants.MCH_ID, wxPayConfigStorage.getMchId());
		parameters.put("mchid", wxPayConfigStorage.getMchId());
        parameters.put(WXPayConstants.NONCE_STR, WXPayUtil.generateNonceStr());
        return parameters;
    }


	/**
	 * Title：authCodeUrl <br>
	 * Description： 获取微信授权url<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:31 <br>
	 * @param
	 * @return java.lang.String
	 */
	@Override
	public String authCodeUrl() {
    	return new StringBuffer(WxTransactionType.AUTH_CODE.getMethod())
			.append("appid=").append(wxPayConfigStorage.getAppId())
			.append("&redirect_uri=").append(wxPayConfigStorage.getAuthRedirectUri())
			.append("&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect").toString();
	}

	/**
	 * Title：getOpenid <br>
	 * Description： 获取openid<br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:32 <br>
	 * @param  code 授权后code
	 * @return java.lang.String
	 */
	@Override
	public String getOpenid(String code) {
		String url = new StringBuffer(WxTransactionType.AUTH_OPENID.getMethod())
				.append("appid=").append(wxPayConfigStorage.getAppId())
				.append("&secret=").append(wxPayConfigStorage.getAppSecret())
				.append("&code=").append(code).append("&grant_type=authorization_code").toString();
		String result = HttpClientUtil.executeByGET(url);
		log.info("微信获取openid---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String openid = ob.getString("openid");
		if(StringUtils.isBlank(openid)){
			log.error("微信获取openid异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(PayErrors.FAILURE,"微信获取openid异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return openid;
	}


	@Override
	public String getRealNameAuthUrl(String scope){
		return new StringBuffer(WxTransactionType.PAY_IDENTITY_INDEX.getMethod())
				.append("mch_id=").append(wxPayConfigStorage.getMchId())
				.append("&appid=").append(wxPayConfigStorage.getAppId())
				.append("&redirect_uri=").append(wxPayConfigStorage.getAuthRedirectUri())
				.append("&response_type=code&scope=").append(scope).append("&state=STATE#wechat_redirect").toString();
	}


	/**
	 * Title：getPayIdentityToken <br>
	 * Description： 实名认证-获取token<br>
	 * author：傅欣荣 <br>
	 * date：2020/6/22 18:55 <br>
	 * @param
	 * @return
	 */
	@Override
	public String getPayIdentityToken(String code,String openid){
		String sign = "";
		Map<String, Object> parameters = new TreeMap<>();
		parameters.put("mch_id",wxPayConfigStorage.getMchId());
		parameters.put("appid",wxPayConfigStorage.getAppId());
		parameters.put("openid",openid);
		parameters.put("code",code);
		parameters.put("scope","SCOPE");
		parameters.put("grant_type","authorization_code");
		parameters.put("sign_type","HMAC-SHA256");
		try {
			sign = WXPayUtil.generateSignature(parameters,wxPayConfigStorage.getApiKey(),WXPayConstants.HMACSHA256.toString());
		} catch (Exception e) {
			log.error("微信请求参数sign签名异常！ {}",e);
			throw new PayErrorException(PayErrors.FAILURE,"微信请求参数sign签名异常"+e);
		}
		parameters.put("sign",sign);
		String url = new StringBuffer(WxTransactionType.PAY_IDENTITY_TOKEN.getMethod())
				.append(WXPayUtil.getMapToString(parameters)).toString();
		String result = HttpClientUtil.executeByGET(url);
		log.info("微信获取实名认证access_token---请求链接【"+url+"】,反馈结果："+result);
		JSONObject ob = JSONObject.parseObject(result);
		String access_token = ob.getString("access_token");
		if(StringUtils.isBlank(access_token) || !ob.getString("retcode").equals("0")){
			log.error("微信获取实名认证access_token异常，请求参数{}，返回结果{}",url,result);
			throw new PayErrorException(PayErrors.FAILURE,"微信获取实名认证access_token异常，请求参数{"+url+"}，返回结果{"+result+"}");
		}
		return access_token;

	}

	@Override
	public <A extends Authentication> Map<String, Object> authentication(A order) {
		WxAuthentication wxOder = (WxAuthentication)order;
		Map<String, Object> parameters = getPublicParameters();
		parameters.put("version","1.0");
		parameters.put("real_name",order.getName());
		parameters.put("cred_id",order.getIdNo());
		parameters.put("cred_type","1");
		parameters.put("openid",wxOder.getOpenid());
		parameters.put("access_token",wxOder.getToken());
		parameters.put("sign_type",WXPayConstants.HMACSHA256);
		parameters.put("sign","");
		return initRequestByType(parameters, (WxTransactionType) wxOder.getTransactionType());
	}



	@Override
	public Map<String, Object> transfer(WxTransferOrder order) {
		if(StringUtils.equals(this.wxPayConfigStorage.getPayTypeApiVersion(),PayConstant.PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){
			return payByV3Api(order);
		}else{
			Map<String, Object> parameters = getPublicParameters();
			parameters.put("partner_trade_no", order.getOutNo());
			parameters.put("amount", Utils.conversionCentAmount(order.getAmount()));
			parameters.put("desc", order.getRemark());
			parameters.put("spbill_create_ip", wxPayConfigStorage.getSpbillCreateIp());
			if (null == order.getTransactionType()) {
				log.error("微信转账类型必填:transactionType！ ");
				throw new PayErrorException(PayErrors.FAILURE,"微信转账类型必填:transactionType");
			}
			((WxTransactionType) order.getTransactionType()).setAttribute(parameters, order);
			Map<String, Object> result = initRequestByType(parameters, (WxTransactionType) order.getTransactionType());
			result.put("pay_type_api_version", this.wxPayConfigStorage.getPayTypeApiVersion());
			return result;
		}
	}

	private Map<String, Object> payByV3Api(WxTransferOrder order) {
		if (wxMchTransferService == null) {
			log.error("微信商家转账V3接口服务未初始化！");
			throw new PayErrorException(PayErrors.FAILURE, "微信商家转账V3接口服务未初始化");
		}

		Map<String, Object> returnMap = new HashMap<>();
		returnMap.put("pay_type_api_version", this.wxPayConfigStorage.getPayTypeApiVersion());
		InitiateMchTransferResponse initiateMchTransferResponse=null;
		try {
			// 调用 V3 接口发起转账
			Integer amount = Utils.conversionCentAmount(order.getAmount()); // 金额（分）

			InitiateMchTransferRequest request=new InitiateMchTransferRequest();
			request.setOpenid(order.getPayeeAccount());
			request.setTransferAmount(amount);
			request.setOutBillNo(order.getOutNo());
			request.setTransferSceneId(this.getWxPayConfigStorage().getMchTrsSceneId());
			request.setTransferRemark(this.getWxPayConfigStorage().getMchTrsRemark());
			request.setUserName(order.getPayeeName());
			request.addTransferSceneReportInfo(this.getWxPayConfigStorage().getMchTrsSceneRptType(),this.getWxPayConfigStorage().getMchTrsSceneRptContent());
			request.setAppid(wxPayConfigStorage.getAppId());
			initiateMchTransferResponse = wxMchTransferService.initiateMchTransfer(request);
			log.info("商家转账执行成功,{}",initiateMchTransferResponse.toString());
		}catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
			// 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
			log.error("微信商家转账V3接口，执行【支付】失败，申请单号：{},e.getResponseBody:{}", order.getOutNo(),e.getResponseBody(),e);
			returnMap.put("return_code", PayConstant.PAY_SUCCESS);
			returnMap.put("result_code", PayConstant.PAY_FAIL);
			returnMap.put("api_version_error_code", "MCH_TRANS_V3_ERROR");
			returnMap.put("err_code", e.getErrorCode());
			returnMap.put("err_code_des",StringUtils.substring(e.getResponseBody(),0,2000));

		} catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
			// 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
			log.error("微信商家转账V3接口补偿支付定时任务，执行【支付】失败，申请单号：{},调用e.getMessage:{}", order.getOutNo(),e.getMessage(),e);
			returnMap.put("return_code", PayConstant.PAY_SUCCESS);
			returnMap.put("result_code", PayConstant.PAY_FAIL);
			returnMap.put("api_version_error_code", "MCH_TRANS_V3_ERROR");
			returnMap.put("err_code", "MalformedMessageException");
			returnMap.put("err_code_des",StringUtils.substring(e.getMessage(),0,2000));
		}catch (Exception e) {
			log.error("微信商家转账V3接口补偿支付定时任务，执行【支付】失败，申请单号：{},调用e.getMessage:{}", order.getOutNo(),e.getMessage(),e);
			throw new PayErrorException(PayErrors.FAILURE, "微信商家转账V3接口调用失败：" + e.getMessage());
		}
		if(initiateMchTransferResponse==null){
			log.info("微信商家转账V3支付接口返回给业务方结果,{}",returnMap);
			return returnMap;
		}

		returnMap.put("payment_no", initiateMchTransferResponse.getTransferBillNo());
		returnMap.put("return_code", initiateMchTransferResponse.getState());
		returnMap.put("trans_sub_code", initiateMchTransferResponse.getState());
		returnMap.put("wx_receive_package_info", initiateMchTransferResponse.getPackageInfo());
		//支付成功
		if (PayConstant.MCH_TRANSFER_FINAL_SUCCESS_SATES.contains(initiateMchTransferResponse.getState())) {
			MchTransferDetailEntity transferDetailEntity = wxMchTransferService.getMchTransferDetailByOutBillNo(order.getOutNo());
			DateTime parse = DateUtil.parse(transferDetailEntity.getUpdateTime());
			returnMap.put("payment_time", DateUtils.formatDate(parse.toJdkDate(),  DateUtils.PTN_YMD_HMS));
			//支付状态设置为已支付
			returnMap.put("result_code", PayConstant.PAY_SUCCESS);
			returnMap.put("return_code", PayConstant.PAY_SUCCESS);
		}

		if (PayConstant.MCH_TRANSFER_FINAL_FAIL_SATES.contains(initiateMchTransferResponse.getState())) {
			MchTransferDetailEntity transferDetailEntity = wxMchTransferService.getMchTransferDetailByOutBillNo(order.getOutNo());
			DateTime parse = DateUtil.parse(transferDetailEntity.getUpdateTime());
			returnMap.put("payment_time", DateUtils.formatDate(parse.toJdkDate(),  DateUtils.PTN_YMD_HMS));
			//支付状态设置为已支付


			returnMap.put("err_code", transferDetailEntity.getFailReason());
			returnMap.put("err_code_des",transferDetailEntity.getFailReason());

			returnMap.put("return_code", PayConstant.PAY_SUCCESS);
			returnMap.put("result_code", PayConstant.PAY_FAIL);
			returnMap.put("api_version_error_code", "MCH_TRANS_V3_ERROR");

		}

		log.info("微信商家转账V3支付接口返回给业务方结果,{}",returnMap);
		return returnMap;
	}


	@Override
	public Map<String, Object> query(String tradeNo, String outTradeNo, TransactionType transactionType) {
		if(StringUtils.equals(this.wxPayConfigStorage.getPayTypeApiVersion(),PayConstant.PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){
			return queryByV3Api(outTradeNo);
		}else{
			Map<String, Object> parameters = getPublicParameters();
			parameters.put("partner_trade_no",outTradeNo);
//		parameters.remove(WXPayConstants.APPID);
			parameters.remove("mchid");
			Map<String, Object> returnMap = initRequestByType(parameters, (WxTransactionType) transactionType);
			returnMap.put("pay_type_api_version", this.wxPayConfigStorage.getPayTypeApiVersion());
			return returnMap;
		}
	}

	private Map<String, Object> queryByV3Api(String outTradeNo) {
		MchTransferDetailEntity transferDetailEntity=null;
		Map<String, Object> returnMap = new HashMap<>();
		returnMap.put("pay_type_api_version", this.wxPayConfigStorage.getPayTypeApiVersion());
		try{
			transferDetailEntity = wxMchTransferService.getMchTransferDetailByOutBillNo(outTradeNo);
		}catch (HttpException e) { // 发送HTTP请求失败
			// 调用e.getHttpRequest()获取请求打印日志或上报监控，更多方法见HttpException定义
			returnMap.put("err_code_des", StringUtils.substring(e.getMessage(),0,2000));
			log.error("微信商家转账V3接口补偿查询定时任务,查询【微信】支付记录失败，申请单号：{}", outTradeNo, e);


		} catch (ServiceException e) { // 服务返回状态小于200或大于等于300，例如500
			// 调用e.getResponseBody()获取返回体打印日志或上报监控，更多方法见ServiceException定义
			returnMap.put("err_code", e.getErrorCode());
			returnMap.put("err_code_des",  StringUtils.substring(e.getResponseBody(),0,2000));
			log.error("微信商家转账V3接口补偿查询定时任务，查询【微信】支付记录失败，申请单号：{}", outTradeNo,e);

		} catch (MalformedMessageException e) { // 服务返回成功，返回体类型不合法，或者解析返回体失败
			// 调用e.getMessage()获取信息打印日志或上报监控，更多方法见MalformedMessageException定义
			returnMap.put("err_code_des", StringUtils.substring(e.getMessage(),0,2000) );
			log.error("微信商家转账V3接口补偿查询定时任务，查询【微信】支付记录失败，申请单号：{}", outTradeNo,e);

		}catch (Exception e){

			returnMap.put("err_code_des", StringUtils.substring(e.getMessage(),0,2000));
			log.error("微信商家转账V3接口补偿查询定时任务，查询【微信】支付记录失败，申请单号：{}", outTradeNo,e);

		}
		if(transferDetailEntity==null){
			return returnMap;
		}

		returnMap.put("return_code", transferDetailEntity.getState());
		returnMap.put("trans_sub_code", transferDetailEntity.getState());
		returnMap.put("detail_id", transferDetailEntity.getTransferBillNo());
		if (PayConstant.MCH_TRANSFER_FINAL_SUCCESS_SATES.contains(transferDetailEntity.getState())) {
			returnMap.put("status", PayConstant.PAY_SUCCESS);
			DateTime parse = DateUtil.parse(transferDetailEntity.getUpdateTime());
			returnMap.put("payment_time", DateUtils.formatDate(parse.toJdkDate(),  DateUtils.PTN_YMD_HMS));

		}
		if(PayConstant.MCH_TRANSFER_FINAL_FAIL_SATES.contains(transferDetailEntity.getState())){
			returnMap.put("status", PayConstant.PAY_FAILED);
			DateTime parse = DateUtil.parse(transferDetailEntity.getUpdateTime());
			returnMap.put("payment_time", DateUtils.formatDate(parse.toJdkDate(),  DateUtils.PTN_YMD_HMS));
			returnMap.put("err_code", transferDetailEntity.getFailReason());

		}
		log.info("微信商家转账V3查询接口返回给业务方结果,{}",returnMap);
		return returnMap;
	}

	public MchTransferDetailEntity getMchTransferDetailByOutBillNo(String outBillNo){
		return wxMchTransferService.getMchTransferDetailByOutBillNo(outBillNo);
	}

	/**
	 * Title：initRequestByType <br>
	 * Description： 发起http请求 <br>
	 * author：傅欣荣 <br>
	 * date：2020/2/28 17:33 <br>
	 * @param  parameters 请求参数,
	 * @param transactionType 请求url，是否需要证书
	 * @return java.util.Map<java.lang.String,java.lang.Object> http响应参数
	 */
	private Map<String, Object> initRequestByType(Map<String, Object> parameters,WxTransactionType transactionType){
    	//将请求参数转换为xml格式
		String data;
		String returnData;
		Map<String, Object> returnMap = new HashMap<>();
		try {
			data = WXPayUtil.generateSignedXml(parameters, wxPayConfigStorage.getApiKey(),wxPayConfigStorage.getSignType());
		} catch (Exception e) {
			log.error("微信请求参数转为xml格式：转换异常！ ",e);
			throw new PayErrorException(PayErrors.FAILURE,"微信请求参数转为xml格式：转换异常！"+e);
		}
		//判断是否需要证书
		try {
			if(transactionType.isNeedCert()){
				returnData = httpostBeltCert(transactionType.getMethod(),data);
			}else{
				returnData = HttpClientUtil.postXML(transactionType.getMethod(),data);
			}
			log.info("微信代付返回的原始报文信息：【{}】", returnData);
		} catch (IOException e) {
			log.error("微信-发起HTTP请求：异常 ！",e);
			throw new PayErrorException(PayErrors.FAILURE,"微信-发起HTTP请求：异常 ！"+e);
		}
		try {
			returnMap = WXPayUtil.xmlToMap(returnData);
			returnMap.put("mchid",this.appId);
			return returnMap;
		} catch (Exception e) {
			log.error("微信—反馈结果转换类型Map：转换异常！ ",e);
			throw new PayErrorException(PayErrors.FAILURE,"微信—反馈结果转换类型Map：转换异常！"+e);
		}
	}

	public String getAppId() {
		return this.wxPayConfigStorage.getAppId();
	}

	public String getAppSecret() {
		return this.wxPayConfigStorage.getAppSecret();
	}

	public String getMchId() {
		return this.wxPayConfigStorage.getMchId();
	}
/**
	public static void main(String[] args) throws Exception {

		//1.打开微信连接
		String CERTPATH = "wxpay/irrflight/cert/apiclient_cert.p12";
		String MCHID="1510023161";
		String MCHAPPID = "wx60cdc9e84eba975f";
		String  KEY ="H8CJhSK7dstK720J9Hhj8jh6nshjhSDF";
		String  check_name= "FORCE_CHECK";
		String  localIp ="***********";

		CloseableHttpClient httpClient ;
		CloseableHttpResponse response;

		KeyStore ks = KeyStore.getInstance("PKCS12");
		//System.out.println(new ClassPathResource(CERTPATH).getInputStream());
		ks.load(new ClassPathResource(CERTPATH).getInputStream(), MCHID.toCharArray());
		SSLContext sslcontext = SSLContexts.custom().loadKeyMaterial(ks, MCHID.toCharArray()).build();
		Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory> create()
				.register("http", PlainConnectionSocketFactory.INSTANCE).register("https", new SSLConnectionSocketFactory(sslcontext)).build();
		PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
		httpClient = HttpClients.custom().setConnectionManager(connManager).build();


		// 2.组装数据
		WxTransferOrder wxpt = new WxTransferOrder();
		wxpt.setOutNo("**********");
		wxpt.setPayeeAccount("ofSj-jklL8rRpdWter_soktpMgk0");//openId
		wxpt.setPayeeName("郑雯雯");
		wxpt.setRemark("航延补偿金");
		wxpt.setAmount(new BigDecimal(1.0));
		wxpt.setBatchNo("**********");

		Map<String, Object> parameters = new TreeMap<>();
		parameters.put("mch_appid", MCHAPPID);
		parameters.put("mchid", MCHID);
		parameters.put("partner_trade_no", wxpt.getOutNo());
		parameters.put("amount", Utils.conversionCentAmount(wxpt.getAmount()));
		parameters.put("desc", wxpt.getRemark());
		parameters.put("spbill_create_ip",localIp );
		parameters.put("openid",wxpt.getPayeeAccount() );
		parameters.put("check_name",check_name);
		parameters.put("re_user_name",wxpt.getPayeeName());



		String dataValue="";
		String  nonceStr= UUID.randomUUID().toString().replaceAll("-", "").substring(0, 32);

		String  signStr = new StringBuilder("amount=").append(wxpt.getAmount())
				.append("&check_name=").append(check_name)
				.append("&desc=").append(wxpt.getRemark())
				.append("&mch_appid=").append(MCHAPPID)
				.append("&mchid=").append(MCHID)
				.append("&nonce_str=").append(nonceStr)
				.append("&openid=").append(wxpt.getPayeeAccount())
				.append("&partner_trade_no=").append(wxpt.getOutNo())
				.append("&re_user_name=").append(wxpt.getPayeeName())
				.append("&spbill_create_ip=").append(localIp).toString();

		//3.封装MD5签名数据
		String data = signStr  + "&key=" + KEY;

		java.security.MessageDigest md = MessageDigest.getInstance("MD5");
		byte[] array = md.digest(data.getBytes("UTF-8"));
		StringBuilder sb = new StringBuilder();
		for (byte item : array) {
			sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));
		}
		//MD5转换后的字符串
		String  md5SignStr =  sb.toString().toUpperCase();
		md5SignStr = md5SignStr.toUpperCase();
		log.info("【微信签名封装参数】： "+md5SignStr);
		//获取随机字符串
		parameters.put("nonce_str" , WXPayUtil.generateNonceStr());
		parameters.put("sign",md5SignStr);

		//4.转换对象为微信支付的对象
		String data2 = WXPayUtil.generateSignedXml(parameters, KEY,"MD5");

		//5.远程调用
		try {
			HttpPost httpost = new HttpPost("https://api.mch.weixin.qq.com/mmpaymkttransfers/promotion/transfers");
			httpost.setConfig(requestConfig);
			httpost.setEntity(new StringEntity(data2, "UTF-8"));
			response = httpClient.execute(httpost);
			try {
				HttpEntity entity = response.getEntity();
				String jsonStr = EntityUtils.toString(response.getEntity(), "UTF-8");
				EntityUtils.consume(entity);
				log.info(jsonStr) ;
			} finally {
				httpost.releaseConnection();
			}
		} finally {
			httpClient.close();
		}


		log.info("微信支付反馈结果： " + response);

	}
**/
}
