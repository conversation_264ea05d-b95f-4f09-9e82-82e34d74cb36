package com.swcares.aps.component.pay.pay.service.chinapay.controller;

import com.swcares.aps.component.pay.pay.bean.chinapay.dto.ChinaPayConfigDto;
import com.swcares.aps.component.pay.pay.service.chinapay.service.ChinaPayConfigService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * @ClassName：ChinaPayConfigServiceController
 * @Description：银联支付配置的控制类
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/23 11:56
 * @version： v1.0
 */
@Slf4j
@RequestMapping("/pay/config/chinapay")
@RestController
@Api(tags = "补偿通支付配置接口")
@ApiVersion(value = "补偿通支付配置接口")
public class ChinaPayConfigController extends BaseController {

    @Autowired
    private ChinaPayConfigService chinaPayConfigService;

    /**
     * @title ChinaPayConfigController.java
     * @description 添加银联支付信息
     * <AUTHOR>
     * @date 2024/5/23 17:03
     * @param chinaPayConfigDto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/add")
    @ApiOperation(value = "新增银联支付配置信息")
    public BaseResult<Object> add(ChinaPayConfigDto chinaPayConfigDto){
        return ok(chinaPayConfigService.add(chinaPayConfigDto));
    }

    /**
     * @title ChinaPayConfigController.java
     * @description 修改银联支付配置信息
     * <AUTHOR>
     * @date 2024/5/24 09:20
     * @param chinaPayConfigDto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/update")
    @ApiOperation(value = "修改银联支付配置信息")
    public BaseResult<Object> update(ChinaPayConfigDto chinaPayConfigDto){
        return ok(chinaPayConfigService.update(chinaPayConfigDto));
    }

    /**
     * @title ChinaPayConfigController.java
     * @description 查询银联配置详情信息
     * <AUTHOR>
     * @date 2024/5/24 09:12
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/detail")
    @ApiOperation(value = "查询银联配置详情信息")
    public BaseResult<Object> detail(String id) throws IOException {
        return ok(chinaPayConfigService.detail(id));
    }

    /**
     * @title ChinaPayConfigController.java
     * @description 禁用或者启用配置 0：禁用， 1：启用
     * <AUTHOR>
     * @date 2024/5/17 14:14
     * @param id
     * @param status
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/updateStatus")
    @ApiOperation(value = "禁用或者启用配置 0：禁用， 1：启用")
    public BaseResult<Object> updateStatus(String id, String status){
        return ok(chinaPayConfigService.updateStatus(id, status));
    }

}
