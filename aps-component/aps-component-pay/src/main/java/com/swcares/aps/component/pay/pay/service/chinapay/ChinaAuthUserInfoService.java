package com.swcares.aps.component.pay.pay.service.chinapay;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.chinapay.secss.SecssUtil;
import com.swcares.aps.component.pay.pay.bean.chinapay.ChinaPayConstant;
import com.swcares.aps.component.pay.pay.service.chinapay.bean.AuthType;
import com.swcares.aps.component.pay.pay.service.chinapay.bean.ChinaPayAuthRequestDTO;
import com.swcares.aps.component.pay.pay.util.chinapay.Base64;
import com.swcares.aps.component.pay.pay.util.chinapay.ChinapaySignUtil;
import com.swcares.aps.component.pay.pay.util.chinapay.Encryptor;
import com.swcares.aps.component.pay.pay.util.chinapay.StringUtil;
import com.swcares.aps.component.pay.utils.DateUtils;
import com.swcares.aps.component.pay.utils.HttpSendUtil;
import com.swcares.aps.component.pay.utils.PayErrors;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.idge.IdGenerate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title： <br>
 * Package： <br>
 * Copyright ©  xnky.travelsky.net Inc. All rights reserved.<br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR>
 * date   <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class ChinaAuthUserInfoService {

    private AuthType authType;

    @Value("${spring.profiles.active}")
    private String profiles;

    @Autowired
    private ChinaPayVerifyUserProcess chinaPayVerifyUserProcess;

    /**
     *
     * Description:[验证银行卡用户信息 是否匹配]<br>
     * 处理逻辑：[业务复杂的方法罗列出处理逻辑，可选]<br>
     * 适用场景：[选择银行卡， 填写卡信息 进行调用接口验证]<br>
     * <AUTHOR> @update 2022年9月28日
     * @param dto {卡号，证件类型，证件号，姓名} json格式
     * @return
     */
    public BaseResult<Object> authCarUserInfo(ChinaPayAuthRequestDTO dto) {
        try {
            return newAuthCardUserInfo(dto);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private BaseResult<Object> newAuthCardUserInfo(ChinaPayAuthRequestDTO dto) throws Exception {
        ChinaPayVerifyUserService chinaPayVerifyUserService = chinaPayVerifyUserProcess.getChinaVerifyUser(UserContext.getTenant() + dto.getBusinessType());
        if(ObjectUtils.isNull(chinaPayVerifyUserService)){
            log.error("【银联实名认证 认证失败，未获取到实名认证的控件】-------------->>请求参数【{}】", JSONUtil.toJsonStr(dto));
            throw new BusinessException(PayErrors.RETURN_MSG_FAILED);
        }

        SecssUtil secssUtil = chinaPayVerifyUserService.getSecssUtil();

        String cardNo = dto.getCardNo().trim();
        String idType = dto.getIdType().trim();
        String idNo = dto.getIdNo().trim();
        String userName = dto.getApplyUser().trim();
        if(StringUtils.isEmpty(cardNo) || StringUtils.isEmpty(idType)
                || StringUtils.isEmpty(idNo) || StringUtils.isEmpty(userName)){
            return BaseResult.back(PayErrors.PARAM_FAILED);
        }

        //1.构建参数
        Map<String, String> riskRateInfo = new HashMap<>();
        String riskRateInfoStr = new String(Base64.encode(JSONUtil.toJsonStr(riskRateInfo).getBytes(StandardCharsets.UTF_8)));

        //2.敏感域map
        Map<String, String> cardInfoMap = new HashMap<>();
        cardInfoMap.put("riskRateInfo", riskRateInfoStr);
        cardInfoMap.put("cardNo", cardNo);
        cardInfoMap.put("certType", processIdType(idType));
        cardInfoMap.put("certNo", idNo);
        cardInfoMap.put("usrName", userName);
        secssUtil.encryptData(JSONUtil.toJsonStr(cardInfoMap));

        //3.最终请求map
        Map<String, String> reqMap = new HashMap<>();
        String orderId =  IdGenerate.uniqueIdL() + "";
        reqMap.put("protocolVersion", "用户授权书2025版");
        reqMap.put("protocolNo", orderId);
        // 0:未获得授权
        // 1:已获得授权
        // 授权标识
        reqMap.put("authFlag", "1");
        reqMap.put("merNo", chinaPayVerifyUserService.getMerId());
        //固定值，这个接口的编号
        reqMap.put("busiType", "1004");
        reqMap.put("orderDate", DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN));
        //这个可以不用管，接口必须要这个参数，我就随便给他生成了
        reqMap.put("orderId", orderId);
        reqMap.put("sensData", secssUtil.getEncValue());

        String reqDataStr = new String(Base64.encode(JSONUtil.toJsonStr(reqMap).getBytes(StandardCharsets.UTF_8)));
        // 计算摘要
        String encReqDataStr = Encryptor.encode(reqDataStr, "SHA-512");

        //商户签名（签名根据摘要（请求内容）计算而来）
        Map<String, String> reqDataMap = new HashMap<String,String>();
        reqDataMap.put("reqData", encReqDataStr);
        secssUtil.sign(reqDataMap);
        String signature = secssUtil.getSign();

        Map<String, Object> sendMap = new HashMap<String,Object>();
        sendMap.put("reqData", reqDataStr);
        sendMap.put("merNo", chinaPayVerifyUserService.getMerId());
        sendMap.put("signature", signature);

        int timeout =  50 * 1000;
        String sendMsg = null;
        try {
            sendMsg = getURLParam(sendMap, false, null);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        //2.发起请求
        String res = HttpRequest.post(chinaPayVerifyUserService.getAuthUserApi()).header("Content-Encoding", "UTF-8")
                .setConnectionTimeout(timeout).setReadTimeout(timeout).body(sendMsg)
                .execute().body();

        log.info("【银联实名认证 原始返回结果】-------------->>>>请求url【{}】, 请求参数【{}】，认证结果respStrDecode1【{}】", chinaPayVerifyUserService.getAuthUserApi(), JSONUtil.toJsonStr(dto), res);
        Map<String, String> resultMap1 = StringUtil.paserStrtoMap(res);
        String respStrDecode1 = new String(Base64.decode(resultMap1.get("respData").toCharArray()), StandardCharsets.UTF_8);
        log.info("【银联实名认证 认证结果respStrDecode1】-------------->>>>请求参数【{}】，认证结果respStrDecode1【{}】", JSONUtil.toJsonStr(dto),respStrDecode1);
        net.sf.json.JSONObject jsonRes = net.sf.json.JSONObject.fromObject(respStrDecode1);
        if(jsonRes.get("respCode").equals("00000000")){
            return BaseResult.ok(true);
        }else{
            log.error("【银联实名认证 认证失败，反馈失败状态】-------------->>>>请求参数【{}】", JSONUtil.toJsonStr(dto));
            return BaseResult.ok(false);
        }
    }

    private String processIdType(String idType){
        String certType = "";
        if(StringUtils.equals(idType, "身份证")){
            certType = AuthType.CERT_TYPE_ID.getMethod();
        }else if(StringUtils.equals(idType, "护照")){
            certType = AuthType.CERT_TYPE_PASS.getMethod();
        }else if(StringUtils.equals(idType, "军官证")){
            certType = AuthType.CERT_TYPE_OFFICERS.getMethod();
        }else if(StringUtils.equals(idType, "回乡证")){
            certType = AuthType.CERT_TYPE_RETURNHOME.getMethod();
        }else if(StringUtils.equals(idType, "台胞证")){
            certType = AuthType.CERT_TYPE_TAIWAN_COMPATRIOTS.getMethod();
        }else if(StringUtils.equals(idType, "警官证")){
            certType = AuthType.CERT_TYPE_COP.getMethod();
        }else if(StringUtils.equals(idType, "士兵证")){

        }else{//其他证件
            certType = AuthType.CERT_TYPE_OTHER.getMethod();
        }
        return certType;
    }

    private static String getURLParam(Map map, boolean isSort, Set removeKey) throws UnsupportedEncodingException {
        StringBuffer param = new StringBuffer();
        List msgList = new ArrayList();
        for (Iterator it = map.keySet().iterator(); it.hasNext();) {
            String key = (String) it.next();
            String value = (String) map.get(key);
            if (removeKey != null && removeKey.contains(key)) {
                continue;
            }
            msgList.add(key + "=" + URLEncoder.encode(value, "UTF-8"));
        }

        if (isSort) {
            // 排序
            Collections.sort(msgList);
        }

        for (int i = 0; i < msgList.size(); i++) {
            String msg = (String) msgList.get(i);
            if (i > 0) {
                param.append("&");
            }
            param.append(msg);
        }

        return param.toString();
    }

    @Deprecated
    private BaseResult<Object> oldAuthCarUserInfo(String data) {
        log.info("【银联实名认证接收参数】-------------->>" + data);
        //测试环境修改金额
        if (!profiles.equals("prod")) {
            log.info("------->>>航延补偿旅客端，模拟测试环境，银联实名认证成功，!profiles.equals(\"prod\")：【{}】", !profiles.equals("prod"));
            //【模拟测试环境，银联实名认证成功】
            return BaseResult.ok(true);
        }

        try {
            JSONObject ob = JSONObject.parseObject(data);
            if(ob ==null ){
                return BaseResult.back(PayErrors.PARAM_FAILED);
            }
            String cardNo = ob.getString("cardNo");
            String idType = ob.getString("idType");
            String idNo = ob.getString("idNo");
            String userName = ob.getString("userName");
            if(StringUtils.isEmpty(cardNo) || StringUtils.isEmpty(idType)
                    || StringUtils.isEmpty(idNo) || StringUtils.isEmpty(userName)){
                return BaseResult.back(PayErrors.PARAM_FAILED);
            }
            // 封装银联参数
            Map<String, String> sendMap = new HashMap<String,String>();
            Map<String, String> userMap = new HashMap<String,String>();
            //去掉空格
            userMap.put("idNo", idNo.replaceAll(" ", ""));
            userMap.put("userName", userName.replaceAll(" ", ""));
            userMap.put("idType", idType.replaceAll(" ", ""));
            userMap.put("cardNo", cardNo.replaceAll(" ", ""));
            log.info("【银联实名认证交易参数封装】-------------->>");
            //进行银联交易参数封装
            sendMap = getChinapayParameter(sendMap,userMap);
            if(sendMap==null){
                log.info(new Date() + "----->> 银联交易参数封装:失败！请检查参数是否正确");
                return   BaseResult.back(PayErrors.PARAM_FAILED);
            }
            log.info(new Date() + "----->> 发送请求报文:"+sendMap.toString());
            String res="";
            res = HttpSendUtil.doPost4Pay(authType.CHINAPAY_CARUSERINFO_URL.getMethod(), sendMap, "utf-8");//发送方式1
            //返回同步报文，转map
            Map<String, String> resultMap = paserStrtoMap(res);
            log.info(new Date() + "----->> 反馈同步报文:"+resultMap.toString());
            //根据返回状态结果 进行返回 是否匹配
            resultMap.remove("merNo");
            resultMap.remove("instId");

            //摘要加密SHA512
            Map<String, String> verifyMap = new HashMap<String, String>();
            String encRespDataStr = resultMap.get("respData");
            encRespDataStr = Encryptor.encode(encRespDataStr, "SHA-512");

            verifyMap.put("respData", encRespDataStr);
            verifyMap.put("Signature", resultMap.get("signature"));
            //进行验证签名
            boolean signatureFlag =  ChinapaySignUtil.verify(verifyMap);
            String result = (signatureFlag?"验签成功":"验签失败");
            log.info(new Date() + "----->> 反馈同步报文进行验证签名:"+result);
            if(!signatureFlag){
                return BaseResult.back(PayErrors.RETURN_MSG_FAILED);
            }
            String respStrDecode="";
            if(StringUtils.isNotEmpty(resultMap.get("respData"))){
                respStrDecode = new String(Base64.decode(resultMap.get("respData").toCharArray()), "UTF-8");
            }else{
                log.info("返回报文respData=["+ (resultMap.get("respData")) +"]");
            }
            log.info("base64解密后的返回报文=["+ respStrDecode +"]");
            JSONObject respJson = JSONObject.parseObject(respStrDecode);
            String respCode = (String) respJson.get("respCode");//00000000：成功；其他为失败
            String respMsg = (String) respJson.get("respMsg");

            if(StringUtils.equals(respCode, ChinaPayConstant.RESPCODE_SUCCESS)){
                log.info("【银联实名认证 认证成功反馈成功状态】-------------->>");
                return BaseResult.ok(true);
            }else{
                log.info("【银联实名认证 认证失败，反馈失败状态】-------------->>");
                return BaseResult.ok(false);
            }
        } catch (JSONException e) {
            log.error("银联授权实名认证请求参数不正确 参数格式错误》》"+new Date() + "----->>" , e);
            //请求参数不正确 参数格式错误
            return BaseResult.back(PayErrors.PARAM_FAILED);
        }catch (Exception e) {
            log.error("银联授权实名认证用户失败》》",e);
            return BaseResult.back(PayErrors.CALL_FAILED);
        }
    }
    /**
     *
     * Description:[封装银联请求参数]<br>
     * 处理逻辑：[业务复杂的方法罗列出处理逻辑，可选]<br>
     * 适用场景：[描述方法使用的业务场景，可选]<br>
     * <AUTHOR>
     * @update 2020年9月28日
     * @return
     */
    @Deprecated
    public Map<String ,String> getChinapayParameter(Map<String, String> sendMap,Map<String, String> userMap){

        try {//请求报文
			/*1、报文格式：JSON
			2、特殊字段加密、签名步骤：
			    （1）先将卡号、手机号、证件号组成JSON字符串;
			    （2）再调用chinapaysecure.jar进行敏感域加密;
			    （3）再放入全报文JSON中，KEY为sensData;
			    （4）请求明文字段组成全JSON字符串（注：请求明文=请求基本字段<2.2.1>+其他业务字段<3.3.1>（sensData+具体的业务字段））
			    （5）全JSON报文编码UTF-8，做base64，得到reqData；
			（6）reqData做SHA512得到摘要，做签名得到signature;*/
            String reqData ="";//= 交易请求报文数据
            String signature ="";//签名

            //交易请求报文map
            Map<String, String> reqDataMap = new HashMap<String,String>();
            String dcType =authType.DP_DCTYPE_DEBIT.getMethod() ;//借贷标志   固定：0表借记卡 1表贷记卡
            String certType ="";
            String idType  =  userMap.get("idType");
            String idNo    =  userMap.get("idNo");
            String cardNo  =  userMap.get("cardNo");
            String userName = userMap.get("userName");
            userName = new String(userName.getBytes("UTF-8"),"UTF-8");//设置编码格式

            if(StringUtils.isEmpty(cardNo) || StringUtils.isEmpty(idType)
                    || StringUtils.isEmpty(idNo) || StringUtils.isEmpty(userName)){
                return null;
            }
            if(StringUtils.isNotEmpty(idType)){
                if(StringUtils.equals(idType, "身份证")){
                    certType =authType.CERT_TYPE_ID.getMethod();
                }else if(StringUtils.equals(idType, "护照")){
                    certType =authType.CERT_TYPE_PASS.getMethod();
                }else if(StringUtils.equals(idType, "军官证")){
                    certType =authType.CERT_TYPE_OFFICERS.getMethod();
                }else if(StringUtils.equals(idType, "回乡证")){
                    certType =authType.CERT_TYPE_RETURNHOME.getMethod();
                }else if(StringUtils.equals(idType, "台胞证")){
                    certType =authType.CERT_TYPE_TAIWAN_COMPATRIOTS.getMethod();
                }else if(StringUtils.equals(idType, "警官证")){
                    certType =authType.CERT_TYPE_COP.getMethod();
                }else if(StringUtils.equals(idType, "士兵证")){

                }else{//其他证件
                    certType =authType.CERT_TYPE_OTHER.getMethod().toString();
                }
            }//敏感域   cardNo  卡号    certNo   证件号
            String sensDataJson = "{\"cardNo\":\""+cardNo+"\",\"certNo\":\""+idNo+"\"}";
            log.info("【敏感域json未进行加密】sensDataJson-------------->>"+sensDataJson.toString());
            //进行敏感域加密
            String  sensData =  ChinapaySignUtil.encryptData(sensDataJson);

            reqDataMap.put("merNo", authType.DP_MERNO.getMethod());
            reqDataMap.put("busiType", authType.DP_BUSITYPE.getMethod());

            String tranDate =DateUtils.formatDate(new Date(), DateUtils.YYYYMMDD);
            reqDataMap.put("orderDate", tranDate);

            reqDataMap.put("orderId", createOrderNo());
            //其它业务字段
            reqDataMap.put("authFlag", authType.AUTHFLAG_SUCCESS.getMethod());
            reqDataMap.put("dcType", dcType);
            reqDataMap.put("certType", certType);
            reqDataMap.put("usrName", userName);
            reqDataMap.put("sensData",sensData);
            //将交易明文map转json字符串
            reqData= JSON.toJSONString(reqDataMap);
            log.info("【交易明文json数据：reqData=】"+reqData);
            //将交易明文json字符串进行 utf编码 做base64
            String encodedReqData = Base64.encodeBase64(reqData.toString().getBytes("UTF-8"));
            //reqData做SHA512得到摘要，做签名得到signature;
            String encReqDataStr = Encryptor.encode(encodedReqData, "SHA-512");
            log.info("摘要加密（SHA-512）encReqDataStr=[" + encReqDataStr + "];");

            Map<String, Object> reqDataMaps = new HashMap<String,Object>();
            reqDataMaps.put("reqData", encReqDataStr);
            //进行签名
            signature = ChinapaySignUtil.sign(reqDataMaps);
            sendMap.put("merNo", authType.DP_MERNO.getMethod().toString());
            sendMap.put("reqData", encodedReqData);
            sendMap.put("signature", signature);

        } catch (UnsupportedEncodingException e) {
            log.error("------------->>【封装参数出现错误！！】");
            log.error(new Date() + "----->>" , e );
            return null;
        }
        return sendMap;

    }
    /**
     *
     * Description:[生成 订单号]<br>
     * 处理逻辑：[以 商户号 后4位  年月日  时分秒 四位随机数]<br>
     * 适用场景：[描述方法使用的业务场景，可选]<br>
     * <AUTHOR>
     * @update 2020年9月28日
     * @return
     */
    public String createOrderNo(String merNo){
        String tranDate = DateUtils.formatDate(new Date(),DateUtils.YYYYMMDDHHMMSS);
        tranDate = tranDate.substring(2, tranDate.length());
        merNo =merNo.substring(merNo.length()-4, merNo.length());//取商户号后4位
        Random ra =new Random();
        int randomNumber = ra.nextInt(9000)+1000;//生成1000-9999之间的随机数
        StringBuffer orderNo = new StringBuffer();
        orderNo.append(merNo);
        orderNo.append(tranDate);
        orderNo.append(randomNumber);
        return orderNo.toString();
    }

    public static Map<String, String> paserStrtoMap(String respStr) {
        Map<String, String> data = new HashMap<String, String>();
        if (StringUtils.isNotEmpty(respStr)) {
            String[] strs = respStr.split("&");
            for (String str : strs) {
                if (StringUtils.isEmpty(str)) {
                    continue;
                }
                int index = str.indexOf("=");
                data.put(str.substring(0, index),str.substring(index+1));
            }
        }
        return data;
    }

    /**
     *
     * Description:[生成 订单号]<br>
     * 处理逻辑：[以 商户号 后4位  年月日  时分秒 四位随机数]<br>
     * 适用场景：[描述方法使用的业务场景，可选]<br>
     * <AUTHOR>
     * @update 2020年9月28日
     * @return
     */
    public String createOrderNo(){
        String tranDate = DateUtils.formatDate(new Date(),DateUtils.YYYYMMDDHHMMSS);
        tranDate = tranDate.substring(2, tranDate.length());
        String merNo = authType.DP_MERNO.getMethod();//商户号
        merNo =merNo.substring(merNo.length()-4, merNo.length());//取商户号后4位
        Random ra =new Random();
        int randomNumber = ra.nextInt(9000)+1000;//生成1000-9999之间的随机数
        StringBuffer orderNo = new StringBuffer();
        orderNo.append(merNo);
        orderNo.append(tranDate);
        orderNo.append(randomNumber);
        return orderNo.toString();
    }


}
