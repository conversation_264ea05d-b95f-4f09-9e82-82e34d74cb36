package com.swcares.aps.component.pay.pay.service.wx;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxPayConfigStorage;
import com.swcares.aps.component.pay.pay.bean.wxpay.entity.WxPayConfigDO;
import com.swcares.aps.component.pay.pay.service.wx.service.WxPayConfigService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：WxPayProcess
 * @Description：存储多个租住的微信支付信息
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/21 11:10
 * @version： v1.0
 */
@Slf4j
public class WxPayProcess {

    /** String为租户id + 业务类型，WxPayService为初始化好的微信支付信息 */
    private final Map<String, WxPayService> wxPayServiceMap = new HashMap<>();
    private final Map<String,List<String>> wxPayTypeApiVersionMap = new HashMap<>();

    public WxPayProcess(WxPayConfigService wxPayConfigService){
        initWxPayService(wxPayConfigService);
    }

    /**
     * @title WxPayProcess.java
     * @description 初始化微信支付信息
     * <AUTHOR>
     * @date 2024/5/21 11:19
     * @return void
     */
    public void initWxPayService(WxPayConfigService wxPayConfigService){
        List<WxPayConfigDO> list =  wxPayConfigService.list();
        log.info("【微信支付初始化】，从COMPENSATION_WXPAY_CONFIG表中查询出【{}】条数据，具体信息为【{}】",
                list.size(), JSONUtil.toJsonStr(list));
        for (WxPayConfigDO d: list){
            wxPayServiceMap.put(d.getTenantId() + d.getBusinessType(), new WxPayService(new WxPayConfigStorage(d)));
            List<String> apiVersions = wxPayTypeApiVersionMap.get(String.valueOf(d.getTenantId()));
            if(apiVersions==null){
                apiVersions=new ArrayList<>();
            }
            apiVersions.add(d.getPayTypeApiVersion());
            wxPayTypeApiVersionMap.put(String.valueOf(d.getTenantId()), apiVersions);
        }
    }

    /**
     * @title WxPayProcess.java
     * @description 通过租户id + 业务类型返回转账对象
     * <AUTHOR>
     * @date 2024/5/21 11:36
     * @param tenantIdAndType
     * @return com.swcares.aps.component.pay.pay.service.wx.WxPayService
     */
    public final WxPayService getWxPayService(String tenantIdAndType){
        return wxPayServiceMap.get(tenantIdAndType);
    }

    public final WxPayService getWxPayService(Long tenantId,String businessType){
       return getWxPayService(tenantId + businessType);
    }

    /**
     * @title WxPayProcess.java
     * @description 移除缓存中的微信配置
     * <AUTHOR>
     * @date 2024/5/21 15:14
     * @param tenantIdAndType
     * @return void
     */
    public final void removeWxPayService(String tenantIdAndType){
        wxPayServiceMap.remove(tenantIdAndType);
    }

    /**
     * @title WxPayProcess.java
     * @description 刷新配置到缓存
     * <AUTHOR>
     * @date 2024/5/21 15:14
     * @param wxPayConfigDO
     * @return void
     */
    public final void reloadWxPayService(WxPayConfigDO wxPayConfigDO){
        wxPayServiceMap.put(wxPayConfigDO.getTenantId() + wxPayConfigDO.getBusinessType(), new WxPayService(new WxPayConfigStorage(wxPayConfigDO)));
    }

    public final String getWxPayTypeApiVersion(Long tenantId){
        List<String> apiVersions = wxPayTypeApiVersionMap.get(String.valueOf(tenantId));
        if(apiVersions==null){return "";}
        return apiVersions.get(0);
    }

}
