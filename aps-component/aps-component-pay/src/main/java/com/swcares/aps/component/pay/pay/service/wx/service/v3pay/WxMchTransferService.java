package com.swcares.aps.component.pay.pay.service.wx.service.v3pay;


import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.CancelMchTransferResponse;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.InitiateMchTransferRequest;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.InitiateMchTransferResponse;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.MchTransferDetailEntity;
import com.wechat.pay.java.core.Config;
import com.wechat.pay.java.core.cipher.PrivacyDecryptor;
import com.wechat.pay.java.core.cipher.PrivacyEncryptor;
import com.wechat.pay.java.core.http.Constant;
import com.wechat.pay.java.core.http.DefaultHttpClientBuilder;
import com.wechat.pay.java.core.http.HttpClient;
import com.wechat.pay.java.core.http.HttpHeaders;
import com.wechat.pay.java.core.http.HttpMethod;
import com.wechat.pay.java.core.http.HttpRequest;
import com.wechat.pay.java.core.http.HttpResponse;
import com.wechat.pay.java.core.http.JsonRequestBody;
import com.wechat.pay.java.core.http.MediaType;
import com.wechat.pay.java.core.http.RequestBody;
import lombok.extern.slf4j.Slf4j;

import static com.wechat.pay.java.core.http.UrlEncoder.urlEncode;
import static com.wechat.pay.java.core.util.GsonUtil.toJson;

/**
 * @ClassName：WxMchtransferService
 * @Description：商家转账API
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/3/4 16:20
 * @version： v1.0
 */
@Slf4j
public class WxMchTransferService {

    private final HttpClient httpClient;
    private final PrivacyEncryptor encryptor;
    private final PrivacyDecryptor decryptor;

    private WxMchTransferService(
            HttpClient httpClient,
            PrivacyEncryptor encryptor,
            PrivacyDecryptor decryptor) {
        this.httpClient = httpClient;
        this.encryptor = encryptor;
        this.decryptor = decryptor;
    }

    /** WxMchTransferService构造器 */
    public static class Builder {

        private PrivacyEncryptor encryptor;
        private PrivacyDecryptor decryptor;
        private DefaultHttpClientBuilder httpClientBuilder =new DefaultHttpClientBuilder();

        public Builder config(Config config) {
            httpClientBuilder.config(config);
            this.encryptor = config.createEncryptor();
            this.decryptor = config.createDecryptor();
            return this;
        }

        public Builder readTimeoutMs(int readTimeoutMs) {
            httpClientBuilder.readTimeoutMs(readTimeoutMs);
            return this;
        }
        public Builder writeTimeoutMs(int writeTimeoutMs) {
            httpClientBuilder.writeTimeoutMs(writeTimeoutMs);
            return this;
        }
        public Builder connectTimeoutMs(int connectTimeoutMs) {
            httpClientBuilder.connectTimeoutMs(connectTimeoutMs);
            return this;
        }

        public WxMchTransferService build() {
            return new WxMchTransferService(httpClientBuilder.build(), encryptor, decryptor);
        }
    }


    /**
     * 发起商家转账
     *
     * @param request 请求参数
     * @return InitiateBatchTransferResponse
     * @throws HttpException 发送HTTP请求失败。例如构建请求参数失败、发送请求失败、I/O错误等。包含请求信息。
     * @throws ValidationException 发送HTTP请求成功，验证微信支付返回签名失败。
     * @throws ServiceException 发送HTTP请求成功，服务返回异常。例如返回状态码小于200或大于等于300。
     * @throws MalformedMessageException 服务返回成功，content-type不为application/json、解析返回体失败。
     */
    public InitiateMchTransferResponse initiateMchTransfer(InitiateMchTransferRequest request) {
        String requestPath = "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills";
        // 加密敏感信息
        InitiateMchTransferRequest realRequest = request.cloneWithCipher(encryptor::encrypt);

        HttpHeaders headers = new HttpHeaders();
        headers.addHeader(Constant.ACCEPT, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.CONTENT_TYPE, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.WECHAT_PAY_SERIAL, encryptor.getWechatpaySerial());
        HttpRequest httpRequest =
                new HttpRequest.Builder()
                        .httpMethod(HttpMethod.POST)
                        .url(requestPath)
                        .headers(headers)
                        .body(createRequestBody(realRequest))
                        .build();
        log.info("发起商家转账的请求为：oriRequest:【{}】realRequest:【{}】" , request,realRequest);
        HttpResponse<InitiateMchTransferResponse> httpResponse =
                httpClient.execute(httpRequest, InitiateMchTransferResponse.class);
        InitiateMchTransferResponse serviceResponse = httpResponse.getServiceResponse();
        log.info("发起商家转账的响应为：【{}】" , serviceResponse);
        return serviceResponse;
    }

    public MchTransferDetailEntity getMchTransferDetailByOutBillNo(String outBillNo) {
        String requestPath =
                "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/{out_bill_no}";

        // 添加 path param
        requestPath = requestPath.replace("{" + "out_bill_no" + "}", urlEncode(outBillNo));

        HttpHeaders headers = new HttpHeaders();
        headers.addHeader(Constant.ACCEPT, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.CONTENT_TYPE, MediaType.APPLICATION_JSON.getValue());
        HttpRequest httpRequest =
                new HttpRequest.Builder()
                        .httpMethod(HttpMethod.GET)
                        .url(requestPath)
                        .headers(headers)
                        .build();
        log.info("查询转账单详情的请求为：【{}】" , requestPath);
        HttpResponse<MchTransferDetailEntity> httpResponse =
                httpClient.execute(httpRequest, MchTransferDetailEntity.class);
        MchTransferDetailEntity serviceResponse = httpResponse.getServiceResponse();
        MchTransferDetailEntity entity = serviceResponse.cloneWithCipher(decryptor::decrypt);
        log.info("查询转账单详情的响应为：oriRes:【{}】,realRes:【{}】" , serviceResponse,entity);
        return entity;
    }


    public CancelMchTransferResponse cancelMchTransferByOutBillNo(String outBillNo) {
        String requestPath =
                "https://api.mch.weixin.qq.com/v3/fund-app/mch-transfer/transfer-bills/out-bill-no/{out_bill_no}/cancel";

        // 添加 path param
        requestPath = requestPath.replace("{" + "out_bill_no" + "}", urlEncode(outBillNo));

        HttpHeaders headers = new HttpHeaders();
        headers.addHeader(Constant.ACCEPT, MediaType.APPLICATION_JSON.getValue());
        headers.addHeader(Constant.CONTENT_TYPE, MediaType.APPLICATION_JSON.getValue());
        HttpRequest httpRequest =
                new HttpRequest.Builder()
                        .httpMethod(HttpMethod.GET)
                        .url(requestPath)
                        .headers(headers)
                        .build();
        log.info("取消转账单详情的请求为：【{}】" , requestPath);
        HttpResponse<CancelMchTransferResponse> httpResponse =
                httpClient.execute(httpRequest, CancelMchTransferResponse.class);
        CancelMchTransferResponse response = httpResponse.getServiceResponse();
        log.info("取消转账单详情的响应为：【{}】" , response);
        return response;

    }

    private RequestBody createRequestBody(Object request) {
        return new JsonRequestBody.Builder().body(toJson(request)).build();
    }
}
