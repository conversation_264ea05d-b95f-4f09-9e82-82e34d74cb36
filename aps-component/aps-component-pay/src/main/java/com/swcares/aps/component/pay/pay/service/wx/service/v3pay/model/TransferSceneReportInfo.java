package com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model;

import com.google.gson.annotations.SerializedName;

/**
 * @ClassName：TransferSceneReportInfo
 * @Description：转账场景报备信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/3/5 10:58
 * @version： v1.0
 */
public class TransferSceneReportInfo {
    /**【信息类型】必填 string(15) 不能超过15个字符，商户所属转账场景下的信息类型，此字段内容为固定值*/
    @SerializedName("info_type")
    private String infoType;

    /**【信息内容】必填 string(32) 不能超过32个字符，商户所属转账场景下的信息内容，商户可按实际业务场景自定义传参*/
    @SerializedName("info_content")
    private String infoContent;

    public String getInfoType() {
        return infoType;
    }

    public TransferSceneReportInfo setInfoType(String infoType) {
        this.infoType = infoType;
        return this;
    }

    public String getInfoContent() {
        return infoContent;
    }

    public TransferSceneReportInfo setInfoContent(String infoContent) {
        this.infoContent = infoContent;
        return this;
    }

    @Override
    public String toString() {
        return "TransferSceneReportInfo{" +
                "infoType='" + infoType + '\'' +
                ", infoContent='" + infoContent + '\'' +
                '}';
    }
}
