package com.swcares.aps.component.pay.pay.bean.wxpay;

import com.swcares.aps.component.pay.pay.bean.wxpay.entity.WxPayConfigDO;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.configuration.Configuration;

import java.io.ByteArrayInputStream;
import java.io.InputStream;


/**
 * ClassName：com.swcares.psi.service.wxpay.WxPayConfigStorage <br>
 * Description：微信支付配置-处理类 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reservewxPayConfigDO. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年2月28日 下午 17:24:07 <br>
 * @version v1.0 <br>
 */
@Getter
@Setter
@Slf4j
public class WxPayConfigStorage {

	private Configuration configs;

	private String appId;//合作者id（商户号）

	private String mchId;//微信支付分配的子商户号

	private String appSecret;// 服务号的应用密钥

	private String apiKey;//密钥

	private String notifyUrl;//异步回调地址

	private String signType;//签名方式

	private String inputCharset;//utf-8

	private InputStream cert;//微信支付证书

	private String spbillCreateIp ;//终端IP

	private String authRedirectUri;//授权回调域名

	private String payTypeApiVersion;//支付类型的接口版本

	private String mchSerialNumber; //商户API证书序列号

	private String apiV3Key; //支付APIV3的key

	private String mchPrivateKey; //商户API证书私钥;

	private String mchTrsSceneId; //商家转账场景ID

	private String mchTrsRemark;  //商家转账remark

	private String mchTrsSceneRptType; //商家转账RptType

	private String mchTrsSceneRptContent; //商家转账RptContent

	public WxPayConfigStorage(WxPayConfigDO wxPayConfigDO) {
		this.appId = wxPayConfigDO.getAppId();
		this.appSecret = wxPayConfigDO.getAppSecret();
		this.mchId = wxPayConfigDO.getMchId();
		this.apiKey = wxPayConfigDO.getApiKey();
		this.signType = wxPayConfigDO.getSignType();
		this.cert = new ByteArrayInputStream(wxPayConfigDO.getCert());
		this.notifyUrl = wxPayConfigDO.getNotifyUrl();
		this.inputCharset = wxPayConfigDO.getInputCharset();
		this.spbillCreateIp = wxPayConfigDO.getSpbillCreateIp();
		this.authRedirectUri = wxPayConfigDO.getAuthRedirectUri();
		this.payTypeApiVersion = wxPayConfigDO.getPayTypeApiVersion();
		this.mchSerialNumber = wxPayConfigDO.getMchSerialNumber();
		this.apiV3Key = wxPayConfigDO.getApiV3Key();
		this.mchPrivateKey = wxPayConfigDO.getMchPrivateKey();
		this.mchTrsSceneId = wxPayConfigDO.getMchTrsSceneId();
		this.mchTrsRemark = wxPayConfigDO.getMchTrsRemark();
		this.mchTrsSceneRptType = wxPayConfigDO.getMchTrsSceneRptType();
		this.mchTrsSceneRptContent = wxPayConfigDO.getMchTrsSceneRptContent();
	}
}