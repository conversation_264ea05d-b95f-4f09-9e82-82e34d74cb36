package com.swcares.aps.component.pay.pay.bean;

import lombok.Data;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * 支付相关的常量定义
 * @date 2020/8/28 16:40
 */
@Data
public class PayConstant {
//    0微信，1银联,2支付宝,3优惠券,4现金,5 易宝
    public static final String PAY_CLASS_WECHAT="0";
    public static final String PAY_CLASS_CHINAPAY="1";
    public static final String PAY_CLASS_ALIPAY="2";
    public static final String PAY_CLASS_COUPONS="3";
    public static final String PAY_CLASS_MONEY="4";
    public static final String PAY_CLASS_YEE="5";
    
    //支付结果状态
/*      s	成功	交易成功
        2	处理中	交易已接受
        3	处理中	财务已确认
        4	处理中	财务处理中
        5	处理中	已发往银行
        6	失败	银行已退单
        7	处理中	重汇已提交
        8	处理中	重汇已发送*/
    public static String PAY_SUCCESS = "SUCCESS";
    //微信支付返回结果-处理中
    public static final String PAY_PROCESSING = "PROCESSING";
    //微信支付返回结果-失败
    public static final String PAY_FAILED = "FAILED";
    public static String ALIPAY_PAY_SUCCESS = "Success";
    public static String CHINAPAY_SUCCESS = "s";
    public static String CHINAPAY_DETIL_6 = "6";
    public static String CHINAPAY_DETIL_9 = "9";
    public static String CHINAPAY_SUCCESS_CODE= "0000";
    public static String CHINAPAY_V2_SUCCESS_CODE= "00";
    public static String CHINAPAY_V2_MSG_SUCCESS_CODE= "00000";
    public static String CHINAPAY_V2_FAIL_CODE= "10";
    public static String PAY_FAIL="FAIL";
    //支付结果状支付宝
    public static String ALIPAY_SUCCESS = "10000";


    //支付状态 - 0-未支付
    public static final String PAY_STATUS_UNPAID = "0";
    //支付状态 - 1-已支付
    public static final String PAY_STATUS_PAID = "1";
    //支付状态 - 2-支付失败
    public static final String PAY_STATUS_FAIL = "2";
    //支付状态 - 3-支付处理中
    public static final String PAY_STATUS_INPROCESS = "3";

    //领取状态 - 0未领取
    public static final String RECEIVE_STATUS_UNPAID = "0";
    //领取状态 - 1已领取
    public static final String RECEIVE_STATUS_PAID = "1";
    //领取状态 - 2领取中
    public static final String RECEIVE_STATUS_ING = "2";
    //领取状态 - 3已逾期
    public static final String RECEIVE_STATUS_FAIL = "3";


    //申领单旅客领取状态 - 3领取失败
    public static final String APPLY_RECEIVE_STATUS_ERROR = "3";
    //申领单旅客领取状态 - 2领取成功
    public static final String APPLY_RECEIVE_STATUS_SUCCESS = "2";
    //申领单旅客领取状态 - 1领取中
    public static final String APPLY_RECEIVE_STATUS_ING = "1";

    //申领类型：不正常航班
    public static String PAY_APPLY_TYPE_DP ="0";
    //申领类型：超售
    public static String PAY_APPLY_TYPE_OB ="2";
    //申领类型：异常行李
    public static String PAY_APPLY_TYPE_LG ="1";

    //不同业务别名区分
    public static final String PAY_CLASS_DP="DP";//航延
    public static final String PAY_CLASS_OVERBOOK="OVERBOOKING";//超售
    public static final String PAY_CLASS_LUGGAGE="LUGGAGE";//行李

    public static final String PAY_CLASS_DP_ALIPAY="DP_ALIPAY";//航延
    public static final String PAY_CLASS_OVERBOOK_ALIPAY="OVERBOOKING_ALIPAY";//超售
    public static final String PAY_CLASS_LUGGAGE_ALIPAY="LUGGAGE_ALIPAY";//行李


    //WXV3商家转账状态
    public static final List<String> MCH_TRANSFER_FINAL_SUCCESS_SATES= Collections.singletonList("SUCCESS");
    public static final List<String> MCH_TRANSFER_FINAL_FAIL_SATES= Arrays.asList("FAIL","CANCELLED");
    public static final List<String> MCH_TRANSFER_ALLOW_CONFIRM_SATES= Arrays.asList("WAIT_USER_CONFIRM","TRANSFERING");

    //申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、 RECEIVED 收款成功 FAIL 收款失败
    public static final String PAX_RECEIVE_STATE_WAITE_CONFIRM="WAITE_CONFIRM";
    public static final String PAX_RECEIVE_STATE_RECEIVED ="RECEIVED";
    public static final String PAX_RECEIVE_STATE_FAIL ="FAIL";
    public static final String PAX_RECEIVE_STATE_CONFIRMING ="CONFIRMING";


    /** 微信支付API版本-商家运营转账V3接口*/
    public final static String PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3 = "WX_PAY_MCH_TRANS_V3";

}
