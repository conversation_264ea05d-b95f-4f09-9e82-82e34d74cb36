package com.swcares.aps.component.pay.pay.service.chinapay.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.component.pay.pay.bean.chinapay.dto.ChinaPayConfigDto;
import com.swcares.aps.component.pay.pay.bean.chinapay.entity.ChinaPayConfigDO;
import com.swcares.aps.component.pay.pay.bean.wxpay.entity.WxPayConfigDO;
import com.swcares.aps.component.pay.pay.service.chinapay.mapper.ChinaPayConfigMapper;
import com.swcares.aps.component.pay.pay.service.chinapay.service.ChinaPayConfigService;
import com.swcares.aps.component.pay.pay.service.wx.mapper.WxPayConfigMapper;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;

/**
 * @ClassName：ChinaPayConfigService
 * @Description：银联配置业务实现类
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/17 10:10
 * @version： v1.0
 */
@Slf4j
@Service
public class ChinaPayConfigServiceImpl extends ServiceImpl<ChinaPayConfigMapper, ChinaPayConfigDO> implements ChinaPayConfigService {

    @Value("${pay.chinapay.singleTrade:a}")
    private String singleTrade;

    @Value("${pay.chinapay.singleQuery:a}")
    public String singleQuery;

    @Value("${pay.chinapay.authUser:a}")
    public String authUser;

    @Autowired
    private ChinaPayConfigMapper chinaPayConfigMapper;

    @Override
    public String getSingleTrade() {
        return singleTrade;
    }

    @Override
    public String getAuthUser() {
        return authUser;
    }

    @Override
    public String getSingleQuery() {
        return singleQuery;
    }

    @Override
    public IPage<ChinaPayConfigDO> list(ChinaPayConfigDto chinaPayConfigDto) {
        return null;
    }

    @Override
    public int add(ChinaPayConfigDto chinaPayConfigDto) {
        ChinaPayConfigDO chinaPayConfigDO = new ChinaPayConfigDO();
        chinaPayConfigDO.setTenantId(UserContext.getTenant());
        BeanUtils.copyProperties(chinaPayConfigDto, chinaPayConfigDO);
        chinaPayConfigDO.setPayType(chinaPayConfigDto.getPayType().getKey());
        try {
            chinaPayConfigDO.setPrivateKey(chinaPayConfigDto.getPrivateKey().getBytes());
            chinaPayConfigDO.setPublicKey(chinaPayConfigDto.getPublicKey().getBytes());
            if(ObjectUtil.isNotEmpty(chinaPayConfigDto.getVerifyFile())){
                chinaPayConfigDO.setVerifyFile(chinaPayConfigDto.getVerifyFile().getBytes());
            }
            if(ObjectUtil.isNotEmpty(chinaPayConfigDto.getSignFile())){
                chinaPayConfigDO.setSignFile(chinaPayConfigDto.getSignFile().getBytes());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        return chinaPayConfigMapper.insert(chinaPayConfigDO);
    }

    @Override
    public int update(ChinaPayConfigDto chinaPayConfigDto) {
        //暂无前端页面，先不实现，优先关注银联密钥本地存储的实现
        return 0;
    }

    @Override
    public ChinaPayConfigDO detail(String id) {
        LambdaQueryWrapper<ChinaPayConfigDO> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(ChinaPayConfigDO::getId, id)
                .eq(ChinaPayConfigDO::getTenantId, UserContext.getTenant());
        return chinaPayConfigMapper.selectOne(queryWrapper);
    }

    @Override
    public int updateStatus(String id, String status) {
       /* log.info("【微信支付配置】用户【{}】启用禁用支付配置，前端传参status【{}】", UserContext.getUserId(), status);
        WxPayConfigDO wxPayConfigDO = new WxPayConfigDO();
        wxPayConfigDO.setStatus(status);
        LambdaUpdateWrapper<WxPayConfigDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.eq(WxPayConfigDO::getId, id);
        int mark = wxPayConfigMapper.update(wxPayConfigDO, updateWrapper);
        log.info("【微信支付配置】用户【{}】启用禁用支付配置，修改数据库，执行结果【{}】", UserContext.getUserId(), mark);
        if(mark > 0){
            if("1".equals(status)){
                log.info("【微信支付配置】用户【{}】启用支付配置，当前wxPayConfigDO【{}】", UserContext.getUserId(), JSONUtil.toJsonStr(wxPayConfigDO));
                //启用
                wxPayProcess.reloadWxPayService(wxPayConfigDO);
            }else{
                log.info("【微信支付配置】用户【{}】禁用支付配置，当前wxPayConfigDO【{}】", UserContext.getUserId(), JSONUtil.toJsonStr(wxPayConfigDO));

                //禁用
                wxPayProcess.removeWxPayService(wxPayConfigDO.getTenantId() + wxPayConfigDO.getBusinessType());
            }
        }*/
        return 0;
    }
}
