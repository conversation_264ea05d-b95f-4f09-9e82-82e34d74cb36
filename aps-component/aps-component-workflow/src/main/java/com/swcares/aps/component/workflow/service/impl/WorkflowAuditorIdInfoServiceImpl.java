package com.swcares.aps.component.workflow.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.aps.component.workflow.mapper.WorkflowAuditorIdInfoMapper;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.dto.WorkflowAuditorVerifyDTO;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.component.workflow.dto.WorkflowAuditorBusinessDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：AuditorIdInfoServiceImpl <br>
 * Description：工作流下一节点审核人service <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/18 <br>
 * @version v1.0 <br>
 */

@Service
@Slf4j
public class WorkflowAuditorIdInfoServiceImpl extends ServiceImpl<WorkflowAuditorIdInfoMapper, WorkflowAuditorIdInfoDO> implements WorkflowAuditorIdInfoService {
    @Override
    public List<AuditorInfoDTO> findAuditorList(List<String> assignees) {
        if(CollectionUtils.isEmpty(assignees)){
            return Collections.EMPTY_LIST;
        }
        Map<String,String> auditorMap=new HashMap<>();
        assignees.forEach(t->{
            String[] split = t.split(":");
            auditorMap.put(split[0],split[1]);
        });
        return this.findAuditorList(auditorMap.get("deptId"),auditorMap.get("userId"),auditorMap.get("roleId"));
    }

    @Override
    public void saveOrUpdateRecords(List<WorkflowAuditorIdInfoDO> auditorIdInfoDOS) {
        if(CollectionUtils.isEmpty(auditorIdInfoDOS)){return;}
        WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = auditorIdInfoDOS.get(0);
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> deleteLambdaQueryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        deleteLambdaQueryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,workflowAuditorIdInfoDO.getProject())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,workflowAuditorIdInfoDO.getBusiness())
                .eq(WorkflowAuditorIdInfoDO::getBusinessValue,workflowAuditorIdInfoDO.getBusinessValue());
        this.baseMapper.delete(deleteLambdaQueryWrapper);

        this.saveBatch(auditorIdInfoDOS);
    }

    @Override
    public void deleteWorkflowAuditorIds(WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO) {
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> deleteLambdaQueryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        deleteLambdaQueryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,workflowAuditorIdInfoDO.getProject())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,workflowAuditorIdInfoDO.getBusiness())
                .eq(WorkflowAuditorIdInfoDO::getBusinessValue,workflowAuditorIdInfoDO.getBusinessValue());
        this.baseMapper.delete(deleteLambdaQueryWrapper);
    }

    @Override
    public boolean checkAuditorId(WorkflowAuditorVerifyDTO auditorVerifyDTO) {
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> queryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        queryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,auditorVerifyDTO.getProject())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,auditorVerifyDTO.getBusiness())
                .eq(WorkflowAuditorIdInfoDO::getBusinessValue,auditorVerifyDTO.getBusinessValue())
                .eq(WorkflowAuditorIdInfoDO::getAuditorId,auditorVerifyDTO.getVerifyAuditorId());
        List<WorkflowAuditorIdInfoDO> infoDOS = this.baseMapper.selectList(queryWrapper);
        return CollectionUtils.isNotEmpty(infoDOS);
    }

    @Override
    public List<String> findAuthBusinessValues(WorkflowAuditorBusinessDTO workflowAuditorBusinessDTO) {
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> queryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        queryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,workflowAuditorBusinessDTO.getProject())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,workflowAuditorBusinessDTO.getBusiness())
                .eq(WorkflowAuditorIdInfoDO::getAuditorId,workflowAuditorBusinessDTO.getVerifyAuditorId())
                .in(WorkflowAuditorIdInfoDO::getBusinessValue,workflowAuditorBusinessDTO.getBusinessValues());
        List<WorkflowAuditorIdInfoDO> infoDOS = this.baseMapper.selectList(queryWrapper);
        if(CollectionUtils.isEmpty(infoDOS)){
            return null;
        }
        return infoDOS.stream().map(WorkflowAuditorIdInfoDO::getBusinessValue).collect(Collectors.toList());
    }

    @Override
    public List<AuditorInfoDTO> findTransportAuditorList(List<String> assignees) {
            if (CollectionUtils.isEmpty(assignees)) {
                return Collections.emptyList();
            }
            Map<String, List<String>> groupedAssignees = assignees.stream()
                    .filter(s -> s != null && s.contains(":"))
                    .map(assignee -> assignee.split(":", 2))
                    .collect(Collectors.groupingBy(
                            parts -> parts[0],
                            Collectors.mapping(parts -> parts[1], Collectors.toList())
                    ));

            String userIds = groupedAssignees.get("userId") != null ? String.join(",", groupedAssignees.get("userId")) : null;
            String deptIds = groupedAssignees.get("deptId") != null ? String.join(",", groupedAssignees.get("deptId")) : null;
            String roleIds = groupedAssignees.get("roleId") != null ? String.join(",", groupedAssignees.get("roleId")) : null;
            return this.findAuditorList(deptIds, userIds, roleIds);
    }

    private List<AuditorInfoDTO> findAuditorList(String deptIds, String userIds, String roleIds) {
        log.info("【aps-component】查询审核人条件：【部门id：{}，人员id：{}，角色id：{}】",deptIds,userIds,roleIds);
        String[] deptId = null;
        String[] userId = null;
        String[] roleId = null;
        if(deptIds!=null && !deptIds.equals("")){
            deptId = deptIds.split(",");
        }
        if(userIds!=null && !userIds.equals("")){
            userId = userIds.split(",");
        }
        if(roleIds!=null && !roleIds.equals("")){
            roleId = roleIds.split(",");
        }
        if(deptId==null && userId==null && roleId==null){
            return new ArrayList<>();
        }
        return this.baseMapper.findReviewer(deptId,userId,roleId);
    }
}
