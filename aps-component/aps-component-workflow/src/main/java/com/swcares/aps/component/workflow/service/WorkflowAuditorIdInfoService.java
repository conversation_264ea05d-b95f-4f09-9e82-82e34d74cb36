package com.swcares.aps.component.workflow.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.dto.WorkflowAuditorBusinessDTO;
import com.swcares.aps.component.workflow.dto.WorkflowAuditorVerifyDTO;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;

import java.util.List;

/**
 * ClassName：com.swcares.component.workflow.service.AuditorIdInfoService <br>
 * Description：工作流下一节点审核人service <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/18 <br>
 * @version v1.0 <br>
 */
public interface WorkflowAuditorIdInfoService extends IService<WorkflowAuditorIdInfoDO> {

    /**
     * 查找审核人
     * @param assignees
     * @return
     */
    List<AuditorInfoDTO> findAuditorList(List<String> assignees);

    /**
     * 存入审核人信息列表
     * @param auditorIdInfoDOS
     */
    void saveOrUpdateRecords(List<WorkflowAuditorIdInfoDO> auditorIdInfoDOS);


    /**
     * 流程结束时删除审核人信息列表
     * @param workflowAuditorIdInfoDO
     */
    void deleteWorkflowAuditorIds(WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO);

    /**
     * 检查审核权限
     * @param auditorVerifyDTO
     * @return
     */
    boolean checkAuditorId(WorkflowAuditorVerifyDTO auditorVerifyDTO);

    /**
     * 查询出有权限审核的业务数据标识
     * @param workflowAuditorBusinessDTO
     * @return
     */
    List<String> findAuthBusinessValues(WorkflowAuditorBusinessDTO workflowAuditorBusinessDTO);

    List<AuditorInfoDTO> findTransportAuditorList(List<String> userIdParams);
}
