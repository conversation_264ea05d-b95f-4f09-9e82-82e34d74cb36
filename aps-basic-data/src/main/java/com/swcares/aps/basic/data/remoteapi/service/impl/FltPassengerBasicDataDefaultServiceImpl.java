package com.swcares.aps.basic.data.remoteapi.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO;
import com.swcares.aps.basic.data.remoteapi.api.DataSourceServiceKey;
import com.swcares.aps.basic.data.remoteapi.api.FlightBasicDataService;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.enums.DataSourceServiceKeyEnum;
import com.swcares.aps.basic.data.remoteapi.mapper.FltFlightRealInfoMapper;
import com.swcares.aps.basic.data.remoteapi.mapper.FltPassengerBaggageInfoMapper;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：BasicDataDefaultServiceImpl
 * @Description：航班、旅客基础数据获取。默认从DB数据库获取
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/15 10:08
 * @version： v1.0
 */
@Primary  //设置默认实现。使用BasicDataService时会默认注入此实现
@Service
@Slf4j
@DataSourceServiceKey(DataSourceServiceKeyEnum.DATA_SOURCE_DEFAULT)
public class FltPassengerBasicDataDefaultServiceImpl implements PassengerBasicDataService, FlightBasicDataService {
    @Autowired
    private FltFlightRealInfoMapper fltFlightRealInfoMapper;
    @Autowired
    private FieldEncryptor encryptor;
    @Autowired
    FltPassengerBaggageInfoMapper fltPassengerBaggageInfoMapper;


    @Override
    public List<FlightUnitInfoVO> getFlightUnitInfo(FlightInfoDTO flightInfoDTO) {
        return fltFlightRealInfoMapper.getFlightUnitInfo(flightInfoDTO);
    }

//下面的方法，处理逻辑，是整合了之前赔付模块所有查询航班数据的接口逻辑。数据是从数据库拿，藏航导入的。

    @Override
    public List<PassengerBasicInfoVO> getPassengerInfo(PassengerQueryDTO dto) {
        List<PassengerBasicInfoVO> result = new ArrayList<>();
        log.info("【赔付旅客查询-开始查询getPassengerInfo】查询参数【{}】", JSONUtil.toJsonStr(dto));
        //兼容赔付多个接口，行李号 【从藏航数据库查询：行李号是单独一个查询sql，其他字段配合航班时间航班号是另一个sql。】
        if(StringUtils.isNotEmpty(dto.getBagTag())){
            List<BaggagePaxInfoVO> list = fltPassengerBaggageInfoMapper.findPassengerByBagTag(dto.getBagTag());
            list.forEach(e->{
                PassengerBasicInfoVO infoVO = new PassengerBasicInfoVO();
                BeanUtils.copyProperties(e,infoVO);
                result.add(infoVO);
            });
            return result;
        }

        //===================== TODO DB表数据来源：暂时从藏航数据库导入=========================
        List<BaggagePaxInfoVO> list = fltPassengerBaggageInfoMapper.findPassengers(dto);
        log.info("【赔付旅客查询-开始查询getPassengerInfo】查询参数【{}】,查询结果【{}】",JSONUtil.toJsonStr(dto),JSONUtil.toJsonStr(list));
        if(ObjectUtils.isEmpty(list)){
            return result;
        }
        Map<String,PassengerBasicInfoVO> map = new LinkedHashMap<>();
        list.forEach(e->{
            if(map.containsKey(e.getPaxId())){
                map.get(e.getPaxId()).setTelephone(map.get(e.getPaxId()).getTelephone()+","+e.getTelephone());
            }
            else{
                PassengerBasicInfoVO infoVO = new PassengerBasicInfoVO();
                BeanUtils.copyProperties(e,infoVO);

                map.put(e.getPaxId(),infoVO);
//                String[] segments = e.getSegmentCh().split("-");
//                e.setSegmentCh(segments[0]+e.getOrgCityAirp()+"-"+segments[1]+e.getDstCityAirp());
                wrapBagTagList(e);
            }
        });
        result.addAll(map.values());
        //======================================================================

        return result;
    }

    private boolean wrapBagTagList(BaggagePaxInfoVO compensationOrderPaxInfoVO) {
        if(StringUtils.isEmpty(compensationOrderPaxInfoVO.getBagTag())){
            return false;
        }
        log.info("【----------赔付旅客行李号封装开始-------】");
        if(compensationOrderPaxInfoVO.getBaggageCount()>1){
            List<BaggagePaxInfoVO> compensationOrderPaxInfoVOS = fltPassengerBaggageInfoMapper.findBaggageNumberById(compensationOrderPaxInfoVO.getPaxId());
            String collect = compensationOrderPaxInfoVOS.stream().map(BaggagePaxInfoVO::getBagTag).collect(Collectors.joining(","));
            compensationOrderPaxInfoVO.setBagTag(collect);
        }
        //分割行李号，封装为list
        String[] strings = compensationOrderPaxInfoVO.getBagTag().split(",");
        List<String> list = new ArrayList<>();
        for (String str : strings) {
            list.add(str);
        }
        compensationOrderPaxInfoVO.setBagTagList(list);
        log.info("【----------赔付旅客行李号封装结束-------】");
        return true;
    }


    //===================== TODO DB数据来源：暂时从藏航数据库导入=========================
    @Override
    public List<SegmentFindVO> findFltSegment(String flightDate, String flightNo) {
        if(ObjectUtils.isEmpty(flightDate) || ObjectUtils.isEmpty(flightNo)){
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        List<SegmentFindVO> list = null;
        try{
            list = fltFlightRealInfoMapper.getSegment(flightDate,flightNo);
        }catch (Exception e){
            log.error("【aps-flight-passenger-impl】查询航段出错",e);
            throw new BusinessException(CommonErrors.QUERY_ERROR);
        }
        //目前默认一个时间点的一趟航班最多三个航段，所以数据库查询出来只会有两条数据（后面若有特殊情况再单独处理）
        if(list.size()>1){
            SegmentFindVO segmentVO = new SegmentFindVO();
            if(list.get(0).getArrivalPortCH().equals(list.get(1).getDepartPortCH())){
                segmentVO.setArrivalPort(list.get(1).getArrivalPort());
                segmentVO.setDepartPort(list.get(0).getDepartPort());
                segmentVO.setArrivalPortCH(list.get(1).getArrivalPortCH());
                segmentVO.setDepartPortCH(list.get(0).getDepartPortCH());
            }else{
                segmentVO.setArrivalPort(list.get(0).getArrivalPort());
                segmentVO.setDepartPort(list.get(1).getDepartPort());
                segmentVO.setArrivalPortCH(list.get(0).getArrivalPortCH());
                segmentVO.setDepartPortCH(list.get(1).getDepartPortCH());
            }
            list.add(segmentVO);
        }
        log.info("【aps-flight-passenger-impl】查询结果【{}】", JSONUtil.toJsonStr(list));
        return list;
    }

    @Override
    public List<FlightBasicnfoVO> getFlightList(FlightBaseQueryDTO dto) {
        if(ObjectUtils.isEmpty(dto)){
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        return fltFlightRealInfoMapper.getFlightInfo(dto);
    }
}
