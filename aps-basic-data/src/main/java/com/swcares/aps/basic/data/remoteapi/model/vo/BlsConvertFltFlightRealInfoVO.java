package com.swcares.aps.basic.data.remoteapi.model.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：BlsConvertFltFlightRealInfoVO bls返回实体转换航班实体的转换类 <br>
 * Package：com.swcares.aps.flght.model.entity <br>
 * Copyright ? 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * date 2022年 03月08日 15:56 <br>
 * @version v1.0 <br>
 */
@Data
public class BlsConvertFltFlightRealInfoVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 航班状态 C 取消 D 延误
     */
    public static final String FLIGHT_STATE_DELAY = "D";
    public static final String FLIGHT_STATE_CANCEL = "C";

    @ApiModelProperty(value = "主键")
    @JSONField(name="id")
    private String id;

    @ApiModelProperty(value = "FOCID")
    private String focId;

    @ApiModelProperty(value = "航班号")
    @JSONField(name="flightNo")
    private String flightNumber;

    @ApiModelProperty(value = "航班日期")
    @JSONField(name="flightDate",format = "yyyy-MM-dd HH:mm:ss")
    private Date flightDate;

    @ApiModelProperty(value = "当地起飞日期")
//     @JSONField(name="LOCAL_DATE")
    private Date localDate;

    @ApiModelProperty(value = "返航备降标识")
//    @JSONField(name="FLG_VR")
    private String flgVr;

    @ApiModelProperty(value = "出发机场")
    @JSONField(name="departureAirportCode")
    private String org;

    @ApiModelProperty(value = "到达机场")
    @JSONField(name="destinationAirportCode")
    private String dst;

    @ApiModelProperty(value = "计划起飞时间")
    @JSONField(name="planTakeOffDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date std;

    @ApiModelProperty(value = "预计起飞时间")
    @JSONField(name="predictTakeOffDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date etd;

    @ApiModelProperty(value = "实际起飞时间")
    @JSONField(name="realTakeOffDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date atd;

    @ApiModelProperty(value = "计划到达时间")
    @JSONField(name="planLandingDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date sta;

    @ApiModelProperty(value = "预计到达时间")
    @JSONField(name="predictLandingDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date eta;

    @ApiModelProperty(value = "实际到达时间")
    @JSONField(name="realLandingDatetime",format = "yyyy-MM-dd HH:mm:ss")
    private Date ata;

    @ApiModelProperty(value = "飞机号")
    private String planecode;

    @ApiModelProperty(value = "航班运行状态")//todo
    private String flightRunState;

    @ApiModelProperty(value = "航班调整状态")//todo
    private String flightAdjustState;

    @JSONField(name="status") //todo是啥状态 航班状态
    private String flightState;

    @ApiModelProperty(value = "航班类型 D-国内|I-国际")
    @JSONField(name="flightSegmentProperty") //todo 航段属性？
    private String flightType;

    @ApiModelProperty(value = "可销售座位数")
//    @JSONField(name="SALE_SEAT")
    private BigDecimal saleSeat;

    @ApiModelProperty(value = "飞行组")
//    @JSONField(name="FLIGHT_GROUP")
    private String flightGroup;

    @ApiModelProperty(value = "乘务组")
//    @JSONField(name="CABIN_CREW")
    private String cabinCrew;

    @ApiModelProperty(value = "座位布局")
//    @JSONField(name="SEAT_LAYOUT")
    private String seatLayout;

    @ApiModelProperty(value = "不正常对内原因")
//    @JSONField(name="INNER_REASON")
    private String innerReason;

    @ApiModelProperty(value = "不正常对外原因")
//    @JSONField(name="EXTER_REASON")
    private String exterReason;

    @ApiModelProperty(value = "登机口")
    @JSONField(name="gate")
    private String gate;

    @ApiModelProperty(value = "飞机型号")
    private String flightModel;

    @ApiModelProperty(value = "是否有氧舱(Y|N)")
//    @JSONField(name="HAS_OXYGEN")
    private String hasOxygen;

    @ApiModelProperty(value = "备降机场1")
//    @JSONField(name="ALTERNATE_AIRPORT")
    private String alternateAirport;

    @ApiModelProperty(value = "备降机场2")
//    @JSONField(name="ALTERNATE_AIRPORT2")
    private String alternateAirport2;


    @ApiModelProperty(value = "进港机位")
//    @JSONField(name="BAY_INCOMING")
    private String bayIncoming;

    @ApiModelProperty(value = "离港机位")
//    @JSONField(name="BAY_OUTGOING")
    private String bayOutgoing;

    @ApiModelProperty(value = "计划性和临时性的明细，4位数值，分别代表延误，备降，取消，换机型。0表示未发生，1表示计划性，2表示临时性，3既有计划性又有临时性；如0201，则表示临时性取消，计划性换机型")
//    @JSONField(name="PLAN_TEMP_DETAIL")
    private String planTempDetail;

    @ApiModelProperty(value = "批复起飞时间")
//    @JSONField(name="HTD")
    private Date htd;

    @ApiModelProperty(value = "批复到达时间")
//    @JSONField(name="HTA")
    private Date hta;

    @ApiModelProperty(value = "航线(aslink)")
    @JSONField(name="airline")
    private String fltRoute;

    @ApiModelProperty(value = "状态(aslink)")
    @JSONField(name="status")
    private String fltStatus;

    @ApiModelProperty(value = "登机号(aslink)")
//    @JSONField(name="FLT_GATE")
    private String fltGate;

    @ApiModelProperty(value = "餐食")
//    @JSONField(name="FLT_MEAL")
    private String fltMeal;

    @ApiModelProperty(value = "GoShow限额")
//    @JSONField(name="FLT_GOSHOW_LIMT")
    private String fltGoshowLimt;

    @ApiModelProperty(value = "值机限额")
//    @JSONField(name="FLT_CKI_LIMT")
    private String fltCkiLimt;

    @ApiModelProperty(value = "CI初始关闭时间")
//    @JSONField(name="FLT_CI_TIME",format = "yyyy-MM-dd HH:mm:ss")
    private Date fltCiTime;

    @ApiModelProperty(value = "CL中间关闭时间")
//    @JSONField(name="FLT_CL_TIME")
    private Date fltClTime;

    @ApiModelProperty(value = "CC完全关闭时间")
//    @JSONField(name="FLT_CC_TIME",format = "yyyy-MM-dd HH:mm:ss")
    private Date fltCcTime;

    @ApiModelProperty(value = "座位布局信息")
//    @JSONField(name="FLT_SEAT_CONFIG")
    private String fltSeatConfig;

    @ApiModelProperty(value = "机型")
    private String fltCode;

    @ApiModelProperty(value = "飞机型号")
    private String fltModel;

    @ApiModelProperty("注册号")
//    @JSONField(name="FLT_CONFIG_ID")
    private String fltConfigId;

    @ApiModelProperty(value = "登机时间")
//    @JSONField(name="FLT_BOARDING_DATE")
    private Date fltBoardingDate;

    @ApiModelProperty(value = "出发航站楼")
//    @JSONField(name="FLT_DEPA_TERMINAL")
    private String fltDepaTerminal;

    @ApiModelProperty(value = "到达航站楼")
//    @JSONField(name="FLT_ARRI_TERMINAL")
    private String fltArriTerminal;

    @ApiModelProperty("航班锁定标识")
//    @JSONField(name="FLT_ENTIRE_HOLD")
    private String fltEntireHold;

    @ApiModelProperty(value = "最大载客量")
//    @JSONField(name="FLT_CAP_NUM")
    private String fltCapNum;

    @ApiModelProperty(value = "布局载客量")
//    @JSONField(name="FLT_CNF_NUM")
    private String fltCnfNum;

    @ApiModelProperty(value = "可利用座位")
//    @JSONField(name="FLT_AV_NUM")
    private String fltAvNum;

    @ApiModelProperty(value = "是否共享（Y-是）")
//    @JSONField(name="FLT_IS_SHARE")
    private String fltIsShare;

    @ApiModelProperty(value = "Aslink数据源")
//    @JSONField(name="SOURCE_HIS_IDS")
    private String sourceHisIds;

    @ApiModelProperty(value = "平均票价")
    private String averageFare;
}
