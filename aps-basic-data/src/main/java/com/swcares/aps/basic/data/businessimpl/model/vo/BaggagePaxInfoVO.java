package com.swcares.aps.basic.data.businessimpl.model.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @ClassName：BaggagePaxInfoVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/7/26 10:37
 * @version： v1.0
 */
@Data
@SecretInfoEntity
public class BaggagePaxInfoVO {
    @ApiModelProperty(value = "旅客ID")
    private String paxId;

    @ApiModelProperty(value = "原航班ID")
    private String flightId;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    private String idType;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "航段")
    private String segmentCh;

    @ApiModelProperty(value = "值机状态PT（出票），NA（未值机），AC（值机），XR（值机取消），CL（订座取消），SB（候补），DL（拉下）")
    private String checkStatus;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "出票舱位")
    private String subClass;

    @ApiModelProperty(value = "承运舱位")
    private String carryClass;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "购票时间")
    private String tktDate;

    @ApiModelProperty(value = "取消时间")
    private String cancelTime;

    @ApiModelProperty(value = "性别C儿童M男F女")
    private String sex;

    @ApiModelProperty(value = "婴儿名字")
    private String babyName;

    @ApiModelProperty(value = "总补偿次数")
    private String payCount;

    @ApiModelProperty(value = "起始航站三字码")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站三字码")
    private String dstCityAirp;

    @ApiModelProperty(value = "联系电话")
    @SecretValue(separator = ",")
    private String telephone;

    @ApiModelProperty(value = "儿童标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "是否携带婴儿标识0否1是")
    private String withBaby;

    @ApiModelProperty(value = "旅客的行李数")
    private Integer baggageCount;

    @ApiModelProperty(value = "行李号（与数据库对应）")
    private String bagTag;

    @ApiModelProperty(value = "旅客类别code")
    private String categoryCode;

    @ApiModelProperty(value = "旅客类型")
    private String categoryType;


    @ApiModelProperty(value = "List类型的行李号")
    private List<String> bagTagList;
}
