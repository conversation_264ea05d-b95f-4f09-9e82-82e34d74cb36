package com.swcares.aps.basic.data.remoteapi.api;

import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName：BasicDataService
 * @Description：旅客 基础数据获取
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/15 9:57
 * @version： v1.0
 */
public interface FlightBasicDataService {

    /**
     * @title findFltSegment
     * @description 获取航班航段
     * <AUTHOR>
     * @date 2024/5/14 13:12
     * @param flightDate
     * @param flightNo
     */
    List<SegmentFindVO> findFltSegment(String flightDate, String flightNo);

    /**
     * @title getFlightList
     * @description 航班信息查询方法
     * <AUTHOR>
     * @date 2024/5/14 13:13
     * @param dto
     * @return java.util.List<com.swcares.aps.flightpassenger.model.base.vo.FlightBaseInfoVo>
     */
    List<FlightBasicnfoVO> getFlightList(FlightBaseQueryDTO dto);

    /**
     * @param flightInfoDTO <br>
     * @return <br>
     * @Title：getFlightUnitInfo <br>
     * @Description：通过航班信息来获取机组信息 <br>
     * @author：王磊 <br>
     * @date：2022/3/15 11:17 <br>
     */
    List<FlightUnitInfoVO> getFlightUnitInfo(@Param("dto") FlightInfoDTO flightInfoDTO);


}
