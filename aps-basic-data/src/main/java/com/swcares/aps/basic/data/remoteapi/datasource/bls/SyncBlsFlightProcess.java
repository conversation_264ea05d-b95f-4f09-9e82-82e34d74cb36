package com.swcares.aps.basic.data.remoteapi.datasource.bls;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.basic.data.remoteapi.model.entity.FltFlightRealInfo;
import com.swcares.aps.basic.data.remoteapi.model.vo.BlsConvertFltFlightRealInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.BlsFlightInfo3pResultVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightInfo3pResultItemVO;
import com.swcares.aps.basic.data.remoteapi.service.FltFlightRealInfoService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.ParameterizedTypeReference;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.time.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName：SyncBlsFlightProcess
 * @Description：同步对账通航班数据，入库
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/19 13:57
 * @version： v1.0
 */
@Slf4j
@Component
public class SyncBlsFlightProcess {
    @Autowired
    private FltFlightRealInfoService fltFlightRealInfoService;
    @Autowired
    private BlsProperties blsProperties;

    @Autowired
    private RedisUtil redisUtil;

    /**
     * @title syncBlsFlightTask
     * @description 同步定时任务【aps-compensation-impl定时任务调用】
     * <AUTHOR>
     * @date 2025/3/19 14:28

     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    public void syncBlsFlightTask(){

        List<BlsProperties.BlsTenantConfig> blsTenants = blsProperties.getTenants();
        if(CollectionUtils.isEmpty(blsTenants)){
            log.info("【调用对账通获取机场航班数据】-[获取配置为空]-------》》");
            return;
        }
        List<BlsProperties.BlsTenantConfig> enabledBlsTenants = blsTenants.stream()
                .filter(BlsProperties.BlsTenantConfig::isEnable) // 根据 enable 属性过滤
                .collect(Collectors.toList()); // 收集结果到一个新的列表
        if(CollectionUtils.isEmpty(enabledBlsTenants)){
            log.info("【调用对账通获取机场航班数据】-[获取配置为空]-------》》");
            return;
        }
        for(BlsProperties.BlsTenantConfig config:enabledBlsTenants){
            try {
                syncBlsFlight(config.getTenantId());
            }catch (Exception e){
                log.error("【调用对账通获取机场航班数据】-[访问第三方接口异常]----tenantId:[{}]---》》",config.getTenantId(),e);
            }
        }
    }

    /***
     * @title process
     * @description 调用对账通接口，根据机场租户id等配置，同步机场航班数据，需要处理入库
     *      每个机场航班数据 对应了不同的租户。
     * <AUTHOR>
     * @date 2025/3/19 13:58

     * @return void
     */
    public void syncBlsFlight(String tenantId){
        //从redis中获取租户同步航班的时间戳。若取不到获取前一天0000点的时间戳查询
        String syncTime = getSyncTimeByTenantId(tenantId);
        // 组装请求
        int conut =0;
        boolean hasMore=true;

        // 创建RestTemplate实例
        RestTemplate restTemplate = new RestTemplate();

        // 设置请求头
        HttpHeaders headers = new HttpHeaders();
        headers.set("contentType", "application/json;charset=UTF-8");
        headers.add("secretKey", blsProperties.getSecretKey());
        // 创建HttpEntity对象，包含header，body可以为null或具体数据
        HttpEntity<MultiValueMap<String, Object>> request = new HttpEntity<>(null, headers);
        //对响应结果装配成指定类型(支持泛型)
        ParameterizedTypeReference<String> responseBodyType = new ParameterizedTypeReference<String>() {};
        String urlHead = blsProperties.getApiUrl().replace("{tenantId}", tenantId);

        log.info("【调用对账通获取机场航班数据】-[开始同步航班数据]-start--tenantId:[{}]",tenantId);

        //【根据接口返回的hasMore值（表示：本次同步还有数据需要多次拉取），每次调用timestamp时间戳取最后一条数据的】
        //【lastTime字段：拉取这个时间戳之后的数据。】
        while (hasMore){
            String realUrl = urlHead.replace("{timestamp}", syncTime);
            log.info("【调用对账通获取机场航班数据】-[开始同步航班数据]---请求url:[{}]-tenantId:[{}]",realUrl,tenantId);
            //发起本次同步的 第一个请求
            ResponseEntity<String> baseResult = restTemplate.exchange(realUrl, HttpMethod.GET, request, responseBodyType);
            BaseResult<BlsFlightInfo3pResultVO> result = JSON.parseObject(baseResult.getBody(),BaseResult.class);
            log.info("【调用对账通获取机场航班数据】-[第三方接口返回数据result]---请求url:[{}]-tenantId:[{}]，接口返回result：[{}]",realUrl,tenantId, JSON.toJSONString(result));
            //访问第三方接口异常
            if (result == null || (result != null && result.getCode() != 200)) {
                log.error("【调用对账通获取机场航班数据】-[访问第三方接口异常]---请求url:[{}]-tenantId:[{}],syncTime:[{}]，",realUrl,tenantId,syncTime);
                break;
            }
            BlsFlightInfo3pResultVO data = JSON.parseObject(String.valueOf(result.getData()),BlsFlightInfo3pResultVO.class);
            List<FlightInfo3pResultItemVO> dataList = Optional.ofNullable(data).orElse(new BlsFlightInfo3pResultVO()).getDataList();

            if(result.getData()==null||data.getDataList()==null){
                log.info("【调用对账通获取机场航班数据】-[第三方接口返回数据为空]---请求url:[{}]-tenantId:[{}]，接口返回：[{}]",realUrl,tenantId, JSON.toJSONString(result));
                break;
            }
            log.info("【调用对账通获取机场航班数据】-[第三方接口返回数据]---请求url:[{}]-tenantId:[{}]，接口返回数据size:[{}],hasMore:[{}] ， 接口返回：[{}]",realUrl,tenantId,data.getDataList().size(),data.getHasMore(), JSON.toJSONString(result));

            //本次同步是否还有数据需要多次拉取
            hasMore = data.getHasMore();
            syncTime = Long.toString(dataList.get(dataList.size()-1).getUpdatedTime().toInstant(ZoneOffset.of("+8")).toEpochMilli());
            LocalDateTime start = LocalDateTime.now();

            //TODO 对返回结果进行处理 若接口异常，那也不印象之前保存的数据？
            //航班数据会更新。你们可能根据主键id更新
            refreshData(dataList);

            LocalDateTime end=LocalDateTime.now();
            Duration duration = Duration.between(start, end);
            long minutes= duration.toMinutes();
            long seconds = duration.getSeconds()%60;
            conut++;
            log.info("【调用对账通获取机场航班数据】-tenantId:[{}]-同步的第三方接口访问次数:[{}] ，接口返回数据size:[{}],syncTime:[{}],minutes:[{}],seconds[{}]-请求url:[{}]-",tenantId,conut,data.getDataList().size(),syncTime,minutes,seconds,realUrl);
        }
        // 把最后一次调用第三方接口的 返回值的最后一条数据的 updateTime的时间戳 存到redis 提供给 定时任务 下次调用
        redisUtil.set("BLS_FLT_SYNC_TIME_" + tenantId,syncTime);
        log.info("【调用对账通获取机场航班数据】-本次同步的第三方接口访问次数共计:[{}]----tenantId:[{}],更新缓存syncTime：[{}]",conut,tenantId,syncTime);
    }


    private String getSyncTimeByTenantId(String tenantId){
        //根据租户获取redis上一次调对账通同步航班的时间戳参数
        String syncTime = redisUtil.get("BLS_FLT_SYNC_TIME_" + tenantId);
        if(StringUtils.isEmpty(syncTime)){
            // 获取当前日期并减去一天以得到前一天的日期
            LocalDate previousDay = LocalDate.now().minusDays(1);

            // 将前一天的日期转换为该天凌晨（0:00）的ZonedDateTime对象，使用系统默认时区
            ZonedDateTime startOfPreviousDay = previousDay.atStartOfDay(ZoneId.systemDefault());

            // 将ZonedDateTime转换为Instant，然后获取时间戳（以毫秒为单位）
            Instant instant = startOfPreviousDay.toInstant();
            String timestamp = String.valueOf(instant.toEpochMilli());
            syncTime = timestamp;
        }
        return syncTime;
    }


    public void refreshData(List<FlightInfo3pResultItemVO> dataList){
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        List<FltFlightRealInfo> realInfoList = new ArrayList<>();
        for(FlightInfo3pResultItemVO itemVO:dataList){
            String jsonString = JSON.toJSONString(itemVO);
            // 从 JSON 字符串反序列化为 BlsConvertFltFlightRealInfoVO 对象
            BlsConvertFltFlightRealInfoVO infoVO = JSON.parseObject(jsonString, BlsConvertFltFlightRealInfoVO.class);
            FltFlightRealInfo realInfo = new FltFlightRealInfo();
            BeanUtil.copyProperties(infoVO,realInfo);
            realInfoList.add(realInfo);
            if(itemVO.getAcdmDeleted()==1){
                log.info("【调用对账通获取机场航班数据】-删除航班--航班id:[{}],acdmDeleted(1：删除 0：正常):[{}],当前同步dataList的size:[{}]",itemVO.getId(),itemVO.getAcdmDeleted(),dataList.size());
                //删除的数据。
                fltFlightRealInfoService.removeById(realInfo.getId());
            }else {
                //需要更新或新增的
                fltFlightRealInfoService.saveOrUpdate(realInfo);
            }
        }
        // 使用 groupingBy 进行分组
        Map<Long, List<FlightInfo3pResultItemVO>> groupedItems = dataList.stream()
                .collect(Collectors.groupingBy(FlightInfo3pResultItemVO::getId));
        // 输出结果
        groupedItems.forEach((category, itemList) -> {
            if(itemList.size()>1){
                System.out.println(itemList);
            }
        });

    }



}
