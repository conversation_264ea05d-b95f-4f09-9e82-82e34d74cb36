package com.swcares.aps.basic.data.remoteapi.model.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.aps.basic.data.remoteapi.api.DataSourceServiceKey;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：TenantFltPaxBasicDataConfig
 * @Description：租户配置的航班数据获取处理实现类、旅客数据获取处理实现类
 *             比如：123租户 航班数据获取处理实现类是基于默认实现（从数据库获取），但旅客数据是从外部接口调用获取
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/19 9:25
 * @version： v1.0
 */
@TableName("TENANT_FLTPAX_SERVICE_CONFIG")
@Data
public class TenantFltPaxBasicDataConfig {

    @ApiModelProperty("租户ID")
    @TableField("TENANT_ID")
    private Long tenantId;
//    如果是像对接对账通航班数据，是先由定时任务处理入库，那业务模块使用的航班、旅客服务实现，默认走DB
    //可以指定同一个服务类。
    @ApiModelProperty("[租户决定使用的数据源（可能是实时调用第三方java接口、第三方数据库、补偿单数据库flt_表）]获取航班数据实现类Key")
    @TableField("FLT_DATA_SERVICE_KEY")
    private String fltDataServiceKey;

    @ApiModelProperty("[租户决定使用的数据源（可能是实时调用第三方java接口、第三方数据库、补偿单数据库flt_表）]获取旅客数据实现类Key")
    @TableField("PAX_DATA_SERVICE_KEY")
    private String paxDataServiceKey;



    public TenantFltPaxBasicDataConfig(Long tenantId, String fltDataServiceKey, String paxDataServiceKey) {
        this.tenantId = tenantId;
        this.fltDataServiceKey = fltDataServiceKey;
        this.paxDataServiceKey = paxDataServiceKey;
    }
}
