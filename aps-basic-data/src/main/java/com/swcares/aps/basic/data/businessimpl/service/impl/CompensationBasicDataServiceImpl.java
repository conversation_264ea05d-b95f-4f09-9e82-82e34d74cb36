package com.swcares.aps.basic.data.businessimpl.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.FltPassengerDataSourceServiceFactory;
import com.swcares.aps.basic.data.remoteapi.api.FlightBasicDataService;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @ClassName：CompensationBasicServiceImpl
 * @Description：赔付模块需要用到的航班、旅客接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/14 14:01
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationBasicDataServiceImpl implements CompensationBasicDataService {

    @Autowired
    private FieldEncryptor encryptor;

    @Autowired
    FltPassengerDataSourceServiceFactory fltPassengerDataSourceServiceFactory;


    @Override
    public List<FlightBasicnfoVO> getFlightBasicInfo(FlightBaseQueryDTO dto) {
        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        return flightService.getFlightList(dto);
    }

    @Override
    public List<PassengerBasicInfoVO> getPassengerInfo(PassengerQueryDTO dto) {
        List<PassengerBasicInfoVO> result = new ArrayList<>();
        if(StringUtils.isNotEmpty(dto.getChoiceSegment())){
            String[] strings = dto.getChoiceSegment().split(",");

            for (String str : strings) {
                dto.setChoiceSegment(str);
                getPaxInfo(dto,result);
            }
        }else {
            getPaxInfo(dto,result);
        }
        log.info("【赔付旅客查询-结束查询】查询参数【{}】，查询结果【{}】",JSONUtil.toJsonStr(dto),JSONUtil.toJsonStr(result));
        return result;
    }

    private void getPaxInfo(PassengerQueryDTO dto,List<PassengerBasicInfoVO> result){
        log.info("【赔付旅客查询-开始查询】查询参数【{}】",JSONUtil.toJsonStr(dto));
        PassengerBasicDataService passengerService = fltPassengerDataSourceServiceFactory.getPassengerService();
        List<PassengerBasicInfoVO> passengerInfo = passengerService.getPassengerInfo(dto);
        if(ObjectUtils.isNotEmpty(passengerInfo)){
            result.addAll(passengerInfo);
        }
    }

    @Override
    public List<SegmentFindVO> findFltSegment(String flightDate, String flightNo) {
        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        return flightService.findFltSegment(flightDate,flightNo);
    }


    @Override
    public FlightFindVO getFlight(String date, String flightNo, String choiceSegment) {
        //注意：choiceSegment  可能会有多个航段（逗号分隔）
        //1.获取该航班航段数量
        List<SegmentFindVO> list1 = findFltSegment(date, flightNo);
        //判断是否是多航段航班
        if(list1.size()==1){
            //单航段直接查结果并返回
            FlightFindVO flightInfo = getFlightInfo(date, flightNo, choiceSegment);
            log.info("【aps-flight-passenger-impl】查询该航班：[{}],航段[{}]的航班[{}]",flightNo+"_"+date,choiceSegment,JSONUtil.toJsonStr(flightInfo));
            return flightInfo;
        }else if(list1.size()==0){
            //查询无结果返回空
            return null;
        }
        //找出每个航段
        String ab = "-";
        String bc = "-";
        String ac = list1.get(list1.size()-1).getDepartPort()+"-"+list1.get(list1.size()-1).getArrivalPort();
        if(list1.get(0).getDepartPort().equals(list1.get(list1.size()-1).getDepartPort())){
            ab = list1.get(0).getDepartPort()+ab+list1.get(0).getArrivalPort();
            bc = list1.get(1).getDepartPort()+bc+list1.get(1).getArrivalPort();
        }else{
            bc = list1.get(0).getDepartPort()+bc+list1.get(0).getArrivalPort();
            ab = list1.get(1).getDepartPort()+ab+list1.get(1).getArrivalPort();
        }
        //判断传入参数是否是多个航段构成
        String[] segments = choiceSegment.split(",");
        if(segments.length==1){
            //判断是不是bc
            if(choiceSegment.equals(bc)){
                return getFlightInfo(date, flightNo, choiceSegment);
            }
            else if(choiceSegment.equals(ab)) {
                return getFlightInfo(date, flightNo, ab);
            }
        }
        //ac段以及多航段选择时都回传ac航段信息但是（目前）延误时间按照ab段取值（后期根据需求变化可能会进行变化）
        FlightFindVO VO1 = getFlightInfo(date, flightNo, ab);
        FlightFindVO VO2 = getFlightInfo(date, flightNo, bc);
        VO1.setFlightId(VO1.getFlightId()+","+VO2.getFlightId());
        VO1.setArrivalPort(VO2.getArrivalPort());
        VO1.setPoa(VO2.getPoa());
        //ac以及多航段，采用ab端计划起飞时间，bc端计划到达时间
        VO1.setSta(VO2.getSta());
        VO1.setAta(VO2.getAta());
        VO1.setEta(VO2.getEta());
        VO1.getStopoverStation().addAll(VO2.getStopoverStation());
        String[] codes = VO2.getSegment().split("-");
        VO1.setSegment(VO1.getSegment()+"-"+codes[1]);
        String[] city = VO2.getSegmentCh().split("-");
        VO1.setSegmentCh(VO1.getSegmentCh()+"-"+city[1]);
        log.info("【aps-flight-passenger-impl】查询该航班：[{}],航段[{}]的航班[{}]",flightNo+"_"+date,choiceSegment,JSONUtil.toJsonStr(VO1));
        return VO1;
    }

    @Override
    public List<FocFlightInfoVO> getFocFlightInfo(FocFlightInfoDTO flightInfoDTO) {
        FlightBaseQueryDTO queryDto = new FlightBaseQueryDTO();
        queryDto.setFlightNo(flightInfoDTO.getFlightNo());
        queryDto.setFlightDate(flightInfoDTO.getFlightDate());
        if(StringUtils.isNotEmpty(flightInfoDTO.getSegment())){
            String[] split = flightInfoDTO.getSegment().split("-");
            queryDto.setOrg(split[0]);
            queryDto.setDst(split[1]);
        }
        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        List<FlightBasicnfoVO> flightList = flightService.getFlightList(queryDto);
        if(ObjectUtils.isEmpty(flightList)){
            return null;
        }
        List<FocFlightInfoVO> resultList = new ArrayList<>();
        for(FlightBasicnfoVO infoVo:flightList){
            FocFlightInfoVO flightInfoVO = new FocFlightInfoVO();
            BeanUtils.copyProperties(infoVo, flightInfoVO);
            flightInfoVO.setOrg(infoVo.getPod());
            flightInfoVO.setDesc(infoVo.getPoa());
            resultList.add(flightInfoVO);
        }
        return resultList;
    }






    private FlightFindVO getFlightInfo(String date, String flightNo, String choiceSegment){
        String[] split = choiceSegment.split("-");
        FlightBaseQueryDTO queryDto = new FlightBaseQueryDTO();
        queryDto.setFlightNo(flightNo);
        queryDto.setFlightDate(date);
        queryDto.setOrg(split[0]);
        queryDto.setDst(split[1]);
        FlightBasicDataService flightService = fltPassengerDataSourceServiceFactory.getFlightService();
        List<FlightBasicnfoVO> flightList = flightService.getFlightList(queryDto);
        if(ObjectUtils.isNotEmpty(flightList) && flightList.size()>0){
            FlightFindVO flightInfoVo = new FlightFindVO();
            BeanUtils.copyProperties(flightList.get(0),flightInfoVo);

            //---------------
            Set<String> set = new HashSet<>();
            set.add(flightInfoVo.getPod());
            set.add(flightInfoVo.getPoa());
            if(StringUtils.isNotEmpty(flightList.get(0).getAlternateAirport())){
                set.add(flightList.get(0).getAlternateAirport());
            }
            if(StringUtils.isNotEmpty(flightList.get(0).getAlternateAirport2())){
                set.add(flightList.get(0).getAlternateAirport2());
            }
            flightInfoVo.setStopoverStation(set);
            //--------------
            flightInfoVo.setDelayReason();


            return flightInfoVo;
        }
        return null;
    }
}
