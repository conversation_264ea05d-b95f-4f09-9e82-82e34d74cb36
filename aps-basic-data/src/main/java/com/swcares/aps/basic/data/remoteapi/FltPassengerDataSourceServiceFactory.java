package com.swcares.aps.basic.data.remoteapi;

import com.alibaba.fastjson.JSON;
import com.swcares.aps.basic.data.remoteapi.api.DataSourceServiceKey;
import com.swcares.aps.basic.data.remoteapi.api.FlightBasicDataService;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.enums.DataSourceServiceKeyEnum;
import com.swcares.aps.basic.data.remoteapi.mapper.TenantFltPaxBasicDataConfigMapper;
import com.swcares.aps.basic.data.remoteapi.model.entity.TenantFltPaxBasicDataConfig;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：FltPassengerDataSourceFactory
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/3/18 11:51
 * @version： v1.0
 */
@Slf4j
@Component
public class FltPassengerDataSourceServiceFactory {

    @Autowired
    TenantFltPaxBasicDataConfigMapper tenantFltPaxBasicDataConfigMapper;

    private final ApplicationContext applicationContext;

    public FltPassengerDataSourceServiceFactory(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }
    private static final Map<String, TenantFltPaxBasicDataConfig> serviceMap = new HashMap<>();



    /**
     * 根据租户ID及租户配置的航班数据源获取对应的航班服务实现。
     *
     * @return 对应的服务实现
     */
    public FlightBasicDataService getFlightService() {
        Long tenantId = UserContext.getUser().getTenantId();
        TenantFltPaxBasicDataConfig config = getTenantFltPaxBasicDataConfig(tenantId);
        if(config == null){
            log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}], 配置航班旅客数据服务key信息:为空, 返回[{}]默认实现", tenantId,FlightBasicDataService.class.getSimpleName());
            return getDefaultService(FlightBasicDataService.class);
        }
        return resolveService(config.getTenantId(),FlightBasicDataService.class,config.getFltDataServiceKey());
    }

    /**
     * 根据租户ID及租户配置的航班数据源获取对应的旅客服务实现。
     *
     * @return 对应的服务实现
     */
    public PassengerBasicDataService getPassengerService() {
        Long tenantId = UserContext.getUser().getTenantId();
        TenantFltPaxBasicDataConfig config = getTenantFltPaxBasicDataConfig(tenantId);
        if(config == null){
            log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}], 配置航班旅客数据服务key信息:为空, 返回[{}]默认实现", tenantId,PassengerBasicDataService.class.getSimpleName());
            return getDefaultService(PassengerBasicDataService.class);
        }
        return resolveService(config.getTenantId(),PassengerBasicDataService.class,config.getPaxDataServiceKey());
    }


    private TenantFltPaxBasicDataConfig getTenantFltPaxBasicDataConfig(Long tenantId){
        if (tenantId == GlobalConstants.TENANT_DEFAULT) {
            log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}], 租户id=TENANT_DEFAULT", tenantId);
            return null;
        }
        TenantFltPaxBasicDataConfig config = serviceMap.get(tenantId);
        log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}],redis获取，配置航班旅客数据服务key:[{}]", tenantId, JSON.toJSONString(config));
        if(config == null){
            // 如果租户数据源key不存在，也返回默认的实现
            Map<String, Object> columnMap = new HashMap<>();
            columnMap.put("TENANT_ID", tenantId);
            List<TenantFltPaxBasicDataConfig> dataConfigs = tenantFltPaxBasicDataConfigMapper.selectByMap(columnMap);
            log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}],DB获取，配置航班旅客数据服务key:[{}]", tenantId, JSON.toJSONString(dataConfigs));
            if(CollectionUtils.isEmpty(dataConfigs)){
                return null;
            }else {
                config = dataConfigs.get(0);
                serviceMap.put(String.valueOf(tenantId),config);
            }
        }
        log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}],最终获取结果，配置航班旅客数据服务key:[{}]", tenantId, JSON.toJSONString(config));
        return config;
    }


    /**
     * 根据租户ID及数据源key枚举类型获取对应的服务实现。
     *
     * @param tenantId 租户ID
     * @param serviceClass 服务类类型
     * @param tenantDataSourceServiceKey 租户配置的数据源key枚举类型
     * @param <T> 服务类型
     * @return 对应的服务实现
     */
    private <T> T resolveService(Long tenantId, Class<T> serviceClass,String tenantDataSourceServiceKey) {
        if (tenantId == GlobalConstants.TENANT_DEFAULT) {
            return getDefaultService(serviceClass);
        }
        DataSourceServiceKeyEnum dataSourceKey;
        try {
            dataSourceKey = DataSourceServiceKeyEnum.fromKey(tenantDataSourceServiceKey);
        } catch (IllegalArgumentException e) {
            log.error("【根据租户ID获取对应航班旅客服务】---tenantId:[{}],获取服务实现类型[{}], 数据源key:[{}] 不在枚举中", tenantId, serviceClass.getSimpleName(),tenantDataSourceServiceKey, e);
            return getDefaultService(serviceClass);
        }

        Map<String, T> serviceBeans = applicationContext.getBeansOfType(serviceClass);
        for (T service : serviceBeans.values()) {
            DataSourceServiceKey annotation = service.getClass().getAnnotation(DataSourceServiceKey.class);
            if (annotation != null && annotation.value() == dataSourceKey) {
                return service;
            }
        }
        log.info("【根据租户ID获取对应航班旅客服务】---tenantId:[{}], 数据源key:[{}], 返回[{}]默认实现", tenantId, tenantDataSourceServiceKey,serviceClass.getSimpleName());
        return getDefaultService(serviceClass);
    }

    /**
     * 获取指定类型服务中默认的@Primary服务实现。
     *
     * @param serviceClass 服务的类类型
     * @param <T>          服务的类型
     * @return 默认的服务实现
     */
    private <T> T getDefaultService(Class<T> serviceClass) {
        return applicationContext.getBeansOfType(serviceClass).values().stream()
                .filter(bean -> bean.getClass().isAnnotationPresent(Primary.class))
                .findFirst()
                .orElseThrow(() -> new IllegalStateException("未找到[" + serviceClass.getSimpleName() + "]默认的服务实现"));
    }
}
