<?xml version="1.0" encoding="UTF-8"?>
        <!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.basic.data.remoteapi.mapper.FltPassengerBaggageInfoMapper">



        <select id="findBaggageNumberById" resultType="com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO">
                select
                fpri.id paxId,
                fpri.passenger_name paxName,
                fpri.id_number idNo,
                fpri.ticket_number tktNo,
                fpbi.baggage_number bagTag,
                fpbi.baggage_count baggageCount,
                fpri.flight_segment segment
                from flt_passenger_real_info fpri left join flt_passenger_baggage_info fpbi on fpri.id = fpbi.passr_id
                where fpri.id = #{id}
        </select>


        <sql id="basePassenger" databaseId="oracle">
                fpri.id paxId,
                nvl2(fpri.passenger_name,fpri.passenger_name,fpri.PASSENGER_NAME_EN) paxName,
                fpri.id_number idNo,
                fpri.id_type idType,
                fpri.ticket_number tktNo,
                fpri.gender sex,
                t.passenger_name babyName,
                <!--                if(fpri.passenger_type = 'CHD' , '1' , '0') isChild,-->
                case
                when fpri.passenger_type = 'CHD' then '1'
                else '0'
                end as isChild,
                fpri.main_cabin mainClass,
                fpri.ISSUE_CABIN subClass,
                fpc.phone_number telephone,
                fpri.check_status checkStatus,
                fpri.ticket_issue_date tktDate,
                concat(concat(concat(get_city_name(fpri.org),fpri.org),'-'),
                concat(get_city_name(fpri.dst),fpri.dst)) segmentCh,
                fpri.flight_segment segment,
                fpri.FLIGHT_ID as flightId,
                fpri.flight_number as flightNo,
                TO_CHAR(fpri.flight_date,'yyyy-mm-dd') as flightDate,
                fpri.org orgCityAirp,
                fpri.dst dstCityAirp,
                fpri.crs_pnr pnr,
                <!--                if(fpri.infant_info is not null,'1','0') withBaby,-->
                case
                when fpri.infant_info is not null then '1'
                else '0'
                end as withBaby,
                nvl(fpri.is_cancel,'N') isCancel

        </sql>
        <select id="findPassengerByBagTag" resultType="com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO">
                select
                <include refid="basePassenger"/>
                from flt_passenger_real_info fpri
                left join (select TICKET_NUMBER,INFT_TICKET_NUMBER,PASSENGER_NAME,PASSENGER_TYPE from flt_passenger_real_info) t
                on fpri.INFT_TICKET_NUMBER = t.TICKET_NUMBER
                left join flt_passenger_contacts fpc    /*连接旅客联系表*/
                on fpri.id = fpc.PASSR_ID
                left join flt_passenger_baggage_info fpbi   /*连接旅客行李表*/
                on fpri.id = fpbi.PASSR_ID
                where fpbi.baggage_number = #{bagTag}
        </select>

        <select id="findPassengers" resultType="com.swcares.aps.basic.data.businessimpl.model.vo.BaggagePaxInfoVO" databaseId="oracle">
                select DISTINCT re.* from
                (select
                fpri.id paxId,
                nvl(fpri.passenger_name,fpri.passenger_name_en) paxName,
                fpri.id_number idNo,
                fpri.id_type idType,
                fpri.ticket_number tktNo,
                fpri.gender sex,
                nvl(t.passenger_name,t.passenger_name_en) babyName,      /*对应infant_info   婴儿信息对应原表infant_name*/
                case
                when fpri.passenger_type = 'CHD' then '1'
                else '0'
                end as isChild,
                fpri.main_cabin mainClass,
                fpri.CATEGORY_CODE categoryCode,
                fpri.CATEGORY_TYPE categoryType,
                fpri.ISSUE_CABIN subClass,
                fpri.carry_cabin carryClass,
                fpc.phone_number telephone,
                fpri.check_status checkStatus,
                fpri.ticket_issue_date tktDate,
                fpri.cancel_time cancelTime,
                concat(concat(concat(get_city_name(fpri.org),fpri.org),'-'),
                concat(get_city_name(fpri.dst),fpri.dst)) segmentCh,
                concat(concat(fpri.org,'-'),fpri.dst) segment,
                fpri.FLIGHT_ID as flightId,
                fpri.flight_number as flightNo,
                TO_CHAR(fpri.flight_date,'yyyy-mm-dd') as flightDate,
                fpri.org orgCityAirp,
                fpri.dst dstCityAirp,
                fpri.pnr_ref pnr,
                fpri.BAGGAGE_COUNT baggageCount,
                fpri.BAGGAGE_NUMBER bagTag,

                case
                when fpri.infant_info is not null then '1'
                else '0'
                end as withBaby,
                nvl(fpri.is_cancel,'N') as isCancel

                <if test="dto.flightNo !=null and dto.flightNo !='' and dto.flightDate !=null and dto.flightDate !=''">
                        ,p2.*
                </if>
                from flt_passenger_real_info fpri
                <if test="dto.flightNo !=null and dto.flightNo !='' and dto.flightDate !=null and dto.flightDate !=''">
                        left join (
                        SELECT count(*) num, id_number no,passenger_name name from flt_passenger_real_info p
                        where p.flight_number = #{dto.flightNo}
                        and TO_CHAR(p.flight_date,'yyyy-mm-dd') = #{dto.flightDate}
                        and p.ticket_number is not null
                        and p.passenger_type != 'INF'
                        group by p.passenger_name,p.id_type,p.id_number having count(*)>1 order by fn_getpy(substr(p.passenger_name,1,1),3)
                        ) p2
                        on fpri.id_number = p2.no
                </if>

                left join (select ticket_number,inft_ticket_number,passenger_name,passenger_type,passenger_name_en from flt_passenger_real_info fpri2) t
                on fpri.inft_ticket_number = t.ticket_number
                left join (select TO_CHAR(wm_concat ( to_char( phone_number ) ))  phone_number,passr_id from flt_passenger_contacts GROUP BY passr_id) fpc
                on fpri.id = fpc.passr_id
                where  fpri.passenger_type != 'INF'
                and fpri.ticket_number is not null
                <if test="dto.paxId !=null and dto.paxId !=''">
                        and fpri.id = #{dto.paxId}
                </if>
                <if test="dto.paxIds !=null and dto.paxIds.size()>0">
                        and fpri.id in
                        <foreach collection="dto.paxIds" item="pId" open="(" close=")" separator=",">
                                #{pId}
                        </foreach>
                </if>
                <if test="dto.flightNo !=null and dto.flightNo !=''">
                        and fpri.flight_number = #{dto.flightNo}
                </if>

                <if test="dto.flightDate !=null and dto.flightDate !=''">
                        and TO_CHAR(fpri.flight_date,'yyyy-mm-dd') = #{dto.flightDate}
                </if>

                <if test="dto.paxName !=null and dto.paxName !=''">
                        and  fpri.passenger_name like #{dto.paxName}
                </if>
                <if test="dto.idNo != null and dto.idNo != ''">
                        and fpri.id_number = #{dto.idNo}
                </if>
                <if test="dto.tktNo != null and dto.tktNo != ''">
                        and fpri.ticket_number = #{dto.tktNo}
                </if>

                <if test="dto.keySearch != null and dto.keySearch != ''">
                        and (fpri.id_number = #{dto.encryptionKeySearch}  or fpri.ticket_number like #{dto.keySearch}  or  fpri.passenger_name like #{dto.keySearch} )
                </if>

                <if test="dto.tktEndDateStart !=null and dto.tktEndDateStart !=''">
                        and TO_CHAR(fpri.ticket_issue_date ,'yyyy-MM-dd HH24:mm') &gt;= #{dto.tktEndDateStart}
                </if>
                <if test="dto.tktEndDateEnd!=null and dto.tktEndDateEnd !=''">
                        and TO_CHAR(fpri.ticket_issue_date ,'yyyy-MM-dd HH24:mm') &lt;= #{dto.tktEndDateEnd}
                </if>
                <if test="dto.cancelDateStart !=null and dto.cancelDateStart !=''">
                        and fpri.is_cancel = 'Y' and TO_CHAR(fpri.cancel_time ,'yyyy-MM-dd HH24:mm') &gt;= #{dto.cancelDateStart}
                </if>
                <if test="dto.cancelDateEnd!=null and dto.cancelDateEnd !=''">
                        and fpri.is_cancel = 'Y' and TO_CHAR(fpri.cancel_time ,'yyyy-MM-dd HH24:mm') &lt;= #{dto.cancelDateEnd}
                </if>
                <!--multi segment-->
                <if test="dto.choiceSegment !=null and dto.choiceSegment !=''">
                        and instr(#{dto.choiceSegment},concat(fpri.org,concat('-',fpri.dst))) >0
                </if>
                <if test="dto.checkStatus!=null and dto.checkStatus !=''">
                        and fpri.check_status= #{dto.checkStatus}
                </if>
                <if test="dto.containsCancel !=null and dto.containsCancel == 0 ">
                        and (fpri.is_cancel = 'N' or fpri.is_cancel is null)
                </if>
                <if test="dto.containsN !=null and dto.containsN == 1">
                        and fpri.issue_cabin != 'N'
                </if>

                <if test="dto.cabin !=null and dto.cabin.size()>0">
                        and fpri.carry_cabin in
                        <foreach collection="dto.cabin" item="cb" open="(" close=")" separator=",">
                                #{cb}
                        </foreach>
                </if>
                <if test="dto.paxType !=null and dto.paxType.size()>0">
                        and
                        <foreach collection="dto.paxType" item="ty" open="(" close=")" separator=" OR">
                                instr(fpri.CATEGORY_CODE ,#{ty}) >0
                        </foreach>
                </if>
                <choose>
                        <when test="dto.flightNo !=null and dto.flightNo !='' and dto.flightDate !=null and dto.flightDate !=''">
                                ORDER BY fn_getpy(substr(p2.name,1,1),3),fpri.ticket_issue_date asc ) re
                                ORDER BY fn_getpy(re.name,5),fn_getpy(substr(re.name,1,1),3),tktDate asc
                        </when>
                        <otherwise>
                                ) re
                        </otherwise>
                </choose>
        </select>



</mapper>
