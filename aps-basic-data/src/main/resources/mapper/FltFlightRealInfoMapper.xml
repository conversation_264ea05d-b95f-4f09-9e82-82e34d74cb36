<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.basic.data.remoteapi.mapper.FltFlightRealInfoMapper">


    <select id="getFlightInfo" resultType="com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO" databaseId="oracle">

        SELECT
        ffri.ID flightId,
        ffri.FLIGHT_DATE flightDate,
        ffri.FLIGHT_NUMBER flightNo,
        ffri.FLIGHT_TYPE dOrI,
        ffri.PLANECODE acReg,
        ffri.FLIGHT_MODEL acType,
        ffri.DST poa,
        ffri.ORG pod,
        get_city_name (ffri.DST) arrivalPort,
        get_city_name (ffri.ORG) departPort,

        ffri.ALTERNATE_AIRPORT alternateAirport,
        ffri.ALTERNATE_AIRPORT2 alternateAirport2,
        get_city_name (ffri.ALTERNATE_AIRPORT) alternateAirportCh,
        get_city_name (ffri.ALTERNATE_AIRPORT2) alternateAirport2Ch,

        CONCAT (CONCAT (ffri.ORG, '-'), ffri.DST) SEGMENT,
        concat(concat(concat(get_city_name(ffri.org),ffri.org),'-'),
        concat(get_city_name(ffri.dst),ffri.dst)) segmentCh,
        ffri.ATA,
        ffri.ATD,
        ffri.SALE_SEAT bookseat,
        ffri.ETA eta,
        ffri.ETD etd,
        ffri.STA sta,
        ffri.STD std,
        ffri.INNER_REASON,
        ffri.EXTER_REASON,
        ffri.FLIGHT_RUN_STATE,

        ffti.ADJUST_TYPE adjustType,
        ffti.BAY_INCOMING bay,
        ffti.BAY_OUTGOING bayOutGoing,
        ffti.FLIGHT_TYPE flightType,
        ffti.FLG_VR
        FROM
            flt_flight_real_info ffri
        LEFT JOIN foc_flight_time_info ffti ON ffri. ID = ffti.flight_id
        WHERE 1=1
        <if test="dto.flightDate != null">
            and TO_CHAR(ffri.FLIGHT_DATE,'yyyy-mm-dd') = #{dto.flightDate}
        </if>
        <if test="dto.flightNo != null">
            AND ffri.FLIGHT_NUMBER = #{dto.flightNo}
        </if>

        <if test="dto.org != null">
            AND ffri.ORG = #{dto.org}
        </if>

        <if test="dto.dst != null">
            AND ffri.DST = #{dto.dst}
        </if>
        <if test="dto.flightId != null">
            AND ffri.ID = #{dto.flightId}
        </if>
        ORDER BY ffti.LOAD_TIME DESC,ffri.STD Asc
    </select>

    <select id="getFlightUnitInfo" resultType="com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO"
            databaseId="oracle">
        <if test="dto.flightId != null">
            SELECT
            ffc.CREW_LINK_LINE AS flightUnitCode,
            ffcj.RANK_NAME AS flightUnitPersonnelJob,
            ffcj.RANK_NO flightUnitPersonnelJobCode,
            ffc.P_CODE AS flightUnitPersonnelCode,
            NVL( ffci.C_NAME, ffci.E_NAME ) AS flightUnitPersonnelName,
            ffci.SEX AS flightUnitPersonnelSex,
            ffci.PHONE AS flightUnitPersonnelPhone,
            ue.JOB_NUMBER AS flightUnitPersonnelWorkNo
            FROM
            FOC_FLIGHT_CREW ffc
            LEFT JOIN FLT_FLIGHT_REAL_INFO ffri ON ffri.FLIGHT_GROUP=ffc.CREW_LINK_LINE
            LEFT JOIN FOC_FLIGHT_CREW_INFO ffci ON ffc.P_CODE = ffci.P_CODE
            LEFT JOIN FOC_FLIGHT_CREW_JOB ffcj ON ffc.RANK_NO = ffcj.RANK_NO
            LEFT JOIN UC_EMPLOYEE ue ON ue.NAME = ffci.C_NAME AND ue.DELETED = 0
            WHERE
            ffri.id=#{dto.flightId}
            <if test="dto.flightUnitPersonnelCodes!=null and dto.flightUnitPersonnelCodes.size()>0">
                and ffc.P_CODE in
                <foreach collection="dto.flightUnitPersonnelCodes" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            ORDER BY ffcj.RANK_NO asc
        </if>
        <if test="dto.flightId == null and dto.flightNo != null and dto.flightDate != null and dto.org != null and dto.dst != null  ">
            SELECT
            ffc.CREW_LINK_LINE AS flightUnitCode,
            ffcj.RANK_NAME AS flightUnitPersonnelJob,
            ffcj.RANK_NO flightUnitPersonnelJobCode,
            ffc.P_CODE AS flightUnitPersonnelCode,
            NVL( ffci.C_NAME, ffci.E_NAME ) AS flightUnitPersonnelName,
            ffci.SEX AS flightUnitPersonnelSex,
            ffci.PHONE AS flightUnitPersonnelPhone,
            ue.JOB_NUMBER AS flightUnitPersonnelWorkNo
            FROM
            FOC_FLIGHT_CREW ffc
            LEFT JOIN FLT_FLIGHT_REAL_INFO ffri ON ffri.FLIGHT_GROUP=ffc.CREW_LINK_LINE
            LEFT JOIN FOC_FLIGHT_CREW_INFO ffci ON ffc.P_CODE = ffci.P_CODE
            LEFT JOIN FOC_FLIGHT_CREW_JOB ffcj ON ffc.RANK_NO = ffcj.RANK_NO
            LEFT JOIN UC_EMPLOYEE ue ON ue.NAME = ffci.C_NAME AND ue.DELETED = 0
            WHERE
            ffri.FLIGHT_DATE = to_date(#{dto.flightDate},'yyyy-mm-dd')
            AND ffri.FLIGHT_NUMBER= #{dto.flightNo}
            AND ffri.ORG=#{dto.org}
            AND ffri.DST = #{dto.dst}
            <if test="dto.flightUnitPersonnelCodes!=null and dto.flightUnitPersonnelCodes.size()>0">
                and ffc.P_CODE in
                <foreach collection="dto.flightUnitPersonnelCodes" item="code" separator="," open="(" close=")">
                    #{code}
                </foreach>
            </if>
            ORDER BY ffcj.RANK_NO asc
        </if>
    </select>
</mapper>