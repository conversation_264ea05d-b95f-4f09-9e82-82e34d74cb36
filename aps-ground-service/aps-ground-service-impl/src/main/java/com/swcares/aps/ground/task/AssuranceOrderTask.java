package com.swcares.aps.ground.task;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.service.AssuranceOrderManageService;
import com.swcares.aps.ground.datasync.service.DataSyncErrorService;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderChangeStatusCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.enums.AuditStatusEnum;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.core.tenant.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：AssuranceOrderTask
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/9/29 15:56
 * @version： v1.0
 */
@Slf4j
@Component
public class AssuranceOrderTask {

    @Autowired
    private AssuranceOrderManageService assuranceOrderManageService;

    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    @Autowired
    private AssuranceOrderInfoMapper assuranceOrderInfoMapper;

    @Autowired
    private DataSyncErrorService dataSyncErrorService;

    @Autowired
    private PlatformTool platformTool;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    private Redisson redisson;

    @Scheduled(fixedDelay = 30000)
    public void doErrorSend(){
        RLock lock = redisson.getLock("ASSURANCE_ORDER_TASK:doErrorSend");
        try {
            boolean resLock = lock.tryLock(20, TimeUnit.SECONDS);
            if (!resLock) {return;}
            String tenantType=platformTool.getTenantType();
            log.info("doErrorSend---begin--{}", System.currentTimeMillis());
            dataSyncErrorService.doErrorSend(tenantType);
            log.info("doErrorSend---end--{}", System.currentTimeMillis());
        }catch (Exception e){
            log.error("重新执行数据同步推送",e);
        }finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    @Scheduled(fixedDelay = 1800000)
    public void overTimeAssuranceOrder(){
        RLock lock = redisson.getLock("ASSURANCE_ORDER_TASK:overTimeAssuranceOrder");
        try {
            boolean resLock = lock.tryLock();
            if (!resLock) {return;}
            log.info("overTimeAssuranceOrder---begin--{}", System.currentTimeMillis());
            assuranceOrderManageService.overTimeAssuranceOrder();
            log.info("overTimeAssuranceOrder---end--{}", System.currentTimeMillis());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Scheduled(fixedDelay = 600000)
    public void finishAssuranceOrder(){
        RLock lock = redisson.getLock("ASSURANCE_ORDER_TASK:finishAssuranceOrder");
        try {
            boolean resLock = lock.tryLock();
            if (!resLock) {return;}
            log.info("finishAssuranceOrder---begin--{}", System.currentTimeMillis());
            assuranceOrderManageService.finishAssuranceOrder();
            log.info("finishAssuranceOrder---end--{}", System.currentTimeMillis());
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    @Scheduled(fixedDelay = 30000)
    public void workflowTask(){
        RLock lock = redisson.getLock("ASSURANCE_ORDER_TASK:refreshWorkflowUser");
        try {
            boolean resLock = lock.tryLock(20, TimeUnit.SECONDS);
            if (!resLock) {return;}
            doWorkFlowTask();
        }catch (Exception e){
            log.error("执行刷新协同通信流程用户任务出错",e);
        }finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    private void doWorkFlowTask() {
        if(platformTool.isSupplierPlatform()){return;}
        String tenantType=platformTool.getTenantType();
        List<AssuranceOrderInfoDO> assuranceOrderInfoDOS = assuranceOrderInfoMapper.selectNeedRefreshWorkflowUserOrder(tenantType);
        if(CollectionUtils.isEmpty(assuranceOrderInfoDOS)){
            log.info("没有需要同步审核流程用户信息节点");
            return;
        }
        log.info("开始执行同步审核流程用户信息节点:{}", JSONObject.toJSONString(assuranceOrderInfoDOS));
        for(AssuranceOrderInfoDO infoDO:assuranceOrderInfoDOS){
            try{
                BaseQueryParamDTO queryParam = BaseQueryParamDTO.builder().businessKey(infoDO.getOrderNo()).build();
                CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(queryParam).getData();
                List<CurrentTaskActivityDTO> currentTaskActivityDTOS = currentTaskActivityVO.getCurrentTaskActivityDTOS();
                log.info("执行同步审核流程用户信息定时任务,获取流程当前啊任务信息，orderId:{},result:{}",infoDO.getId(), JSONObject.toJSONString(currentTaskActivityDTOS));
                CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityDTOS.get(0);

                //【更新赔偿单状态 = 驳回】当前赔偿单状态=审核中 && 当前节点 = 发起节点 && 上一个节点审批状态 = 驳回
                if(WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey()) &&
                      AuditStatusEnum.REJECT.getKey().equals(currentTaskActivityVO.getPreOptionCode())){
                    //处理航司端与机场端，驳回到发起人节点，补偿单状态不一致问题。
                    log.info("执行同步审核流程用户信息定时任务,上一个节点审批状态为驳回，当前节点为发起人节点，更新赔偿单状态为驳回，orderId:{},result:{}",infoDO.getId(), JSONObject.toJSONString(currentTaskActivityDTOS));

                    AssuranceOrderChangeStatusCommand command=new AssuranceOrderChangeStatusCommand();
                    command.setOrderId(infoDO.getId());
                    command.setTargetStatus(AssuranceOrderState.AUDIT_BACK.getCode());
                    assuranceOrderManageService.changeAssuranceOrderStatus(command);
                    continue;
                }
                if(WorkflowUtils.isSyncProcessTask(currentTaskActivityDTO.getNodeKey())
                        || WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey())){
                    continue;
                }
                TenantContextHolder.setTenant(infoDO.getTenantId());
                if(currentTaskActivityDTO.getIsEndActivity()){
                    String preOptionCode = currentTaskActivityVO.getPreOptionCode();
                    AssuranceOrderChangeStatusCommand command=new AssuranceOrderChangeStatusCommand();
                    command.setOrderId(infoDO.getId());
                    command.setTargetStatus(AssuranceOrderState.SERVICE_ING.getCode());
                    if(StringUtils.equals(preOptionCode, AuditStatusEnum.DISAGREE.getKey())){
                        command.setTargetStatus(AssuranceOrderState.AUDIT_REJECT.getCode());
                    }
                    assuranceOrderManageService.changeAssuranceOrderStatus(command);
                    continue;
                }
                CurrentTaskActivityDTO currentTask = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
                if(StringUtils.isBlank(currentTask.getLastAssignee())){
                    log.error("执行同步审核流程用户信息执行失败,节点没有配置审核人信息,Tenant:{},orderId:{}",infoDO.getTenantId(),infoDO.getId());
                    continue;
                }
                List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService
                        .findAuditorList(Collections.singletonList(currentTask.getLastAssignee()));
                if(CollectionUtils.isEmpty(auditorList)){
                    log.error("执行同步审核流程用户信息执行失败,节点没有配置审核人信息,Tenant:{},orderId:{}",infoDO.getTenantId(),infoDO.getId());
                    continue;
                }
                Long[] auditorIds = auditorList.stream().map(AuditorInfoDTO::getReviewerId).toArray(Long[]::new);
                AssuranceOrderReviewerSaveCommand command=new AssuranceOrderReviewerSaveCommand();
                command.setOrderId(infoDO.getId());
                command.setTaskId(currentTask.getTaskId());
                command.setAuditorIds(auditorIds);
                assuranceOrderManageService.saveAuditReviewer(command);
                log.info("执行同步审核流程用户信息执行成功,Tenant:{},orderId:{},auditors:{}",infoDO.getTenantId(),infoDO.getId(),JSONObject.toJSONString(command));
            }catch (Exception e){
                log.error("执行同步审核流程用户信息执行失败,Tenant:{},orderId:{}", infoDO.getTenantId(), infoDO.getId(), e);
            }
        }
    }

}
