package com.swcares.aps.ground.assurance.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightInfoDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightUnitInfoVO;
import com.swcares.aps.ground.assurance.mapper.PassengerCategoryConfigureMapper;
import com.swcares.aps.ground.assurance.service.AssuranceFltPaxQueryService;
import com.swcares.aps.ground.models.assurance.dto.PaxQueryDTO;
import com.swcares.aps.ground.models.assurance.vo.AssurancePassengerVO;
import com.swcares.aps.ground.models.assurance.vo.ProtectFltInfoVO;
import com.swcares.aps.ground.models.passengerCatefory.PassengerCategoryConfigureDepository;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * @ClassName：FlightPassenegerQueryController
 * @Description：航班&旅客信息查询接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/21 16:28
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/fltpax/query")
@Api(tags = {"航班&旅客信息查询接口"})
@ApiVersion({"保障单相关Api"})
public class AssuranceFltPaxQueryController extends BaseController {


    @Autowired
    AssuranceFltPaxQueryService assuranceFltPaxQueryService;

    @Autowired
    PassengerCategoryConfigureMapper passengerCategoryConfigureMapper;
    @GetMapping("/getFltInfo")
    @ApiOperation(value = "通过航班号航班日期查询航段和服务航站")
    public BaseResult<ProtectFltInfoVO> getFltInfo(@ApiParam(value = "航班号", required = true) String flightNo,
                                                   @ApiParam(value = "航班日期", required = true) String flightDate) {
        return ok(assuranceFltPaxQueryService.getFltInfo(flightNo, flightDate));
    }

    @PostMapping("/queryPax")
    @ApiOperation(value = "选择查询旅客保障对象")
    public BaseResult<List<AssurancePassengerVO>> queryPax(@RequestBody @Valid PaxQueryDTO dto) {
        return ok(assuranceFltPaxQueryService.queryPax(dto));
    }

    @PostMapping("/getFlightUnitInfo")
    @ApiOperation(value = "选择查询机组保障对象")
    public BaseResult<List<FlightUnitInfoVO>> getFlightUnitInfo(@RequestBody @Valid List<FlightInfoDTO> flightInfoDTO){
        return ok(assuranceFltPaxQueryService.getFlightUnitInfo(flightInfoDTO));
    }

    @GetMapping("/getPassengerCategoryInfo")
    @ApiOperation(value = "旅客类别获取接口")
    public BaseResult<List<PassengerCategoryConfigureDepository>> getPassengerCategoryInfo(){
        return ok(passengerCategoryConfigureMapper.selectList(Wrappers.lambdaQuery(PassengerCategoryConfigureDepository.class)));
    }

}
