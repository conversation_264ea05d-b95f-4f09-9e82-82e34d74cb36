package com.swcares.aps.ground.assurance.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.aps.component.workflow.entity.WorkflowModelCodeInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.enums.AuditStatusEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.component.workflow.service.WorkflowModelCodeInfoService;
import com.swcares.aps.ground.assurance.mapper.AssuranceOrderInfoMapper;
import com.swcares.aps.ground.assurance.service.AssuranceWorkflowDomainService;
import com.swcares.aps.ground.common.TenantService;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.ground.constants.GroundServicePubConstants;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderReviewerSaveCommand;
import com.swcares.aps.ground.models.assurance.dto.AssuranceWorkflowAuditCommand;
import com.swcares.aps.ground.models.assurance.entity.AssuranceOrderInfoDO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditHistoryVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditResultVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditorOrderVO;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.CustomerDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityVO;
import com.swcares.aps.workflow.dto.StartProcessParamsDTO;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * @ClassName：AssuranceWorkflowDomainServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/22 10:29
 * @version： v1.0
 */
@Service
@Slf4j
public class AssuranceWorkflowDomainServiceImpl implements AssuranceWorkflowDomainService {

    public static final int PROCESS_INSTANCE_NOT_EXIST = 31002;

    @Autowired
    private WorkflowModelCodeInfoService workflowModelCodeInfoService;

    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    @Autowired
    private AssuranceOrderInfoMapper assuranceOrderInfoMapper;

    @Autowired
    private TenantService tenantService;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    private Redisson redisson;

    @Autowired
    private PlatformTool platformTool;

    private static final String REDISSON_PRE_KEY = "ASSURANCE_ORDER_AUDIT:";

    private static final String WORKFLOW_BUSINESS_NAME="assurance_order";

    @Override
    public AssuranceWorkflowAuditResultVO start(AssuranceOrderDomain assuranceOrderDomain,
                                                StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO) {
        return executeTask("保障单服务发起流程",
                assuranceOrderDomain,
                startSyncWorkflowInfoDTO,
                null,
                (orderDomain,syncWorkflowInfo, command) -> processStart(orderDomain,syncWorkflowInfo));
    }
    @Override
    public AssuranceWorkflowAuditResultVO submit(AssuranceOrderDomain assuranceOrderDomain) {
        return executeTask("保障单服务提交流程",
                assuranceOrderDomain,
                null,
                null,
                (orderDomain,syncWorkflowInfo, command) -> processSubmit(orderDomain));
    }
    @Override
    public AssuranceWorkflowAuditResultVO process(AssuranceOrderDomain assuranceOrderDomain,AssuranceWorkflowAuditCommand command) {
        AuditStatusEnum.build(command.getAuditStatus());
        return executeTask("保障单服务流程节点审核",
                assuranceOrderDomain,
                null,
                command,
                (orderDomain,syncWorkflowInfo, auditCommand) -> processAudit(orderDomain, auditCommand));
    }

    @Override
    public List<AuditorInfoDTO> saveReviewer(AssuranceOrderDomain assuranceOrderDomain, AssuranceOrderReviewerSaveCommand command) {
        List<AuditorInfoDTO> auditorInfoDTOS = this.findReviewer(command.getTaskId(), command.getOrderId());
        if(CollectionUtils.isEmpty(auditorInfoDTOS)){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"错误的审核人数据【当前taskId没有找到任何审核人信息】");
        }
        List<String> allowUserIds = auditorInfoDTOS.stream().map(AuditorInfoDTO::getReviewerId).map(String::valueOf).collect(Collectors.toList());
        List<Long> userIds = Arrays.asList(command.getAuditorIds());
        Optional<Long> any = userIds.stream().filter(t -> !allowUserIds.contains(String.valueOf(t))).findAny();
        if(any.isPresent()){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"错误的审核人数据");
        }
        this.deleteWorkflowAuditorIds(assuranceOrderDomain.getDomainOrderNo());
        this.addWorkflowAuditorIds(command.getTaskId(),assuranceOrderDomain.getDomainOrderNo(),userIds);

        List<String> collect = userIds.stream().map(String::valueOf).collect(Collectors.toList());
        return auditorInfoDTOS.stream().filter(t->collect.contains(String.valueOf(t.getReviewerId()))).collect(Collectors.toList());
    }

    @Override
    public List<AssuranceWorkflowAuditorOrderVO> getOrderAuthUsers(List<String> orderIds, String userId) {

        List<AssuranceOrderInfoDO> assuranceOrderInfoDOS = assuranceOrderInfoMapper.selectBatchIds(orderIds);
        if(CollectionUtils.isEmpty(assuranceOrderInfoDOS)){ return Collections.EMPTY_LIST; }

        Map<String, String> orderNoToOrderIdMp = assuranceOrderInfoDOS.stream()
                .collect(Collectors
                        .toMap(AssuranceOrderInfoDO::getOrderNo, AssuranceOrderInfoDO::getId));

        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> queryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        queryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,ApsProjectEnum.GROUND_SERVICE.getProjectType())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,WORKFLOW_BUSINESS_NAME)
                .in(WorkflowAuditorIdInfoDO::getBusinessValue,orderNoToOrderIdMp.keySet());
        if(StringUtils.isNotBlank(userId)){
            queryWrapper.eq(WorkflowAuditorIdInfoDO::getAuditorId,userId);
        }
        List<WorkflowAuditorIdInfoDO> infoDOS = workflowAuditorIdInfoService.list(queryWrapper);
        if(CollectionUtils.isEmpty(infoDOS)){return Collections.EMPTY_LIST;}

        return infoDOS.stream().map(t ->
                AssuranceWorkflowAuditorOrderVO.builder()
                        .orderId(orderNoToOrderIdMp.get(t.getBusinessValue()))
                        .taskId(t.getTaskId())
                        .auditorId(t.getAuditorId()).build()
        ).collect(Collectors.toList());
    }

    @Override
    public List<AuditorInfoDTO> findReviewer(String taskId, String orderId) {
        AssuranceOrderInfoDO infoDO = assuranceOrderInfoMapper.selectById(orderId);
        if(infoDO==null){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");
        }
        LambdaQueryWrapper<WorkflowAuditorIdInfoDO> queryWrapper = Wrappers.lambdaQuery(WorkflowAuditorIdInfoDO.class);
        queryWrapper.eq(WorkflowAuditorIdInfoDO::getProject,ApsProjectEnum.GROUND_SERVICE.getProjectType())
                .eq(WorkflowAuditorIdInfoDO::getBusiness,WORKFLOW_BUSINESS_NAME)
                .eq(WorkflowAuditorIdInfoDO::getBusinessValue,infoDO.getOrderNo());
        if(StringUtils.isNotBlank(taskId)){
            queryWrapper.eq(WorkflowAuditorIdInfoDO::getTaskId,taskId);
        }
        List<WorkflowAuditorIdInfoDO> infoDOS = workflowAuditorIdInfoService.list(queryWrapper);
        if(CollectionUtils.isEmpty(infoDOS)){return Collections.EMPTY_LIST;}
        String userIds = infoDOS.stream().map(WorkflowAuditorIdInfoDO::getAuditorId).collect(Collectors.joining(","));
        return workflowAuditorIdInfoService.findAuditorList(Arrays.asList("userId:"+userIds));
    }

    @Override
    public List<AssuranceWorkflowAuditHistoryVO> findAuditHistory(String orderId) {
        AssuranceOrderInfoDO infoDO = assuranceOrderInfoMapper.selectById(orderId);
        if(infoDO==null){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"非法保障单ID");
        }
        //历史节点+当前节点+未来节点
        //1.历史任务数据+包含待执行任务
        HistoryTaskAuditActivityVO historyTaskAuditActivityVO = workflowApi.historyTaskAuditActivity(BaseQueryParamDTO.builder().businessKey(infoDO.getOrderNo()).build()).getData();
        if(historyTaskAuditActivityVO==null){return Collections.EMPTY_LIST;}

        List<AssuranceWorkflowAuditHistoryVO> historyVOS=createWorkflowAuditHistoryVOs(historyTaskAuditActivityVO);
        if(CollectionUtils.isEmpty(historyVOS)){return Collections.EMPTY_LIST;}

        wrapWorkflowAuditHistoryVOs(historyVOS,historyTaskAuditActivityVO.getRequestBusinessName(),infoDO.getBelongTenantType());

        wrapWorkflowReadyVOs(historyVOS,orderId);

        return historyVOS;
    }

    @Override
    public List<AuditorInfoDTO> findAuditorList(List<String> userIds) {
        if(CollectionUtils.isEmpty(userIds)){
            return Collections.emptyList();
        }
        String join = String.join(",", userIds);
        return workflowAuditorIdInfoService.findAuditorList(Collections.singletonList("userId:" + join));
    }

    private void wrapWorkflowReadyVOs(List<AssuranceWorkflowAuditHistoryVO> historyVOS, String orderId) {
        //获取未来节点
        AssuranceWorkflowAuditHistoryVO auditHistoryVO = historyVOS.get(historyVOS.size() - 1);
        if(!auditHistoryVO.getStatus().equals(AssuranceWorkflowAuditHistoryVO.ready)){return;}

        List<AuditorInfoDTO> reviewers= findReviewer(auditHistoryVO.getTaskId(), orderId);
        if(CollectionUtils.isNotEmpty(reviewers)){
            String reviewerId=reviewers.stream().map(t->String.valueOf(t.getReviewerId())).collect(Collectors.joining(","));
            String reviewerInfo=reviewers.stream().map(t->t.getReviewerNameNo()+":"+t.getReviewerPhone()).collect(Collectors.joining(","));
            auditHistoryVO.setReviewerInfo(reviewerInfo);
            auditHistoryVO.setReviewerId(reviewerId);
            auditHistoryVO.setWaiteAuditReviewers(reviewers);
        }
        BaseResult<CurrentTaskActivityDTO> nextTaskInfo = workflowApi.getNextTask(auditHistoryVO.getTaskId());
        if(ObjectUtils.isNotEmpty(nextTaskInfo.getData())){
            AssuranceWorkflowAuditHistoryVO vo  = new AssuranceWorkflowAuditHistoryVO();
            vo.setReviewerInfo("未审核");
            vo.setStatus("3");
            historyVOS.add(vo);
        }
    }

    private void wrapWorkflowAuditHistoryVOs(List<AssuranceWorkflowAuditHistoryVO> historyVOS,String requestBusinessName,String orderBelongTenantType) {
        String userIds = String.join(",", historyVOS.stream()
                .map(AssuranceWorkflowAuditHistoryVO::getReviewerId)
                .filter(NumberUtil::isLong)
                .collect(Collectors.toSet()));
        Map<String, AuditorInfoDTO> idToAuditMp=Collections.EMPTY_MAP;
        if(StringUtils.isNotEmpty(userIds)){
            List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findAuditorList(Collections.singletonList("userId:" + userIds));
            if(CollectionUtils.isNotEmpty(auditorList)){
                idToAuditMp = auditorList.stream().collect(Collectors.toMap(t->String.valueOf(t.getReviewerId()), t -> t, (o, n) -> n));
            }
        }

        String businessName = tenantService.getTenantNameById(TenantHolder.getTenant());
        for(AssuranceWorkflowAuditHistoryVO vo:historyVOS){
            if(WorkflowUtils.isSubmitterTask(vo.getNodeKey())){
                if(!platformTool.getTenantType().equals(orderBelongTenantType)){
                    vo.setReviewerId(null);
                    vo.setReviewerInfo(null);
                    vo.setObjName(requestBusinessName);
                    continue;
                }
            }
            if(WorkflowUtils.isSyncProcessTask(vo.getNodeKey())){
                vo.setObjName(requestBusinessName);
            }else{
                vo.setObjName(businessName);
                String reviewerId = vo.getReviewerId();
                if(StringUtils.isBlank(reviewerId)){continue;}
                AuditorInfoDTO auditorInfoDTO = idToAuditMp.get(reviewerId);
                if(auditorInfoDTO!=null){
                    String reviewerInfo=auditorInfoDTO.getReviewerNameNo()+":"+auditorInfoDTO.getReviewerPhone();
                    vo.setReviewerInfo(reviewerInfo);
                    vo.setObjName(businessName+"-"+auditorInfoDTO.getOrgName());
                }else{
                    if(AuditStatusEnum.DISAGREE.getKey().equals(vo.getNodeName()) && GroundServicePubConstants.ADMIN_END_ID.equals(reviewerId)){
                        vo.setReviewerInfo(GroundServicePubConstants.ADMIN_END);
                    }
                }
            }
        }
    }

    private List<AssuranceWorkflowAuditHistoryVO> createWorkflowAuditHistoryVOs(HistoryTaskAuditActivityVO historyTaskAuditActivityVO) {

        //历史节点+当前节点+未来节点
        //1.历史任务数据+包含待执行任务

        List<HistoryTaskAuditActivityDTO> auditActivityDTOList = historyTaskAuditActivityVO
                .getHistoryTaskAuditActivityDTOS()
                .stream()
                .filter(t->!(WorkflowUtils.isSubmitterTask(t.getNodeKey()) && t.getTaskEndTime() == null)) //当前节点是等待提交数据,不展示
                .collect(Collectors.toList());
        if (ListUtils.isEmpty(auditActivityDTOList)) { return Collections.EMPTY_LIST; }


        List<AssuranceWorkflowAuditHistoryVO> historyVOS=new ArrayList<>(auditActivityDTOList.size());

        for (HistoryTaskAuditActivityDTO taskDO : auditActivityDTOList){
            AssuranceWorkflowAuditHistoryVO vo=new AssuranceWorkflowAuditHistoryVO();
            vo.setTaskId(taskDO.getTaskId());
            vo.setNodeKey(taskDO.getNodeKey());
            vo.setStatus(AssuranceWorkflowAuditHistoryVO.ready);
            if(null != taskDO.getTaskEndTime()){
                String formatDate = DateUtils.formatDate(taskDO.getTaskEndTime(), DatePattern.PURE_DATETIME_PATTERN);
                LocalDateTime localDateTime = LocalDateTimeUtil.parse(formatDate, DatePattern.PURE_DATETIME_PATTERN);
                vo.setDoneDate(localDateTime);
                vo.setStatus(AssuranceWorkflowAuditHistoryVO.complete);
            }
            taskDO.setTaskStatus(vo.getStatus());
            vo.setRemarks(taskDO.getComment());

            vo.setReviewerId(taskDO.getAssignee());
            //设置审核人id
            if(StringUtils.isNotEmpty(taskDO.getAssignee()) && taskDO.getAssignee().split(":").length>=2){
                vo.setReviewerId(taskDO.getAssignee().split(":")[1]);
            }
            vo.setNodeName(taskDO.getOptionCode());

            if(WorkflowUtils.isAutomaticTask(taskDO)){
                vo.setNodeName("AGREE");
                vo.setReviewerInfo("ADMIN");
                vo.setRemarks("提交的补偿单已自动审核通过");
                vo.setObjName("");
            }

            //判断是否是发起节点
            if (WorkflowUtils.isSubmitterTask(taskDO.getNodeKey())) {
                vo.setNodeName(taskDO.getNodeKey());
            }
            historyVOS.add(vo);
        }
        return historyVOS;
    }

    private AssuranceWorkflowAuditResultVO processStart(AssuranceOrderDomain assuranceOrderDomain,StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO) {
        CurrentTaskActivityVO currentTaskActivityVO = null;
        //第一步先查询，防止之前有创建流程，但是由于业务异常代码回滚（但流程没有回滚）；后面又重新发起流程
        try {
            currentTaskActivityVO = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(assuranceOrderDomain.getDomainId()).build()).getData();
            log.info("保障单id:【{}】,查询当前流程结果：【{}】", assuranceOrderDomain.getDomainOrderNo(), JSONUtil.toJsonStr(currentTaskActivityVO));
        }catch (DecoderHandlerException b){
            if(b.getCode()!=PROCESS_INSTANCE_NOT_EXIST){throw b;}
        }
        if(currentTaskActivityVO!=null){return submit(assuranceOrderDomain);}

        WorkflowModelCodeInfoDO  workflowModelCodeInfoDO = workflowModelCodeInfoService
                .findByProjectAndBusiness(ApsProjectEnum.GROUND_SERVICE.getProjectType(), WORKFLOW_BUSINESS_NAME);
        if(workflowModelCodeInfoDO==null){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"获取流程配置key失败流程操作失败");
        }

        String userId = platformTool.getCurrentOperatorUserId();


        Long tenantId = assuranceOrderDomain.getOrderInfoDO().getTenantId();
        String tenantCode = assuranceOrderDomain.getOrderInfoDO().getTenantCode();
        String tenantName = tenantService.getTenantNameById(tenantId);
        CustomerDTO customerDTO=new CustomerDTO();
        customerDTO.setCustomer(tenantCode);
        customerDTO.setCustomerCategory(platformTool.getTenantType());
        customerDTO.setBusinessName(tenantName);


        StartProcessParamsDTO startProcessParamsDTO = new StartProcessParamsDTO();
        startProcessParamsDTO.setAssignee("userId:" + userId);
        startProcessParamsDTO.setBusinessKey(assuranceOrderDomain.getDomainOrderNo());
        startProcessParamsDTO.setProcDefKey(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
        startProcessParamsDTO.setCustomerDTO(customerDTO);
        startProcessParamsDTO.setStartSyncWorkflowInfoDTO(startSyncWorkflowInfoDTO);
        if(startSyncWorkflowInfoDTO!=null){
            startProcessParamsDTO.setProcessFlag(startProcessParamsDTO.RESPONSE);
        }else {
            startProcessParamsDTO.setProcessFlag(startProcessParamsDTO.REQUEST);
        }

        long startTime = System.currentTimeMillis();
        BaseResult<CurrentTaskActivityVO> startResult = workflowApi.startProcess(startProcessParamsDTO);
        log.info("保障单工作流耗时统计——startProcess启动流程实例【结束】，当前步骤（非累计）耗时：【{}】-------",  (System.currentTimeMillis() - startTime));
        log.info("保障单工作流-startProcess启动流程实例【结束】，保障单id[{}],参数【{}】----启动流程实例返回信息[{}]", assuranceOrderDomain.getDomainOrderNo(), JSONUtil.toJsonStr(startProcessParamsDTO),startResult);


        return this.processSubmit(assuranceOrderDomain,startResult.getData());

    }
    private AssuranceWorkflowAuditResultVO processSubmit(AssuranceOrderDomain assuranceOrderDomain){
        CurrentTaskActivityVO taskActivityVO = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(assuranceOrderDomain.getDomainOrderNo()).build()).getData();
        return processSubmit(assuranceOrderDomain,taskActivityVO);
    }

    private AssuranceWorkflowAuditResultVO processSubmit(AssuranceOrderDomain assuranceOrderDomain,CurrentTaskActivityVO taskActivityVO) {
        CurrentTaskActivityDTO currentTaskActivityDTO = taskActivityVO.getCurrentTaskActivityDTOS().get(0);
        if(!WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey())){
            throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"不能进行提交操作【非提交节点】");
        }

        CompleteProcessParamsDTO completeProcessParamsDTO=new CompleteProcessParamsDTO();
        completeProcessParamsDTO.setTaskId(currentTaskActivityDTO.getTaskId());
        completeProcessParamsDTO.setBusinessKey(assuranceOrderDomain.getDomainOrderNo());
        completeProcessParamsDTO.setOptionCode(AuditStatusEnum.SUBMIT.getKey());
        completeProcessParamsDTO.setBusiData(assuranceOrderDomain.createWorkflowSubmitBusiData());
        completeProcessParamsDTO.setUserId("userId:" + platformTool.getCurrentOperatorUserId());
        CurrentTaskActivityVO activityVO = workflowApi.completeTask(completeProcessParamsDTO).getData();
        return createCurrentTaskActivityVO(activityVO,assuranceOrderDomain);
    }


    private AssuranceWorkflowAuditResultVO processAudit(AssuranceOrderDomain assuranceOrderDomain, AssuranceWorkflowAuditCommand command) {
        String userId = StringUtils.isBlank(command.getUserId())?platformTool.getCurrentOperatorUserId():command.getUserId();
        CompleteProcessParamsDTO completeDto = new CompleteProcessParamsDTO();
        completeDto.setBusinessKey(command.getOrderId());
        completeDto.setTaskId(command.getTaskId());
        completeDto.setUserId("userId:" + userId);
        completeDto.setOptionCode(command.getAuditStatus());
        completeDto.setComment(command.getRemarks());
        CurrentTaskActivityVO activityVO = workflowApi.completeTask(completeDto).getData();
        return createCurrentTaskActivityVO(activityVO,assuranceOrderDomain);
    }

    private AssuranceWorkflowAuditResultVO createCurrentTaskActivityVO(CurrentTaskActivityVO activityVO, AssuranceOrderDomain assuranceOrderDomain) {

        CurrentTaskActivityDTO currentTask = activityVO.getCurrentTaskActivityDTOS().get(0);
        AssuranceWorkflowAuditResultVO resultVO=new AssuranceWorkflowAuditResultVO(activityVO);

        this.deleteWorkflowAuditorIds(assuranceOrderDomain.getDomainOrderNo());

        //结束节点
        if(currentTask.getIsEndActivity()){
            resultVO.setIsCancel(false);
            resultVO.setTaskId(null);
            resultVO.setOrderAuditorList(null);
            resultVO.setIsPrompt(false);
            return resultVO;
        }


        //流程同步节点
        if(WorkflowUtils.isSyncProcessTask(currentTask.getNodeKey())){
            resultVO.setOrderId(assuranceOrderDomain.getDomainId());
            resultVO.setIsCancel(false);
            resultVO.setTaskId(currentTask.getTaskId());
            resultVO.setOrderAuditorList(null);
            resultVO.setIsPrompt(false);
            return resultVO;

        }

        //提交节点
        if(WorkflowUtils.isSubmitterTask(currentTask.getNodeKey())){
            resultVO.setOrderId(assuranceOrderDomain.getDomainId());
            resultVO.setIsCancel(false);
            resultVO.setTaskId(currentTask.getTaskId());
            resultVO.setIsPrompt(false);
            if(StringUtils.equals(AuditStatusEnum.REJECT.getKey(),activityVO.getPreOptionCode())
                    && StringUtils.isNotBlank(currentTask.getLastAssignee())){
                List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findAuditorList(Collections.singletonList(currentTask.getLastAssignee()));
                resultVO.setOrderAuditorList(auditorList);
            }
            return resultVO;

        }

        //普通节点的逻辑
        List<AuditorInfoDTO> auditorList=null;
        if(StringUtils.equals(AuditStatusEnum.REJECT.getKey(),activityVO.getPreOptionCode())
                && StringUtils.isNotBlank(currentTask.getLastAssignee())){
            auditorList = workflowAuditorIdInfoService.findAuditorList(Collections.singletonList(currentTask.getLastAssignee()));
        }
        if(CollectionUtils.isEmpty(auditorList)){
            List<String> assignees = currentTask.getAssignees();
            auditorList = workflowAuditorIdInfoService.findAuditorList(assignees);
        }

        if(CollectionUtils.isEmpty(auditorList)){

            log.error("报销单审核流-回调异常，节点没有没有执行人,notice:{}", JSONUtil.toJsonStr(activityVO));
            AssuranceWorkflowAuditCommand command=new AssuranceWorkflowAuditCommand();
            command.setRemarks(GroundServicePubConstants.ADMIN_END_REMARKS);
            command.setAuditStatus(AuditStatusEnum.DISAGREE.getKey());
            command.setUserId(GroundServicePubConstants.ADMIN_END_ID);
            command.setTaskId(currentTask.getTaskId());
            AssuranceWorkflowAuditResultVO auditResultVO = processAudit(assuranceOrderDomain, command);

            resultVO.setCurrentTaskActivityVO(auditResultVO.getCurrentTaskActivityVO());
            resultVO.setOrderId(assuranceOrderDomain.getDomainId());
            resultVO.setIsCancel(false);
            resultVO.setTaskId(null);
            resultVO.setOrderAuditorList(null);
            resultVO.setIsPrompt(true);
            return resultVO;
        }
        List<Long> auditorInfoUserIds = auditorList.stream().map(AuditorInfoDTO::getReviewerId).collect(Collectors.toList());

        this.addWorkflowAuditorIds(currentTask.getTaskId(),assuranceOrderDomain.getDomainOrderNo(),auditorInfoUserIds);

        resultVO.setOrderId(assuranceOrderDomain.getDomainId());
        resultVO.setIsCancel(false);
        resultVO.setTaskId(currentTask.getTaskId());
        resultVO.setOrderAuditorList(auditorList);
        resultVO.setIsPrompt(false);
        return resultVO;
    }

    private void addWorkflowAuditorIds(String taskId, String domainOrderNo, List<Long> auditorInfoUserIds) {
        List<WorkflowAuditorIdInfoDO> collect = auditorInfoUserIds.stream().map(t -> {
            WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = new WorkflowAuditorIdInfoDO();
            workflowAuditorIdInfoDO.setAuditorId(String.valueOf(t));
            workflowAuditorIdInfoDO.setProject(ApsProjectEnum.GROUND_SERVICE.getProjectType());
            workflowAuditorIdInfoDO.setBusiness(WORKFLOW_BUSINESS_NAME);
            workflowAuditorIdInfoDO.setBusinessValue(domainOrderNo);
            workflowAuditorIdInfoDO.setTaskId(taskId);
            return workflowAuditorIdInfoDO;
        }).collect(Collectors.toList());

        workflowAuditorIdInfoService.saveOrUpdateRecords(collect);
    }

    private void deleteWorkflowAuditorIds(String domainOrderNo) {
        WorkflowAuditorIdInfoDO delWorkflowAuditorIdInfoDO=new WorkflowAuditorIdInfoDO();
        delWorkflowAuditorIdInfoDO.setProject(ApsProjectEnum.GROUND_SERVICE.getProjectType());
        delWorkflowAuditorIdInfoDO.setBusiness(WORKFLOW_BUSINESS_NAME);
        delWorkflowAuditorIdInfoDO.setBusinessValue(domainOrderNo);
        workflowAuditorIdInfoService.deleteWorkflowAuditorIds(delWorkflowAuditorIdInfoDO);
    }

    private AssuranceWorkflowAuditResultVO executeTask( String taskName,AssuranceOrderDomain assuranceOrderDomain,StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO,AssuranceWorkflowAuditCommand command, ExecuteFunction function){
        log.info("开始{}:{}",taskName,System.currentTimeMillis());
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + assuranceOrderDomain.getDomainOrderNo());
        try {
            boolean resLock = lock.tryLock();
            if (!resLock) {throw new BusinessException(GroundServicePubConstants.COMMON_ERROR,"获取分布式锁失败流程操作失败"); }
            return function.execute(assuranceOrderDomain,startSyncWorkflowInfoDTO,command);
        } finally {
            if (lock != null && lock.isLocked() && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        }
    }

    interface ExecuteFunction{
        AssuranceWorkflowAuditResultVO execute(AssuranceOrderDomain assuranceOrderDomain,
                                      StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO,
                                      AssuranceWorkflowAuditCommand command);
    }
}
