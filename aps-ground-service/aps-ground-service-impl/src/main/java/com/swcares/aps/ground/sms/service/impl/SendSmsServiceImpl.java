package com.swcares.aps.ground.sms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.component.com.platform.CurrentUserTenantTypeInfo;
import com.swcares.aps.component.com.platform.PlatformTool;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.ground.enums.AssuranceOrderState;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.sms.service.SendSmsService;
import com.swcares.aps.msg.model.enums.MessageLevelEnum;
import com.swcares.aps.usercenter.model.innermail.dto.InnerMailSendDto;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO;
import com.swcares.aps.usercenter.remote.api.innermail.MessageInnerMailApi;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvUserApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.components.msg.dto.InteriorMessageReceiveDTO;
import com.swcares.components.msg.dto.InteriorMessageReceiveUserDTO;
import com.swcares.components.msg.entity.InteriorMessageDepository;
import com.swcares.components.msg.enums.ReceiveModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @ClassName：SendSmsServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/10/24 10:34
 * @version： v1.0
 */
@Slf4j
@Service
public class SendSmsServiceImpl implements SendSmsService {

    @Autowired
    private MessageInnerMailApi messageInnerMailApi;

    @Autowired
    private PlatformTool platformTool;

    @Autowired
    ReaptvUserApi userApi;

    /**
     * eq:/web/compensationGuaranteeManagement/guaranteeFormManagement/detailGuaranteeForm?id=1851453188175429632&orderNo=3uxxxx&needAudit=N&taskId=&uuid=detail1851453188175429632
     */
    private final String ASSURANCE_PC_URL="/compensationGuaranteeManagement/guaranteeFormManagement/detailGuaranteeForm?id=%s&orderNo=%s&needAudit=%s&taskId=%s&uuid=%s";

    /**
     * eq:
     * /compensationPolicy/detail
     * 参数   id: orderServiceId,
     *                 orderNo,
     *                 orderId,
     */
    private final String ASSURANCE_H5_URL="/compensationPolicy/detail?id=%s&orderId=%s&orderNo=%s";

    @Override
    public void sendAssuranceInnerSMS(List<AuditorInfoDTO> orderAuditorList,
                                      AssuranceOrderDomain orderDomain,
                                      String taskId) {
        log.info("开始发送站内信审核保障单消息,{},{},{}", orderDomain.getDomainId(),taskId,JSONObject.toJSONString(orderAuditorList));
        try {
            InnerMailSendDto innerMail=createAssuranceInnerSMS(orderAuditorList,orderDomain,taskId);
            log.info("开始发送站内信审核保障单消息,{},{},{}",orderDomain.getDomainId(),taskId,JSONObject.toJSONString(innerMail));
            messageInnerMailApi.sendMessageAsUser(innerMail);
            log.info("完成发送站内信审核保障单消息,{},{}",orderDomain.getDomainId(),taskId);
        }catch (Exception e){
            log.error("发送站内信审核保障单消息失败,{},{}",orderDomain.getDomainId(),taskId,e);
        }

    }

    private InnerMailSendDto createAssuranceInnerSMS(List<AuditorInfoDTO> orderAuditorList,
                                                     AssuranceOrderDomain orderDomain,
                                                     String taskId) {

        List<InteriorMessageReceiveUserDTO> interiorMessageReceiveUserDTOS = orderAuditorList
                .stream()
                .map(orderAuditor -> {
                    InteriorMessageReceiveUserDTO receiveUser = new InteriorMessageReceiveUserDTO();
                    receiveUser.setRecipient(orderAuditor.getUcUserName());
                    receiveUser.setReceiveNum(orderAuditor.getReviewerPhone());
                    return receiveUser;
                }).collect(Collectors.toList());


        InteriorMessageReceiveDTO interiorMessageReceiveDTO = new InteriorMessageReceiveDTO();
        interiorMessageReceiveDTO.setReceiveList(interiorMessageReceiveUserDTOS);
        interiorMessageReceiveDTO.setReceiveMode(ReceiveModeEnum.INNER);

        InteriorMessageDepository interiorMessageDepository = createAssuranceInteriorMessage(orderDomain,taskId);

        InnerMailSendDto mailSendDto=new InnerMailSendDto();
        CurrentUserTenantTypeInfo currentUserTenantTypeInfo = platformTool.getCurrentUserTenantTypeInfo();
        mailSendDto.setSenderName(currentUserTenantTypeInfo.getCurrentOperatorUserName());
        if(StringUtils.equals(orderDomain.getDomainState(), AssuranceOrderState.AUDIT_ING.getCode())){
            try{
                BaseResult<ReaptvUserDetailVO> userApiById = userApi.getById(Long.valueOf(orderDomain.getOrderInfoDO().getCreatedId()));
                ReaptvUserDetailVO detailVO = userApiById.getData();
                mailSendDto.setSenderName(detailVO.getName());
            }catch (Exception e){
                log.error("userApi.getById",e);
                mailSendDto.setSenderName("system");
            }
        }

        mailSendDto.setReceiveList(Collections.singletonList(interiorMessageReceiveDTO));
        mailSendDto.setInteriorMessageDepository(interiorMessageDepository);
        return mailSendDto;
    }

    private InteriorMessageDepository createAssuranceInteriorMessage(AssuranceOrderDomain orderDomain,String taskId) {
        InteriorMessageDepository interiorMessageDepository=new InteriorMessageDepository();
        String needAudit= StringUtils.isNotBlank(taskId)?"Y":"N";
        interiorMessageDepository.setH5TargetUrl(String.format(ASSURANCE_H5_URL,orderDomain.getOrderServiceDOs().get(0).getId(),orderDomain.getDomainId(),orderDomain.getDomainOrderNo()));
        interiorMessageDepository.setTargetUrl(String.format(ASSURANCE_PC_URL,orderDomain.getDomainId(),orderDomain.getDomainOrderNo(),needAudit,taskId,"detail"+orderDomain.getDomainId()));
        interiorMessageDepository.setSystemCode("assurance_sms");
        interiorMessageDepository.setBusinessType("补偿保障单审核");
        interiorMessageDepository.setMsgContent("补偿保障单单(单号:【"+orderDomain.getDomainOrderNo()+"】)已被审核");
        interiorMessageDepository.setMsgTitle("补偿保障单审核通知");
        interiorMessageDepository.setSendType(0);
        interiorMessageDepository.setMasterId(Long.parseLong(orderDomain.getDomainId()));
        interiorMessageDepository.setUrgency(String.valueOf(MessageLevelEnum.NORMAL.getKey()));
        if(StringUtils.equals(orderDomain.getDomainState(), AssuranceOrderState.AUDIT_BACK.getCode())){
            String msgContent = String.format("您发起的【%s】【%s】 内的补偿保障单(单号:【%s】)被驳回，请点击前往处理。",
                    DateUtils.formatDate(orderDomain.getOrderInfoDO().getFlightDate()),
                    orderDomain.getOrderInfoDO().getFlightNo(),
                    orderDomain.getDomainOrderNo());
            interiorMessageDepository.setMsgContent(msgContent);
            interiorMessageDepository.setUrgency(String.valueOf(MessageLevelEnum.URGENCY.getKey()));
            interiorMessageDepository.setMsgTitle("补偿保障单审核驳回通知");
        }
        if(StringUtils.equals(orderDomain.getDomainState(), AssuranceOrderState.AUDIT_ING.getCode())){
            String msgContent = String.format("【%s】【%s】 发起的补偿保障单(单号:【%s】)正在等待您的审核，请点击前往审核。",
                    DateUtils.formatDate(orderDomain.getOrderInfoDO().getFlightDate()),
                    orderDomain.getOrderInfoDO().getFlightNo(),
                    orderDomain.getDomainOrderNo());
            interiorMessageDepository.setMsgContent(msgContent);
            interiorMessageDepository.setUrgency(String.valueOf(MessageLevelEnum.URGENCY.getKey()));
            interiorMessageDepository.setMsgTitle("补偿保障单待审核通知");
        }
        if(StringUtils.equalsAny(orderDomain.getDomainState(),AssuranceOrderState.AUDIT_REJECT.getCode()
                ,AssuranceOrderState.SERVICE_ING.getCode())){
            String auditResult="审核不通过";
            interiorMessageDepository.setUrgency(String.valueOf(MessageLevelEnum.URGENCY.getKey()));
            if(!StringUtils.equals(orderDomain.getDomainState(),AssuranceOrderState.AUDIT_REJECT.getCode())){
                interiorMessageDepository.setUrgency(String.valueOf(MessageLevelEnum.NORMAL.getKey()));
                auditResult="审核通过";
            }
            String msgContent = String.format("您发起的【%s】【%s】 内的补偿保障单(单号:【%s】)已完成审核，审核结果为【%s】，请注意查看。",
                    DateUtils.formatDate(orderDomain.getOrderInfoDO().getFlightDate()),
                    orderDomain.getOrderInfoDO().getFlightNo(),
                    orderDomain.getDomainOrderNo(),
                    auditResult);
            interiorMessageDepository.setMsgContent(msgContent);

            interiorMessageDepository.setMsgTitle("补偿保障单审核结果通知");
        }
        return interiorMessageDepository;
    }
}
