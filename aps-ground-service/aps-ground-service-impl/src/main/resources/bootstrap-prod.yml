spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.1.211:8848,192.168.1.212:8848,192.168.1.213:8848
        namespace: travelsky-pro-airport
        username: nacos
        password: Nc@ApsPro!2025
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        refresh-enabled: true
        file-extension: yml
        namespace: travelsky-pro-airport
        group: prod
        username: nacos
        password: Nc@ApsPro!2025
        shared-configs:
          - data-id: application-bash-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
            group: prod
logging:
  config: classpath:logback.xml
  level:
    com.swcares: info
    org.springframework: warn
