package com.swcares.coordinate.api.controller;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.remote.api.privilege.BusinessDataSyncApi;
import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.CoordinateDispatcherRequestDTO;
import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.CoordinateDispatcherResponseDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessDataUploadDTO;
import com.swcares.aps.cpe.coordinate.util.CoordinateApiUtil;
import com.swcares.aps.ground.GroundDataApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.feign.FeignTokenContext;
import com.swcares.coordinate.api.config.CoordinateApiErrors;
import com.swcares.coordinate.api.config.CoordinateConfig;
import com.swcares.coordinate.api.config.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> Wang Yi
 * @Classname BusinessPrivilegeCoordinateController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/7/14 12:01
 * @Version 1.0
 */
@RestController
@Slf4j
public class BusinessDataCoordinateController extends BaseController {
    @Autowired
    CoordinateConfig coordinateConfig;
    @Autowired
    BusinessDataSyncApi businessDataSyncApi;
    @Autowired
    GroundDataApi groundDataApi;

    @PostMapping(value = "/coordinate/businessData")
    public BaseResult<CoordinateDispatcherResponseDTO> processBusinessConfigureCoordinateRequest(
            @RequestBody CoordinateDispatcherRequestDTO requestDTO){

        log.info("【业务数据同步】收到请求: {}", JSONUtil.toJsonStr(requestDTO));
        String publicKey = coordinateConfig.getKeys().get(requestDTO.getAppSecretKeyVersion());
        if (publicKey == null){
            //抛出异常
            log.error("找不到指定版本{}的密钥", requestDTO.getAppSecretKeyVersion());
            new BusinessException(CoordinateApiErrors.CANNOT_FIND_APPSECRETKEY, requestDTO.getAppSecretKeyVersion());
        }

        //验证请求的有效性
        boolean isValid = CoordinateApiUtil.validate(requestDTO, publicKey, coordinateConfig.getExpireDurationInSeconds());

        if (!isValid){
            log.info("【业务数据同步】请求内容无效，可能是签名错误或者已经过期");
            throw new BusinessException(CoordinateApiErrors.INVALID_REQUEST);
        }

        //读取请求数据
        String coordinateData = CoordinateApiUtil.getDecryptedData(requestDTO, publicKey);
        BusinessDataUploadDTO businessDataDTO = JSONUtil.toBean(coordinateData, BusinessDataUploadDTO.class);

        log.info("【业务数据同步】业务数据: {}", JSONUtil.toJsonStr(businessDataDTO));
        FeignTokenContext.setToken(TokenUtils.getToken());
        if("SERVICE_SUPPORT".equals(businessDataDTO.getBusinessType())){
            groundDataApi.syncData(businessDataDTO);
        }else {
            businessDataSyncApi.syncAirportBusinessData(businessDataDTO);
        }
        CoordinateDispatcherResponseDTO responseDTO = new CoordinateDispatcherResponseDTO();
        return ok(responseDTO);
    }
}
