package com.swcares.aps.compensation.remote.api.privilege;

import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.CoordinateDispatcherRequestDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessConfigureDataCoordinateDTO;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeApi
 * @Description 航司端同步机场端 的业务数据
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/14 13:18
 * @Version 1.0
 */
@FeignClient(name= "compensation-impl", path = "/api/compensation-impl/configureDataSync", contextId ="ConfigureDataSyncApi" )
public interface ConfigureDataSyncApi {

    @PostMapping("/syncConfigureData")
    BaseResult<Object> syncConfigureData(@RequestBody BusinessConfigureDataCoordinateDTO businessConfigureDTO);

}
