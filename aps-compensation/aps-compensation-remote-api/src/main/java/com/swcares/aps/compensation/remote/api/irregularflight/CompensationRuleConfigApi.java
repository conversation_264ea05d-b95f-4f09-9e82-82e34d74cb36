package com.swcares.aps.compensation.remote.api.irregularflight;

import com.swcares.aps.compensation.model.rools.dto.CompensationRuleFactoryDTO;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Map;

@FeignClient(name= "compensation-impl", path = "/api/compensation-impl", contextId ="CompensationRuleConfigApi" )
public interface CompensationRuleConfigApi {

    @GetMapping("/compensation/rule/getAll")
    BaseResult<List<Map<String, CompensationRuleFactoryDTO>>> getAll();

    @PostMapping("/compensation/rule/save")
    BaseResult<CompensationRuleFactoryDTO> save(@RequestBody CompensationRuleFactoryDTO request);
}
