package com.swcares.aps.compensation.remote.api.privilege;

import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeRequestDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeApi
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/14 13:18
 * @Version 1.0
 */
@FeignClient(name= "compensation-impl", path = "/api/compensation-impl/businessPrivilege", contextId ="businessPrivilegeApi" )
public interface BusinessPrivilegeApi {
    @PostMapping("/v1/create")
    public BaseResult<Boolean> createAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto);

    @PostMapping("/v1/update")
    public BaseResult<Boolean> updateAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto);

    @PostMapping("/v1/list")
    public PagedResult<List<BusinessPrivilegeDTO>> list(@RequestBody SearchBusinessPrivilegeDTO searchCriteria);

    @RequestMapping("/v1/detail")
    public BaseResult<BusinessPrivilegeDTO> getDetail(@RequestParam(value = "id") Long id);

    @RequestMapping("/v1/detailByAirportCode")
    public BaseResult<BusinessPrivilegeDTO> getDetailByAirportCode(@RequestParam(value = "airportCode") String airportCode);

    @PostMapping("/v1/enable")
    public BaseResult<Boolean> enableBusinessPrivileges(@RequestBody List<Long> ids);

    @PostMapping("/v1/disable")
    public BaseResult<Boolean> disableBusinessPrivileges(@RequestBody List<Long> ids);

    @PostMapping("/v1/coordinate")
    public BaseResult<Boolean> coordinateBusinessPrivileges(@RequestBody BusinessPrivilegeDTO businessPrivilegeDTO);
}
