package com.swcares.aps.compensation.remote.api.privilege;

import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.BasicDataDispatchDTO;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname CoordinateCustomerAPI
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/27 14:10
 * @Version 1.0
 */
@FeignClient(name = "compensation-impl", path = "/api/compensation-impl", contextId = "coordinateCustomerApi")
public interface CoordinateCustomerAPI {

    @PostMapping("/coordinate/customer/saveOrUpdate")
    public BaseResult<Boolean> saveOrUpdateCustomer(@RequestBody BasicDataDispatchDTO dispatchDTO);


}
