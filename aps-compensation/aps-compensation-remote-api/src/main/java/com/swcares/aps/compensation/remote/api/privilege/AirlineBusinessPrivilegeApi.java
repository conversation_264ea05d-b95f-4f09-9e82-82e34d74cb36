package com.swcares.aps.compensation.remote.api.privilege;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname AirlineBusinessPrivilegeApi
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/9/13 15:27
 * @Version 1.0
 */
@FeignClient(name= "compensation-impl", path = "/api/compensation-impl/airline/businessPrivilege", contextId ="airlineBusinessPrivilegeApi" )
public interface AirlineBusinessPrivilegeApi {
    @ApiOperation(value = "机场端获取事故单补偿单的创建授权")
    @GetMapping("/verify/{code}")
    public BaseResult<Boolean> verify(@RequestParam("code") String typeCode);

    @ApiOperation(value = "机场端获取授权航司二字码")
    @GetMapping("/getBelongAirline/{code}")
    public BaseResult<List<BelongAirlineDTO>> getBelongAirline(@RequestParam("code") String typeCode);


    @ApiOperation(value="分页查询业务授权列表")
    @PostMapping("/v1/list")
    public PagedResult<List<BusinessPrivilegeDTO>> list(@RequestBody SearchBusinessPrivilegeDTO searchCriteria);

    @ApiOperation(value="查询指定的业务授权的详情")
    @GetMapping("/v1/detail")
    public BaseResult<BusinessPrivilegeDTO> getDetail(@RequestParam(value = "id") Long id);

}
