package com.swcares.aps.compensation.remote.api.irregularflight;

import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.assist.dto.CheckInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportDetailVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.*;
import com.swcares.aps.compensation.model.baggage.luggage.vo.*;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.vo.*;
import com.swcares.aps.compensation.model.dataconfig.dto.CabinConfigPageDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.aps.compensation.model.replace.dto.*;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * ClassName：irregularflight <br>
 * Description：不正常航班事故单提供接口 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月15日 10:44 <br>
 * @version v1.0 <br>
 */
@FeignClient(name= "compensation-impl", path = "/api/compensation-impl", contextId ="compensationInfoApi" )
public interface CompensationInfoApi {

    /**
     * @title delete
     * @description 根据id删除记录
     * <AUTHOR>
     * @date 2022/2/23 14:51
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @DeleteMapping("/accident/info/delete/{id}")
    BaseResult<Object> delete(@PathVariable Long id);

    //修改
    /**
     * @title update
     * @description 修改记录
     * <AUTHOR>
     * @date 2022/2/23 14:54
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PutMapping("/accident/info/update")
    BaseResult<Object> update(@RequestBody FlightAccidentInfoDTO dto);

    /**
     * @title findById
     * @description 根据事故单id查事故单信息
     * <AUTHOR>
     * @date 2022/2/23 14:56
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO>
     */
    @GetMapping("/accident/info/find/{id}")
    BaseResult<FlightAccidentInfoDetailsVO> findById(@PathVariable Long id);

    /**
     * @title toVoid
     * @description 通过id作废记录
     * <AUTHOR>
     * @date 2022/2/23 14:57
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PutMapping("/accident/info/toVoid/{id}")
    BaseResult<Object> toVoid(@PathVariable Long id);

    /**
     * @title findCompensationOrderById
     * @description 根据事故单id查赔偿单列表
     * <AUTHOR>
     * @date 2022/2/23 14:57
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO>>
     */
    @GetMapping("/accident/info/findCompensationOrder/{id}")
    BaseResult<List<AccidentCompensationOrderVO>> findCompensationOrderById(@PathVariable Long id);

    /**
     * Title： page <br>
     * Description： 分页查询不正常航班事故单 <br>
     * author：傅欣荣 <br>
     * date：2021/10/15 11:07 <br>
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO>> <br>
     */
    @PostMapping("/accident/info/page")
    PagedResult<List<FlightAccidentInfoVO>> page(@RequestBody FlightAccidentInfoPagedDTO dto);

    /**
     * Title：save <br>
     * Description：保存事故单 <br>
     * author：王磊 <br>
     * date：2021/10/27 20:03 <br>
     *
     * @param dto <br>
     * @return <br>
     */
    @PostMapping("/accident/info/save")
    BaseResult<Object> save(@Validated @RequestBody FlightAccidentInfoDTO dto);

    /**
     * Title：findFilghtExistsAccident <br>
     * Description：通过航班号和时间来获取航班下是否存在事故单 <br>
     * author：王磊 <br>
     * date：2021/10/27 20:26 <br>
     *
     * @param date
     * @param flightNo <br>flightNo,date,id
     * @return <br>
     */
    @GetMapping("/accident/info/findFilghtExistsAccident")
    BaseResult<List<FlightExistsAccidentVO>> findFilghtExistsAccident(@ApiParam(value = "航班号", required = true) @RequestParam(value = "flightNo") String flightNo,
                                                                      @ApiParam(value = "航班日期", required = true) @RequestParam(value = "date") String date,
                                                                      @ApiParam(value = "主键") @RequestParam(value = "id", required = false) Long id);

    /**
     * Title：findChoiceSegment <br>
     * Description： 通过事故单号查已选航段数据<br>
     * author：傅欣荣 <br>
     * date：2021/11/18 10:07 <br>
     *
     * @param id
     * @return
     */
    @GetMapping("/accident/info/findChoiceSegment")
    BaseResult<List<Map<String, Object>>> findChoiceSegment(@RequestParam("id") Long id);

    /**
     * Title： findCompensationSubTypeKey<br>
     * Description：赔偿类型key-获取赔偿子类型数据字典key<br>
     * author：傅欣荣 <br>
     * date：2021/11/18 13:40 <br>
     * @param key <br>
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.String> <br>
     */
    @GetMapping("/accident/info/findCompensationSubTypeKey/{key}")
    BaseResult<String> findCompensationSubTypeKey(@PathVariable String key);
    //-----------------------赔偿单对外接口-------------------------------------------

    /**
     * @title pages
     * @description 赔偿单(带审核)分页列表
     * <AUTHOR>
     * @date 2021/11/24 11:14
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO>>
     */
    @PostMapping("/compensation/order/info/pages")
    PagedResult<List<CompensationOrderInfoExamineVO>> pages(@RequestBody CompensationOrderInfoPagedDTO dto);

    /**
     * @title orderDelete
     * @description 赔偿单操作-删除（草稿状态下）
     * <AUTHOR>
     * @date 2022/2/23 15:08
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @DeleteMapping("/compensation/order/info/delete/{id}")
    BaseResult<Object> orderDelete(@PathVariable Long id);


    @GetMapping("/complaint/compensation/detail/{id}")
    BaseResult<GeneralCompensationOrderInfoDetailVO> detail(@PathVariable(value = "id") Long id);
    /**
     * Title：save <br>
     * Description：保存赔偿单 <br>
     * author：王磊 <br>
     * date：2021/11/2 10:01 <br>
     *
     * @param dto <br>
     * @return <br>
     */
    @PostMapping("/compensation/order/info/save")
    BaseResult<Object> save(@Validated @RequestBody CompensationSyntheticalSaveDTO dto);

    /**
     * @title findOrderDetails
     * @description 赔偿单详情- 赔偿信息查询
     * <AUTHOR>
     * @date 2022/2/23 15:08
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO>
     */
    @GetMapping("/compensation/order/info/findOrderDetails/{id}")
    BaseResult<CompensationOrderDetailsVO> findOrderDetails(@PathVariable Long id);

    /**
     * @title getOrderEditEchoById
     * @description 赔偿单编辑查询详情- 赔偿信息查询
     * <AUTHOR>
     * @date 2022/2/23 15:09
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderEditEchoVO>
     */
    @GetMapping("/compensation/order/info/findOrderEchoInfo/{id}")
    BaseResult<CompensationOrderEditEchoVO> getOrderEditEchoById(@PathVariable Long id);

    /**
     * @title findChoicePax
     * @description 赔偿单详情- 赔偿单旅客列表信息
     * <AUTHOR>
     * @date 2022/2/23 15:11
     * @param paxFrozenDTO
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO>>
     */
    @PostMapping("/compensation/pax/info/findChoicePax")
    BaseResult<List<CompensationChoicePaxVO>> findChoicePax(@RequestBody CompensationPaxFrozenDTO paxFrozenDTO);

    /**
     * @title getPaxOrderInfo
     * @description 通过paxId、flightNo、flightDate查询旅客赔偿记录
     * <AUTHOR>
     * @date 2022/2/23 15:12
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO>>
     */
    @PostMapping("/compensation/pax/info/findPaxOrderInfo")
    BaseResult<List<PaxCompensationCountVO>> getPaxOrderInfo(@RequestBody PaxOrderInfoQueryDTO dto);

    /**
     * Title：PaxReceiveRecordVO <br>
     * Description： 通过旅客PaxID-查领取记录<br>
     * author：傅欣荣 <br>
     * date：2021/11/29 17:11 <br>
     * @param orderId <br>
     * @param paxId <br>
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO>> <br>
     */
    @GetMapping("/compensation/order/info/findPaxReceiveRecord")
    BaseResult<List<PaxReceiveRecordVO>> findPaxReceiveRecord(@RequestParam("orderId") Long orderId,@RequestParam("paxId") String paxId);

    /**
     * Title：findOrderRule <br>
     * Description： 根据补偿单id查补偿标准信息<br>
     * author：傅欣荣 <br>
     * date：2021/11/2 14:18 <br>
     * @param id <br>
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationStandardVO>> <br>
     */
    @GetMapping("/compensation/order/info/findOrderRule/{id}")
    BaseResult<List<CompensationStandardVO>> findOrderRule(@PathVariable Long id);

    /**
     * Title： freezeOrderPax<br>
     * Description： 冻结|| 解冻<br>
     * author：傅欣荣 <br>
     * date：2021/11/18 10:07 <br>
     * @param freezeOrderPaxDTO <br>
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object> <br>
     */
    @PostMapping("/compensation/pax/info/freezeOrderPax")
    BaseResult<Object> freezeOrderPax(@RequestBody FreezeOrderPaxDTO freezeOrderPaxDTO);


//    /**
//    * @title findSelectedPax
//    * @description 已选择旅客多条件查询
//    * @param choosePaxSearchDTO 已选择旅客查询条件封装对象
//    * <AUTHOR>
//    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
//    * @date 2021/11/11 10:20
//    */
//    @PostMapping("/compensation/pax/info/findSelectedPax")
//    BaseResult<Object> findSelectedPax(@RequestBody ChoosePaxSearchDTO choosePaxSearchDTO) ;

    /**
     * @title findSelectedPax
     * @description 根据赔偿单id查已选择旅客
     * <AUTHOR>
     * @date 2021/11/11 16:15
     * @param orderId
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/compensation/pax/info/findSelectedPax")
    BaseResult<Object> findSelectedPax(@RequestParam Long orderId);

    /**
     * @title takeEffect
     * @description 通过ID进行补偿单发放操作
     * <AUTHOR>
     * @date 2021/11/11 10:23
     * @param id 补偿单id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PutMapping("/compensation/order/info/takeEffect/{id}")
    BaseResult<Object> takeEffect(@PathVariable Long id);

    /**
     * @title close
     * @description 通过ID进行补偿单关闭操作
     * <AUTHOR>
     * @date 2021/11/11 10:23
     * @param id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PutMapping("/compensation/order/info/close/{id}")
    BaseResult<Object> close(@PathVariable Long id);

    /**
     * Title：updatePaxReceiveInfo <br>
     * Description：申领单保存时修改旅客申领信息 <br>
     * author：王磊 <br>
     * date：2021/12/2 15:53 <br>
     * @param paxIds
     * @param receiveChannel
     * @param receiveWay
     * @param receiveStatus <br>
     * @return <br>
     */
    @PostMapping("/compensation/pax/info/updatePaxReceiveInfo")
    BaseResult<Object> updatePaxReceiveInfo(@RequestParam List<Long> paxIds, @RequestParam String receiveChannel, @RequestParam String receiveWay, @RequestParam String receiveStatus);

    /**
     * @title getSameTypeOrders
     * @description 通过航班日期，航班号查询同类型赔偿单
     * <AUTHOR>
     * @date 2021/12/13 13:32
     * @param flightDate
     * @param flightNo
     * @param accidentType
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/compensation/order/info/findSameTypeOrders")
    BaseResult<Object> getSameTypeOrders(@RequestParam("flightDate") String flightDate, @RequestParam("flightNo")String flightNo, @RequestParam("accidentType")String accidentType,@RequestParam("orderId")Long orderId);
    //-----------赔偿单审核-----------------------------------------------------
    @PostMapping("/compensation/audit/info/operation")
    BaseResult<Object> auditOperation(@RequestBody AuditProcessorDTO dto);
    /**
     * @title findReviewer
     * @description 查询可审核人员
     * @param orgId 部门id
     * @param userInfo 审核人信息
     * @param taskId 下一审核节点id
     * @param orderId 赔偿单id
     * <AUTHOR>
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     * @date 2021/11/24 15:58
     */
    @GetMapping("/compensation/audit/info/findReviewer")
    BaseResult<Object> findReviewer(@RequestParam("orgId") Long orgId,@RequestParam("userInfo") String userInfo,@RequestParam("taskId") String taskId,@RequestParam("orderId") Long orderId);

    /**
     * @title saveReviewer
     * @description 选择审核人
     * <AUTHOR>
     * @date 2021/11/26 14:28
     * @param dto 审核人选择封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/audit/info/saveReviewer")
    BaseResult<Object> saveReviewer(@RequestBody CompensationAuditInfoDTO dto);

    /**
     * @title findAuditRecord
     * @description 查看审核记录
     * <AUTHOR>
     * @date 2021/11/24 16:00
     * @param orderId 赔偿单id
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/compensation/audit/info/findAuditRecord")
    BaseResult<Object> findAuditRecord(@RequestParam("orderId")Long orderId,@RequestParam("orderNo")String orderNo);

    /**
     * @title findReviewers
     * @description 用于cpc获取审核人id
     * <AUTHOR>
     * @date 2021/12/21 11:06
     * @param positions
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/audit/info/cpcFindReviewerIds")
    BaseResult<Object> findReviewerIds(@RequestBody List<String> positions);

    /**
     * @title getReviewer
     * @description 用于cpc查询审核人详情
     * <AUTHOR>
     * @date 2021/12/21 11:06
     * @param deptIds
     * @param userIds
     * @param roleIds
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/compensation/audit/info/cpcFindReviewer")
    BaseResult<Object> getReviewer(@RequestParam(value = "部门id") String deptIds, @RequestParam(value = "审核人id") String userIds,@RequestParam(value = "角色id") String roleIds);

    /**
     * @title autoAuditUpd
     * @description 自动审核-修改业务表状态
     * <AUTHOR>
     * @date 2022/01/12 16:29
     * @param busiKey
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/audit/info/autoAuditUpd")
    BaseResult<Object> autoAuditUpd(@RequestBody String busiKey);

    /**
     * @title getReplaceRule
     * @description 获取当前配置的代领人所有的领取规则
     * <AUTHOR>
     * @date 2022/2/23 15:19
     * @param
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO>
     */
    @GetMapping("/compensation/replace/rule/getRule")
    BaseResult<ReplaceRuleDTO> getReplaceRule();


    /**
     * @title saveReplaceBaseRule
     * @description 保存代领人审核规则
     * <AUTHOR>
     * @date 2022/01/10 13:28
     * @param dto 代领人审核规则封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/replace/rule/saveBaseRule")
    BaseResult<Object> saveReplaceBaseRule(@RequestBody ReplaceBaseRuleDTO dto);

    /**
     * @title saveReplacePayWaitPeriodRule
     * @description 保存代领人审核支付等待期规则
     * <AUTHOR>
     * @date 2022/01/11 16:28
     * @param dto 代领人审核规则支付等待期封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/replace/rule/savePayWaitPeriod")
    BaseResult<Object> saveReplacePayWaitPeriodRule(@RequestBody ReplacePayPeriodRuleDTO dto);


    /**
     * @title getAllReplaceRejectReason
     * @description 获取所有的代领审核拒绝原由
     * <AUTHOR>
     * @date 2022/2/23 15:21
     * @param
     * @return com.swcares.baseframe.common.base.BaseResult<java.util.List<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO>>
     */
    @GetMapping("/compensation/replace/rejectReason/list")
    BaseResult<List<ReplaceRejectReasonDTO>> getAllReplaceRejectReason();

    /**
     * @title replaceRejectReasonPage
     * @description 分页查询代领审核拒绝原由
     * <AUTHOR>
     * @date 2022/2/23 15:22
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO>>
     */
    @PostMapping("/compensation/replace/rejectReason/pages")
    BaseResult<Map<String, RuleManageDTO>> replaceRejectReasonPage();


    /**
     * @title saveReplaceRejectReason
     * @description 保存代领审核拒绝原由
     * <AUTHOR>
     * @date 2022/01/12
     * @param dto 代领代领审核拒绝原由封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/replace/rejectReason/create")
    BaseResult<Object> saveReplaceRejectReason(@RequestBody ReplaceRejectReasonDTO dto);


    /**
     * @title updateReplaceRejectReason
     * @description 修改代领审核拒绝原由
     * @date 2022/01/12
     * <AUTHOR>
     * @param dto 代领代领审核拒绝原由封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/replace/rejectReason/update")
    BaseResult<Object> updateReplaceRejectReason(@RequestBody ReplaceRejectReasonDTO dto);


    /**
     * @title updateReplaceRejectReason
     * @description 刪除代领审核拒绝原由
     * <AUTHOR>
     * @date 2022/01/12
     * @param dto 代领代领审核拒绝原由封装对象
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/compensation/replace/rejectReason/delete")
    BaseResult<Object> deleteReplaceRejectReason(@RequestBody ReplaceConfigDeleteDTO dto);
    //------------------------现金协助----------------------------------
    /**
     * @title delete
     * @description 储存申领单
     * <AUTHOR>
     * @date 2022/1/24 9:16
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/assist/apply/saveBehalfApply")
    BaseResult<Object> saveBehalfApply(@RequestBody ApplyBehalfOrderDTO dto);

    /**
     * @title findPaxCompensation
     * @description 查询具体某个旅客的可申领信息
     * <AUTHOR>
     * @date 2022/1/24 9:19
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/assist/pax/compensation/findPaxCompensation")
    BaseResult<CompensationInfoVO> findPaxCompensation(@RequestBody AuthPaxDTO dto);

    /**
     * @title checked
     * @description 信息校验
     * <AUTHOR>
     * @date 2022/1/24 9:20
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @PostMapping("/assist/pax/compensation/check")
    BaseResult<Object> checked(@RequestBody CheckInfoDTO dto);

    /**
     * @title findPaxList
     * @description 航班信息查询满足条件的旅客列表
     * <AUTHOR>
     * @date 2022/1/24 9:20
     * @param flightNo
     * @param flightDate
     * @param paxName
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/assist/pax/compensation/findPaxList")
    BaseResult<Object> findPaxList(@RequestParam String flightNo,@RequestParam String flightDate,@RequestParam String paxName);

    /**
     * @title sendSMSCode
     * @description 发送短信验证码
     * <AUTHOR>
     * @date 2022/1/24 9:24
     * @param phoneNum
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/assist/sms/sendSMSCode")
    BaseResult<Object> sendSMSCode(@RequestParam String phoneNum);

    /**
     * @title verificationSMSCode
     * @description 验证短信验证码
     * <AUTHOR>
     * @date 2022/1/24 9:24
     * @param phoneNum
     * @param authCode
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    @GetMapping("/assist/sms/verificationSMSCode")
    BaseResult<Object> verificationSMSCode(@RequestParam String phoneNum,@RequestParam String authCode);

    /**
    * @title findPaxAmount
    * @description 通过手机号查询半年内协助领取人数
    * <AUTHOR>
    * @date 2022/2/15 13:19
    * @param phone
     * @param idNo
     * @param paxName
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/assist/apply/findPaxAmount")
    BaseResult<Object> findPaxAmount(@RequestParam(value = "phone") String phone,@RequestParam(value = "idNo") String idNo,@RequestParam(value = "paxName") String paxName);
    //<---------------------------箱包管理---------------------------->
    /**
    * @title findSegment
    * @description 根据三字码获取航站信息
    * <AUTHOR>
    * @date 2022/2/11 9:28
    * @param code
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/luggage/findSegment")
    BaseResult<Object> findSegment(@RequestParam(value = "code") String code);

    /**
    * @title findLuggageList
    * @description 箱包列表查询
    * <AUTHOR>
    * @date 2022/2/11 9:28
    * @param dto
    * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO>>
    */
    @PostMapping("/luggage/findLuggage")
    PagedResult<List<LuggageInfoVO>> findLuggageList(@RequestBody LuggageInfoPageDTO dto);

    /**
    * @title saveLuggage
    * @description 新建箱包信息
    * <AUTHOR>
    * @date 2022/2/11 9:29
    * @param dto
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @PostMapping("/luggage/save")
    BaseResult<Object> saveLuggage(@RequestBody LuggageInfoDTO dto);

    /**
    * @title removeLuggage
    * @description 根据箱包id逻辑删除箱包信息
    * <AUTHOR>
    * @date 2022/2/11 9:29
    * @param id
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @DeleteMapping("/luggage/remove")
    BaseResult<Object> removeLuggage(@RequestParam(value = "id") Long id);

    /**
    * @title updateLuggage
    * @description 根据箱包id修改箱包信息
    * <AUTHOR>
    * @date 2022/2/11 9:29
    * @param dto
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @PutMapping("/luggage/update")
    BaseResult<Object> updateLuggage(@RequestBody LuggageInfoDTO dto);

    //谭睿
    /**
    * @title findLuggageConsumptions
    * @description 根据箱包id查询该箱包消耗明细列表
    * <AUTHOR>
    * @date 2022/2/11 9:30
    * @param dto
    * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO>>
    */
    @PostMapping("/luggage/consumption/page")
    PagedResult<List<LuggageConsumptionDetailVO>> findLuggageConsumptions(LuggageConsumptionDetailPageDTO dto);

    //唐康
    /**
    * @title findLuggageStockDetailed
    * @description 根据箱包id查询该箱包库存明细列表
    * <AUTHOR>
    * @date 2022/2/11 9:32
    * @param dto
    * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO>>
    */
    @PostMapping("/luggage/stock/detailed")
    PagedResult<List<FindLuggageStockInfoVO>> findLuggageStockDetailed(@RequestBody FindLuggageStockInfoDTO dto);

    /**
    * @title updateLuggageStock
    * @description 箱包库存管理操作-增、减
    * <AUTHOR>
    * @date 2022/2/11 9:36
    * @param dto
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @PutMapping("/luggage/stock/update")
    BaseResult<Object> updateLuggageStock(@RequestBody LuggageStockInfoDTO dto);


    /**
     * Title：findById <br>
     * Description：通过ID查询旅客信息 <br>
     * author：王磊 <br>
     * date：2022/2/14 10:34 <br>
     * @param paxId <br>
     * @return <br>
     */
    @GetMapping("/compensation/pax/info/findById")
    CompensationPaxInfoDO findPaxInfoById(@RequestParam Long paxId);

    //-----------异常行李-----------------------------------------------------
    /**
    * @title baggageSave
    * @description 异常行李事故单新增
    * <AUTHOR>
    * @date 2022/3/10 15:38
    * @param dto
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @PostMapping("/baggage/accident/save")
    BaseResult<Object> baggageSave(@RequestBody BaggageAccidentInfoDTO dto);

    /**
    * @title edit
    * @description 异常行李事故单编辑回显
    * <AUTHOR>
    * @date 2022/3/10 15:39
    * @param accidentId
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/baggage/accident/edit")
    BaseResult<Object> edit(@RequestParam String accidentId);

    /**
    * @title saveExpress
    * @description 赔偿单快递信息新增
    * <AUTHOR>
    * @date 2022/3/10 15:39
    * @param dto
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @PostMapping("/baggage/accident/express/save")
    BaseResult<Object> saveExpress(@RequestBody CompensationExpressInfoDTO dto);


    /**
     * @title findBaggageAccidentList
     * @description 箱包事故单信息列表查询
     * <AUTHOR>
     * @date 2022/3/4 13:15
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO>>
     */
    @PostMapping("/baggage/accident/findBaggageAccidentList")
    PagedResult<List<FindBaggageVO>> findBaggageAccidentList(@RequestBody FindBaggageDTO dto);

    /**
     * @title baggageAccidentDetailInfo
     * @description 箱包事故单详情信息
     * <AUTHOR>
     * @date 2022/3/8 14:03
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVO>
     */
    @PostMapping("/baggage/accident/baggageAccidentDetailInfo")
    BaseResult<BaggageDetailFinalVO> baggageAccidentDetailInfo(@RequestBody FindBaggageDetailDTO dto);

    /**
     * @title getCompensationDataConfigByTypes
     * @description 查询配置
     * <AUTHOR>
     * @date 2022/3/23 15:53
     * @param types
     * @return
     */
    @GetMapping("/compensation/config/getByTypes")
    BaseResult<List<CompensationConfigVO>> getCompensationDataConfigByTypes(@RequestBody List<String> types);

    /**
    * @title removeExpress
    * @description 根据id进行赔偿单快递信息删除
    * <AUTHOR>
    * @date 2022/3/10 15:40
    * @param expressId
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @DeleteMapping("/baggage/accident/express/remove")
    BaseResult<Object> removeExpress(@RequestParam String expressId);

    /**
    * @title toLost
    * @description 少收转丢失
    * @param accidentId
    * <AUTHOR>
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    * @date 2022/3/7 13:48
    */
    @GetMapping("/baggage/accident/toLost")
    BaseResult<Object> toLost(@RequestParam String accidentId);

    /**
    * @title toMatch
    * @description 匹配多/少收查询
    * <AUTHOR>
    * @date 2022/3/9 17:26
    * @param accidentId 当前事故单id
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/baggage/accident/toMatch")
    BaseResult<Object> toMatch(@RequestParam(value = "accidentId") String accidentId);

    /**
    * @title saveMatch
    * @description 匹配绑定多/少收
    * <AUTHOR>
    * @date 2022/3/9 17:25
    * @param accidentId  当前事故单id
     * @param accidentNo  被绑定事故单号
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/baggage/accident/saveMatch")
    BaseResult<Object> saveMatch(@RequestParam(value = "accidentId")String accidentId, @RequestParam(value = "accidentNo")String accidentNo);

    /**
    * @title relieveMatch
    * @description 解除匹配绑定多/少收
    * <AUTHOR>
    * @date 2022/3/10 15:45
    * @param accidentId
     * @param accidentNo
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/baggage/accident/relieveMatch")
    BaseResult<Object> relieveMatch(@RequestParam(value = "accidentId")String accidentId, @RequestParam(value = "accidentNo")String accidentNo);

    /**
    * @title changeStatus
    * @description 异常行李事故单状态扭转
    * <AUTHOR>
    * @date 2022/3/15 16:25
    * @param accidentId
     * @param targetStatus
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    @GetMapping("/baggage/accident/changeStatus")
    BaseResult<Object> changeStatus(@RequestParam(value = "accidentId")String accidentId,@RequestParam(value = "targetStatus")String targetStatus);

    /**
     * @title deleteById
     * @description 通过id删除异常行李事故单
     * <AUTHOR>
     * @date 2022/3/15 19:22
     * @param id
     * @return BaseResult<Object>
     */
    @DeleteMapping("/baggage/accident/delete/{id}")
    BaseResult<Object> deleteById(@PathVariable Long id);

    /**
     * @title getBaggageAccidentNumber
     * @description 获取已经生成的补偿单数量
     * <AUTHOR>
     * @date 2022/3/17 16:12
     * @param dto
     * @return BaseResult<Integer>
     */
    @PostMapping("/baggage/accident/getBaggageAccidentNumber")
    BaseResult<Integer> getBaggageAccidentNumber(@RequestBody PaxMaintainSearchDTO dto);



    /**
     * @title getExistAccidentNumber
     * @description 已存在的事故单数量
     * <AUTHOR>
     * @date 2024/6/19 14:49
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Integer>
     */
    @PostMapping("/baggage/accident/existAccidentNumber")
    public BaseResult<Integer> getExistAccidentNumber(@Validated @RequestBody VerifyAlikePaxOrderDTO dto);

    /**
     * @title getExistCompensationNumber
     * @description 已存在的补偿单数量
     * <AUTHOR>
     * @date 2024/6/19 14:49
     * @param dto
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Integer>
     */
    @PostMapping("/baggage/accident/existCompensationNumber")
    public BaseResult<Integer> getExistCompensationNumber(@Validated @RequestBody VerifyAlikePaxOrderDTO dto);




        //补偿单相关接口开始
    /**
     * @title compensationBaseInfoPage
     * @description 条件分页查询赔偿单最基本信息记录
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param request
     * @return
     */
    @PostMapping("/compensation/query/page")
    PagedResult<List<CompensationBaseInfoVO>> compensationBaseInfoPage(@RequestBody CompensationBaseInfoRequestDTO request);

    /**
     * @title compensationDetailSearch
     * @description 条件查询补偿单详情信息
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param orderId
     * @return
     */
    @GetMapping("/compensation/query/detail/{orderId}")
    BaseResult<Object> compensationDetailSearch(@PathVariable String  orderId);

    /**
     * @title compensationAuditInfoSearch
     * @description 条件查询补偿单审核人信息
     * <AUTHOR>
     * @date 2022/3/16 9:29
     * @param request
     * @return
     */
    @PostMapping("/compensation/query/audit/info")
    BaseResult<List<CompensationAuditInfoVO>> compensationAuditInfoSearch(@RequestBody CompensationAuditInfoRequestDTO request);

    /**
     * @title addMaterialCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/compensation/command/material/add")
    BaseResult<String> addMaterialCompensation(@RequestBody CompensationMaterialAddCommandDTO request);

    /**
     * @title editMaterialCompensation
     * @description 编辑实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("/compensation/command/material/edit")
    BaseResult<String> editMaterialCompensation(@RequestBody CompensationMaterialEditCommandDTO request);


    /**
     * @title addCashCompensation
     * @description 创建现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("/compensation/command/cash/add")
    BaseResult<String> addCashCompensation(@RequestBody CompensationCashAddCommandDTO request);

    /**
     * @title editCashCompensation
     * @description 编辑现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("/compensation/command/cash/edit")
    BaseResult<String> editCashCompensation(@RequestBody @Validated CompensationCashEditCommandDTO request);

    /**
     * @title submit
     * @description 提交补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:29
     * @param orderId
     * @return
     */
    @PostMapping("/compensation/command/submit/{orderId}")
    BaseResult<CompensationAuditOperationVO> submitCompensation(@PathVariable("orderId") String orderId);

    /**
     * @title delete
     * @description 删除补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:30
     * @param orderId
     * @return
     */
    @GetMapping("/compensation/command/delete")
    BaseResult<String> deleteCompensation(@RequestParam(value = "orderId") String orderId);

    /**
     * @title auditPassToTakeEffect
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:27
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("/compensation/command/auditPassToTakeEffect")
    BaseResult<String> auditPassToTakeEffect(@RequestParam(value = "orderId") String orderId);

    /**
     * @title closeCompensation
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:45
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("/compensation/command/closeCompensation")
    BaseResult<String> closeCompensation(@RequestParam(value = "orderId") String orderId);

    /**
     * @title mailGrant
     * @description 邮寄发放
     * <AUTHOR>
     * @date 2022/5/7 9:29
     * @param compensationExpressInfoDTO
     * @return BaseResult<Object>
     */
    @PostMapping("/compensation/command/mailGrant")
    BaseResult<Object> mailGrant(@RequestBody CompensationExpressInfoDTO compensationExpressInfoDTO);

    /**
     * @title findCashCompensationList
     * @description 查询现金补偿单信息
     * <AUTHOR>
     * @date 2022/4/18 9:46
     * @param request
     * @return BaseResult<CompensationBaseExtCashVO>
     */
    @PostMapping("/compensation/query/findCashCompensationList")
    PagedResult<List<Object>> findCashCompensationList(@RequestBody CompensationBaseInfoRequestDTO request);

    //补偿单相关接口结束

    //-------------箱包补偿单-------------
    /**
     * @title findCompensationLuggageList
     * @description 箱包补偿单列表查询
     * <AUTHOR>
     * @date 2022/4/15 12:30
     * @param  dto
     * @return CompensationMaterialQueriesListVO
     */
    @PostMapping("/compensation/material/findCompensationLuggageList")
     PagedResult<List<CompensationMaterialQueriesListVO>> findCompensationLuggageList(@RequestBody CompensationMaterialListDTO dto );

    /**
     * @title findCompensationLuggageDetailInfo
     * @description 箱包补偿单详情
     * <AUTHOR>
     * @date 2022/4/15 12:30
     * @param  dto
     * @return CompensationMaterialDetailFinalVO
     */
    @PostMapping("/compensation/material/findCompensationLuggageDetailInfo")
    BaseResult<CompensationMaterialDetailFinalVO> findCompensationLuggageDetailInfo(@RequestBody @Valid CompensationMaterialDetailDTO dto);



    /**
     * @title offlineGrant
     * @description 箱包补偿单线下发放
     * <AUTHOR>
     * @date 2022/4/15 12:30
     * @param  offlineGrantDTO
     * @return object
     */
    @PostMapping("/compensation/pax/info/offlineGrant")
    BaseResult<Object> offlineGrant(@RequestBody  OfflineGrantDTO offlineGrantDTO);

    @GetMapping("/luggage/getLuggageManagementList")
    BaseResult<List<LuggageInfoVO>> getLuggageManagementList(@RequestParam(value = "brand" ,required = false) List<String> brand);
    @PostMapping("/luggage/getLuggageCompensationReportDetail")
    PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReportDetail(@RequestBody LuggageCompensatePageDTO dto);
    @PostMapping("/compensation/query/getCashBusinessCostsDetail")
    PagedResult<List<CashBusinessCostsDetailVO>> getCashBusinessCostsDetail(@RequestBody CompensationCashReportDTO dto);
    @PostMapping("/compensation/query/getCashPayDetail")
    PagedResult<List<CashPayDetailVO>> getCashPayDetail(@RequestBody CompensationCashReportDTO dto);

    @PostMapping("/compensation/query/luggageBusinessReview")
    BaseResult<Object> luggageBusinessReview(@RequestBody LuggageBusinessReviewDTO dto);
    @PostMapping("/compensation/query/getLuggageBusinessCostsDetail")
    PagedResult<List<LuggageBusinessCostsDetailVO>> getLuggageBusinessCostsDetail(@RequestBody CompensationLuggageReportDTO dto);
    @PostMapping("/compensation/query/getLuggageBusinessCostsDetailExport")
    PagedResult<List<LuggageBusinessCostsDetailVO>> getLuggageBusinessCostsDetailExport(@RequestBody CompensationLuggageReportDTO dto);


    @PostMapping("/compensation/sms/sendSMS")
    BaseResult<Object> sendSMS(@RequestBody CompensationSMSDTO dto);
    @PostMapping("/compensation/sms/sendPassengerSMS")
    BaseResult<Object> sendPassengerSMS(@RequestBody CompensationSMSDTO dto);
    @PostMapping("/compensation/sms/authPassengerCode")
    BaseResult<Object> authPassengerCode(CompensationSMSDTO dto);

    @PostMapping("/compensation/query/getCashBusinessCostsDetailReport")
    PagedResult<List<CashBusinessCostsDetailVO>> getCashBusinessCostsDetailReport(CompensationCashReportDTO dto);

    @PostMapping("/compensation/query/getCashPayDetailReport")
    PagedResult<List<CashPayDetailVO>> getCashPayDetailReport(CompensationCashReportDTO dto);

    @PostMapping("/luggage/getLuggageCompensationReport")
    PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReport(LuggageCompensatePageDTO dto);


    //航班四要素等查询航班信息
    @PostMapping("/compensation/fltPaxData/getFlightList")
    BaseResult<List<FlightBasicnfoVO>> getFlightBasicInfo(@RequestBody FlightBaseQueryDTO dto);
    //旅客信息 查询旅客数据
    @PostMapping("/compensation/fltPaxData/findPassengers")
    BaseResult<List<PassengerBasicInfoVO>> getPassengers(@RequestBody PassengerQueryDTO dto);

    //新建事故单查询航班信息
    @GetMapping("/compensation/fltPaxData/findFlight")
    BaseResult<FlightFindVO> getFlight(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment) ;

    //新建事故单查询航段
    @GetMapping("/compensation/fltPaxData/findSegment")
    BaseResult<List<SegmentFindVO>> getSegment(@RequestParam(value = "date", required = true) String date, @RequestParam(value = "flightNo", required = true) String flightNo) ;

    //获取航站
    @GetMapping("/compensation/fltPaxData/terminal/info/list")
    BaseResult<Object> getTerminal() ;

    @GetMapping("/compensation/fltPaxData/findAlternateAndStop")
    BaseResult<String> getAlternateAndStop(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo);

    //获取航班的所有航站：包括（始发，经停，备降，到达航站）
    @GetMapping("/compensation/fltPaxData/getFltAirStation")
    BaseResult<Set<String>> getFltAirStation(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment);

    @PostMapping("/compensation/cabin//list")
    BaseResult<List<CabinConfigVO>> loadCabinByAirline();

    @PostMapping("/compensation/cabin/pageList")
    PagedResult<List<CabinConfigVO>> loadCabinByAirlinePageList(@RequestBody CabinConfigPageDTO dto);

    @PostMapping("/baggage/accident/undeliveredDropdown")
    BaseResult<List<BaggageAccidentDropdownVO>> getUndeliveredAccidentDropdown(@RequestBody UndeliveredAccidentDTO dto);

    @PostMapping("/baggage/accident/canEditTransport/{accidentNo}")
    BaseResult<Boolean> canEditTransport(@PathVariable(value = "accidentNo") String accidentNo);

    @PostMapping("/baggage/accident/transport/save")
    BaseResult<Object> saveTransportInfo(@Validated @RequestBody BaggageTransportInfoDTO dto);

    @PostMapping("/baggage/accident/transport/list")
     PagedResult<List<BaggageTransportListVO>> queryBaggageTransportList(@RequestBody @Valid BaggageTransportQueryDTO dto);

    @GetMapping("/baggage/accident/transport/detail/{transportId}")
    public BaseResult<BaggageTransportDetailVO> getTransportDetail(@PathVariable(value = "transportId") Long transportId);

    @PostMapping("/baggage/accident/transport/address")
    BaseResult<Boolean> updateTransportAddress(@Valid @RequestBody BaggageTransportAddressDTO dto);

    /**
     * 提交运输单到工作流
     * 
     * @param transportId 运输单ID
     * @return 工作流审核结果
     */
    @PostMapping("/baggage/accident/transport/submit/{transportId}")
    BaseResult<Object> submitTransport(@PathVariable(value = "transportId") Long transportId);

    /**
     * 审核运输单
     * 
     * @param dto 审核信息DTO
     * @return 工作流审核结果
     */
    @PostMapping("/baggage/accident/transport/audit")
    BaseResult<Object> auditTransport(@Valid @RequestBody BaggageTransportAuditDTO dto);

    /**
     * 查询运输单可选审核人
     * 
     * @param transportId 运输单ID
     * @param taskId      任务ID，可选
     * @return 审核人列表
     */
    @GetMapping("/baggage/accident/transport/reviewers/{transportId}")
    BaseResult<Object> getTransportReviewers(@PathVariable(value = "transportId") Long transportId,
            @RequestParam(required = false) String taskId);

    /**
     * 保存运输单审核人
     * 
     * @param dto 审核人保存DTO
     * @return 保存后的审核人列表
     */
    @PostMapping("/baggage/accident/transport/reviewers/save")
    BaseResult<Object> saveTransportReviewers(@Valid @RequestBody BaggageTransportReviewerSaveDTO dto);
}

