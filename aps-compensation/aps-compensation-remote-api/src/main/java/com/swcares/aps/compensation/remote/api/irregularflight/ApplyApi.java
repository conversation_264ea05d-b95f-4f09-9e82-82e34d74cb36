package com.swcares.aps.compensation.remote.api.irregularflight;

import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.*;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：ApplyApi <br>
 * Description：申领单提供接口 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月25日 10:44 <br>
 * @version v1.0 <br>
 */
@FeignClient(name= "compensation-impl", path = "/api/compensation-impl", contextId ="applyApi" )
public interface ApplyApi {

    @PostMapping("/apply/applyPax/authPax")
    BaseResult<String> authPax(AuthPaxDTO dto);

    @GetMapping("/apply/applyPax/sendSMS")
    BaseResult<String> sendSMS(@RequestParam String phoneNum);

    @GetMapping("/apply/applyPax/verificationSMS")
    BaseResult<String> verificationSMS(@RequestParam String phoneNum, @RequestParam String authCode);

    @PostMapping("/apply/wechat/offiaccount/openid")
    BaseResult<String> getAccessTokenByOfficialAccounts(String code);

    @GetMapping("/apply/wechat/miniprogram/openid")
    BaseResult<String> getAccessTokenByMiniProgram(@RequestParam String tenantCode,@RequestParam String code);

    @PostMapping("/apply/applyPax/findCompensationOrder")
    BaseResult<CompensationInfoVO> findCompensationOrder(@RequestBody AuthPaxDTO dto);

    @PostMapping("/apply/order/saveApply")
    BaseResult<Object> saveApply(@RequestBody ApplyOrderDTO dto);

    /** 本人领取H5 */
    @PostMapping("/apply/receive/queryRecord")
    BaseResult queryRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO);

    @PostMapping("/apply/receive/myRecord")
    PagedResult<List<ReceivingRecordVO>> myRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO);

    @PostMapping("/apply/receive/receive")
    PagedResult<List<ReceivingRecordVO>> getReceive(@RequestBody @Valid ApplyGetReceiveDTO applyGetReceiveDTO);

    @GetMapping("/apply/receive/queryDetails")
    BaseResult<ApplyDetailsInfoVO> queryDetails(@RequestParam String recordId,@RequestParam String idCard);

    /** 代人领取H5 */
    @PostMapping("/apply/receive/replaceRecord")
    PagedResult<List<ReceivingRecordVO>> replaceRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO);

    @PostMapping("/apply/receive/replaceFilterRecord")
    PagedResult<List<ReceivingRecordVO>> replaceFilterRecord(@RequestBody @Valid ReplaceFilterRecordDTO replaceFilterRecordDTO);

    @GetMapping("/apply/receive/queryReplaceDetails")
    BaseResult<ReplaceDetailsInfoVO> queryReplaceDetails(@RequestParam String recordId,@RequestParam String idCard);

    /** 代领 */
    @PostMapping("/applyBehalf/behalfOrder/saveBehalfApply")
    public BaseResult<Object> saveBehalfApply(@RequestBody @Valid ApplyBehalfOrderDTO dto);

    @PostMapping("/applyBehalf/applyBehalfPax/authBehalfPax")
    public BaseResult<Object> authBehalfPax(@RequestBody @Valid AuthBehalfPaxDTO dto);

    @GetMapping("/applyBehalf/applyBehalfPax/sendSMSBehalf")
    public BaseResult<Object> sendSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum);

    @GetMapping("/applyBehalf/applyBehalfPax/verificationSMSBehalf")
    public BaseResult<Object> verificationSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode);

    @GetMapping("/applyBehalf/applyBehalfPax/findFlightInfo")
    public BaseResult<CompensationFlightInfoVO> findFlightInfo(@ApiParam(value = "航班号", required = true) @RequestParam String flightNo, @ApiParam(value = "航班日期", required = true) @RequestParam String flightDate);

    @PostMapping("/applyBehalf/applyBehalfPax/findCompensationOrderBehalf")
    public BaseResult<List<List<CompensationOrderInfoVO>>> findCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos);

    @GetMapping("/applyBehalf/applyBehalfPax/getReplaceRule")
    public BaseResult<Object> getReplaceRule();

    @PostMapping("/web/substituteCollar/page")
    public PagedResult<List<SubstituteCollarPaxPageVO>> webPage(@RequestBody SubstituteCollarPaxPageDTO dto);

    @GetMapping("/web/substituteCollar/find")
    public BaseResult<SubstituteCollarPaxDetailsVO> find(@ApiParam(value = "主键id", required = true)@RequestParam Long id);

    @PostMapping("/web/substituteCollar/updQuickPay")
    public BaseResult<Object> updQuickPay(@RequestBody SubstituteCollarUpdDTO substituteCollarUpdDTO);

    @PostMapping("/applyBehalf/applyBehalfPax/authCompensationOrderBehalf")
    public BaseResult<AuthCompensationOrderVO> authCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos);

    @PostMapping("/apply/order/audit")
    public BaseResult<Object> applyOrderAudit(@RequestBody ApplyAuditDTO applyAuditDTO);

    @PostMapping("/apply/order/nextNodeNotice")
    public BaseResult<Object> nextNodeNotice(@RequestBody Object nodeNoticeDTO);

    @PostMapping("/apply/applyPax/updateApplyPaxStatus")
    public BaseResult<Object> updateApplyPaxStatus(@RequestBody @Valid UpdateApplyPaxStatusDTO dto);

    @PostMapping("/applyBehalf/behalfOrder/saveAssistApply")
    BaseResult<Object> saveAssistApply(@RequestBody @Valid ApplyBehalfOrderDTO dto);

    @PostMapping("/apply/order/task/findUnpaidOrderInfo")
    public BaseResult<List<ApplyOrderPayVO>> findUnpaidOrderInfo();

    @PostMapping("/apply/order/task/saveRecord")
    public BaseResult<Object> saveRecord(@RequestBody PayRecordDO payRecordDO);

    @PostMapping("/apply/order/task/updatePayRecordById")
    public BaseResult<Object> updatePayRecordById(@RequestBody PayRecordDO payRecordDO);

    @PostMapping("/apply/order/task/updOrderPaxStatus")
    public BaseResult<Object> updOrderPaxStatus(@RequestBody OrderPaxStatusPayUpdDTO dto);

    @PostMapping("/apply/order/task/updApplyOrderPaxStatus")
    public BaseResult<Object> updApplyOrderPaxStatus(@RequestBody ApplyOrderPaxStatusUpdDTO dto);

    @PostMapping("/apply/order/task/updApplyOrderInfo")
    public BaseResult<Object> updApplyOrderInfo(@RequestParam String applyCode, @RequestParam(value = "receieveTime" ,required = false) String receieveTime);

    @GetMapping("/apply/wechat/miniprogram/login")
    BaseResult<Object> miniProgramLogin(@ApiParam(value = "小程序端的openId", required = true) @RequestParam String openId, @ApiParam(value = "租户Code", required = true)@RequestParam String tenantCode);


    @PostMapping("/api/chinaPay/authUnionPay")
    BaseResult<Object> authUnionPay(@RequestBody @Validated ChinaPayAuthRequestDTO dto);


    @PostMapping("/apply/receive/getCaptcha")
    BaseResult<Object> getCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto) ;

    @PostMapping("/apply/receive/validateCaptcha")
    BaseResult<Object> validateCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto);

    //旅客申领 获取验证码接口
    @PostMapping("/apply/captcha/getCaptcha")
    public BaseResult<Object> getCaptchaByApply();

    //旅客申领 校验验证码接口
    @PostMapping("/apply/captcha/validateCaptcha")
    public BaseResult<Object> validateCaptchaByApply(@RequestBody @Valid ApplyCaptchaDTO dto);

    //获取当前租户的短信验证码配置信息
    @GetMapping("/apply/captcha/getCaptchaConfig")
    public BaseResult<Object> getCaptchaConfigByTenant();
}
