package com.swcares.aps.compensation.remote.api.baggage;

import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageTransportAuditDTO;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageTransportReviewerSaveDTO;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * ClassName：BaggageTransportRemoteApi <br>
 * Description：行李运输单远程API接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2025/1/28 <br>
 * @version v1.0 <br>
 */
@FeignClient(name = "aps-compensation-impl", path = "/baggage/accident")
@Api(tags = "行李运输单远程API")
public interface BaggageTransportRemoteApi {

    /**
     * 提交运输单到工作流
     * 
     * @param transportId 运输单ID
     * @return 工作流审核结果
     */
    @PostMapping("/transport/submit/{transportId}")
    @ApiOperation(value = "提交运输单到工作流")
    BaseResult<Object> submitTransport(@PathVariable(value = "transportId") Long transportId);

    /**
     * 审核运输单
     * 
     * @param dto 审核信息DTO
     * @return 工作流审核结果
     */
    @PostMapping("/transport/audit")
    @ApiOperation(value = "审核运输单")
    BaseResult<Object> auditTransport(@Valid @RequestBody BaggageTransportAuditDTO dto);

    /**
     * 查询运输单可选审核人
     * 
     * @param transportId 运输单ID
     * @param taskId      任务ID，可选
     * @return 审核人列表
     */
    @GetMapping("/transport/reviewers/{transportId}")
    @ApiOperation(value = "查询运输单可选审核人")
    BaseResult<Object> getTransportReviewers(@PathVariable(value = "transportId") Long transportId,
            @ApiParam(value = "任务ID，可选") @RequestParam(required = false) String taskId);

    /**
     * 保存运输单审核人
     * 
     * @param dto 审核人保存DTO
     * @return 保存后的审核人列表
     */
    @PostMapping("/transport/reviewers/save")
    @ApiOperation(value = "保存运输单审核人")
    BaseResult<Object> saveTransportReviewers(@Valid @RequestBody BaggageTransportReviewerSaveDTO dto);
}