package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.entity.CompensationOrderInfo <br>
 * Description：赔偿单信息 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderInfoDO对象", description="赔偿单信息")
@TableName(value = "compensation_order_info")
public class CompensationOrderInfoDO {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    private Long tenantId;

    @ApiModelProperty(value = "事故单ID")
    @NotNull
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    @NotNull
    private String accidentNo;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    @NotNull
    private String compensateType;

    @ApiModelProperty(value = "补偿标准类型：1标准补偿金额 2自定义")
    private String compensateStandard;

    @ApiModelProperty(value = "标准补偿选择类型 1百分比 2固定金额")
    private String standardSelectType;

    @ApiModelProperty(value = "【支付使用此字段判断收款方式】补偿子方式： 1现金-补偿子方式包含( 1微信，2银联，3数字人民币)【与补偿方式字段关联关系】")
    @NotNull
    private String compensateSubType;

    @ApiModelProperty(value = "物品id")
    private String commodityId;

    @ApiModelProperty(value = "服务航站")
    @NotNull
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    @NotNull
    private String choiceSegment;

    @ApiModelProperty(value = "所选航段中文")
    @NotNull
    private String choiceSegmentCh;

    @ApiModelProperty(value = "备注")
    @Size(max = 200)
    private String remark;

    @ApiModelProperty(value = "保障服务（0不存在，1存在）")
    @NotNull
    private String ensureType;

    @ApiModelProperty(value = "0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    @NotNull
    private String status;

    @ApiModelProperty(value = "航班ID")
    @NotNull
    private String flightId;

    @ApiModelProperty(value = "申领有效期(补偿单生效)默认一年有效")
    private LocalDateTime expiryDate;

    @ApiModelProperty(value = "合计金额")
    private BigDecimal sumMoney;

    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

    @ApiModelProperty(value = "生效状态操作人")
    private String issUser;

    @ApiModelProperty(value = "关闭人")
    private String closeUser;

    @ApiModelProperty(value = "关闭时间")
    private LocalDateTime closeTime;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 6超售 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "所属航班全航段")
    private String fullSegment;

    @ApiModelProperty(value = "补偿单来源 机场/航司")
    private String source;

    @ApiModelProperty(value = "补偿单来源（对应的TENANT_CODE） 机场/航司")
    private String sourceTenantCode;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "发放时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date releaseTime;

    @ApiModelProperty(value = "发放人")
    private String release;

    @ApiModelProperty(value = "预设金额")
    private BigDecimal presetAmount;

    @ApiModelProperty(value = "是否有资金授权（0否1是）")
    private String grantBankroll;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

    @ApiModelProperty(value = "是否临时补偿")
    private Boolean temporaryCompensation;
}
