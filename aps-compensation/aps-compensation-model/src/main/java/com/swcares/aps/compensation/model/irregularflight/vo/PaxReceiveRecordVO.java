package com.swcares.aps.compensation.model.irregularflight.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.aps.compensation.model.irregularflight.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月29日 16:50 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="PaxReceiveRecordVO对象", description="旅客领取记录")
@SecretInfoEntity
public class PaxReceiveRecordVO {

    @ApiModelProperty(value = "申领人")
    private String applyUser;

    @ApiModelProperty(value = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "本次申领金额（元)")
    private String applyAmount;

    @ApiModelProperty(value = "申领人电话")
    @SecretValue(separator = ",")
    private String telephone;

    @ApiModelProperty(value = "申领单总申领金额")
    private String applySumAmount;

    @ApiModelProperty(value = "申领方式 是否代领(本人0,代领1)")
    private String applyType;

    @ApiModelProperty(value = "收款方式 -数据字典取 领取方式(1微信、2支付宝、3银联、4卡券、5里程、6积分、7实物-箱包)")
    private String payType;

    @ApiModelProperty(value = "收款方式子类型 ")
    private String paySubType;

    @ApiModelProperty(value = "申领时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date applyTime;

    @ApiModelProperty(value = "支付流水号")
    private String paySerialNumber;

    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date payTime;

    @ApiModelProperty(value = "失败原因")
    private String failReason;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;
}
