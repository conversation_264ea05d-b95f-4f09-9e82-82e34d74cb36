package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.model.replace.dto.ReplaceConfigDeleteDTO <br>
 * Description：  数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-11 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="ReplaceConfigDeleteDTO对象", description="删除对象DTO")
public class CompensationConfigDeleteDTO implements BaseDTO {
    @ApiModelProperty(value = "是否同步到当前租户的其他授权航司，Y是 N否")
    private String toSync;

    @ApiModelProperty(value = "配置主类型")
    private String type;

    @ApiModelProperty(value = "配置子类型")
    private String subType;


    @ApiModelProperty(value = "删除的ids")
    private String id;

    @ApiModelProperty(value = "删除的操作人id")
    private String updatedBy;

}
