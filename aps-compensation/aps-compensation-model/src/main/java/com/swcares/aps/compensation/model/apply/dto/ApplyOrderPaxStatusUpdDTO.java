package com.swcares.aps.compensation.model.apply.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * @ClassName：ApplyOrderPaxStatusUpdDTO
 * @Description：支付后修改申领单下旅客领取状态对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/10/28 14:19
 * @version： v1.0
 */
@Builder
@Data
public class ApplyOrderPaxStatusUpdDTO {
    @ApiModelProperty(value = "航延补偿旅客明细表ID")
    private Long paxInfoId;

    @ApiModelProperty(value = "申领单ID，即apply_order的主键在这个表的外键，用于关联查询")
    private Long applyId;

    @ApiModelProperty(value = "赔偿单id")
    private Long orderId;

    @ApiModelProperty(value = "申领旅客领取状态")
    private String applyPaxStatus;

    @ApiModelProperty(value = "申领失败的描述")
    private String applyPaxError;
}
