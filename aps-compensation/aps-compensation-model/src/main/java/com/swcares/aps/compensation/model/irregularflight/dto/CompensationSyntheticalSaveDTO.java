package com.swcares.aps.compensation.model.irregularflight.dto;

import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.C;

import javax.validation.Valid;
import java.util.List;

/**
 * ClassName：com.swcares.aps.compensating.mode.irregularflight.dto.CompensationSyntheticalSaveDto <br>
 * Description：赔偿单新增集合Dto <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/10/29 14:17 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationSyntheticalSaveDto", description="赔偿单新增集合Dto")
public class CompensationSyntheticalSaveDTO implements BaseDTO {

    @ApiModelProperty(value = "不正常航班事故单信息Dto")
    private FlightAccidentInfoDTO accidentInfoDTO;

    @ApiModelProperty(value = "投诉旅客事故单信息Dto")
    private ComplaintAccidentInfoEntity complaintAccidentInfoEntity;

    @ApiModelProperty(value = "补偿单信息Dto")
    private CompensationOrderInfoDTO orderInfoDTO;

    @ApiModelProperty(value = "补偿单航班信息Dto")
    private CompensationFlightInfoDTO flightInfoDTO;

    @ApiModelProperty(value = "补偿单旅客信息Dto集合")
    private List<CompensationPaxInfoDTO> paxInfoDTO;

    @ApiModelProperty(value = "补偿单规则信息Dto集合")
    @Valid
    private List<CompensationRuleRecordDTO> ruleRecordDTO;

    @ApiModelProperty(value = "关联的工作流引擎;可为空")
    private StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO;
}
