package com.swcares.aps.compensation.model.irregularflight.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ClassName：com.swcares.irregularflight.dto.DpFlightAccidentInfoPagedDTO <br>
 * Description： 分页数据传输对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="FlightAccidentInfoPagedDTO分页对象", description="")
public class FlightAccidentInfoPagedDTO extends PagedDTO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态 0草稿、1待处理、2处理中、3已结案、4作废")
    private List<String> accidentStatus;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航变类型-1延误，2取消，3备降，4返航，5补班")
    private String fcType;

    @ApiModelProperty(value = "航变类型原因归属-字典枚举值fc_type_owner")
    private String fcTypeOwner;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "归属航司")
    private List<String> belongAirline;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "事故单来源-字典枚举值accident_source")
    private String accidentSource;

    @ApiModelProperty(value = "起始航站")
    private String orgCity;

    @ApiModelProperty(value = "到达航站")
    private String dstCity;

    //用于运行时行权限的实现，字段可能会有多个值，需要拼接到对应查询的sql中
    @ApiModelProperty(value = "事故单类型")
    private List<String> accidentType;

    @ApiModelProperty(value = "工作航站")
    private List<String> workStations;

}
