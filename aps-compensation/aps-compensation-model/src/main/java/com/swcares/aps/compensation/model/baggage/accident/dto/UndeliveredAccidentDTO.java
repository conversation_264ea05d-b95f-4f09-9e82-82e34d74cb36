package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value="UndeliveredAccidentDTO", description="运输单下拉列表DTO")
public class UndeliveredAccidentDTO {

    @ApiModelProperty(value = "航班号", notes = "支持模糊搜索")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "合并的accidentNo信息")
    private List<String> mergedId;
}
