package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@SecretInfoEntity
@ApiModel(value = "BaggageTransportListVO", description = "行李运输列表返回对象")
public class BaggageTransportListVO {

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "ID")
    private Long id;

    @ApiModelProperty(value = "事故类型")
    private String accidentType;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "收件人")
    private String receiverName;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "录入机场")
    private String inputAirport;

    @ApiModelProperty(value = "收件人电话1")
    @SecretValue
    @JsonIgnore
    private String receiverPhone1;

    @ApiModelProperty(value = "收件人电话2")
    @SecretValue
    @JsonIgnore
    private String receiverPhone2;

    @ApiModelProperty(value = "最新录入人")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "最新录入时间")
    private LocalDateTime lastUpdatedTime;

    @ApiModelProperty(value = "运输时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime transportDate;

    @ApiModelProperty(value = "运输公司")
    private String transportCompany;

    @ApiModelProperty(value = "运输单号")
    private String transportNumber;

    @ApiModelProperty(value = "运输总费用")
    private Double transportCost;

    @ApiModelProperty(value = "事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "审核状态：1-审核中，2-审核通过，3-审核不通过")
    private String auditStatus;

    @ApiModelProperty(value = "主事故单标识")
    private Boolean mainAccident;

    @ApiModelProperty(value = "审核标识")
    private Boolean auditFlag;

    @ApiModelProperty(value = "任务id")
    private String taskId;
}