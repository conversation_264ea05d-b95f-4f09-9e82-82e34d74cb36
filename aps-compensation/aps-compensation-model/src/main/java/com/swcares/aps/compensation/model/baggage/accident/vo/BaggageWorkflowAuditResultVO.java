package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：BaggageWorkflowAuditResultVO
 * @Description：行李运输工作流审核结果VO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： system
 * @Date： 2024/12/22
 * @version： v1.0
 */
@Data
public class BaggageWorkflowAuditResultVO {
    @ApiModelProperty(value = "行李运输信息ID")
    private String transportId;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "审核人列表")
    private List<AuditorInfoDTO> auditorList;

    @ApiModelProperty(value = "当前任务活动信息")
    private CurrentTaskActivityVO currentTaskActivityVO;

    public BaggageWorkflowAuditResultVO(String transportId, String taskId, List<AuditorInfoDTO> auditorList) {
        this.transportId = transportId;
        this.taskId = taskId;
        this.auditorList = auditorList;
    }

    public BaggageWorkflowAuditResultVO(CurrentTaskActivityVO currentTaskActivityVO) {
        this.currentTaskActivityVO = currentTaskActivityVO;
    }

    public BaggageWorkflowAuditResultVO() {
    }

    /**
     * 判断工作流是否已结束
     * <p>
     * 通过检查当前任务活动信息中的结束节点标识来判断工作流是否已结束。
     * 只有当工作流正常结束时，才应该更新业务状态。
     * </p>
     *
     * @return true-工作流已结束，false-工作流未结束
     */
    public boolean isWorkflowEnded() {
        if (currentTaskActivityVO == null ||
                currentTaskActivityVO.getCurrentTaskActivityDTOS() == null ||
                currentTaskActivityVO.getCurrentTaskActivityDTOS().isEmpty()) {
            return false;
        }

        // 检查当前任务是否为结束节点
        return currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getIsEndActivity();
    }

    /**
     * 获取前一个节点的执行人信息
     * <p>
     * 当工作流结束时，需要记录结束节点前一个节点的执行人信息作为机场审核人。
     * 这个信息通常存储在LastAssignee字段中。
     * </p>
     *
     * @return 前一个节点的执行人ID，如果没有则返回null
     */
    public String getLastExecutor() {
        if (currentTaskActivityVO == null ||
                currentTaskActivityVO.getCurrentTaskActivityDTOS() == null ||
                currentTaskActivityVO.getCurrentTaskActivityDTOS().isEmpty()) {
            return null;
        }

        return currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getLastAssignee();
    }

}
