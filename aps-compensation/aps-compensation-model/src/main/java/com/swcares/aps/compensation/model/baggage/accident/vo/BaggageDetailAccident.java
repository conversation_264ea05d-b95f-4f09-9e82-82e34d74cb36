package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ClassName： FindBaggageDetailAccident
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 18:11
 * @version v1.0
 */
@Data
@ApiModel(value="异常行李事故单详情下的事故信息VO", description="异常行李事故单详情下的事故信息VO")
public class BaggageDetailAccident {

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

    @ApiModelProperty(value = "原因类型（1航司，2非航司）")
    private String reasonType;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态(0草稿,1待处理,2处理中,3已结案,4作废)")
    private String accidentStatus;

    @ApiModelProperty(value = "异常行李类型(21破损,22少收,23多收,24内件缺少,25丢失)")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "破损类型")   //1、破损类型
    private String damageType;

    @ApiModelProperty(value = "破损部位")   //1、破损类型
    private String damagePart;

    @ApiModelProperty(value = "破损程度")   //1、破损类型
    private String damageDegree;

    @ApiModelProperty(value = "行李品牌")   //1、破损类型
    private String baggageBrand;

    @ApiModelProperty(value = "丢失重量")   //2、3、4、丢失类型、少收类型内件缺失类型
    private String lostWeight;

    @ApiModelProperty(value = "少收类型")   //2、少收类型
    private String lostType;

    @ApiModelProperty(value = "事故提醒")   //2、少收类型
    private String reminderTime;

    @ApiModelProperty(value = "是否超时提醒（Y是N否）")   //2、少收类型
    private String overTime;

    @ApiModelProperty(value = "已丢失时间")   //3、丢失类型
    private String lostTime;

    @ApiModelProperty(value = "确认丢失时间")   //3、丢失类型
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date confirmLostTime;

    @ApiModelProperty(value = "丢失件数")   //4、内件缺失类型
    private String lostAmount;

    @ApiModelProperty(value = "丢失品申报")   //4、内件缺失类型
    private String declarationType;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "旅客相关图片")  //图片
    private String collectIdentityPaxPhotos;

    @ApiModelProperty(value = "单据相关图片")  //图片
    private String collectIdentityVoucherPhotos;

    @ApiModelProperty(value = "行李相关图片")  //图片
    private String collectIdentityBaggagePhotos;

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "作废人ID")
    private String tovoidBy;

    @ApiModelProperty(value = "作废时间")
    private String tovoidTime;

    @ApiModelProperty(value = "结案人")
    private String closeUser;

    @ApiModelProperty(value = "结案时间")
    private String closeTime;

    @ApiModelProperty(value = "旅客签名")
    private String signature;
}
