package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName： FindBaggageDtailFightVO
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 18:11
 * @version v1.0
 */
@Data
@ApiModel(value="异常行李事故单详情下的航班信息VO", description="异常行李事故单详情下的航班信息VO")
public class BaggageDetailFightVO {
    @ApiModelProperty(value = "旅客航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "旅客航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "旅客航班航段")
    private String paxSegment;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String fltCode;
}
