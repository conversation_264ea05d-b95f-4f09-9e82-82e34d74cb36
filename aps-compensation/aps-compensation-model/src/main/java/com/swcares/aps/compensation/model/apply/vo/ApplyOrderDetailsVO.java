package com.swcares.aps.compensation.model.apply.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * ClassName：com.swcares.aps.apply.model.vo <br>
 * Description：web代领旅客- 申领单信息 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月17日 14:41 <br>
 * @version v1.0 <br>
 */
@ApiModel(value = "ApplyOrderDetailsVO对象", description = "web代领-申领单信息")
@Data
public class ApplyOrderDetailsVO {


    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty(value = "申领类型")
    private String accidentType;

    @ApiModelProperty(value = "审核状态(1审核中2审核通过3审核不通过)字典表id")
    private String applyStatus;

    @ApiModelProperty(value = "申领单编号")
    private String applyCode;

    @ApiModelProperty(value = "申领时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "申领人姓名")
    private String applyUser;

    @ApiModelProperty(value = "申领人电话")
    private String telephone;

    @ApiModelProperty(value = "收款方式(237微信、238支付宝、239银联、240卡券、241里程、242积分、243实物-箱包)")
    private String getMoneyWay;

    @ApiModelProperty(value = "领取账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "开户行")
    private String openBankName;

    @ApiModelProperty(value = "开户人")
    private String accountHolder;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "航段")
    private String segmentCh;

    @ApiModelProperty(value = "代领支付等待时间（分钟）")
    private String paymentWaitingPeriod;

    @ApiModelProperty(value = "审核时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date auditTime;

    @ApiModelProperty(value = "1标识为快速支付0默认状态")
    private String quickPay;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;
}
