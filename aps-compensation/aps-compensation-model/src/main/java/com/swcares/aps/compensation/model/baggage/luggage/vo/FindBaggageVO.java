package com.swcares.aps.compensation.model.baggage.luggage.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @title: FindBaggageVO
 * @projectName aps
 * @description: 箱包事故单信息列表VO
 * @date 2022/3/4 9:30
 */
@Data
@ApiModel(value="箱包事故单信息列表VO", description="箱包事故单信息列表VO")
@SecretInfoEntity
public class FindBaggageVO {
    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "异常行李类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "航段")
    private String paxSegment;

    @ApiModelProperty(value = "补偿单数量")  //自定义的
    private String compensationNumber;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客证件号")
    private String idType;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "是否超时提醒（Y是N否）")   //2、少收类型
    private String overTime;


    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String source;

    @ApiModelProperty(value = "数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;




}
