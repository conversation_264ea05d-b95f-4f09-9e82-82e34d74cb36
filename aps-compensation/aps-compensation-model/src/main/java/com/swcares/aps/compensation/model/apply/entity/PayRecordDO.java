package com.swcares.aps.compensation.model.apply.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.compensation.entity.PayRecord <br>
 * Description：航延补偿支付记录表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Data
@SecretInfoEntity
@TableName("compensation_pay_record")
@ApiModel(value="PayRecord对象", description="航延补偿支付记录表")
public class PayRecordDO{

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "赔偿单id")
    @TableId(type = IdType.INPUT)
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "赔偿单id")
    private Long orderId;

    @ApiModelProperty(value = "申领单id")
    private Long applyId;

    @ApiModelProperty(value = "赔偿单旅客表id")
    private Long applyPaxId;

    @ApiModelProperty(value = "转账金额（分）")
    private BigDecimal transAmount;

    @ApiModelProperty(value = "转账返回的流水号")
    private String returnSerialNo;

    @ApiModelProperty(value = "返回code")
    private String transCode;

    @ApiModelProperty(value = "返回code描述")
    private String transMsg;

    @ApiModelProperty(value = "返回子code")
    private String transSubCode;

    @ApiModelProperty(value = "返回子code描述")
    private String transSubMsg;

    @ApiModelProperty(value = "领取方式(1微信、2支付宝、3银联、4卡券、5里程、6积分、7实物-箱包、8现金)")
    private String payType;

    @ApiModelProperty(value = "支付状态(0未支付,1支付成功,2支付失败，3.支付中)")
    private String payStatus;

    @ApiModelProperty(value = "转账返回时间")
    private LocalDateTime payReturnTime;

    @ApiModelProperty(value = "转账请求时间")
    private LocalDateTime payStartTime;

    @ApiModelProperty(value = "错误code")
    private String errCode;

    @ApiModelProperty(value = "错误code描述")
    private String errCodeDes;

    @SecretValue
    @ApiModelProperty(value = "转账账户")
    private String applyAccount;

    @ApiModelProperty(value = "手续费")
    private BigDecimal feeAmount;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1取消领取资格=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "转账日期，调银联查询接口必填字段")
    private String transDate;
}
