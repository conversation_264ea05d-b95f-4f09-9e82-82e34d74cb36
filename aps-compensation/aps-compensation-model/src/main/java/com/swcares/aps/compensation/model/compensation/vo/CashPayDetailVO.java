package com.swcares.aps.compensation.model.compensation.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CashPayDetailVO
 * @Description：现金支付明细
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/7/25 17:13
 * @version： v1.0
 */
@Data
@ApiModel(value = "CashPayDetailVO对象",description = "现金支付明细的VO对象")
@ExcelTarget("CashPayDetailVO")
public class CashPayDetailVO {

    @ApiModelProperty(value = "支付时间")
    @Excel(name = "支付时间")
    private String payTime;

    @ApiModelProperty(value = "支付方式：1:微信，2:支付宝，3:银联,4:卡劵,5:里程,6:积分,7:实物-箱包,8:现金")
    @Excel(name = "支付方式")
    private String payType;

    @ApiModelProperty(value = "支付金额")
    @Excel(name = "支付金额")
    private Integer payAmount;

    @ApiModelProperty(value = "支付状态:0:未支付，1:支付成功，2:支付失败")
    @Excel(name = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "支付失败原因")
    @Excel(name = "支付失败原因")
    private String payFailReason;

    @ApiModelProperty(value = "申领单号")
    @Excel(name = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "户名")
    @Excel(name = "户名")
    private String applyUser;

    @ApiModelProperty(value = "开户银行")
    @Excel(name = "开户银行")
    private String openBankName;

    @ApiModelProperty(value = "账号")
    @Excel(name = "账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "支付流水号")
    @Excel(name = "支付流水号")
    private String paySerialNumber;
}
