package com.swcares.aps.compensation.model.irregularflight.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value="GeneralCompensationOrderInfoDetailVO", description="统一事故单详情接口返回信息")
public class GeneralCompensationOrderInfoDetailVO {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "赔偿单ID")
    private Long id;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "补偿单状态：0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    private String status;

    @ApiModelProperty(value = "补偿单来源 机场/航司")
    private String source;

    @ApiModelProperty(value = "补偿航站也==服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "创建人信息")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "事故单类型==补偿类型- 1.不正常航班 2异常行李 3超售 4 旅客投诉")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型:(不正常航班：1延误，2取消，3备降，4返航，5补班）")
    private String fcType;

    @ApiModelProperty(value = "计划执行人数（所有补偿人数）")
    private String planCarryOutNum;

    @ApiModelProperty(value = "实际执行人数（已补偿人数）")
    private String actualCarryOutNum;

    @ApiModelProperty(value = "冻结人数")
    private String frozenNum;

    @ApiModelProperty(value = "实际执行金额==补偿金额（前为已领取金额）")
    private String planCompensateMoney;

    @ApiModelProperty(value = "计划执行金额==补偿金额（后为总金额）")
    private String actualCompensateMoney;

    @ApiModelProperty(value = "是否可以进行审核，Y是 N否")
    private String toExamine;

    @ApiModelProperty(value = "审核节点id")
    private String taskId;

    @ApiModelProperty(value = "系统账户id")
    private String userId;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "发放时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date releaseTime;

    @ApiModelProperty(value = "发放人")
    private String release;

    @ApiModelProperty(value = "物品ID")
    private String commodityID;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "关闭人")
    private String closeUser;

    @ApiModelProperty(value = "关闭时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date closeTime;

    @ApiModelProperty(value = "补偿子方式")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿标准")
    private String compensationStandard;

    @ApiModelProperty(value = "标准补偿选择类型 1百分比 2固定金额")
    private String standardSelectType;

    @ApiModelProperty(value = "临时补偿")
    private Boolean temporaryCompensation;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;
}
