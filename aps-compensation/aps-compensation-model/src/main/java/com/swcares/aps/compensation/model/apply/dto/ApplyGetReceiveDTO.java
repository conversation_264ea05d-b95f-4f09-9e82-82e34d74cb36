package com.swcares.aps.compensation.model.apply.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ApplyGetReceive <br>
 * Package：com.swcares.aps.apply.model.dto <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月09日 10:44 <br>
 * @version v1.0 <br>
 */
@ApiModel(value="ApplyGetReceive对象", description="领取记录查询参数")
@Data
public class ApplyGetReceiveDTO extends PagedDTO implements BaseDTO {

    @NotBlank(message = "姓名不能为空")
    @ApiModelProperty(value = "姓名")
    private String name;

    @NotBlank(message = "身份证号不能为空")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班开始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "申领开始日期")
    private String createdStartTime;

    @ApiModelProperty(value = "申领结束始日期")
    private String createdEndTime;

    @ApiModelProperty(value = "申领单类型")
    private String[] accidentType;

    @ApiModelProperty(value = "申领单状态")
    private String[] applyStatus;



}
