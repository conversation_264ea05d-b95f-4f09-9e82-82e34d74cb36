package com.swcares.aps.compensation.model.privilege.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> <PERSON>
 * @Classname SearchBusinessPrivilegeDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/11 16:51
 * @Version 1.0
 */
@Data
@ApiModel(value="SearchBusinessPrivilegeDTO", description="业务权限查询DTO")
public class SearchBusinessPrivilegeDTO extends PagedDTO {
    @ApiModelProperty(value = "被授权机场的三字码")
    String recipientCode;

    @ApiModelProperty(value = "被授权机场的简称")
    String recipientName;

    @ApiModelProperty(value = "授权状态，0-启用，1-禁用，不传递表示所有")
    String status;

    @ApiModelProperty(value="授权航司的二字码")
    String grantorCode;

    @ApiModelProperty(value="授权航司的简称")
    String grantorName;
}
