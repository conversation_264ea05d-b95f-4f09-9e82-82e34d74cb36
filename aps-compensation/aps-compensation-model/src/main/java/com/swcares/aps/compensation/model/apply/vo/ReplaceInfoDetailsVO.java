package com.swcares.aps.compensation.model.apply.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ReplaceInfoDetailsVO <br>
 * Package：com.swcares.aps.apply.model.vo <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2022年 01月10日 10:24 <br>
 * @version v1.0 <br>
 */
@ApiModel(value = "ReplaceInfoDetailsVO对象", description = "代领领取详情对象")
@Data
@SecretInfoEntity
public class ReplaceInfoDetailsVO {

    @ApiModelProperty(value = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "收款方式")
    private String getMoneyWay;

    @ApiModelProperty(value = "收款账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "申领时间")
    private String createdTime;

    @ApiModelProperty(value = "到账时间")
    private String receieveTime;

    @ApiModelProperty(value = "航班日期")
    private String flightData;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "起飞时间")
    private String std;

    @ApiModelProperty(value = "到达时间")
    private String sta;

    @ApiModelProperty(value = "中文航段")
    private String segmentCh;

    @ApiModelProperty(value = "三字码航段")
    private String segment;

    @ApiModelProperty(value = "申领人姓名")
    private String applyUser;

    @ApiModelProperty(value = "身份证")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "手机号")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "代领人证照ID")
    private String photoId;

    @ApiModelProperty(value = "事故单类型")
    private String accidentType;

    @ApiModelProperty(value = "审核状态")
    private String applyStatus;

    @ApiModelProperty(value = "申领单状态")
    private String status;

    @ApiModelProperty(value = "申领领取状态")
    private String applyPaxStatus;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;

}
