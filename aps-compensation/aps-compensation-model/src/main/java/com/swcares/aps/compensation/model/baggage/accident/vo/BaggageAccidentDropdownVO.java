package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "BaggageAccidentDropdownVO", description = "行李事故单下拉列表数据")
public class BaggageAccidentDropdownVO {
    
    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;
    
    @ApiModelProperty(value = "事故单号")
    private String accidentNo;
    
    @ApiModelProperty(value = "旅客姓名")
    private String passengerName;
    
    @ApiModelProperty(value = "事故类型")
    private String accidentType;
    
    @ApiModelProperty(value = "行李号")
    private String baggageNo;
} 