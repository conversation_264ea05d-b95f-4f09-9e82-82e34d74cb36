package com.swcares.aps.compensation.model.baggage.accident.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: BaggagecompensationVO
 * @projectName aps
 * @description: TODO
 * @date 2022/3/7 11:02
 */
@Data
@ApiModel(value="异常行李事故单下赔偿单信息VO", description="异常行李事故单下赔偿单信息VO")
public class BaggageCompensationVO {

    @ApiModelProperty(value = "补偿单id")
    private String orderId;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿单状态(0草稿,1审核中,2审核不通过,3驳回,4审核通过,5生效,6关闭,7逾期)")
    private String status;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "补偿子方式(实物、纪念品)")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿标准类型：1标准补偿金额 2自定义")
    private String compensateStandard;

    @ApiModelProperty(value = "物品id")
    private String commodityId;

    @ApiModelProperty(value = "补偿总金额")
    private String sumMoney;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "补偿事故单子类型- 补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private String accidentSubType;

    @ApiModelProperty(value = "补偿事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;
}
