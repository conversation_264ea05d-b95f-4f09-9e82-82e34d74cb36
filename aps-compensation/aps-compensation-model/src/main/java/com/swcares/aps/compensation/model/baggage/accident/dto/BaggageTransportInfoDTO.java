package com.swcares.aps.compensation.model.baggage.accident.dto;

import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value="BaggageTransportInfoDTO", description="异常行李运输单信息对象")
public class BaggageTransportInfoDTO {

    @ApiModelProperty(value = "运输单d")
    private Long id;
    
    @ApiModelProperty(value = "关联事故单ID")
    private String accidentNo;

    private Long accidentId;
    
    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人电话1")
    @SecretValue
    private String receiverPhone1;

    @ApiModelProperty(value = "收件人电话2(可选)")
    @SecretValue
    private String receiverPhone2;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "运输公司")
    private String transportCompany;

    @ApiModelProperty(value = "运输单号")
    private String transportNumber;

    @ApiModelProperty(value = "运输日期")
    private Date transportDate;

    @ApiModelProperty(value = "运输总费用")
    private Double transportCost;

    @ApiModelProperty(value = "是否是一个合并运输行李信息")
    private Boolean mergedTransport;

    @ApiModelProperty(value = "审核状态：1-审核中，2-审核通过，3-审核不通过")
    private String auditStatus;

    @ApiModelProperty(value = "合并运输行李信息-事故单号集合")
    private List<String> mergedId;

    @ApiModelProperty(value = "是否可以编辑")
    private Boolean editFlag;
    
    @ApiModelProperty(value = "行李总件数")
    private Integer totalBaggageCount;

    @ApiModelProperty(value = "是否已复核")
    private Boolean reviewed;

    @ApiModelProperty(value = "复核人")
    private String reviewedBy;

    @ApiModelProperty(value = "复核时间")
    private LocalDateTime reviewedTime;
}
