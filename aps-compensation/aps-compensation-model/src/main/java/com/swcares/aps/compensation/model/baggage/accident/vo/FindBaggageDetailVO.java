package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @title: FindBaggageDetailVo
 * @projectName aps
 * @description: 查询异常行李事故单详情信息VO
 * @date 2022/3/7 10:12
 */
@Data
@SecretInfoEntity
@ApiModel(value="异常行李事故单详情信息VO", description="异常行李事故单详情信息VO")
public class FindBaggageDetailVO {

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "原因类型（1航司，2非航司）")
    private String reasonType;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态(0草稿,1待处理,2处理中,3已结案,4作废)")
    private String accidentStatus;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

    @ApiModelProperty(value = "异常行李类型(21破损,22少收,23多收,24内件缺少,25丢失)")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "破损类型")   //1、破损类型
    private String damageType;

    @ApiModelProperty(value = "破损部位")   //1、破损类型
    private String damagePart;

    @ApiModelProperty(value = "破损程度")   //1、破损类型
    private String damageDegree;

    @ApiModelProperty(value = "行李品牌")   //1、破损类型
    private String baggageBrand;

    @ApiModelProperty(value = "丢失重量")   //2、3、4、丢失类型、少收类型内件缺失类型
    private String lostWeight;

    @ApiModelProperty(value = "少收类型")   //2、少收类型
    private String lostType;

    @ApiModelProperty(value = "事故提醒")   //2、少收类型
    private String reminderTime;

    @ApiModelProperty(value = "是否超时提醒（Y是N否）")   //2、少收类型
    private String overTime;

    @ApiModelProperty(value = "已丢失时间")   //3、丢失类型
    private String lostTime;

    @ApiModelProperty(value = "确认丢失时间")   //3、丢失类型
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date confirmLostTime;

    @ApiModelProperty(value = "丢失件数")   //4、内件缺失类型
    private String lostAmount;

    @ApiModelProperty(value = "丢失品申报")   //4、内件缺失类型
    private String declarationType;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "旅客相关图片")  //图片
    private String collectIdentityPaxPhotos;

    @ApiModelProperty(value = "单据相关图片")  //图片
    private String collectIdentityVoucherPhotos;

    @ApiModelProperty(value = "行李相关图片")  //图片
    private String collectIdentityBaggagePhotos;

    @ApiModelProperty(value = "作废人ID")
    private String tovoidBy;

    @ApiModelProperty(value = "作废时间")
    private String tovoidTime;

    @ApiModelProperty(value = "结案人")
    private String closeUser;

    @ApiModelProperty(value = "结案时间")
    private String closeTime;

//------------------航班信息-----------------------
    @ApiModelProperty(value = "旅客航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "旅客航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "旅客航班航段")
    private String paxSegment;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String fltCode;

//------------------旅客信息-----------------------
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客票号")
    private String tktNo;

    @ApiModelProperty(value = "旅客手机号")
    @SecretValue
    private String phone;

//------------------行李信息-----------------------
    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "行李航班日期")
    private String baggageFlightDate;

    @ApiModelProperty(value = "行李航班号")
    private String baggageFlightNo;

    @ApiModelProperty(value = "行李航线")
    private String baggageSegment;

    @ApiModelProperty(value = "行李经停站")
    private String stopoverSegment;

    @ApiModelProperty(value = "备降航站")
    private String alternateTerminal;

    @ApiModelProperty(value = "行李规格")
    private String baggageType;

    @ApiModelProperty(value = "逾重行李号")
    private String overweightNo;

//------------------多收或少收异常行李事故单信息-----------------------
    @ApiModelProperty(value = "该事故单下关联的少收或多收的异常行李事故单信息列表")
    private List<MatchResultVO> matchResultList;

//------------------快递信息信息-----------------------
    @ApiModelProperty(value = "异常行李事故单下关联快递单信息列表")
    private List<BaggageExpressVO> baggageExpressList;

//------------------补偿单信息-----------------------
    @ApiModelProperty(value = "异常行李事故单下关联补偿单信息列表")
    private List<BaggageCompensationVO> baggageCompensationList;

}
