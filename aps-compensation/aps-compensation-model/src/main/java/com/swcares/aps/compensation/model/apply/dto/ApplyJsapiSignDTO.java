package com.swcares.aps.compensation.model.apply.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @ClassName：ApplyJsapiSignDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/6/16 16:13
 * @version： v1.0
 */
@ApiModel(value="ApplyJsapiSignDTO对象", description="获取JsapiSign")
@Data
public class ApplyJsapiSignDTO {

    @NotBlank(message = "url不能为空")
    private String url;

    @NotBlank(message = "applyId不能为空")
    private String applyId;
}
