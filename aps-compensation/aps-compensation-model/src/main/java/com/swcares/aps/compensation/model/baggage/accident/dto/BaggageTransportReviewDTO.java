package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 运输单复核请求DTO
 */
@Data
@ApiModel(value = "BaggageTransportReviewDTO", description = "运输单复核请求DTO")
public class BaggageTransportReviewDTO {

    @ApiModelProperty(value = "事故单ID", required = true)
    @NotNull(message = "事故单ID不能为空")
    private Long accidentId;
}
