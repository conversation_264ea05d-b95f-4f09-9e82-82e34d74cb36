package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @title: BaggageAccidentInfoDTO
 * @projectName aps
 * @description: BaggageAccidentInfoDTO对象
 * @date 2022/3/3 17:11
 */
@Data
@ApiModel(value="BaggageAccidentInfoDTO对象", description="异常行李事故单信息对象")
public class BaggageAccidentInfoDTO {

    @ApiModelProperty(value = "id,仅编辑时传")
    private Long id;

    @ApiModelProperty(value = "事故单号,不传")
    private String accidentNo;

    @ApiModelProperty(value = "事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "原因类型（1航司，2非航司）")
    private String reasonType;

    @ApiModelProperty(value = "数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

    @ApiModelProperty(value = "异常行李类型（21破损,22少收,23多收,24内件缺少,25丢失 26批量少收）")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "旅客航班ID")
    private String paxFlightId;

    @ApiModelProperty(value = "旅客航班号")
    @NotNull(message = "旅客航班号不能为空")
    private String paxFlightNo;

    @ApiModelProperty(value = "旅客航班日期")
    @NotNull(message = "旅客航班日期不能为空")
    private String paxFlightDate;

    @ApiModelProperty(value = "旅客航班航段")
    @NotNull(message = "旅客航班航段不能为空")
    private String paxSegment;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "行李号")
    @NotNull(message = "行李号不能为空")
    private String baggageNo;

    @ApiModelProperty(value = "行李航班号")
    private String baggageFlightNo;

    @ApiModelProperty(value = "行李航班日期")
    private String baggageFlightDate;

    @ApiModelProperty(value = "行李航线")
    private String baggageSegment;

    @ApiModelProperty(value = "经停航站")
    private String stopoverSegment;

    @ApiModelProperty(value = "行李类型")
    private String baggageType;

    @ApiModelProperty(value = "逾重行李号")
    private String overweightNo;

    @ApiModelProperty(value = "破损类型，直接传中文逗号分割（撕裂,划痕,压碎,污染,凹坑,洞穿,其他）")
    private String damageType;

    @ApiModelProperty(value = "破损程度，直接传中文（全部,大面积,小面积）")
    private String damageDegree;

    @ApiModelProperty(value = "破损部位，直接传中文（箱体,把手,锁,拉杆,轮子,其他）")
    private String damagePart;

    @ApiModelProperty(value = "行李品牌")
    private String baggageBrand;

    @ApiModelProperty(value = "丢失重量(KG)")
    private Integer lostWeight;

    @ApiModelProperty(value = "少收类型（1常规,2自定义）")
    private String lostType;

    @ApiModelProperty(value = "事故提醒时间设置-自然日（常规类型下，默认21个自然日）")
    private Integer reminderTime;

    @ApiModelProperty(value = "丢失数量（件）")
    private Integer lostAmount;

    @ApiModelProperty(value = "丢失申报类型（1全部申报,2部分申报,3未申报）")
    private String declarationType;

    @ApiModelProperty(value = "旅客相关图片")
    private List<String> paxImgs;

    @ApiModelProperty(value = "单据相关图片")
    private List<String> voucherImgs;

    @ApiModelProperty(value = "行李相关图片")
    private List<String> baggageImgs;

    @ApiModelProperty(value = "旅客姓名")
    @NotNull(message = "旅客姓名不能为空")
    private String paxName;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客证件类型")
    @NotNull(message = "旅客凭证类型不能为空")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @NotNull(message = "旅客凭证号不能为空")
    private String idNo;

    @ApiModelProperty(value = "旅客票号")
    @NotNull(message = "票号不能为空")
    private String tktNo;

    @ApiModelProperty(value = "旅客手机号")
    @NotNull(message = "电话号码不能为空")
    private String phone;

    @ApiModelProperty(value = "创建者，不传")
    private String createdBy;

    @ApiModelProperty(value = "备降航站")
    private String alternateTerminal;

    @ApiModelProperty(value = "是否存为草稿,Y是")
    private String isDraft;

    @ApiModelProperty(value = "异常行李事故单状态草稿转为待处理的时间")
    private LocalDateTime todoTime;

    @ApiModelProperty(value = "行李运输信息")
    private BaggageTransportInfoDTO transportInfoDTO;

    @ApiModelProperty(value = "旅客签名")
    private String signature;



    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String fltCode;
}
