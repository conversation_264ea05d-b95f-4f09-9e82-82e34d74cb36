package com.swcares.aps.compensation.model.baggage.accident.dto;

import javax.validation.constraints.NotBlank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 异常行李运输地址修改DTO
 */
@Data
@ApiModel(value = "BaggageTransportAddressDTO", description = "异常行李运输地址修改DTO")
public class BaggageTransportAddressDTO {

    @ApiModelProperty(value = "事故单号", required = true)
    @NotBlank(message = "事故单号不能为空")
    private String accidentNo;

    @ApiModelProperty(value = "省份", required = true)
    @NotBlank(message = "省份不能为空")
    private String province;

    @ApiModelProperty(value = "城市", required = true)
    @NotBlank(message = "城市不能为空")
    private String city;

    @ApiModelProperty(value = "区县", required = true)
    @NotBlank(message = "区县不能为空")
    private String district;

    @ApiModelProperty(value = "详细地址", required = true)
    @NotBlank(message = "详细地址不能为空")
    private String addressDetail;
}