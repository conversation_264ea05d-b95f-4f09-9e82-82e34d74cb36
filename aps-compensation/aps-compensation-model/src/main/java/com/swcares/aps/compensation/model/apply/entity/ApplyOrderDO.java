package com.swcares.aps.compensation.model.apply.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.compensation.entity.ApplyOrder <br>
 * Description：航延补偿申领单信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Data
@SecretInfoEntity
@TableName("compensation_apply_order")
@ApiModel(value = "ApplyOrder对象", description = "航延补偿申领单信息表")
public class ApplyOrderDO  extends BaseEntity  {

    private static final long serialVersionUID = 1L;

    private Long tenantId;

    @ApiModelProperty(value = "申领单编号")
    private String applyCode;

    @ApiModelProperty(value = "申领人姓名")
    @NotBlank(message = "申领人姓名不能为空")
    private String applyUser;

    @ApiModelProperty(value = "申领人电话")
    @NotBlank(message = "申领人电话不能为空")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "申领金额")
    private BigDecimal applyAmount;

    @ApiModelProperty(value = "申领人数(普通渠道默认1,代领渠道根据人数计算)")
    private Integer applyCustNum;

    @ApiModelProperty(value = "领取方式(本人0,代领1,协助2)")
    @NotBlank(message = "领取方式(本人0,代领1,协助2)")
    private String applyWay;

    @ApiModelProperty(value = "审核状态(1审核中2审核通过3审核不通过)字典表id")
    private String applyStatus;

    @ApiModelProperty(value = "代领人身份证照片关联附件资源信息表ID")
    private String collectIdentityCardPhoto;

    @ApiModelProperty(value = "【废弃字段】收款方式(237微信、238支付宝、239银联、240卡券、241里程、242积分、243实物-箱包、260现金)")
    private String getMoneyWay;

    @ApiModelProperty(value = "【支付使用此字段判断收款方式】补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "【支付使用此字段判断收款方式】补偿子方式： 1现金-补偿子方式包含( 1微信，2银联，3数字人民币)【与补偿方式字段关联关系】")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售 4投诉")
    private String accidentType;

    @SecretValue
    @ApiModelProperty(value = "领取账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "开户行")
    private String openBankName;

    @ApiModelProperty(value = "普通领取银联方式选择必填身份证号如果购票证件号不是身份证")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "1标识为快速支付0默认状态 2支付等待期满支付")
    private String quickPay;

    @ApiModelProperty(value = "支付等待期")
    private Integer paymentWaitingPeriod;

    @ApiModelProperty(value = "收款时间[目前协助领取在设置值，本人和代领没有用。]")
    private LocalDateTime receieveTime;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;


    @ApiModelProperty(value = "签名照片")
    private String signFile;

    @ApiModelProperty(value = "数据来源 1机场/2航司")
    private String source;

    @ApiModelProperty(value = "归属航司（航司二字码）")
    private String belongAirline;

    @ApiModelProperty(value = "航班号")
    @NotNull
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    @NotNull
    private String flightDate;

    @ApiModelProperty(value = "三字码航段")
    private String segment;

    @ApiModelProperty(value = "航段中文")
    @JsonProperty("segmentCH")
    private String segmentCh;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;
}
