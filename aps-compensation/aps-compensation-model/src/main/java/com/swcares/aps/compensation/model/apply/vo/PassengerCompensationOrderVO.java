package com.swcares.aps.compensation.model.apply.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName：com.swcares.aps.apply.model.vo <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月18日 9:39 <br>
 * @version v1.0 <br>
 */

@ApiModel(value = "PassengerPaxInfoVO对象", description = "web代领旅客详情-乘机人信息")
@Data
@SecretInfoEntity
public class PassengerCompensationOrderVO {

    @ApiModelProperty(value = "旅客表主键id")
    private String paxId;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "事故单id")
    private String accidentId;

    @ApiModelProperty(value = "赔偿单ID")
    private String orderId;

    @ApiModelProperty(value = "事故单类型- 1.不正常航班 2异常行李 3超售")
    private String accidentType;

    @ApiModelProperty(value = "补偿票号")
    private String tktNo;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "婴儿标识(0或者1")
    private String withBaby;

    @ApiModelProperty(value = "补偿金额（后为总金额）")
    private String currentAmount;

    @ApiModelProperty(value = "申领旅客领取状态")
    private String applyPaxStatus;

    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "支付时间")
    private String payTime;

    @ApiModelProperty(value = "失败原因")
    private String errCodeDes;

    @ApiModelProperty(value = "快速支付（0 或者1快速）")
    private String quickPay;

    @ApiModelProperty(value = "旅客paxId")
    private String paxInfoId;

    @ApiModelProperty(value = "普通领取银联方式选择必填身份证号如果购票证件号不是身份证")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "乘机人姓名")
    private String paxName;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;


}
