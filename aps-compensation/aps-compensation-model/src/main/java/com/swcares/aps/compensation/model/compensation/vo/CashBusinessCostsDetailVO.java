package com.swcares.aps.compensation.model.compensation.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CashBusinessCostsDetailVO
 * @Description：现金补偿明细表-业务成本明细表返回的VO对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 31947
 * @Date： 2022/7/25 10:55
 * @version： v1.0
 */
@Data
@ApiModel(value = "CashBusinessCostsDetailVO对象",description = "业务成本明细表返回的VO对象")
@ExcelTarget("CashBusinessCostsDetailVO")
public class CashBusinessCostsDetailVO {
    @ApiModelProperty(value = "航班号")
    @Excel(name = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班时间")
    @Excel(name = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "起始航站")
    @Excel(name = "起始航站")
    private String orgCity;

    @ApiModelProperty(value = "到达航站")
    @Excel(name = "到达航站")
    private String dstCity;

    @ApiModelProperty(value = "服务航站")
    @Excel(name = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "地区类型：D-国内，I-国际")
    @Excel(name = "地区类型")
    private String regionType;

    @ApiModelProperty(value = "机型")
    @Excel(name = "机型")
    private String acType;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    @Excel(name = "补偿类型")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    @Excel(name = "补偿子类型")
    private String accidentSubType;

    @ApiModelProperty(value = "补偿单id")
    private Long orderId;
    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "补偿单单号")
    @Excel(name = "补偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "事故单号")
    @Excel(name = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "旅客姓名")
    @Excel(name = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "票号")
    @Excel(name = "票号")
    private String tktNo;

    @ApiModelProperty(value = "补偿金额")
    @Excel(name = "补偿金额")
    private Integer compensateAmount;

    @ApiModelProperty(value = "领取方式")
    @Excel(name = "领取方式")
    private String receiveWay;

    @ApiModelProperty(value = "领取状态：0:待领取，1:已领取，2:领取中,3:已逾期")
    @Excel(name = "领取状态")
    private String receiveStatus;

    @ApiModelProperty(value = "审核状态：1审核中2审核通过3审核不通过")
    @Excel(name = "审核状态")
    private String applyStatus;

    @ApiModelProperty(value = "户名")
    @Excel(name = "户名")
    private String applyUser;

    @ApiModelProperty(value = "开户银行")
    @Excel(name = "开户银行")
    private String openBankName;

    @ApiModelProperty(value = "账号")
    @Excel(name = "账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "申领单号")
    @Excel(name = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "支付方式：1:微信，2:支付宝，3:银联,4:卡劵,5:里程,6:积分,7:实物-箱包,8:现金")
    @Excel(name = "支付方式")
    private String payType;

    @ApiModelProperty(value = "支付状态:0:未支付，1:支付成功，2:支付失败")
    @Excel(name = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "支付时间")
    @Excel(name = "支付时间")
    private String payTime;

    @ApiModelProperty(value = "支付流水号")
    @Excel(name = "支付流水号")
    private String paySerialNumber;

    @ApiModelProperty(value = "异常行李业务复核状态 1已复核/0未复核")
    private String  luggageBusinessReview;
}
