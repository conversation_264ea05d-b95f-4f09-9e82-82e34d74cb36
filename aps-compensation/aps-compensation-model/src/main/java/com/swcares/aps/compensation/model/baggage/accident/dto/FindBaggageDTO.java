package com.swcares.aps.compensation.model.baggage.accident.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * <AUTHOR>
 * @title: findBaggageDTO
 * @projectName aps
 * @description: 查询异常行李事故单列表信息封装DTO
 * @date 2022/3/4 9:26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="查询异常行李事故单列表信息封装DTO", description="查询异常行李事故单列表信息封装DTO")
public class FindBaggageDTO extends PagedDTO {
    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "出发航站")
    private String pod;

    @ApiModelProperty(value = "到达航站")
    private String poa;

    @ApiModelProperty(value = "查询航班起始时间")
    private String startFlightDate ;

    @ApiModelProperty(value = "查询航班结束时间")
    private String endFlightDate;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "事故单状态")
    private String accidentStatus;

    @ApiModelProperty(value = "（前端不用传）分隔后的事故单状态数组 ")
    private String[] accidentStatusSplits;

    @ApiModelProperty(value = "事故类型")
    private String types;

    @ApiModelProperty(value = "（前端不用传）分隔后的事故类型数组 ")
    private String[] typesSplits;

    @ApiModelProperty(value = "旅客凭证类型（1身份证,2票号,3行李号）")
    private String idType;

    @ApiModelProperty(value = "凭证号")
    private String idNo;

//    @ApiModelProperty(value = "票号")
//    private String tktNo;
//
//    @ApiModelProperty(value = "行李号")
//    private String baggageNo;

    @ApiModelProperty(value = "创建人 (前端不用传)")
    private String createdBy;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;
    
    @ApiModelProperty(value = "归属航司")
    private List<String> belongAirline;
    
    @ApiModelProperty(value = "原因类型（1航司，2非航司）") 
    private String reasonType;

//    public String getTktNo(){
//        if("2".equals(idType)){
//            return this.idNo;
//        }
//        return null;
//    }
//
//    public String getIdNo(){
//        if("1".equals(idType)){
//            return this.idNo;
//        }
//        return null;
//    }
//
//    public String getBaggageNo(){
//        if("3".equals(idType)){
//            return this.idNo;
//        }
//        return null;
//    }
}
