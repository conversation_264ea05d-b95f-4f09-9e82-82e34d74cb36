package com.swcares.aps.compensation.model.privilege.dto;

import cn.hutool.core.bean.BeanUtil;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeChangeHistory;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeChangeHistoryDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/11 15:57
 * @Version 1.0
 */
@Data
@ApiModel(value="BusinessPrivilegeChangeHistoryDTO", description="业务权限变更历史DTO")
public class BusinessPrivilegeChangeHistoryDTO {
    @ApiModelProperty(value = "业务类型编码")
    private String businessTypeCode;

    @ApiModelProperty(value = "变更类型")
    private String updateType;

    @ApiModelProperty(value = "变更记录")
    private String changeLog;

    @ApiModelProperty(value = "变更时间")
    private LocalDateTime changeTime;

    @ApiModelProperty(value = "变更人员工姓名")
    private String updaterName;

    @ApiModelProperty(value = "变更人员工工号")
    private String updaterJobNumber;

    public static BusinessPrivilegeChangeHistoryDTO fromEntity(AirlineBusinessPrivilegeChangeHistory entity)
    {
        BusinessPrivilegeChangeHistoryDTO dto = new BusinessPrivilegeChangeHistoryDTO();
        BeanUtil.copyProperties(entity, dto);

        dto.setChangeLog("旧值:" + entity.getOldValue() + "\n新值:" + entity.getNewValue());

        return dto;
    }

    public static  List<BusinessPrivilegeChangeHistoryDTO> fromEntityList(List<AirlineBusinessPrivilegeChangeHistory> entityList)
    {
        List<BusinessPrivilegeChangeHistoryDTO> dtoList = new ArrayList<>();
        entityList.forEach(entity -> {
            dtoList.add(fromEntity(entity));
        });

        return dtoList;
    }
}
