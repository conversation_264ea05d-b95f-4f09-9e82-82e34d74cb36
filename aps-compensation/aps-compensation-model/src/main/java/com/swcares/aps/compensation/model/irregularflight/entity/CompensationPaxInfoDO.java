package com.swcares.aps.compensation.model.irregularflight.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.entity.CompensationPaxInfo <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationPaxInfoDO对象", description="")
@TableName(value = "compensation_pax_info")
@SecretInfoEntity
public class CompensationPaxInfoDO {

    private static final long serialVersionUID = 1L;
    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    private Long tenantId;

    @ApiModelProperty(value = "旅客ID")
    @NotNull
    private String paxId;

    @ApiModelProperty(value = "旅客姓名")
    @NotNull
    private String paxName;

    @ApiModelProperty(value = "证件类型")
    @NotNull
    private String idType;

    @ApiModelProperty(value = "证件号码")
    @NotNull
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "性别 M(男) F(女)C(儿童)")
    @NotNull
    private String sex;

    @ApiModelProperty(value = "手机号")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "航段")
    @NotNull
    private String segment;

    @ApiModelProperty(value = "航段中文")
    private String segmentCh;

    @ApiModelProperty(value = "出发航站")
    private String orgCityAirp;

    @ApiModelProperty(value = "到达航站")
    private String dstCityAirp;

    @ApiModelProperty(value = "旅客状态")
    private String paxStatus;

    @ApiModelProperty(value = "是否取消0否1是")
    private String isCancel;

    @ApiModelProperty(value = "主仓位")
    @NotNull
    private String mainClass;

    @ApiModelProperty(value = "子仓位")
    private String subClass;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "行李号，多个逗号分隔")
    private String pkgNo;

    @ApiModelProperty(value = "行李重量")
    private String pkgWeight;

    @ApiModelProperty(value = "逾重行李重量")
    private String pkgOverWeight;

    @ApiModelProperty(value = "是否携带婴儿0否1是")
    private String withBaby;

    @ApiModelProperty(value = "携带婴儿此字段必填")
    private String babyPaxName;

    @ApiModelProperty(value = "购票时间")
    private LocalDateTime tktIssueDate;

    @ApiModelProperty(value = "当前领取金额")
    private BigDecimal currentAmount;

    @ApiModelProperty(value = "领取渠道(0普通,1代领,2现金)")
    private String receiveChannel;

    @ApiModelProperty(value = "领取方式（0微信，1银联，2现金）")
    private String receiveWay;

    @ApiModelProperty(value = "领取时间时分秒")
    private LocalDateTime receiveTime;

    @ApiModelProperty(value = "领取状态(0未领取,1已领取,2处理中3已逾期)")
    private String receiveStatus;

    @ApiModelProperty(value = "旅客申领资格开关(默认0有资格，1取消领取资格=冻结)")
    private String switchOff;

    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    @ApiModelProperty(value = "儿童旅客标识0否1是")
    private String isChild;

    @ApiModelProperty(value = "PNR票号")
    private String pnr;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;

}
