package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value = "BaggageTransportDetailVO", description = "运输单详情")
public class BaggageTransportDetailVO {

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人电话1")
    private String receiverPhone1;

    @ApiModelProperty(value = "收件人电话2")
    private String receiverPhone2;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city;

    @ApiModelProperty(value = "区县")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "运输公司")
    private String transportCompany;

    @ApiModelProperty(value = "运输单号")
    private String transportNumber;

    @ApiModelProperty(value = "运输日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date transportDate;

    @ApiModelProperty(value = "运输总费用")
    private Double transportCost;

    @ApiModelProperty(value = "异常行李code码")
    private String baggageCode;

    @ApiModelProperty(value = "是否是合并运输")
    private Boolean mergedTransport;

    @ApiModelProperty(value = "审核状态：1-审核中，2-审核通过，3-审核不通过")
    private String auditStatus;

    @ApiModelProperty(value = "关联的事故单信息")
    private List<BaggageAccidentInfoDO> accidentInfos;

    @ApiModelProperty(value = "更新人")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "录入机场")
    private String inputAirport;

    @ApiModelProperty(value = "审核标识")
    private Boolean auditFlag;

    @ApiModelProperty(value = "任务ID")
    private String taskId;

    @ApiModelProperty(value = "行李总件数")
    private Integer totalBaggageCount;

    @ApiModelProperty(value = "运输机场")
    private String transportAirport;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "机场审核人")
    private String airportAuditor;

    @ApiModelProperty(value = "航司审核人")
    private String airlineAuditor;
}