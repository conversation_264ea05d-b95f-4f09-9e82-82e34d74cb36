package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@ApiModel(value="CompensationConfigDTO对象", description="赔偿单配置表dto")
public class CompensationConfigDTO implements BaseDTO {
    @ApiModelProperty(value = "是否同步到当前租户的其他授权航司，Y是 N否")
    private String toSync;

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "配置主类型")
    private String type;

    @ApiModelProperty(value = "配置子类型")
    private String subType;

    @ApiModelProperty(value = "配置值，这里为具体的拒绝原因",required = true)
    @NotNull
    private String value;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "修改人")
    private String updatedBy;

    @ApiModelProperty(value = "创建时间")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "修改时间")
//    @JsonFormat(pattern="yyyy-MM-dd HH:mm")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "机场或航司编码")
    private String airCode;

    @ApiModelProperty(value = "编码中文")
    private String codeCn;
}
