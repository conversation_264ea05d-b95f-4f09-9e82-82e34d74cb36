package com.swcares.aps.compensation.model.privilege.dto;

import cn.hutool.core.bean.BeanUtil;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.baseframe.common.base.entity.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeDTO
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/11 15:30
 * @Version 1.0
 */
@Data
@ApiModel(value = "BusinessPrivilegeDTO", description = "业务权限DTO")
public class BusinessPrivilegeDetailDTO implements BaseDTO {
    @ApiModelProperty(value = "业务权限ID")
    private Long id;

    //授权方客户编码
    @ApiModelProperty(value = "授权方客户编码")
    private String grantorCode;

    //授权方客户类型
    @ApiModelProperty(value = "授权方客户类型")
    private String grantorCategory;

    //授权方客户名称
    @ApiModelProperty(value = "授权方客户名称")
    private String grantorName;

    //被授权方客户编码
    @ApiModelProperty(value = "被授权方客户编码")
    private String recipientCode;

    //被授权方客户类型
    @ApiModelProperty(value = "被授权方客户类型")
    private String recipientCategory;

    //被授权方客户名称
    @ApiModelProperty(value = "被授权方客户名称")
    private String recipientName;

    //停用标识（1：停用；0：启用）
    @ApiModelProperty(value = "停用标识")
    private boolean disabled;

    //备注
    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "业务权限明细")
    private List<BusinessPrivilegeItemDTO> businessPrivilegeItemList;


    public static BusinessPrivilegeDetailDTO fromEntity(AirlineBusinessPrivilege entity){
        if (entity == null){
            return null;
        }
        BusinessPrivilegeDetailDTO dto = new BusinessPrivilegeDetailDTO();
        BeanUtil.copyProperties(entity, dto);

        return dto;
    }

    public AirlineBusinessPrivilege toEntity(){
        AirlineBusinessPrivilege entity = new AirlineBusinessPrivilege();
        BeanUtil.copyProperties(this, entity);

        return entity;
    }

    public static List<BusinessPrivilegeDetailDTO> fromEntityList(List<AirlineBusinessPrivilege> entityList){
        if (entityList == null || entityList.isEmpty()){
            return new ArrayList<BusinessPrivilegeDetailDTO>();
        }
        return entityList.stream().map(BusinessPrivilegeDetailDTO::fromEntity).collect(Collectors.toList());
    }

    public static List<AirlineBusinessPrivilege> toEntityList(List<BusinessPrivilegeDetailDTO> dtoList){
        if (dtoList == null || dtoList.isEmpty()){
            return new ArrayList<AirlineBusinessPrivilege>();
        }
        return dtoList.stream().map(BusinessPrivilegeDetailDTO::toEntity).collect(Collectors.toList());
    }
}
