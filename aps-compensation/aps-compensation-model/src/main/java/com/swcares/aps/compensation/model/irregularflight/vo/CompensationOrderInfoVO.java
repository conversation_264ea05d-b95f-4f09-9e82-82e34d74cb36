package com.swcares.aps.compensation.model.irregularflight.vo;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.checkerframework.checker.units.qual.A;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.vo.CompensationOrderInfoVO <br>
 * Description：赔偿单信息返回展示对象 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value="CompensationOrderInfoVO对象", description="赔偿单信息")
public class CompensationOrderInfoVO{

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "赔偿单ID")
    private Long id;

    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "补偿单号")
    private String orderNo;

    @ApiModelProperty(value = "补偿方式 1现金，2虚拟，3实物")
    private String compensateType;

    @ApiModelProperty(value = "补偿状态：0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过")
    private String status;

    @ApiModelProperty(value = "补偿单来源 机场/航司")
    private String source;

    @ApiModelProperty(value = "补偿航站也==服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "所选航段")
    private String choiceSegment;

    @ApiModelProperty(value = "所选航段中文")
    private String choiceSegmentCh;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "创建人信息")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date createdTime;

    @ApiModelProperty(value = "事故单类型==补偿类型- 1.不正常航班 2异常行李 3超售 4 旅客投诉")
    private String accidentType;

    @ApiModelProperty(value = "补偿子类型:(不正常航班：1延误，2取消，3备降，4返航，5补班）")
    private String fcType;

    @ApiModelProperty(value = "计划执行人数（已领取人数）")
    private String planCarryOutNum;

    @ApiModelProperty(value = "实际执行人数（后为总人数）")
    private String actualCarryOutNum;

    @ApiModelProperty(value = "冻结人数")
    private String frozenNum;

    @ApiModelProperty(value = "实际执行金额==补偿金额（前为已领取金额）")
    private String planCompensateMoney;

    @ApiModelProperty(value = "计划执行金额==补偿金额（后为总金额）")
    private String actualCompensateMoney;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

}
