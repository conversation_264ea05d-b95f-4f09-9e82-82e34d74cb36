package com.swcares.aps.compensation.model.overbook.vo;

import com.alibaba.fastjson.annotation.JSONField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @ClassName：OverBookH5AccidentDetailsVO
 * @Description：H5超售详情-事故单信息
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 15:44
 * @version： v1.0
 */
@Data
public class OverBookAccidentDetailsVO {


    @ApiModelProperty(value = "事故单ID")
    private Long accidentId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "事故类型：1改签，2退票")
    private String type;

    @ApiModelProperty(value = "事故说明")
    private String accidentReason;

    @ApiModelProperty(value = "创建人")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private String createdTime;

    @ApiModelProperty(value = "作废人")
    private String toVoidBy;

    @ApiModelProperty(value = "作废时间")
    private String toVoidTime;

    @ApiModelProperty(value = "附件")
    private String imgUrl;



    //原航班信息
    @ApiModelProperty(value = "原航班ID")
    @NotNull
    private String flightId;

    @ApiModelProperty(value = "原航班-航班号")
    private String flightNo;

    @ApiModelProperty(value = "原航班-航班日期")
    private String flightDate;

    @ApiModelProperty(value = "原航班-航段-三字码格式")
    private String segment;

    @ApiModelProperty(value = "原航班-航段-中文格式")
    private String segmentCh;

    @ApiModelProperty(value = "原航班-计划起飞")
    private String std;


    @ApiModelProperty(value = "经济舱全价")
    private String fullEconomyFare;

    //改签航班信息
    @ApiModelProperty(value = "改签航班号")
    private String overBookFlightNo;

    @ApiModelProperty(value = "改签航班日期")
    private String overBookFlightDate;

    @ApiModelProperty(value = "改签航班的计划起飞时间")
    private String overBookStd;


}
