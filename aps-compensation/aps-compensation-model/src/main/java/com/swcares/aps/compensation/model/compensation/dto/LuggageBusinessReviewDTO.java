package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value="LuggageBusinessReviewDTO", description="异常行李-复核操作请求对象")
public class LuggageBusinessReviewDTO {
    @ApiModelProperty(value = "赔偿单id")
    private Long orderId;
    @ApiModelProperty(value = "旅客id")
    private String paxId;
}
