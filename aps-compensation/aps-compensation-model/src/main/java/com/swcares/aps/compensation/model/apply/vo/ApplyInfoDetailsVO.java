package com.swcares.aps.compensation.model.apply.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ApplyInfoDetailsVO <br>
 * Package：com.swcares.aps.apply.model.vo <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月10日 9:30 <br>
 * @version v1.0 <br>
 */
@ApiModel(value = "ApplyInfoDetailsVO对象", description = "申领详情信息")
@Data
public class ApplyInfoDetailsVO {

    @ApiModelProperty(value = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "收款方式")
    private String getMoneyWay;

    @ApiModelProperty(value = "收款账号")
    private String getMoneyAccount;

    @ApiModelProperty(value = "申领时间")
    private String createdTime;

    @ApiModelProperty(value = "到账时间")
    private String receieveTime;

    @ApiModelProperty(value = "航班日期")
    private String flightData;

    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "起飞时间")
    private String std;

    @ApiModelProperty(value = "到达时间")
    private String sta;

    @ApiModelProperty(value = "中文航段")
    private String segmentCh;

    @ApiModelProperty(value = "三字码航段")
    private String segment;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;

}
