package com.swcares.aps.compensation.model.complaint.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.sql.Timestamp;
import java.util.Date;

@Data
@TableName("PASSENGER_ACCIDENT_INFO")
@SecretInfoEntity
public class PassengerAccidentInfoEntity {

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "事故单主键id")
    private Long accidentPrimaryId;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "主舱")
    private String mainClass;

    @ApiModelProperty(value = "子舱")
    private String subClass;

    @ApiModelProperty(value = "值机")
    private String checkIn;

    @ApiModelProperty(value = "PNR")
    private String pnr;

    @ApiModelProperty(value = "是否儿童")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿")
    private String isBaby;

    @ApiModelProperty(value = "是否取消")
    private String isCancel;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date cancelTime;

    @ApiModelProperty(value = "购票时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private Date tktIssueTime;

}
