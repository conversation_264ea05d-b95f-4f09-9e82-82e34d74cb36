package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageTransportInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * ClassName： FindBaggageDetailFinnalVO
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 18:04
 * @version v1.0
 */
@Data
@ApiModel(value="异常行李事故单详情信息VO", description="异常行李事故单详情信息VO")
public class BaggageDetailFinalVO {
    //------------------事故信息-----------------------
    @ApiModelProperty(value = "异常行李事故单详情信息下的事故信息")
    private BaggageDetailAccident accidentDetail;
    //------------------航班信息-----------------------
    @ApiModelProperty(value = "异常行李事故单详情信息下的航班信息")
    private BaggageDetailFightVO fightDetail;
    //------------------旅客信息-----------------------
    @ApiModelProperty(value = "异常行李事故单详情信息下的旅客信息")
    private BaggageDetailPaxVO paxDetail;
    //------------------行李信息-----------------------
    @ApiModelProperty(value = "异常行李事故单详情信息下的行李信息")
    private BaggageDetailBaggageVO baggageDetail;

    //------------------多收或少收异常行李事故单信息-----------------------
    @ApiModelProperty(value = "该事故单下关联的少收或多收的异常行李事故单信息列表")
    private List<MatchResultVO> matches;

    //------------------快递信息信息-----------------------
    @ApiModelProperty(value = "异常行李事故单下关联快递单信息列表")
    private List<BaggageExpressVO> expresses;

    //------------------补偿单信息-----------------------
    @ApiModelProperty(value = "异常行李事故单下关联补偿单信息列表")
    private List<BaggageCompensationVO> compensations;

    @ApiModelProperty(value = "运输单信息")
    private BaggageTransportInfoDTO transportInfoDTO;

    @ApiModelProperty(value = "运输单绑定的事故单信息")
    List<BaggageAccidentInfoDO> bandingBaggageAccidentInfoDOS;
}
