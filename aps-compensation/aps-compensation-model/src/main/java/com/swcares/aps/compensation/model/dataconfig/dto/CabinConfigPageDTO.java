package com.swcares.aps.compensation.model.dataconfig.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CabinConfigPageDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/1/8 14:21
 * @version： v1.0
 */
@Data
public class CabinConfigPageDTO  extends PagedDTO {
    @ApiModelProperty(value = "航司二字码")
    private String airlineCode;

    @ApiModelProperty(value = "航司简称")
    private String airlineName;
}
