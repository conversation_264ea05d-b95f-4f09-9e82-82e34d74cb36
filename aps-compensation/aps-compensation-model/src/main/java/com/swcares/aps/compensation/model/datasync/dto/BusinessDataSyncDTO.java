package com.swcares.aps.compensation.model.datasync.dto;

import com.swcares.aps.compensation.model.apply.entity.ApplyAuditDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.*;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.irregularflight.entity.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.rools.entity.CompensationRuleConfig;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @ClassName：BusinessDataDTO
 * @Description：业务数据推送|同步的dto ：包含了事故单、补偿单实体对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/7/11 13:36
 * @version： v1.0
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessDataSyncDTO implements Serializable {
    private static final long serialVersionUID = 1L;
    @ApiModelProperty( value = "业务类型", required = true)
    private String businessType;

    @ApiModelProperty( value = "数据类型", required = true )
    private String dataType;

    @ApiModelProperty(value = "接收方客户代码(航司二字码)", required = true)
    private String receiverCustomer;

    @ApiModelProperty(value = "发送方客户代码(机场三字码)", required = true)
    private String senderCustomer;

    @ApiModelProperty(value = "接收方客户id(航司租户id)")
    private Long receiverCustomerId;

    @ApiModelProperty(value = "不正常航班-事故单对象")
    FlightAccidentInfoDO flightAccidentInfoDO;

    //-----------------异常行李
    @ApiModelProperty(value = "异常行李-事故单对象")
    BaggageAccidentInfoDO baggageAccidentInfoDO;

    @ApiModelProperty(value = "异常行李-运输单对象")
    BaggageTransportInfoDO baggageTransportInfoDO;

    @ApiModelProperty(value = "异常行李-运输单关联对象")
    List<BaggageTransportAccidentRelDO> baggageTransportAccidentRelDOS;

    @ApiModelProperty(value = "异常行李-箱包事故单DO对象")
    FindBaggageDO findBaggageDO;

    @ApiModelProperty(value = "异常行李-赔偿_快递信息")
    CompensationExpressInfoDO compensationExpressInfoDO;

    //-----------------航班超售
    @ApiModelProperty(value = "航班超售-事故单对象")
    OverBookAccidentInfoDO overBookAccidentInfoDO;

    //-----------------旅客投诉
    @ApiModelProperty(value = "旅客投诉-事故单对象")
    ComplaintAccidentInfoEntity complaintAccidentInfoEntity;

    @ApiModelProperty(value = "旅客投诉-事故单旅客对象")
    List<PassengerAccidentInfoEntity> passengerAccidentInfoEntity;

    //-----------------补偿单对象
    @ApiModelProperty(value = "补偿单对象")
    CompensationOrderInfoDO compensationOrderInfoDO;
    @ApiModelProperty(value = "补偿单-旅客对象")
    List<CompensationPaxInfoDO> compensationPaxInfoDO;
    @ApiModelProperty(value = "补偿单-航班对象")
    CompensationFlightInfoDO compensationFlightInfoDO;
    @ApiModelProperty(value = "补偿单-补偿规则对象")
    List<CompensationRuleRecordDO> compensationRuleRecordDO;
    @ApiModelProperty(value = "补偿单-赔偿实物信息")
    List<CompensationMaterialInfoDO> compensationMaterialInfoDO;

    @ApiModelProperty(value = "流程同步信息")
    StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO;

    //-----------------申领单对象
    @ApiModelProperty(value = "申领单对象")
    ApplyOrderDO applyOrderDO;
    @ApiModelProperty(value = "申领单-申领旅客信息")
    List<ApplyPaxDO> applyPaxDOList;
    @ApiModelProperty(value = "申领单-申领审核信息")
    List<ApplyAuditDO> applyAuditDOS;
    @ApiModelProperty(value = "补偿单-申领支付记录信息")
    List<PayRecordDO> payRecordDOS;

    @ApiModelProperty(value = "业务-图片信息")
    BusinessImgDO businessImgDO;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class BusinessImgDO{

        @ApiModelProperty(value = "异常行李-旅客相关图片")
        private List<String> paxImgs;

        @ApiModelProperty(value = "异常行李-单据相关图片")
        private List<String> voucherImgs;

        @ApiModelProperty(value = "异常行李-行李相关图片")
        private List<String> baggageImgs;

        @ApiModelProperty(value = "【旅客投诉||航班超售】附件")
        private List<String> fileUrl;
        // 添加更多的映射
    }

    //-----------------配置类数据
    @ApiModelProperty(value = "补偿规则配置对象")
    List<CompensationRuleConfig> compensationRuleConfigs;

    List<PassengerCategoryConfigureDepository> passengerCategoryConfigureDepositories;

    List<DataConfigDO> dataConfigDos;

    @ApiModelProperty(value = "舱位配置信息")
    CabinConfigDO cabinConfigDO;



}
