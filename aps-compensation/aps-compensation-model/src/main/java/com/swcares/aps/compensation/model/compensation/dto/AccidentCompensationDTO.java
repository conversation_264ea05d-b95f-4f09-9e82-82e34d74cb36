package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationRuleRecordDO;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName：AccidentCompensationDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 10:28
 * @version： v1.0
 */
@Data
public class AccidentCompensationDTO extends CompensationOrderInfoDO{

    @ApiModelProperty(value = "是否自定义现金")
    private String isCustom;
    /**
     * 补偿单旅客信息
     */
    @ApiModelProperty(value = "补偿单下旅客列表信息")
    private List<CompensationPaxInfoDO> compensationPaxInfo;

    /**
     * 补偿单航班信息
     */
    @ApiModelProperty(value = "补偿单下航班信息")
    private CompensationFlightInfoDO compensationFlightInfo;

    /**
     * 赔偿的实物
     */
    @ApiModelProperty(value = "补偿单下实物列表信息")
    private List<CompensationMaterialInfoDO> compensationMaterialInfo;
    private ComplaintAccidentInfoEntity complaintAccidentInfoEntity;
    /**
     * 赔偿规则
     */
    private List<CompensationRuleRecordDO> compensationRuleRecordinfo;

    @ApiModelProperty(value = "流程同步信息;可以为空")
    private StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO;

    public static AccidentCompensationDTO of(CompensationOrderInfoDO compensationOrderInfoDO,
                                             List<CompensationPaxInfoDO> compensationPaxInfo,
                                             CompensationFlightInfoDO compensationFlightInfo,
                                             List<CompensationMaterialInfoDO> compensationMaterialInfo,
                                             List<CompensationRuleRecordDO> compensationRuleRecordDO,
                                             ComplaintAccidentInfoEntity complaintAccidentInfoEntity){
        AccidentCompensationDTO accidentCompensationDTO = ObjectUtils.copyBean(compensationOrderInfoDO, AccidentCompensationDTO.class);
        accidentCompensationDTO.setCompensationPaxInfo(compensationPaxInfo);
        accidentCompensationDTO.setCompensationFlightInfo(compensationFlightInfo);
        accidentCompensationDTO.setCompensationMaterialInfo(compensationMaterialInfo);
        accidentCompensationDTO.setCompensationRuleRecordinfo(compensationRuleRecordDO);
        accidentCompensationDTO.setComplaintAccidentInfoEntity(complaintAccidentInfoEntity);

        return accidentCompensationDTO;
    }

    public static AccidentCompensationDTO of(CompensationOrderInfoDO compensationOrderInfoDO,
                                             List<CompensationPaxInfoDO> compensationPaxInfo,
                                             CompensationFlightInfoDO compensationFlightInfo,
                                             List<CompensationMaterialInfoDO> compensationMaterialInfo, ComplaintAccidentInfoEntity complaintAccidentInfoEntity){
        return of(compensationOrderInfoDO,compensationPaxInfo,compensationFlightInfo,compensationMaterialInfo,null,complaintAccidentInfoEntity);
    }

    public static AccidentCompensationDTO of(CompensationOrderInfoDO compensationOrderInfoDO,List<CompensationPaxInfoDO> compensationPaxInfo){
        return of(compensationOrderInfoDO,compensationPaxInfo,null,null,null);
    }

    public static AccidentCompensationDTO of(CompensationOrderInfoDO compensationOrderInfoDO){
        return of(compensationOrderInfoDO,null,null,null,null);
    }

    public static AccidentCompensationDTO of(CompensationOrderInfoDO compensationOrderInfoDO,List<CompensationPaxInfoDO> compensationPaxInfo,List<CompensationRuleRecordDO> compensationRuleRecordDO){
        return of(compensationOrderInfoDO,compensationPaxInfo,null,null,compensationRuleRecordDO,null);
    }
}
