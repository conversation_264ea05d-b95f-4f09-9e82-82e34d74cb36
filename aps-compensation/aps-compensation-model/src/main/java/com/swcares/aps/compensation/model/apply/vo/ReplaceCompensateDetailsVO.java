package com.swcares.aps.compensation.model.apply.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ReplaceCompensateDetailsVO <br>
 * Package：com.swcares.aps.apply.model.vo <br> 
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2022年 01月10日 13:35 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "ReplaceCompensateDetailsVO对象", description = "代领旅客领取详情对象")
@SecretInfoEntity
public class ReplaceCompensateDetailsVO {

    @ApiModelProperty(value = "补偿姓名")
    private String paxName;

    @ApiModelProperty(value = "身份证")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "证照ID")
    private String photoId;

    @ApiModelProperty(value = "补偿金额")
    private String currentAmount;

    @ApiModelProperty(value = "补偿类型")
    private String accidentType;

    @ApiModelProperty(value = "舱位描述(公务舱，经济舱)")
    private String classDes;

    @ApiModelProperty(value = "主仓位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "婴儿标识(0或者1")
    private String withBaby;

    @ApiModelProperty(value = "成人标识(0或者1)")
    private String isChild;

    @ApiModelProperty(value = "领取状态")
    private String receiveStatus;

    @ApiModelProperty(value = "失败原因")
    private String errCodeDes;

    @ApiModelProperty(value = "行李号")
    private String pkgNo;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;

    @ApiModelProperty(value ="支付类型的接口版本")
    private String payTypeApiVersion;

}
