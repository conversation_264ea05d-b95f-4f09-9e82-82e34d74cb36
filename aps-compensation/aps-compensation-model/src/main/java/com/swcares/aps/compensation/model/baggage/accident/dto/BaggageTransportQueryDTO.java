package com.swcares.aps.compensation.model.baggage.accident.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

import com.swcares.baseframe.common.base.entity.PagedDTO;

@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="BaggageTransportQueryDTO", description="异常行李运输单查询DTO")
public class BaggageTransportQueryDTO extends PagedDTO {
    
    @ApiModelProperty(value = "收件人姓名", notes = "支持模糊搜索")
    private String receiverName;
    
    @ApiModelProperty(value = "运输开始日期")
    private String transportStartDate;
    
    @ApiModelProperty(value = "运输结束日期")
    private String transportEndDate;
    
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    
    @ApiModelProperty(value = "航班开始日期")
    private String flightStartDate;
    
    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;
    
    @ApiModelProperty(value = "事故单号", notes = "精准检索")
    private String accidentNo;
    
    @ApiModelProperty(value = "事故类型", notes = "下拉单选")
    private String accidentType;
    
    @ApiModelProperty(value = "归属航司", notes = "下拉多选")
    private List<String> belongAirline;
    
    @ApiModelProperty(value = "补偿航站", notes = "下拉单选")
    private String compensationStation;
    
    @ApiModelProperty(value = "快递单号", notes = "精准检索")
    private String transportNumber;
    
    @ApiModelProperty(value = "录入人", notes = "模糊检索")
    private String createdBy;
    
    @ApiModelProperty(value = "录入开始时间")
    private String createdStartTime;

    @ApiModelProperty(value = "录入人", notes = "模糊检索")
    private String lastUpdatedBy;

    @ApiModelProperty(value = "录入结束时间")
    private String createdEndTime;

    @ApiModelProperty(value = "运输公司", notes = "下拉多选并且精准搜索")
    private List<String> transportCompany;
    
    @ApiModelProperty(value = "审核状态：1-审核中，2-审核通过，3-审核不通过", notes = "下拉多选")
    private List<String> auditStatus;

    @ApiModelProperty(value = "工号",hidden = true)
    private String jobNumber;

    @ApiModelProperty(value = "运输单ID", notes = "精准检索")
    private String id;

    @ApiModelProperty(value = "复核", notes = "1已复核 0未复核")
    private String reviewed;
} 