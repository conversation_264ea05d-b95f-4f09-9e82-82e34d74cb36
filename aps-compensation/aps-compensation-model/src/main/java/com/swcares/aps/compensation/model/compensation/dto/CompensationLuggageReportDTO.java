package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CompensationLuggageReportDTO
 * @Description：异常行李明细表-业务成本明细表
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/7/25 10:24
 * @version： v1.0
 */
@Data
@ApiModel(value = "CompensationLuggageReportDTO对象",description = "业务成本明细表DTO对象")
public class CompensationLuggageReportDTO extends PagedDTO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "起始航站")
    private List<String> orgCity;

    @ApiModelProperty(value = "到达航站")
    private List<String> dstCity;

    @ApiModelProperty(value = "行李号")
    private String bagTag;

    @ApiModelProperty(value = "事故单编号")
    private String accidentNo;

}