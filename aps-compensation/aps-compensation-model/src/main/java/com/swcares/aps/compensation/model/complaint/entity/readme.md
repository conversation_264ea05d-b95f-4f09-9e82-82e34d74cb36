- code info 
```sql
-- 旅客投诉事故信息表
CREATE TABLE "COMPENSATION_TRAVELSKY_DEV"."COMPLAINT_ACCIDENT_INFO" 
   (	"ID" NUMBER(20,0) NOT NULL ENABLE, 
	"ACCIDENT_ID" VARCHAR2(255) NOT NULL ENABLE, 
	"ACCIDENT_STATUS" VARCHAR2(4) NOT NULL ENABLE, 
	"ACCIDENT_SOURCE" VARCHAR2(2) NOT NULL ENABLE, 
	"BELONG_AIRLINE" VARCHAR2(10) NOT NULL ENABLE, 
	"ACCIDENT_TYPE" VARCHAR2(2) NOT NULL ENABLE, 
	"ACCIDENT_SUB_TYPE" VARCHAR2(2) NOT NULL ENABLE, 
	"REASON_TYPE" VARCHAR2(2) NOT NULL ENABLE, 
	"FLIGHT_DATE" TIMESTAMP (6) NOT NULL ENABLE, 
	"FLIGHT_NO" VARCHAR2(7) NOT NULL ENABLE, 
	"SEGMENT" VARCHAR2(20) NOT NULL ENABLE, 
	"CREATED_USER" VARCHAR2(30) NOT NULL ENABLE, 
	"CREATED_TIME" TIMESTAMP (6) NOT NULL ENABLE, 
	"INVALID_USER" VARCHAR2(10), 
	"INVALID_TIME" TIMESTAMP (6), 
	"CONTACT_INFO" VARCHAR2(20) NOT NULL ENABLE, 
	"COMPLAINT_DEP" VARCHAR2(50) NOT NULL ENABLE, 
	"COMPLAINT_CHANNEL" VARCHAR2(10) NOT NULL ENABLE, 
	"FILES_URL" VARCHAR2(255), 
	"ACCIDENT_DES" VARCHAR2(255) NOT NULL ENABLE, 
	 PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV";
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ID  IS '主键ID，用于关联旅客信息表';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_ID  IS '旅客投诉事故单单号';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_STATUS  IS '事故单状态';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_SOURCE  IS '事故单来源';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_CHANNEL  IS '事故单渠道';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.BELONG_AIRLINE  IS '归属航司';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_TYPE  IS '事故单类型：旅客投诉';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_SUB_TYPE  IS '事故类型：地面服务、客舱服务、客票退改、
		 客票购买、航班异常、行李异常、其他';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.REASON_TYPE  IS '原因类型';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.FLIGHT_DATE  IS '航班日期';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.FLIGHT_NO  IS '航班号';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.SEGMENT  IS '选择航段';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.CREATED_USER  IS '创建人';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.CREATED_TIME  IS '创建时间';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.INVALID_USER  IS '作废人';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.INVALID_TIME  IS '作废时间';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.CONTACT_INFO  IS '旅客电话';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.COMPLAINT_DEP  IS '投诉部门';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.COMPLAINT_CHANNEL  IS '投诉渠道';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.FILES_URL  IS '附件';
COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.ACCIDENT_DES  IS '投诉事故描述';
COMMENT ON TABLE COMPLAINT_ACCIDENT_INFO is '旅客投诉事故信息表';

```

```sql
-- 旅客投诉事故旅客信息表
  CREATE TABLE "COMPENSATION_TRAVELSKY_DEV"."PASSENGER_ACCIDENT_INFO" 
   (	"ID" NUMBER(20,0) NOT NULL ENABLE, 
	"ACCIDENT_PRIMARY_ID" NUMBER(20,0) NOT NULL ENABLE, 
	"PAX_NAME" VARCHAR2(10) NOT NULL ENABLE, 
	"ID_TYPE" VARCHAR2(2) NOT NULL ENABLE, 
	"TKT_NO" VARCHAR2(20) NOT NULL ENABLE, 
	"ID_NO" VARCHAR2(50) NOT NULL ENABLE, 
	"SEGMENT" VARCHAR2(20) NOT NULL ENABLE, 
	"MAIN_CLASS" VARCHAR2(2), 
	"SUB_CLASS" VARCHAR2(2), 
	"CHECK_IN" VARCHAR2(5), 
	"IS_BABY" VARCHAR2(2) NOT NULL ENABLE, 
	"IS_CHILD" VARCHAR2(2) NOT NULL ENABLE, 
	"PNR" VARCHAR2(10) NOT NULL ENABLE, 
	"IS_CANCEL" VARCHAR2(1), 
	"CANCEL_TIME" TIMESTAMP (6), 
	"TKT_ISSUE_TIME" TIMESTAMP (6), 
	 PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV";
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.ID  IS '主键ID';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.ACCIDENT_PRIMARY_ID  IS '事故单主键ID';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.PAX_NAME  IS '旅客姓名';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.ID_TYPE  IS '证件类型';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.TKT_NO  IS '客票号';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.ID_NO  IS '证件号码';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.SEGMENT  IS '航段';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.MAIN_CLASS  IS '主舱';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.SUB_CLASS  IS '子舱';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.CHECKIN  IS '值机';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.IS_BABY  IS '携带婴儿';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.IS_CHILD  IS '是否儿童';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.PNR  IS 'PNR';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.IS_CANCEL  IS '是否取消';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.CANCEL_TIME  IS '取消时间';
 COMMENT ON COLUMN PASSENGER_ACCIDENT_INFO.TKT_ISSUE_TIME  IS '购票时间';
 COMMENT ON TABLE PASSENGER_ACCIDENT_INFO is '旅客投诉事故旅客信息表';

```
```sql
CREATE TABLE "COMPENSATION_TRAVELSKY_DEV"."COMPENSATION_ORDER_INFO" 
   (	"ID" NUMBER(20,0) NOT NULL ENABLE, 
	"ACCIDENT_ID" NUMBER(20,0) NOT NULL ENABLE, 
	"ACCIDENT_NO" VARCHAR2(32) NOT NULL ENABLE, 
	"ORDER_NO" VARCHAR2(32) NOT NULL ENABLE, 
	"COMPENSATE_TYPE" VARCHAR2(32) NOT NULL ENABLE, 
	"SERVICE_CITY" VARCHAR2(32) NOT NULL ENABLE, 
	"CHOICE_SEGMENT" VARCHAR2(100) NOT NULL ENABLE, 
	"CHOICE_SEGMENT_CH" VARCHAR2(200), 
	"REMARK" VARCHAR2(600), 
	"ENSURE_TYPE" VARCHAR2(4), 
	"STATUS" VARCHAR2(32) NOT NULL ENABLE, 
	"FLIGHT_ID" VARCHAR2(64), 
	"EXPIRY_DATE" TIMESTAMP (6), 
	"SUM_MONEY" NUMBER, 
	"FLIGHT_NO" VARCHAR2(7) NOT NULL ENABLE, 
	"FLIGHT_DATE" VARCHAR2(10) NOT NULL ENABLE, 
	"ISS_USER" VARCHAR2(20), 
	"CLOSE_USER" VARCHAR2(64), 
	"CLOSE_TIME" TIMESTAMP (6), 
	"ACCIDENT_TYPE" VARCHAR2(32), 
	"ACCIDENT_SUB_TYPE" VARCHAR2(32), 
	"FULL_SEGMENT" VARCHAR2(64), 
	"CREATED_BY" VARCHAR2(64), 
	"CREATED_TIME" TIMESTAMP (6), 
	"UPDATED_BY" VARCHAR2(64), 
	"UPDATED_TIME" TIMESTAMP (6), 
	"RESPONSIBLE_UNIT" VARCHAR2(16), 
	 CONSTRAINT "COMPENSATION_ORDER_INFO_PK" PRIMARY KEY ("ID")
  USING INDEX PCTFREE 10 INITRANS 2 MAXTRANS 255 COMPUTE STATISTICS 
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV"  ENABLE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV"
```
```sql
CREATE TABLE "COMPENSATION_TRAVELSKY_DEV"."COMPENSATION_PAX_INFO" 
   (	"ID" NUMBER(20,0), 
	"PAX_ID" VARCHAR2(64), 
	"PAX_NAME" VARCHAR2(100) NOT NULL ENABLE, 
	"ID_TYPE" VARCHAR2(30) NOT NULL ENABLE, 
	"ID_NO" VARCHAR2(200) NOT NULL ENABLE, 
	"SEX" VARCHAR2(4), 
	"TELEPHONE" NVARCHAR2(1000), 
	"SEGMENT" VARCHAR2(50), 
	"SEGMENT_CH" VARCHAR2(100), 
	"ORG_CITY_AIRP" VARCHAR2(3) NOT NULL ENABLE, 
	"DST_CITY_AIRP" VARCHAR2(3) NOT NULL ENABLE, 
	"PAX_STATUS" VARCHAR2(10), 
	"IS_CANCEL" VARCHAR2(2), 
	"MAIN_CLASS" VARCHAR2(3), 
	"SUB_CLASS" VARCHAR2(3), 
	"TKT_NO" VARCHAR2(100), 
	"PKG_NO" VARCHAR2(500), 
	"PKG_WEIGHT" VARCHAR2(10), 
	"PKG_OVER_WEIGHT" VARCHAR2(10), 
	"WITH_BABY" VARCHAR2(2) DEFAULT '0', 
	"BABY_PAX_NAME" VARCHAR2(32), 
	"TKT_ISSUE_DATE" TIMESTAMP (6), 
	"CURRENT_AMOUNT" NUMBER, 
	"RECEIVE_CHANNEL" VARCHAR2(4) DEFAULT '0', 
	"RECEIVE_WAY" VARCHAR2(4) DEFAULT '0', 
	"RECEIVE_TIME" TIMESTAMP (6), 
	"RECEIVE_STATUS" VARCHAR2(4) DEFAULT '0', 
	"ORDER_ID" NUMBER(20,0) NOT NULL ENABLE, 
	"IS_CHILD" VARCHAR2(2) DEFAULT '0', 
	"PNR" VARCHAR2(32), 
	"SWITCH_OFF" VARCHAR2(10) DEFAULT '0' NOT NULL ENABLE, 
	"CREATED_BY" VARCHAR2(64), 
	"CREATED_TIME" TIMESTAMP (6), 
	"UPDATED_BY" VARCHAR2(64), 
	"UPDATED_TIME" TIMESTAMP (6), 
	"CANCEL_TIME" DATE
   ) SEGMENT CREATION IMMEDIATE 
  PCTFREE 10 PCTUSED 40 INITRANS 1 MAXTRANS 255 
 NOCOMPRESS LOGGING
  STORAGE(INITIAL 65536 NEXT 1048576 MINEXTENTS 1 MAXEXTENTS 2147483645
  PCTINCREASE 0 FREELISTS 1 FREELIST GROUPS 1
  BUFFER_POOL DEFAULT FLASH_CACHE DEFAULT CELL_FLASH_CACHE DEFAULT)
  TABLESPACE "COMPENSATION_TRAVELSKY_DEV"
```

-- 表结构变动sql
```sql
 ALTER TABLE COMPENSATION_ORDER_INFO ADD SOURCE VARCHAR2(10);
 COMMENT ON COLUMN COMPENSATION_ORDER_INFO.SOURCE IS '补偿单来源';

 alter table COMPLAINT_ACCIDENT_INFO add SEGMENT_CH VARCHAR2(200)
 COMMENT ON COLUMN COMPLAINT_ACCIDENT_INFO.SEGMENT_CH IS '航段中文';

 alter table COMPENSATION_ORDER_INFO add BELONG_AIRLINE VARCHAR2(10)
 COMMENT ON COLUMN COMPENSATION_ORDER_INFO.BELONG_AIRLINE IS '归属航司';

alter table COMPENSATION_ORDER_INFO add RELEASE_TIME TIMESTAMP
COMMENT ON COLUMN COMPENSATION_ORDER_INFO.RELEASE_TIME IS '发放时间'

alter table COMPENSATION_ORDER_INFO add RELEASE VARCHAR2(50)
COMMENT ON COLUMN COMPENSATION_ORDER_INFO.RELEASE IS '发放人'

ALTER TABLE COMPENSATION_ORDER_INFO add COMPENSATE_SUB_TYPE VARCHAR2(10)
COMMENT ON COLUMN COMPENSATION_ORDER_INFO.COMPENSATE_SUB_TYPE IS '补偿子方式'

ALTER TABLE COMPENSATION_ORDER_INFO add COMMODITY_ID VARCHAR2(100)
COMMENT ON COLUMN COMPENSATION_ORDER_INFO.COMMODITY_ID IS '物品id'

ALTER TABLE FLIGHT_ACCIDENT_INFO ADD BELONG_AIRLINE VARCHAR2(10)
COMMENT ON COLUMN FLIGHT_ACCIDENT_INFO.BELONG_AIRLINE is '归属航司'

ALTER TABLE FLIGHT_ACCIDENT_INFO ADD ACCIDENT_SOURCE VARCHAR2(2)
COMMENT ON COLUMN FLIGHT_ACCIDENT_INFO.ACCIDENT_SOURCE is '事故单来源'

ALTER TABLE FLIGHT_ACCIDENT_INFO ADD ATD VARCHAR2(100)
COMMENT ON COLUMN FLIGHT_ACCIDENT_INFO.ATD is '实际起飞时间'

alter table passenger_accident_info MODIFY ID_TYPE VARCHAR2(10)

alter table COMPENSATION_ORDER_INFO add PRESET_AMOUNT NUMBER
COMMENT ON COLUMN COMPENSATION_ORDER_INFO.PRESET_AMOUNT IS '预设金额'
```

- 函数迁移
```sql
-- 获取城市名称函数
CREATE OR REPLACE FUNCTION        "GET_CITY_NAME"(city_code IN NCHAR) 
RETURN NVARCHAR2
AS CITY_NAME NVARCHAR2(50);
BEGIN
	SELECT AIRPORT_NAME INTO CITY_NAME FROM BD_AIRPORT_INFO WHERE AIRPORT_3CODE = city_code;
	RETURN CITY_NAME;
END;
```

- 基础数据添加
```sql
-- 事故单类型：旅客投诉  accident_type
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('528', 'accident_type', '1', '1', NULL, '1', NULL, NULL, '0', 'swcares', TO_DATE('2024-05-21 11:14:03', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:14:11', 'SYYYY-MM-DD HH24:MI:SS'), '0', '4', '旅客投诉', '旅客投诉', '4'); 

-- 事故类型：complaint_sub_type
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21001', '旅客投诉事故单子类型', 'complaint_sub_type', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '旅客投诉事故案子类型', '0');

-- 地面服务，客舱服务，客票退改，客票购票，航班异常，行李异常，其他
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100001', 'complaint_sub_type', '1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '地面服务', '地面服务', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100002', 'complaint_sub_type', '2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '客舱服务', '客舱服务', '2');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100003', 'complaint_sub_type', '3', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '3', '客票退改', '客票退改', '3');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100004', 'complaint_sub_type', '4', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '4', '客票购票', '客票购票', '4');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100005', 'complaint_sub_type', '5', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '5', '航班异常', '航班异常', '5');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100006', 'complaint_sub_type', '6', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '6', '行李异常', '行李异常', '6');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100007', 'complaint_sub_type', '7', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '7', '其他','其他', '7');


-- 旅客投诉渠道 地面，客舱，热线 complaint_channel
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21002', '投诉渠道', 'complaint_channel', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '投诉渠道', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100008', 'complaint_channel','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '地面', '地面', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100009', 'complaint_channel','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '客舱', '客舱', '2');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100010', 'complaint_channel','3', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '3', '热线', '热线', '3');

-- 旅客投诉原因类型 航司，非航司 complaint_reason_type
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21003', '投诉原因类型', 'complaint_reason_type', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '投诉原因类型', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100011', 'complaint_reason_type','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '航司', '航司', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100012', 'complaint_reason_type','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'),'0', '2', '非航司', '非航司', '2');

-- 补偿方式 现金  payment_method
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21004', '补偿方式', 'payment_method', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '补偿方式', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100013', 'payment_method','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '现金', '现金', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100024', 'payment_method','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'),'0', '2', '虚拟', '虚拟', '2');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100025', 'payment_method','3', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'),'0', '3', '实物', '实物', '3');

-- 补偿金额方式 微信 银联 数字人民币 payment_amount_type 
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21005', '补偿金额方式', 'payment_amount_type', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '补偿金额方式', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100014', 'payment_amount_type','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '微信', '微信', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100015', 'payment_amount_type','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '银联', '银联', '2');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100016', 'payment_amount_type','3', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '3', '数字人民币', '数字人民币', '3');

-- 事故单来源 机场 航司 accident_source
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21006', '事故单来源', 'accident_source', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '事故单来源', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100017', 'accident_source','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '机场', '机场', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100018', 'accident_source','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '航司', '航司', '2');

-- 补偿金额 标准补偿金额 统一自定义金额 逐人自定义金额 payment_amount
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21007', '补偿金额', 'payment_amount', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '补偿金额', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100019', 'payment_amount','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '标准补偿金额', '标准补偿金额', '1');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100020', 'payment_amount','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '统一自定义金额', '统一自定义金额', '2');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100021', 'payment_amount','3', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '3', '逐人自定义金额', '逐人自定义金额', '3');

-- 旅客申领资格开关(默认0有资格，1取消领取资格=冻结)
INSERT INTO "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('21008', '旅客申领资格开关', 'passenger_apply_status', '1', '1', 'swacres', TO_DATE('2024-05-21 11:43:49', 'SYYYY-MM-DD HH24:MI:SS'), 'swcares', TO_DATE('2024-05-21 11:43:55', 'SYYYY-MM-DD HH24:MI:SS'), '旅客申领资格开关', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100022', 'passenger_apply_status','1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '0', '否', '否', '0');
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('2100023', 'passenger_apply_status','2', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '是', '是', '1');

-- receive_status 领取状态

-- compensate_status 补偿单状态

-- 实物补偿子方式
INSERT INTO "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('32100013', 'compensation_commodity_type', '1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '1', '箱包', '箱包', '1');

INSERT INTO  "SYS_DICTIONARY_DATA" ("ID", "DICT_TYPE", "SORT_NUM", "IS_SYS", "IS_DEFAULT", "STATUS", "DESCRIPTION", "REMARK", "CORP_CODE", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "DELETED", "DICT_ITEM", "ITEM_VALUE", "DICT_LABEL", "DICT_VALUE") VALUES ('32100014', 'compensation_commodity_type', '1', '0', NULL, '1', NULL, NULL, '0', '1', TO_DATE('2024-05-21 14:06:43', 'SYYYY-MM-DD HH24:MI:SS'), '1', TO_DATE('2024-05-21 14:06:46', 'SYYYY-MM-DD HH24:MI:SS'), '0', '2', '纪念品', '纪念品', '2');

INSERT INTO  "SYS_DICTIONARY_TYPE" ("ID", "DICT_NAME", "DICT_TYPE", "IS_SYS", "STATUS", "CREATED_BY", "CREATED_TIME", "UPDATED_BY", "UPDATED_TIME", "REMARK", "DELETED") VALUES ('1793893908011016193', '实物-补偿子方式', 'compensation_commodity_type', '0', '1', 'admin', TO_DATE('2024-05-24 14:40:59', 'SYYYY-MM-DD HH24:MI:SS'), 'liujian', TO_DATE('2024-07-11 15:13:45', 'SYYYY-MM-DD HH24:MI:SS'), '实物-补偿子方式 1.箱包 2.纪念品', '0');


```

- 流程引擎
```sql
INSERT INTO WORKFLOW_MODEL_CODE_INFO (ID,BUSINESS,PROJECT,MODEL_CODE) VALUES('5','complaint','compensation-impl','complaint')

INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('56', 'complaint', 'compensation-impl', 'complaint', 'submitter', 'submitter');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('57', 'complaint', 'compensation-impl', 'complaint', 'Activity_1', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('58', 'complaint', 'compensation-impl', 'complaint', 'Activity_2', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('59', 'complaint', 'compensation-impl', 'complaint', 'Activity_3', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('60', 'complaint', 'compensation-impl', 'complaint', 'Activity_4', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('61', 'complaint', 'compensation-impl', 'complaint', 'Activity_5', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('62', 'complaint', 'compensation-impl', 'complaint', 'Activity_6', 'common');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('63', 'complaint', 'compensation-impl', 'complaint', 'end', 'end');
INSERT INTO "COMPENSATION_TRAVELSKY_DIAOYAN"."WORKFLOW_MODE_NODE_INFO" ("ID", "BUSINESS", "PROJECT", "MODEL_CODE", "NODE_KEY", "NODE_BUSINESS_TYPE") VALUES ('64', 'complaint', 'compensation-impl', 'complaint', 'DEFAULT', 'common');

```

- 协同中心数据推送
```sql
create table "DATA_PUSH_RECORD" ( "ID" NUMBER(20) NOT NULL,
"TENANT_ID" NUMBER(20) NOT NULL,
"TENANT_CODE" VARCHAR2(50) NOT NULL,
"BUSINESS_ID" NUMBER(20) NOT NULL,
"BUSINESS_TYPE" VARCHAR2(50) NOT NULL,
"PUSH_TYPE" VARCHAR2(50) NOT NULL,
"PUSH_STATUS" VARCHAR2(50) NOT NULL,
"CREATED_TIME" DATE NOT NULL)

```