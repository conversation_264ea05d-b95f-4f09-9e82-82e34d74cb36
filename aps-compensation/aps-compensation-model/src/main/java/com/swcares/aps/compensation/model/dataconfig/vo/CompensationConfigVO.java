package com.swcares.aps.compensation.model.dataconfig.vo;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName：DamageTypeDO <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/8 <br>
 * @version v1.0 <br>
 */
@Data
@ApiModel(value = "CompensationConfigVO对象",description = "异常行李损坏类型VO")
public class CompensationConfigVO {

	@ApiModelProperty(value = "主键id")
	private String id;

	@ApiModelProperty(value = "配置主类型")
	private String type;

	@ApiModelProperty(value = "配置子类型")
	private String subType;

	@ApiModelProperty(value = "配置值")
	private String value;

	@ApiModelProperty(value = "配置描述")
	private String description;

	@ApiModelProperty(value = "类型标签")
	private String typeLabel;

	@ApiModelProperty(value = "是否系统配置")
	private String isSys;

	@ApiModelProperty(value = "数据项是否逻辑删除")
	private Long deleted;

	@ApiModelProperty(value = "机场或航司编码")
	private String airCode;

	@ApiModelProperty(value = "编码中文")
	private String codeCn;

	@ApiModelProperty(value = "创建人")
	private String createdBy;

	@ApiModelProperty(value = "修改人")
	private String updatedBy;

	@ApiModelProperty(value = "创建时间")
	private LocalDateTime createdTime;

	@ApiModelProperty(value = "修改时间")
	private LocalDateTime updatedTime;

}
