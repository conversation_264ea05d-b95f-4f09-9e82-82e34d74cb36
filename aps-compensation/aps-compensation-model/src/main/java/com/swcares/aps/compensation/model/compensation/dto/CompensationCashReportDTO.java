package com.swcares.aps.compensation.model.compensation.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @ClassName：CashBusinessCostsPagedDto
 * @Description：现金补偿明细表-业务成本明细表
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/7/25 10:24
 * @version： v1.0
 */
@Data
@ApiModel(value = "CompensationCashReportDTO对象",description = "业务成本明细表DTO对象")
public class CompensationCashReportDTO extends PagedDTO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班起始日期")
    private String flightStartDate;

    @ApiModelProperty(value = "航班结束日期")
    private String flightEndDate;

    @ApiModelProperty(value = "起始航站")
    private List<String> orgCity;

    @ApiModelProperty(value = "到达航站")
    private List<String> dstCity;

    @ApiModelProperty(value = "赔偿单单号")
    private String orderNo;

    @ApiModelProperty(value = "事故单编号")
    private String accidentNo;

    @ApiModelProperty(value = "服务航站")
    private List<String> serviceCity;

    @ApiModelProperty(value = "补偿类型-1不正常航班，2异常行李，3超售")
    private String accidentType;

    @ApiModelProperty(value = "支付起始时间")
    private String payStartDate;

    @ApiModelProperty(value = "支付截止时间")
    private String payEndDate;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "申领单号")
    private String applyCode;

    @ApiModelProperty(value = "领取方式(本人0,代领1,协助2)")
    private String receiveWay;

    @ApiModelProperty(value = "领取状态：0:待领取，1:已领取，2:领取中,3:已逾期")
    private List<String> receiveStatus;

    @ApiModelProperty(value = "支付方式：1:微信，2:支付宝，3:银联,4:卡劵,5:里程,6:积分,7:实物-箱包,8:现金")
    private List<String> payType;

    @ApiModelProperty(value = "支付状态:0:未支付，1:支付成功，2:支付失败")
    private List<String> payStatus;

    @ApiModelProperty(value = "补偿子类型-1延误，2取消，3备降，4返航，5补班; 21破损,22少收,23多收,24内件缺失,25丢失")
    private List<String> accidentSubType;

    @ApiModelProperty(value = "1审核中,2审核不通过,3审核通过")
    private List<String> applyStatus;
}
