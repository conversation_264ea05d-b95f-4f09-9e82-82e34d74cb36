package com.swcares.aps.compensation.model.baggage.accident.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * ClassName： FindBaggageDetailpaxVO
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 18:10
 * @version v1.0
 */

@Data
@SecretInfoEntity
@ApiModel(value="异常行李事故单详情下的旅客信息VO", description="异常行李事故单详情下的旅客信息VO")
public class BaggageDetailPaxVO {
    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客英文姓名")
    private String paxEnName;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客票号")
    private String tktNo;

    @ApiModelProperty(value = "旅客手机号")
    @SecretValue
    private String phone;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "补偿次数")
    private Integer frequency;

    @ApiModelProperty(value = "主舱位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "值机")
    private String checkIn;

    @ApiModelProperty(value = "取消")
    private String isCancel;

    @ApiModelProperty(value = "取消时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String cancelTime;

    @ApiModelProperty(value = "购票日期")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone = "GMT+8")
    private String tktIssueTime;

    @ApiModelProperty(value = "pnr")
    private String pnr;

    @ApiModelProperty(value = "是否儿童")
    private String isChild;

    @ApiModelProperty(value = "是否携带婴儿")
    private String isBaby;
}
