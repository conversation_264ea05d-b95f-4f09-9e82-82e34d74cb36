package com.swcares.aps.compensation.model.apply.vo;

import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：CompensateDetaulsVO <br>
 * Package：com.swcares.aps.apply.model.vo <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月10日 13:34 <br>
 * @version v1.0 <br>
 */
@ApiModel(value = "CompensateDetailsVO对象", description = "补偿单详情信息")
@SecretInfoEntity
public class CompensateDetailsVO {

    @ApiModelProperty(value = "申领人姓名")
    private String applyUser;

    @ApiModelProperty(value = "身份证")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "票号")
    private String tktNo;

    @ApiModelProperty(value = "行李号")
    private String pkgNo;

    @ApiModelProperty(value = "手机号")
    @SecretValue
    private String telephone;

    @ApiModelProperty(value = "舱位描述(公务舱，经济舱)")
    private String classDes;

    @ApiModelProperty(value = "婴儿标识(0或者1")
    private String withBaby;

    @ApiModelProperty(value = "成人标识(0或者1)")
    private String isChild;

    @ApiModelProperty(value = "补偿金额")
    private String currentAmount;

    @ApiModelProperty(value = "补偿类型")
    private String accidentType;

    @ApiModelProperty(value = "支付状态")
    private String payStatus;

    @ApiModelProperty(value = "失败原因")
    private String errCodeDes;

    @ApiModelProperty(value = "补偿姓名")
    private String paxName;

    @ApiModelProperty(value = "主仓位")
    private String mainClass;

    @ApiModelProperty(value = "子舱位")
    private String subClass;

    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;

    @ApiModelProperty(value ="支付类型的接口版本")
    private String payTypeApiVersion;

    public String getIsChild() {
        return isChild;
    }

    public void setIsChild(String isChild) {
        this.isChild = isChild;
    }

    public String getClassDes() {
        return classDes;
    }

    public void setClassDes(String classDes) {
        this.classDes = classDes;
    }

    public String getApplyUser() {
        return applyUser;
    }

    public void setApplyUser(String applyUser) {
        this.applyUser = applyUser;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getIdNo() {
        return idNo;
    }

    public void setIdNo(String idNo) {
        this.idNo = idNo;
    }

    public String getCurrentAmount() {
        return currentAmount;
    }

    public void setCurrentAmount(String currentAmount) {
        this.currentAmount = currentAmount;
    }

    public String getWithBaby() {
        return withBaby;
    }

    public void setWithBaby(String withBaby) {
        this.withBaby = withBaby;
    }

    public String getTktNo() {
        return tktNo;
    }

    public void setTktNo(String tktNo) {
        this.tktNo = tktNo;
    }

    public String getMainClass() {
        return mainClass;
    }

    public void setMainClass(String mainClass) {
        this.mainClass = mainClass;
    }

    public String getSubClass() {
        return subClass;
    }

    public void setSubClass(String subClass) {
        this.subClass = subClass;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getPayStatus() {
        return payStatus;
    }

    public void setPayStatus(String payStatus) {
        this.payStatus = payStatus;
    }

    public String getErrCodeDes() {
        return errCodeDes;
    }

    public void setErrCodeDes(String errCodeDes) {
        this.errCodeDes = errCodeDes;
    }

    public String getPaxName() {
        return paxName;
    }

    public void setPaxName(String paxName) {
        this.paxName = paxName;
    }

    public String getPkgNo() {
        return pkgNo;
    }

    public void setPkgNo(String pkgNo) {
        this.pkgNo = pkgNo;
    }

    public String getPaxReceiveState() {
        return paxReceiveState;
    }

    public CompensateDetailsVO setPaxReceiveState(String paxReceiveState) {
        this.paxReceiveState = paxReceiveState;
        return this;
    }

    public String getPayTypeApiVersion() {
        return payTypeApiVersion;
    }

    public CompensateDetailsVO setPayTypeApiVersion(String payTypeApiVersion) {
        this.payTypeApiVersion = payTypeApiVersion;
        return this;
    }

    @Override
    public String toString() {
        return "CompensateDetailsVO{" +
                "applyUser='" + applyUser + '\'' +
                ", idNo='" + idNo + '\'' +
                ", tktNo='" + tktNo + '\'' +
                ", pkgNo='" + pkgNo + '\'' +
                ", telephone='" + telephone + '\'' +
                ", classDes='" + classDes + '\'' +
                ", withBaby='" + withBaby + '\'' +
                ", isChild='" + isChild + '\'' +
                ", currentAmount='" + currentAmount + '\'' +
                ", accidentType='" + accidentType + '\'' +
                ", payStatus='" + payStatus + '\'' +
                ", errCodeDes='" + errCodeDes + '\'' +
                ", paxName='" + paxName + '\'' +
                ", mainClass='" + mainClass + '\'' +
                ", subClass='" + subClass + '\'' +
                ", paxReceiveState='" + paxReceiveState + '\'' +
                ", payTypeApiVersion='" + payTypeApiVersion + '\'' +
                '}';
    }
}
