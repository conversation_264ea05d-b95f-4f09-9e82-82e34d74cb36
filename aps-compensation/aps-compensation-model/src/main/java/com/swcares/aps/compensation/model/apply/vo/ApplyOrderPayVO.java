package com.swcares.aps.compensation.model.apply.vo;

import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @ClassName：ApplyOrderPayVO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/7/26 9:18
 * @version： v1.0
 */
@Data
public class ApplyOrderPayVO extends ApplyOrderDO {

    @ApiModelProperty("支付订单号（支付记录表主键）")
    private Long recordId;

    @ApiModelProperty(value = "申领单旅客id对应apply_pax表paxInfoId字段")
    private Long applyPaxId;

    @ApiModelProperty(value = "赔偿单ID")
    private Long orderId;

    @ApiModelProperty("转账金额（用于支付转账）")
    private BigDecimal transAmount;

    @ApiModelProperty(value = "支付状态(0未支付,1支付成功,2支付失败,3支付中)")
    private String payStatus;

    @ApiModelProperty(value = "转账返回时间")
    private Date payReturnTime;

    @ApiModelProperty(value = "转账请求时间")
    private Date payStartTime;

    @ApiModelProperty(value = "转账日期")
    private String transDate;

    @ApiModelProperty(value = "支付类型的接口版本")
    private String payTypeApiVersion;


}
