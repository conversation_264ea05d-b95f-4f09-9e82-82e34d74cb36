package com.swcares.aps.compensation.model.apply.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ReceivingRecordVO <br>
 * Package：com.swcares.aps.apply.model.vo <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月07日 13:33 <br>
 * @version v1.0 <br>
 */
@ApiModel(value = "ReceivingRecordVO对象", description = "申领记录查询返回信息")
public class ReceivingRecordVO {
    @ApiModelProperty(value = "航班号")
    private String flightNo;
    @ApiModelProperty(value = "航班日期")
    private String flightDate;
    @ApiModelProperty(value = "航段")
    private String segmentCh;
    @ApiModelProperty(value = "补偿类型")
    private String accidentType;
    @ApiModelProperty(value = "金额")
    private String applyAmount;
    @ApiModelProperty(value = "申领单状态")
    private String status;
    @ApiModelProperty(value = "申领旅客领取状态")
    private String applyPaxStatus;
    @ApiModelProperty(value = "申领单id")
    private String recordId;
    @ApiModelProperty(value = "申领金旅客账户收款状态 默认空、WAITE_CONFIRM 待确认、CONFIRMING 确认中、RECEIVED 收款成功 、FAIL 收款失败")
    private String paxReceiveState;

    public String getApplyPaxStatus() {
        return applyPaxStatus;
    }

    public void setApplyPaxStatus(String applyPaxStatus) {
        this.applyPaxStatus = applyPaxStatus;
    }

    @ApiModelProperty(value = "审核状态")
    private String applyStatus;

    public String getApplyStatus() {
        return applyStatus;
    }

    public void setApplyStatus(String applyStatus) {
        this.applyStatus = applyStatus;
    }

    public String getRecordId() {
        return recordId;
    }

    public void setRecordId(String recordId) {
        this.recordId = recordId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }
    public String getFlightNo() {
        return flightNo;
    }

    public void setFlightNo(String flightNo) {
        this.flightNo = flightNo;
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate;
    }

    public String getSegmentCh() {
        return segmentCh;
    }

    public void setSegmentCh(String segmentCh) {
        this.segmentCh = segmentCh;
    }

    public String getAccidentType() {
        return accidentType;
    }

    public void setAccidentType(String accidentType) {
        this.accidentType = accidentType;
    }

    public String getApplyAmount() {
        return applyAmount;
    }

    public void setApplyAmount(String applyAmount) {
        this.applyAmount = applyAmount;
    }

    public String getPaxReceiveState() {
        return paxReceiveState;
    }

    public void setPaxReceiveState(String paxReceiveState) {
        this.paxReceiveState = paxReceiveState;
    }
}
