package com.swcares.aps.compensation.model.compensation.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelTarget;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @ClassName：CashBusinessCostsDetailVO
 * @Description：异常行李补偿明细表-业务成本明细表返回的VO对象
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 31947
 * @Date： 2022/7/25 10:55
 * @version： v1.0
 */
@Data
@ApiModel(value = "LuggageBusinessCostsDetailVO对象",description = "业务成本明细表返回的VO对象")
@ExcelTarget("LuggageBusinessCostsDetailVO")
public class LuggageBusinessCostsDetailVO {

    @ApiModelProperty(value = "事故单号")
    @Excel(name = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "航班号")
    @Excel(name = "航班号")
    private String flightNo;

    @ApiModelProperty(value = "航班时间")
    @Excel(name = "航班日期")
    private String flightDate;

    @ApiModelProperty(value = "飞机号")
    @Excel(name = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "航段")
    private String segment;

    @ApiModelProperty(value = "航段中文")
    @Excel(name = "航段")
    private String segmentCh;

    @ApiModelProperty(value = "行李号 ")
    @Excel(name = "行李号")
    private String bagTag;

    @ApiModelProperty(value = "服务航站")
    @Excel(name = "服务航站")
    private String serviceCity;

    @ApiModelProperty(value = "箱包赔偿收入")
    @Excel(name = "箱包赔偿收入")
    private String luggageCompensationIncome ;

    @ApiModelProperty(value = "运送行李费用")
    @Excel(name = "运送行李费用")
    private String luggageTransportationFee;

    @ApiModelProperty(value = "现金赔偿费用")
    @Excel(name = "现金赔偿费用")
    private String cashCompensationFee ;

    @ApiModelProperty(value = "临时生活费用")
    @Excel(name = "临时生活费用")
    private String tempFee;

    @ApiModelProperty(value = "合计")
    @Excel(name = "合计")
    private String totalCost;



}
