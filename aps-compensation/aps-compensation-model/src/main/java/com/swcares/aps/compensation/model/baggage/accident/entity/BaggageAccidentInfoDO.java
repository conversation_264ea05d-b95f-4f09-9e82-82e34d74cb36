package com.swcares.aps.compensation.model.baggage.accident.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * ClassName：com.swcares.baggage.entity.AccidentInfo <br>
 * Description：异常行李事故单信息表 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-03-03 <br>
 * @version v1.0 <br>
 */
@Data
@SecretInfoEntity
@TableName("baggage_accident_info")
@ApiModel(value="BaggageAccidentInfo对象", description="异常行李事故单信息表")
public class BaggageAccidentInfoDO{

    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.ASSIGN_ID)
    @ApiModelProperty(value = "主键id")
    private Long id;

    private Long tenantId;

    @ApiModelProperty(value = "事故单号")
    private String accidentNo;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "归属航司-中文简称")
    private String belongAirlineAbbr;

    @ApiModelProperty(value = "原因类型（1航司，2非航司）")
    private String reasonType;

    @ApiModelProperty(value = "事故来源 1机场/2航司")
    private String accidentSource;

    @ApiModelProperty(value = "旅客数据输入的来源 SYSTEM（系统查询）MANUAL_INPUT（人工输入），")
    private String inputSource;

    @ApiModelProperty(value = "事故单状态（0草稿,1待处理,2处理中,3已结案,4作废）")
    private String accidentStatus;

    @ApiModelProperty(value = "事故原因")
    private String accidentReason;

    @ApiModelProperty(value = "异常行李类型（21破损,22少收,23多收,24内件缺少,25丢失）")
    private String type;

    @ApiModelProperty(value = "服务航站")
    private String serveSegment;

    @ApiModelProperty(value = "旅客航班ID")
    private String paxFlightId;

    @ApiModelProperty(value = "旅客航班号")
    private String paxFlightNo;

    @ApiModelProperty(value = "旅客航班日期")
    private String paxFlightDate;

    @ApiModelProperty(value = "旅客航班航段")
    private String paxSegment;

    @ApiModelProperty(value = "到达航站")
    private String poa;

    @ApiModelProperty(value = "起飞航站")
    private String pod;

    @ApiModelProperty(value = "计划起飞时间")
    private String std;

    @ApiModelProperty(value = "计划到达时间")
    private String sta;

    @ApiModelProperty(value = "预计起飞时间")
    private String etd;

    @ApiModelProperty(value = "行李号")
    private String baggageNo;

    @ApiModelProperty(value = "行李航班号")
    private String baggageFlightNo;

    @ApiModelProperty(value = "行李航班日期")
    private String baggageFlightDate;

    @ApiModelProperty(value = "行李航线")
    private String baggageSegment;

    @ApiModelProperty(value = "经停航站")
    private String stopoverSegment;

    @ApiModelProperty(value = "行李规格")
    private String baggageType;

    @ApiModelProperty(value = "逾重行李号")
    private String overweightNo;

    @ApiModelProperty(value = "破损类型（1撕裂,2划痕,3压碎,4污染,5凹坑,6洞穿,7其他）")
    private String damageType;

    @ApiModelProperty(value = "破损程度（1全部,2大面积,3小面积）")
    private String damageDegree;

    @ApiModelProperty(value = "破损部位（1箱体,2把手,3锁,4拉杆,5轮子,6其他）")
    private String damagePart;

    @ApiModelProperty(value = "行李品牌")
    private String baggageBrand;

    @ApiModelProperty(value = "丢失重量(KG)")
    private Integer lostWeight;

    @ApiModelProperty(value = "少收类型（1常规,2自定义）")
    private String lostType;

    @ApiModelProperty(value = "事故提醒时间设置-自然日（常规类型下，默认21个自然日）")
    private Integer reminderTime;

    @ApiModelProperty(value = "超时提醒的时间")
    private LocalDateTime overTime;

    @ApiModelProperty(value = "丢失数量（件）")
    private Integer lostAmount;

    @ApiModelProperty(value = "丢失申报类型（1全部申报,2部分申报,3未申报）")
    private String declarationType;

    @ApiModelProperty(value = "旅客相关图片关联附件资源信息表ID")
    @TableField("COLLECT_IDENTITY_PAX_IMGS")
    private String collectIdentityPaxPhotos;

    @ApiModelProperty(value = "单据相关图片关联附件资源信息表ID")
    @TableField("COLLECT_IDENTITY_VOUCHER_IMGS")
    private String collectIdentityVoucherPhotos;

    @ApiModelProperty(value = "行李相关图片关联附件资源信息表ID")
    @TableField("COLLECT_IDENTITY_BAGGAGE_IMGS")
    private String collectIdentityBaggagePhotos;

    @ApiModelProperty(value = "旅客姓名")
    private String paxName;

    @ApiModelProperty(value = "旅客id")
    private String paxId;

    @ApiModelProperty(value = "旅客证件类型")
    private String idType;

    @ApiModelProperty(value = "旅客证件号")
    @SecretValue
    private String idNo;

    @ApiModelProperty(value = "旅客票号")
    private String tktNo;

    @ApiModelProperty(value = "旅客手机号")
    @SecretValue
    private String phone;

    @ApiModelProperty(value = "作废人ID")
    private String tovoidBy;

    @ApiModelProperty(value = "作废时间")
    private LocalDateTime tovoidTime;

    @ApiModelProperty(value = "结案人")
    private String closeUser;

    @ApiModelProperty(value = "结案时间")
    private LocalDateTime closeTime;

    @ApiModelProperty(value = "绑定的多/少收事故单号")
    private String matchAccidentNo;

    @ApiModelProperty(value = "确认丢失时间")
    private LocalDateTime confirmLostTime;

    @ApiModelProperty(value = "已丢失时间（天/自然日）")
    private String lostTime;

    @ApiModelProperty(value = "备降航站")
    private String alternateTerminal;

    @ApiModelProperty(value = "异常行李事故单状态草稿转为待处理的时间")
    private LocalDateTime todoTime;

    @ApiModelProperty(value = "创建者")
    private String createdBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createdTime;

    @ApiModelProperty(value = "更新者")
    private String updatedBy;

    @ApiModelProperty(value = "更新时间")
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "少收超时提醒发送状态，0-已发送，1-发送失败")
    private String overMsgStatus;

    @ApiModelProperty(value = "飞机号")
    private String planeCode;

    @ApiModelProperty(value = "机型")
    private String fltCode;
}
