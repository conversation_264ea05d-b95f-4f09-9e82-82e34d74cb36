package com.swcares.aps.compensation.model.compensation.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * @ClassName：CompensationAddCommandDTO
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 13:31
 * @version： v1.0
 */
@Data
public class CompensationAddAndEditCommandDTO {

    /**
     * 事故单信息
     */
    @ApiModelProperty(value = "事故单信息")
    @NotNull
    private AddAccidentCompensationDTO accident;

    /**
     * 航班事故信息
     */
    @ApiModelProperty(value = "航班事故信息")
    private AddFlightCompensationDTO flight;


    /***
     * 旅客ids
     */
    @ApiModelProperty(value = "旅客ids")
    private List<String> paxIds;


    @ApiModelProperty(value = "实物补偿总金额")
    private BigDecimal compensationAllMoney;

    @ApiModelProperty(value = "补偿方式(补偿方式 1现金，2虚拟，3实物)")
    private String compensateType;

    @ApiModelProperty(value = "临时补偿")
    private Boolean temporaryCompensation;

    @ApiModelProperty(value = "补偿子方式(实物、纪念品)")
    private String compensateSubType;

    @ApiModelProperty(value = "补偿标准类型：1标准补偿金额 2自定义")
    private String compensateStandard;

    @ApiModelProperty(value = "物品id")
    private String commodityId;
    /***
     * 补偿单原因
     */
    @ApiModelProperty(value = "补偿单原因")
    @NotNull
    private String compensationReason;


    /**
     * 服务航站
     */
    @ApiModelProperty(value = "服务航站")
    @NotNull
    private String serviceCity;

    /**
     * 保障服务
     */
    @ApiModelProperty(value = "保障服务（0不存在，1存在）")
    @NotNull
    @Pattern(regexp="^[0|1]$")
    private String ensureType;
}
