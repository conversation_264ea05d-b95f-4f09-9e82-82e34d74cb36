package com.swcares.aps.compensation.model.baggage.accident.entity;

import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.components.encrypt.annotation.SecretInfoEntity;
import com.swcares.components.encrypt.annotation.SecretValue;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.Date;

@Data
@SecretInfoEntity
@TableName("BAGGAGE_TRANSPORT_INFO")
@ApiModel(value="BaggageTransportInfo", description="异常行李运输单")
public class BaggageTransportInfoDO {

    private Long id;
    
    private Long tenantId;

    @ApiModelProperty(value = "关联事故单号")
    private String accidentNo; 

    @ApiModelProperty(value = "事故单id")    
    private Long accidentId;

    @ApiModelProperty(value = "收件人姓名")
    private String receiverName;

    @ApiModelProperty(value = "收件人电话1")
    @SecretValue
    private String receiverPhone1;

    @ApiModelProperty(value = "收件人电话2(可选)")
    @SecretValue
    private String receiverPhone2;

    @ApiModelProperty(value = "省份")
    private String province;

    @ApiModelProperty(value = "城市")
    private String city; 

    @ApiModelProperty(value = "区县")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String addressDetail;

    @ApiModelProperty(value = "运输公司")
    @TableField(value = "TRANSPORT_COMPANY", updateStrategy = FieldStrategy.IGNORED,jdbcType = JdbcType.VARCHAR)
    private String transportCompany;

    @ApiModelProperty(value = "运输单号")
    private String transportNumber;

    @ApiModelProperty(value = "运输日期")
    private Date transportDate;

    @ApiModelProperty(value = "运输总费用")
    private Double transportCost;

    @ApiModelProperty(value = "异常行李code码")
    private String baggageCode;

    @ApiModelProperty(value = "是否是一个合并运输行李信息")
    private Boolean mergedTransport;

    @ApiModelProperty(value = "审核状态：1驳回 2审核中 3审核通过 7审核不通过")
    private String auditStatus;

    private String updatedBy;

    @ApiModelProperty(value = "更新时间", hidden = true)
    private LocalDateTime updatedTime;

    @ApiModelProperty(value = "运输机场")
    private String transportAirport;

    @ApiModelProperty(value = "归属航司")
    private String belongAirline;

    @ApiModelProperty(value = "机场审核人")
    private String airportAuditor;

    @ApiModelProperty(value = "航司审核人")
    private String airlineAuditor;

    @ApiModelProperty(value = "是否已复核")
    private Boolean reviewed;

    @ApiModelProperty(value = "复核人")
    private String reviewedBy;

    @ApiModelProperty(value = "复核时间")
    private LocalDateTime reviewedTime;
}
