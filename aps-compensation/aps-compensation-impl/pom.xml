<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aps-compensation</artifactId>
        <groupId>com.swcares.aps</groupId>
        <version>1.0.1_aps-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.swcares.aps</groupId>
    <artifactId>aps-compensation-impl</artifactId>

    <dependencies>
        <!-- 导出数据字典 -->
        <dependency>
            <groupId>cn.smallbun.screw</groupId>
            <artifactId>screw-core</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-compensation-model</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.oracle.database.jdbc</groupId>
            <artifactId>ojdbc8</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-dict</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-com</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-core</artifactId>
            <version>1.3-RC1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.spockframework</groupId>
            <artifactId>spock-spring</artifactId>
            <version>1.3-RC1-groovy-2.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-message-center-remote-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-workflow-remote-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-workflow</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-drools</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-file-attachment</artifactId>
        </dependency>
      <!--  <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-flight-passenger-remote-api</artifactId>
        </dependency>-->
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-user-center-remote-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-pay</artifactId>
            <version>${project.version}</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-message-center-remote-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-basic-data</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>coordinate-util</artifactId>
        </dependency>
        <!--这个依赖千万不能删除-->
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-tenant</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.0</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <!--fork:如果没有该项配置,整个devtools不会起作用 -->
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
   <!-- <profiles>
        <profile>
            <id>starter</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.22.2</version>
                        <configuration>
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>runJar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.22.2</version>
                        <configuration>
                            <skipTests>true</skipTests>
                        </configuration>
                    </plugin>
                    <plugin>
                        <groupId>org.springframework.boot</groupId>
                        <artifactId>spring-boot-maven-plugin</artifactId>
                        <version>2.5.0</version>
                        <configuration>
                            <includeSystemScope>true</includeSystemScope>
                            &lt;!&ndash;fork:如果没有该项配置,整个devtools不会起作用 &ndash;&gt;
                            <fork>true</fork>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>-->

</project>