package irregularFlightRulesTest

import com.swcares.aps.component.drools.model.dto.AbnormalFlightDTO
import com.swcares.aps.component.drools.model.dto.AbnormalDelayDTO
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO
import java.math.BigDecimal
import java.util.List
import java.util.ArrayList
import java.util.concurrent.atomic.AtomicReference
import java.util.stream.Collectors

global java.lang.Double delayHour
global java.lang.Boolean flag

declare RuleReturn
    auditRate : java.lang.Double
    childrenRate : java.lang.Double
    infantsRate : java.lang.Double
    economyClass : java.math.BigDecimal
    businessClass : java.math.BigDecimal
end

query getDelayRules(AbnormalFlightDTO $dto)
    $result: RuleReturn()
end

rule delayRuleInfo
    when
        $abnormalFlightDTO: AbnormalFlightDTO($abnormalDelay: abnormalDelay, abnormalDelay.size() > 0)
        $delay: AbnormalDelayDTO(maxDelay >= delayHour, minDelay < delayHour) from $abnormalDelay
    then
        RuleReturn ruleResult = new RuleReturn();
        ruleResult.setAuditRate($abnormalFlightDTO.getAuditRate());
        ruleResult.setChildrenRate($abnormalFlightDTO.getChildrenRate());
        ruleResult.setInfantsRate($abnormalFlightDTO.getInfantsRate());
        ruleResult.setEconomyClass($delay.getEconomyClass());
        ruleResult.setBusinessClass($delay.getBusinessClass());
        if (flag) {
            getTotalAmount(ruleResult,$abnormalFlightDTO);
        }
        insert(ruleResult);
end

function void getTotalAmount(RuleReturn ruleResult, AbnormalFlightDTO abnormalFlightDTO) {
    AtomicReference<BigDecimal> mountRef = new AtomicReference<>(abnormalFlightDTO.getTotalAmount());
    if (mountRef.get() == null) {
        mountRef.set(BigDecimal.ZERO); // 避免 mount 为 null
    }

    List<Object> rawPassengerInfo = abnormalFlightDTO.getPassengerInfo();
    if (rawPassengerInfo == null || rawPassengerInfo.isEmpty()) {
        return;
    }
    List<CompensationChoicePaxVO> passengers = rawPassengerInfo.stream()
        .filter(obj -> obj instanceof CompensationChoicePaxVO)
        .map(obj -> (CompensationChoicePaxVO) obj)
        .collect(Collectors.toList());

    passengers.forEach(p -> {
        if(!"1".equals(p.getWithBaby()) && !"1".equals(p.getIsChild()) && (abnormalFlightDTO.getEconomyCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getAuditRate()).multiply(ruleResult.getEconomyClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getEconomyClass().multiply(BigDecimal.valueOf(ruleResult.getAuditRate()))));
            return;
        }else if(!"1".equals(p.getWithBaby()) && !"1".equals(p.getIsChild()) && (abnormalFlightDTO.getBusinessCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getAuditRate()).multiply(ruleResult.getBusinessClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getBusinessClass().multiply(BigDecimal.valueOf(ruleResult.getAuditRate()))));
            return;
        }else if("1".equals(p.getWithBaby()) && !"1".equals(p.getIsChild()) && (abnormalFlightDTO.getEconomyCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getInfantsRate()).multiply(ruleResult.getEconomyClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getEconomyClass().multiply(BigDecimal.valueOf(ruleResult.getInfantsRate()))));
            return;
        }else if("1".equals(p.getWithBaby()) && !"1".equals(p.getIsChild()) && (abnormalFlightDTO.getBusinessCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getInfantsRate()).multiply(ruleResult.getBusinessClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getBusinessClass().multiply(BigDecimal.valueOf(ruleResult.getInfantsRate()))));
            return;
        }else if(!"1".equals(p.getWithBaby()) && "1".equals(p.getIsChild()) && (abnormalFlightDTO.getEconomyCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getChildrenRate()).multiply(ruleResult.getEconomyClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getEconomyClass().multiply(BigDecimal.valueOf(ruleResult.getChildrenRate()))));
            return;
        }else if(!"1".equals(p.getWithBaby()) && "1".equals(p.getIsChild()) && (abnormalFlightDTO.getBusinessCabin().contains(p.getSubClass()))){
            p.setCurrentAmount(BigDecimal.valueOf(ruleResult.getChildrenRate()).multiply(ruleResult.getBusinessClass()).toString());
            mountRef.set(mountRef.get().add(ruleResult.getBusinessClass().multiply(BigDecimal.valueOf(ruleResult.getChildrenRate()))));
            return;
        } else {
            p.setCurrentAmount("0");
            return;
        }
    });
    abnormalFlightDTO.setTotalAmount(mountRef.get());
}

