package com.swcares.aps.compensation.impl.complaint.service

import com.swcares.CompensationImplApplication
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler
import com.swcares.aps.compensation.model.datasync.dto.BusinessDataSyncDTO
import com.swcares.baseframe.common.security.LoginUserDetails
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class CoordinateTest extends Specification {

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getJobNumber() >> "778899"
        authentication.getUsername() >> "测试mock"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        authentication.getTenantCode() >> "CTU"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    def "test"() {
        given:
        def dto =new BusinessDataSyncDTO.BusinessDataSyncDTOBuilder()
                .businessType(BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT)
                .dataType(BusinessDataSyncConstant.DATA_TYPE_ACCIDENT)
                .receiverCustomer("3U")
                .build()
        when:
        def push = businessDataPushHandler.processBusinessDataPush(dto)
        then:
        noExceptionThrown()
    }

    def "dataStore in table"(){
        given:
        def businessId = 1812670623503052800
        def businessType = BusinessDataSyncConstant.BUSINESS_COMPLAINT
        def pushType = BusinessDataSyncConstant.DATA_TYPE_ACCIDENT
        when:
        businessDataPushHandler.dataStore(businessId,businessType, pushType)
        then:
        noExceptionThrown()
    }

    def "dataPush task"(){
        given:
        businessDataPushHandler.coordinateDataPushTask()
    }
}