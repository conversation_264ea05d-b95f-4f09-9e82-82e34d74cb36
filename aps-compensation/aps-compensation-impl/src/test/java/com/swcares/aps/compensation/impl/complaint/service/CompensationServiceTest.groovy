package com.swcares.aps.compensation.impl.complaint.service

import com.swcares.CompensationImplApplication
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintCompensationService
import com.swcares.aps.compensation.impl.dataconfig.controller.CabinConfigController
import com.swcares.aps.compensation.model.complaint.dto.*
import com.swcares.baseframe.common.security.LoginUserDetails
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class CompensationServiceTest extends Specification {

    @Autowired
    ComplaintCompensationService compensationService;
    @Autowired
    CabinConfigController cabinConfigController;

    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getUsername() >> "测试mock"
        authentication.getEmployeeName() >> "刘健"
        authentication.getJobNumber() >> "2522030"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        authentication.getTenantCode() >> "3U"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }


    def "passenger select query"() {
        given: "input properties"
        def dto = new PassengerSelectInfoDto(accidentType: "4", flightNo: "TV9855", flightDate: "2024-05-16", segment: "CTU-LJG",predicate: "370181198901304444")
        when:
        def select = compensationService.select(dto)
        print(select)
        then:
        noExceptionThrown()
    }

    def "count list"(){
        given: "input properties"
        def dto = new PassengerSelectInfoDto(accidentType: "3", flightNo: "TV9917", flightDate: "2024-05-16", segment: "MIG-SZX",ticketNumber: "0883709701343","predicate":"邹科")
        when:
        def select = compensationService.countList(dto)
        print(select)
        then:
        noExceptionThrown()
    }

    def "create compensation"(){
        given:""
        def dto = new ComplaintCompensationCreateDto(id: 1816294070479433728, flag: "1",orderId: 1816306363124625408,
                passengerComplaintDtoList: [
                        new PassengerComplaintDto(passengerName: "李飞飞", idType: "NI", idNumber: "620523200111173494", ticketNumber: "0882182262709")
                ],
                completeCompensationInfoDto: new CompleteCompensationInfoDto(compensationAirport: "LXA", compensationReason: "给你钱", compensationWay: "1", compensationAmount: "123",compensationAmountSubType: "2",compensationWaySubType: "1,2,3",
                        compensationPassengerAmountDto: [new CompensationPassengerAmountDto(ticketNo: "0882182262709", passengerName: "李飞飞", currentAmount: "123")]
                )
        )
        when:
        def select = compensationService.create(dto)
        print(select)
        then:
        noExceptionThrown()
    }

    def "fast check compensation"(){
        given:""
        def dto = new FastCreateComplaintInfoDto(
                passengerComplaintInfoList: [new PassengerComplaintDto(idNumber: "5101012023111151234", passengerName: "景立峰", ticketNumber: "4793961779819", idType: "NI",),
                                             new PassengerComplaintDto(idNumber: "5101012023111151232", passengerName: "周辉", ticketNumber: "4793961780328", idType: "NI",),
                                             new PassengerComplaintDto(idNumber: "5101012023111151231", passengerName: "赖厚晶", ticketNumber: "4793963553087", idType: "NI",)],
                passengerSelectInfo: new PassengerSelectInfoDto(accidentType: "3", flightDate: "2024-05-16", flightNo: "TV9917", segment: "MIG-SZX"),
                passengerCompensationInfoDto: new PassengerCompensationInfoDto(accidentSubType: "1", reasonType: "1", complaintDep: "部门id", complaintChannel: "1", accidentDes: "地面服务差劲", contactInfo: "18628218225", fileUrl: "",),
                complaintCompensationCreateDto: new ComplaintCompensationCreateDto(
                        passengerComplaintDtoList:[new PassengerComplaintDto(passengerName: "景立峰", idType: "NI", idNumber: "5101012023111151234", ticketNumber: "4793961779819"),
                                                   new PassengerComplaintDto(passengerName: "周辉", idType: "NI", idNumber: "5101012023111151232", ticketNumber: "4793961780328"),
                                                   new PassengerComplaintDto(passengerName: "赖厚晶", idType: "NI", idNumber: "5101012023111151231", ticketNumber: "4793963553087")],
                        flag: "0",
                        completeCompensationInfoDto: new CompleteCompensationInfoDto(compensationAirport: "TV", compensationReason: "what can i say？", compensationWay: "1", compensationAmount: "200",
                                compensationPassengerAmountDto: [new CompensationPassengerAmountDto(ticketNo: "4793961779819", passengerName: "景立峰", currentAmount: "200"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "4793961780328", passengerName: "周辉", currentAmount: "200"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "4793963553087", passengerName: "赖厚晶", currentAmount: "200")]
                        )
                )
        )
        when:
        def check = compensationService.fastCheck(dto)
        print(check)
        then:
        noExceptionThrown()
    }

    def "check compensation"(){
        given:""
        def dto = new ComplaintCompensationCreateDto(
                        passengerComplaintDtoList:[new PassengerComplaintDto(passengerName: "景立峰", idType: "NI", idNumber: "H03875630", ticketNumber: "0882151347491"),
                                                   new PassengerComplaintDto(passengerName: "周辉", idType: "NI", idNumber: "H08786708", ticketNumber: "0882151347490"),
                                                   new PassengerComplaintDto(passengerName: "赖厚晶", idType: "NI", idNumber: "H04150489", ticketNumber: "0882151347489")],
                        flag: "1",
                        id: 1808372921610256384,
                        orderId:1816360575081447424,
                        completeCompensationInfoDto: new CompleteCompensationInfoDto(compensationAirport: "LXA", compensationReason: "what can i say？", compensationWay: "1", compensationAmount: "77",compensationWaySubType: "3",compensationAmountSubType: "1",
                                compensationPassengerAmountDto: [new CompensationPassengerAmountDto(ticketNo: "0882151347491", passengerName: "罗爱群", currentAmount: "77"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "0882151347490", passengerName: "周辉", currentAmount: "77"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "0882151347489", passengerName: "赖厚晶", currentAmount: "77")]
                        )
                )
        when:
        def check = compensationService.check(dto)
        print(check)
        then:
        noExceptionThrown()
    }

    def "fast create accident and compensation"(){
        given:"mock properties"
        def dto = new FastCreateComplaintInfoDto(
                passengerComplaintInfoList: [new PassengerComplaintDto(idNumber: "5101012023111151234", passengerName: "景立峰", ticketNumber: "4793961779819", idType: "NI",),
                                             new PassengerComplaintDto(idNumber: "5101012023111151232", passengerName: "周辉", ticketNumber: "4793961780328", idType: "NI",),
                                             new PassengerComplaintDto(idNumber: "5101012023111151231", passengerName: "赖厚晶", ticketNumber: "4793963553087", idType: "NI",)],
                passengerSelectInfo: new PassengerSelectInfoDto(accidentType: "3", flightDate: "2024-05-16", flightNo: "TV9917", segment: "MIG-SZX"),
                passengerCompensationInfoDto: new PassengerCompensationInfoDto(accidentSubType: "1", reasonType: "1", complaintDep: "部门id", complaintChannel: "1", accidentDes: "地面服务差劲", contactInfo: "18628218225", fileUrl: "",),
                complaintCompensationCreateDto: new ComplaintCompensationCreateDto(
                        passengerComplaintDtoList:[new PassengerComplaintDto(passengerName: "景立峰", idType: "NI", idNumber: "5101012023111151234", ticketNumber: "4793961779819"),
                                                   new PassengerComplaintDto(passengerName: "周辉", idType: "NI", idNumber: "5101012023111151232", ticketNumber: "4793961780328"),
                                                   new PassengerComplaintDto(passengerName: "赖厚晶", idType: "NI", idNumber: "5101012023111151231", ticketNumber: "4793963553087")],
                        flag: "0",
                        completeCompensationInfoDto: new CompleteCompensationInfoDto(compensationAirport: "TV", compensationReason: "what can i say？", compensationWay: "1", compensationAmount: "200",
                                compensationPassengerAmountDto: [new CompensationPassengerAmountDto(ticketNo: "4793961779819", passengerName: "景立峰", currentAmount: "200"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "4793961780328", passengerName: "周辉", currentAmount: "200"),
                                                                 new CompensationPassengerAmountDto(ticketNo: "4793963553087", passengerName: "赖厚晶", currentAmount: "200")]
                        )
                )
        )
        when:
        def create = compensationService.fastCreate(dto)
        print(create)
        then:
        noExceptionThrown()
    }

}