package com.swcares.aps.compensation.impl.irregularflight.controller

import com.swcares.CompensationImplApplication
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxFrozenDTO
import com.swcares.baseframe.common.security.LoginUserDetails
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class CompensationFltPaxDataControllerTest extends Specification {

    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getJobNumber() >> "778899"
        authentication.getUsername() >> "测试mock"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    @Autowired
    CompensationFltPaxDataController compensationFltPaxDataController

    def "getPassengers"() {
        /**
         * containsCancel: 0
         containsN: 0
         flightDate: 2024-05-16
         flightNo: 3U9917
         checkStatus:
         choiceSegment: MIG-SZX
         choiceSegment: LXA-MIG
         choiceSegment: LXA-SZX
         compensateType: 1
         */
        given:
        PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO(containsCancel: 0, containsN: 0, flightDate: "2024-05-16", flightNo: "3U9917", checkStatus: "", choiceSegment: "MIG-SZX", compensateType: 1)
        when:
        def result = compensationFltPaxDataController.getPassengers(passengerQueryDTO)
        def data = result.getData()
        print(data.size())
        then:
        noExceptionThrown()
    }

    @Autowired
    CompensationPaxInfoController compensationPaxInfoController

    def "findChoicePax"(){
        given:
        def dto = new CompensationPaxFrozenDTO(orderId: '1825815246957903872')
        when:
        compensationPaxInfoController.findChoicePax(dto)
        then:
        noExceptionThrown()
    }

}
