package com.swcares.aps.compensation.impl.complaint.service

import com.alibaba.fastjson.JSON
import com.swcares.CompensationImplApplication
import com.swcares.aps.compensation.impl.compensation.controller.CompensationAuditController
import com.swcares.aps.compensation.impl.compensation.controller.CompensationOrderCommandsController
import com.swcares.aps.compensation.impl.compensation.controller.CompensationOrderQueriesController
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService
import com.swcares.aps.compensation.impl.compensation.service.impl.CompensationAuditServiceImpl
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintCompensationService
import com.swcares.aps.compensation.impl.irregularflight.controller.CompensationOrderInfoController
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService
import com.swcares.aps.compensation.impl.irregularflight.service.impl.CompensationPaxInfoServiceImpl
import com.swcares.aps.compensation.impl.privilege.controller.AirlineBusinessPrivilegeController
import com.swcares.aps.compensation.model.irregularflight.dto.*
import com.swcares.baseframe.common.security.LoginUserDetails
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class CompensationManagerTest extends Specification {

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService

    @Autowired
    private ComplaintCompensationService compensationService

    @Autowired
    AccidentCompensationDomainService accidentCompensationDomainService

    @Autowired
    private CompensationAuditServiceImpl compensationAuditService

    @Autowired
    private CompensationPaxInfoServiceImpl compensationPaxInfoService

    @Autowired
    CompensationOrderQueriesController compensationOrderQueriesController

    @Autowired
    CompensationAuditController compensationAuditController

    @Autowired
    CompensationOrderCommandsController compensationOrderCommandsController

    @Autowired
    CompensationOrderInfoController compensationOrderInfoController

    @Autowired
    AirlineBusinessPrivilegeController airlineBusinessPrivilegeController;


    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getJobNumber() >> "778899"
        authentication.getUsername() >> "测试mock"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        authentication.getTenantCode() >> "ctu"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    // test about compensation manager page query
    def "testCompensationManagerPageQuery"() {
        given:
        def dto = new CompensationOrderInfoPagedDTO(flightStartDate: "2024-05-01", flightEndDate: "2024-05-30", pageSize: 1, pageNumber: 1, userId: 1793089353696133120)
        and:
        def result = compensationOrderInfoService.pages(dto)
        print(JSON.toJSONString(result.getRecords()))
        expect:
        result.getRecords().size() == 1
    }

    // compensation detail info test
    def "testCompensationDetailInfo"() {
        given:
        def info = compensationService.getCompensationOrderInfo(1803399196532961280)
        print(JSON.toJSONString(info))
        expect:
        info.getId() == 1803399196532961280
    }

    // test general close compensation
    def "testGeneralCloseCompensation"() {
        given:
        accidentCompensationDomainService.closeCompensation("1803330973962022912")
        when:
        print("test general close compensation success")
        then:
        noExceptionThrown()
    }

    // test compensation delete
    def "testCompensationDelete"() {
        given:
        accidentCompensationDomainService.delete("1805203977592193024")
        when:
        print("delete order table and  pax info table")
        then:
        noExceptionThrown()
    }

    // test find choose segment pax info
    def "testFindChooseSegmentPaxInfo"() {
        given:
        def o = new CompensationPaxFrozenDTO(orderId: 1805143507607928832)
        when:
        def result = compensationPaxInfoService.findChoicePax(o)
        then:
        noExceptionThrown()
    }


    def "compensationDetailSearch"(){
        given:
        def search = compensationOrderQueriesController.compensationDetailSearch("1807627408279633920")
        when:
        print(JSON.toJSONString(search))
        then:
        noExceptionThrown()
    }

    def "workflow-submit"(){
        given:
        def orderId= "1805866545962315776"
        when:
        compensationOrderCommandsController.submit(orderId)
        then:
        noExceptionThrown()
    }

    def "audit controller test"(){
        given:
        def o = AuditProcessorDTO.builder()
                .orderId(1806251067208675328)
                .auditStatus("AGREE")
                .taskId("9b08b3d8-3463-11ef-bdac-000c29a14563")
                .remarks("1")
                .build()
        when:
        compensationAuditController.auditOperation(o)
        then:
        noExceptionThrown()
    }

    def "release order info"(){
        given:
        def orderId = 1805850796096905216
        when:
        compensationOrderInfoController.takeEffect(orderId)
        then:
        noExceptionThrown()
    }

    def "freezeOrderPax"(){
        given:
        def dto = new FreezeOrderPaxDTO(paxIds: [null], orderId: 1808343904605683712,status: "0")
        when:
        compensationPaxInfoService.freezeOrderPax(dto)
        then:
        noExceptionThrown()
    }
    
    def "saveReviewer"(){
        given:
        Long[] a = [1791337908400361472,1793089353696133120,1807973772729544704,1807970731683405824,1807979718694952960,1807979920650690560,1791314543652442112,1793879236203233280,1795266338279112704,1808020283008200704,1808020439069863936]
        def dto = CompensationAuditInfoDTO.builder()

                .orderId(1810514737954033664)
                .taskId("6b33ae37-3da2-11ef-a638-000c29f00c4c")
                .auditorIds(a)
                .build()
        when:
        compensationAuditService.saveReviewer(dto)
        then:
        noExceptionThrown()
    }

    def "getBelongAirline"() {
        given:
        def dto = "1"
        when:
        def result = airlineBusinessPrivilegeController.getBelongAirline(dto)
        then:
        noExceptionThrown()
    }
}