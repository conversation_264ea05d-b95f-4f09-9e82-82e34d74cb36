package com.swcares.aps.compensation.impl.complaint.service

import com.alibaba.fastjson.JSON
import com.swcares.CompensationImplApplication
import com.swcares.aps.compensation.impl.dataconfig.service.CabinConfigService
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxFrozenDTO
import com.swcares.aps.compensation.model.rools.dto.AbnormalDelayDTO
import com.swcares.aps.compensation.model.rools.dto.AbnormalFlightDTO
import com.swcares.aps.component.drools.impl.service.DroolsRuleService

import com.swcares.baseframe.common.security.LoginUserDetails
import com.swcares.baseframe.common.security.UserContext
import org.kie.api.runtime.KieSession
import org.kie.api.runtime.rule.QueryResultsRow
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootContextLoader
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.security.core.Authentication
import org.springframework.security.core.context.SecurityContext
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.security.oauth2.provider.OAuth2Authentication
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

@SpringBootTest(classes = CompensationImplApplication.class)
@ContextConfiguration(loader = SpringBootContextLoader.class)
class DroolsTest extends Specification {

    @Autowired
    DroolsRuleService droolsRuleService

    @Autowired
    CabinConfigService cabinConfigService

    @Autowired
    CompensationPaxInfoService compensationPaxInfoService

    def setup() {
        def authentication = Mock(LoginUserDetails)
        authentication.getUsername() >> "测试mock"
        authentication.getEmployeeName() >> "刘健"
        authentication.getJobNumber() >> "2522030"
        authentication.getId() >> 1793089353696133120
        authentication.getTenantId() >> 123L
        authentication.getTenantCode() >> "3U"
        def securityContext = Mock(SecurityContext)
        def mock = Mock(OAuth2Authentication)
        def authentication_mock = Mock(Authentication)
        authentication_mock.getPrincipal() >> authentication
        mock.getUserAuthentication() >> authentication_mock
        mock.getPrincipal() >> authentication
        securityContext.getAuthentication() >> mock
        SecurityContextHolder.setContext(securityContext)
    }

    def "delayFlight Drools"(){
        given:
        def pax = compensationPaxInfoService.findChoicePax(new CompensationPaxFrozenDTO(orderId: 1825813705857368064))
        def airline = cabinConfigService.loadCabinByAirline(UserContext.getCurrentUser().getTenantCode())
        def dto = new AbnormalFlightDTO(abnormalDelay:[new AbnormalDelayDTO(minDelay: Double.valueOf("1.4"), maxDelay: Double.valueOf("2"),economyClass: new BigDecimal("200"), businessClass: new BigDecimal("400"))
                                                       , new AbnormalDelayDTO(minDelay: Double.valueOf("2"), maxDelay: Double.valueOf("2.5"),economyClass: new BigDecimal("400"), businessClass: new BigDecimal("800"))],
        childrenRate: Double.valueOf("0.5"), auditRate: Double.valueOf("0.5"),infantsRate: Double.valueOf("0.5"),
        businessCabin: airline.getBusinessCabin(),economyCabin: airline.getEconomyCabin(),passengerInfo: pax)
        when:
        KieSession kSession = droolsRuleService.getKieSessionFromDrl("delayFlight")
        kSession.insert(dto)
        kSession.setGlobal("delayHour", Double.valueOf("2.4"))
        kSession.setGlobal("flag", true)

        then:
        // 执行规则必须为大于0
        def rules = kSession.fireAllRules()
        def results = kSession.getQueryResults("getDelayRules", dto)
        results.each { QueryResultsRow row ->
            def delay = row.get("\$result")
            print("delayInfo"+JSON.toJSONString(delay))
        }
        print(JSON.toJSONString(dto))

    }

    def "test bigDecimal check"(){
        given:
        BigDecimal a = new BigDecimal("1")
        a  = a.setScale(2, BigDecimal.ROUND_HALF_UP)
        println(a.compareTo(new BigDecimal("0"))>0)
    }

}