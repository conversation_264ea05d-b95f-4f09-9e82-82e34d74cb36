<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper">

    <select id="page" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoVO" databaseId="oracle">
        SELECT OI.id,
        OI.accident_id ,
        OI.accident_no,
        OI.order_no,
        MAX(OI.choice_segment ) choice_segment,
        OI.flight_no ,
        OI.flight_date ,
        MAX(OI.choice_segment_ch) choice_segment_ch,
        MAX(get_cityNameAnd3Code(OI.service_city)) service_city,
        <!--状态-->
        MAX(OI.status) status,
        MAX(OI.compensate_type) compensateType,
        MAX(fo.accident_type) accidentType,
        MAX(fo.fc_type) fcType,
        <!-- 计划/实际执行 、 计划/实际金额、冻结人数-->
        COUNT(decode(pi.receive_status,1,'true',null))
        + COUNT(decode(pi.receive_status,1,decode(pi.with_baby,'1','true',null),null))
        actualCarryOutNum,
        COUNT(DISTINCT pi.pax_id) +COUNT(decode(pi.with_baby,'1','true',null))  as planCarryOutNum,
        COUNT(decode(pi.switch_off,1,'true',null)) as frozenNum,
        SUM(decode(pi.receive_status,1,current_amount,0)) actualCompensateMoney,
        max(oi.sum_money) as planCompensateMoney,
        max(concat(ue.name,ue.job_number)) created_by,
        max(OI.created_time) created_time
        FROM compensation_order_info OI
        LEFT JOIN flight_accident_info fo on fo.id = OI.accident_id
        LEFT JOIN compensation_pax_info pi on oi.id = pi.order_id
        LEFT JOIN uc_user uc on to_char(uc.id) = OI.created_by
        LEFT JOIN uc_employee ue ON uc.employee_id = ue.id
        WHERE 1=1 and 1 = fo.accident_type

        <if test="dto.orderNo != null and dto.orderNo != ''">
            and   OI.order_no = #{dto.orderNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and   OI.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  OI.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  fo.segment like concat(concat('%',#{dto.orgCity}),'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  fo.segment like concat(concat('%-',#{dto.dstCity}),'%')
        </if>
        <if test="dto.status != null and dto.status.size()>0">
            and  OI.status IN
            <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.accidentType != null and dto.accidentType != ''">
            and #{dto.accidentType} = fo.accident_type
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and #{dto.fcType} = fo.fc_type
        </if>

        <if test="dto.compensateType != null and dto.compensateType != ''">
            and #{dto.compensateType} = OI.compensate_type
        </if>

        <if test="dto.serviceCity != null and dto.serviceCity != ''">
            and OI.service_city = #{dto.serviceCity}
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            <!--and (uc.employee_id = #{dto.createdBy} or uc.name like "%"#{dto.createdBy}"%")-->
            and (to_char(uc.id) = #{dto.createdBy} or uc.name like concat('%',concat(#{dto.createdBy},'%')))
        </if>

        GROUP BY OI.id,OI.accident_id ,OI.accident_no,OI.order_no,OI.flight_no ,OI.flight_date
        ORDER BY OI.flight_date DESC ,OI.flight_no,MAX(oi.status) ASC
    </select>

    <select id="page" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoVO" databaseId="mysql">
        SELECT * FROM
        (SELECT OI.id,
        OI.accident_id ,
        OI.accident_no,
        OI.order_no,
        MAX(OI.choice_segment ) choice_segment,
        OI.flight_no ,
        OI.flight_date ,
        MAX(OI.choice_segment_ch) choice_segment_ch,
        MAX(get_cityNameAnd3Code(OI.service_city)) service_city,
        <!--状态-->
        MAX(OI.status) status,
        MAX(OI.compensate_type) compensateType,
        MAX(fo.accident_type) accidentType,
        MAX(fo.fc_type) fcType,
        <!-- 计划/实际执行 、 计划/实际金额、冻结人数-->
        COUNT(if(pi.receive_status = 1,true,null))
        + COUNT(if((pi.receive_status = 1 and pi.with_baby='1'),true,null))
        actualCarryOutNum,
        COUNT(DISTINCT pi.pax_id) +COUNT(if(pi.with_baby='1',true,null))  as planCarryOutNum,
        COUNT(if(pi.switch_off=1,true,null)) as frozenNum,
        SUM(if(pi.receive_status = 1,current_amount,0)) actualCompensateMoney,
        max(oi.sum_money) as planCompensateMoney,
        max(concat(ue.name,ue.job_number)) created_by,
        max(OI.created_time) created_time
        FROM compensation_order_info OI
        LEFT JOIN flight_accident_info fo on fo.id = OI.accident_id
        LEFT JOIN compensation_pax_info pi on oi.id = pi.order_id
        LEFT JOIN uc_user uc on uc.id = OI.created_by
        LEFT JOIN uc_employee ue ON uc.employee_id = ue.id
        WHERE 1=1 and 1 = fo.accident_type

        <if test="dto.orderNo != null and dto.orderNo != ''">
            and   OI.order_no = #{dto.orderNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and   OI.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  OI.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  fo.segment like concat('%',#{dto.orgCity},'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  fo.segment like concat('%-',#{dto.dstCity},'%')
        </if>
        <if test="dto.status != null and dto.status.size()>0">
            and  OI.status IN
            <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.accidentType != null and dto.accidentType != ''">
            and #{dto.accidentType} = fo.accident_type
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and #{dto.fcType} = fo.fc_type
        </if>

        <if test="dto.compensateType != null and dto.compensateType != ''">
            and #{dto.compensateType} = OI.compensate_type
        </if>

        <if test="dto.serviceCity != null and dto.serviceCity != ''">
            and OI.service_city = #{dto.serviceCity}
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            <!--and (uc.employee_id = #{dto.createdBy} or uc.name like "%"#{dto.createdBy}"%")-->
            and (to_char(uc.id) = #{dto.createdBy} or uc.name like concat('%',concat(#{dto.createdBy},'%')))
        </if>

        GROUP BY OI.id,OI.accident_id ,OI.accident_no,OI.order_no,OI.flight_no ,OI.flight_date
        ORDER BY OI.flight_date DESC ,OI.flight_no,MAX(oi.status) ASC
        ) as t
    </select>


    <select id="findById" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO" databaseId="oracle">
        SELECT
        coi.id,
        coi.accident_id ,
        coi.accident_no,
        coi.COMPENSATE_STANDARD compensateStandard,
        coi.order_no,
        max(get_cityNameAnd3Code(coi.service_city)) service_city,
        max(coi.choice_segment_ch) choiceSegmentCh,
        max(coi.status) status,
        max(coi.compensate_type) compensateType,
        max(coi.compensate_sub_type) compensateSubType,
        max(coi.ensure_type) ensureType,
        max(coi.remark) remark,
        max(coi.sum_money) sumMoney,
        count(cpi.order_id) + count(decode(cpi.with_baby,'1','true',null)) choiceNum,
        count(decode(cpi.sex,'C',null,decode(cpi.is_child,'0','true',null))) adultNum,
        count(decode(cpi.is_child,'1','true',null)) childrenNum,
        count(decode(cpi.with_baby,'1','true',null)) babyNum,
        max(case
        when get_audit_count(#{userId,jdbcType=VARCHAR},coi.id)=0 then 'N'
        when get_audit_count(#{userId,jdbcType=VARCHAR},coi.id)>0 then 'Y'
        end) as toExamine,
        (SELECT max(cai.task_id) FROM compensation_audit_info cai WHERE cai.order_id = ${id}) taskId,
        coi.ACCIDENT_TYPE
        FROM compensation_order_info coi
        LEFT JOIN compensation_pax_info cpi on coi.id = cpi.order_id
        WHERE 1=1
        AND coi.id = ${id}
        GROUP BY coi.id,coi.accident_id ,coi.accident_no,coi.order_no,coi.ACCIDENT_TYPE,coi.COMPENSATE_STANDARD
    </select>

    <select id="findById" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO" databaseId="mysql">
        SELECT
        coi.id,
        coi.accident_id ,
        coi.accident_no,
        coi.order_no,
        max(get_cityNameAnd3Code(coi.service_city)) service_city,
        max(coi.choice_segment_ch) choiceSegmentCh,
        max(coi.status) status,
        max(coi.compensate_type) compensateType,
        max(coi.ensure_type) ensureType,
        max(coi.remark) remark,
        max(coi.sum_money) sumMoney,
        count(cpi.order_id) + count(if(cpi.with_baby='1',true,null)) choiceNum,
        count(if((cpi.sex != 'C' and cpi.is_child='0'),true,null)) adultNum,
        count(if(cpi.is_child='1',true,null)) childrenNum,
        count(if(cpi.with_baby='1',true,null)) babyNum,
        max(case
        when get_audit_count(#{userId},coi.id)=0 then 'N'
        when get_audit_count(#{userId},coi.id)>0 then 'Y'
        end) as toExamine,
        (SELECT max(cai.task_id) FROM compensation_audit_info cai WHERE cai.order_id = ${id}) taskId,
        coi.ACCIDENT_TYPE
        FROM compensation_order_info coi
        LEFT JOIN compensation_pax_info cpi on coi.id = cpi.order_id
        WHERE 1=1
        AND coi.id = ${id}
        GROUP BY coi.id,coi.accident_id ,coi.accident_no,coi.order_no,coi.ACCIDENT_TYPE
    </select>


    <select id="findOrderCountByAccidentId" resultType="java.lang.Integer" databaseId="mysql">
        select IFNULL(CAST(right(max(t.order_no),4) AS UNSIGNED),0) from compensation_order_info t where t.accident_id = #{accidentId}
    </select>

    <select id="findOrderCountByAccidentId" resultType="java.lang.Integer" databaseId="oracle">
        select nvl(CAST(SUBSTR(max(t.order_no),-4,4) AS number),0)  from compensation_order_info t where t.accident_id = #{accidentId}
    </select>


    <delete id="updRemoveOrderByStatus">
        DELETE FROM compensation_order_info WHERE id=#{id} AND status in('0','1')
    </delete>

    <update id="upAccidentStatus" databaseId="mysql">
        update flight_accident_info  f inner join (
            select fai.accident_no,fai.id,count(*) c1, count(coi.status='6' or coi.status='5' or null) c2 from 	flight_accident_info fai
            left join compensation_order_info coi on fai.accident_no=coi.accident_no
            where fai.id = (select accident_id from compensation_order_info where id = #{orderId})
            group by fai.accident_no,fai.id
            ) re
        on f.accident_no = re.accident_no
        set f.accident_status = '3'
        where re.c1=re.c2 and re.c1 !=0 and f.id=re.id
    </update>

    <update id="upAccidentStatus" databaseId="oracle">
        UPDATE
        FLIGHT_ACCIDENT_INFO fai
        SET fai.ACCIDENT_STATUS  = '3'
        WHERE fai.id = (
        SELECT f.id
        FROM
        FLIGHT_ACCIDENT_INFO f
        INNER JOIN
        (	SELECT
            fai.accident_no,
            fai.id,
            count(*) c1,
            count(DECODE(coi.status,5,1,6,1,8,1,9,1,NULL,1)) c2
            FROM
            flight_accident_info fai
            LEFT JOIN compensation_order_info coi ON fai.accident_no = coi.accident_no
            WHERE
            fai.id = ( SELECT accident_id FROM compensation_order_info WHERE id = #{orderId} )
            GROUP BY
            fai.accident_no,
            fai.id ) re
        ON f.accident_no = re.accident_no
        WHERE
        re.c1 = re.c2
        AND re.c1 != 0
        AND f.id = re.id
        )
    </update>

    <update id="updCompensationOrderStatus">
        update compensation_order_info set status = #{status}
        where id = ${id}
    </update>

    <select id="pages" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO" databaseId="oracle">
        SELECT
        OI.id,
        OI.accident_id,
        OI.accident_no,
        OI.BELONG_AIRLINE as belongAirline,
        OI.order_no,
        OI.choice_segment choice_segment,
        OI.flight_no,
        OI.flight_date,
        OI.choice_segment_ch choice_segment_ch,
        get_cityNameAnd3Code ( OI.service_city ) service_city,
        OI.status status,
        OI.source source,
        OI.compensate_type compensateType,
        OI.accident_type AS accidentType,
        CASE WHEN OI.ACCIDENT_TYPE = '1' THEN NULL ELSE WM_CONCAT(DISTINCT pi.PAX_NAME) END AS paxName,
        OI.ACCIDENT_SUB_TYPE AS fcType,
        COUNT( decode( pi.receive_status, 1, 'true', NULL ) ) + COUNT( decode( pi.receive_status, 1, decode( pi.with_baby, '1', 'true', NULL ), NULL ) ) actualCarryOutNum,
        COUNT( DISTINCT pi.pax_id ) AS planCarryOutNum,
        COUNT( decode( pi.switch_off, 1, 'true', NULL ) ) AS frozenNum,
        SUM( decode( pi.receive_status, 1, current_amount, 0 ) ) actualCompensateMoney,
        oi.sum_money  AS planCompensateMoney,
        OI.CREATED_BY AS created_by,
        OI.CREATED_TIME AS created_time,
        case
        when get_audit_count(#{dto.userId},OI.id)=0 then 'N'
        when get_audit_count(#{dto.userId},OI.id)>0 then 'Y'
        end as toExamine,
        uc.id userId,
        (SELECT DISTINCT task_id FROM compensation_audit_info WHERE order_id = oi.id) as taskId
        FROM
        compensation_order_info OI
        LEFT JOIN compensation_pax_info pi ON oi.id = pi.order_id
        LEFT JOIN uc_employee ue ON OI.CREATED_BY = concat(ue.name ,ue.JOB_NUMBER)
        LEFT JOIN UC_USER uc ON uc.EMPLOYEE_ID = ue.id
        WHERE
        1 = 1
        <if test="dto.accidentType != null and dto.accidentType != ''">
            AND OI.ACCIDENT_TYPE = #{dto.accidentType}
        </if>
        <if test='dto.source != null and dto.source != "" and dto.source != "-1" '>
            AND OI.SOURCE = #{dto.source}
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.belongAirline)">
            and OI.BELONG_AIRLINE in
            <foreach collection="dto.belongAirline" item="item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.orderNo != null and dto.orderNo != ''">
            and   OI.order_no = #{dto.orderNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and   OI.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  OI.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  OI.CHOICE_SEGMENT like concat(concat('%',#{dto.orgCity}),'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  OI.CHOICE_SEGMENT like concat(concat('%-',#{dto.dstCity}),'%')
        </if>
        <if test="dto.status != null and dto.status.size()>0">
            and OI.status IN
            <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and  OI.ACCIDENT_SUB_TYPE in
            <foreach collection="dto.fcTypeList" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <if test="dto.compensateType != null and dto.compensateType != ''">
            and #{dto.compensateType} = OI.compensate_type
        </if>

        <if test="dto.serviceCity != null and dto.serviceCity != ''">
            and OI.service_city in
            <foreach collection="dto.serviceCityList" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            and (OI.CREATED_BY like concat(concat('%',#{dto.createdBy}),'%') )
        </if>
        <!--           行权限-->
        <if test="dto.accidentTypes != null and dto.accidentTypes.size() >0">
            and OI.accident_type in(
            select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
            <foreach collection="dto.accidentTypes" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
            )
        </if>
        <if test="dto.workStations != null and dto.workStations.size() >0">
            and OI.service_city in
            <foreach collection="dto.workStations" item="station"  open="(" separator="," close=")">
                #{station}
            </foreach>
        </if>
        GROUP BY
        OI.id,
        uc.id,
        OI.status,
        OI.BELONG_AIRLINE,
        OI.compensate_type,
        OI.service_city,
        OI.accident_id,
        OI.accident_no,
        OI.order_no,
        OI.flight_no,
        OI.flight_date,
        OI.CHOICE_SEGMENT,
        OI.ACCIDENT_TYPE,
        OI.ACCIDENT_SUB_TYPE,
        OI.CREATED_TIME,
        OI.CREATED_BY,
        oi.sum_money,
        OI.source,
        OI.choice_segment_ch
        ORDER BY
        OI.flight_date DESC,
        MAX( oi.status ) ASC
    </select>

    <select id="getSameTypeOrders" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderAddTipsVO" databaseId="oracle">
        select
        coi.id orderId,coi.accident_id,coi.order_no,
        coi.status,
        coi.choice_segment_ch, coi.sum_money,
        coi.created_by,
        (select count(pi.pax_id) +count(decode(pi.with_baby,'1','true',null)) FROM compensation_pax_info pi where pi.order_id = coi.id) sumPersons
        from flight_accident_info fai
        LEFT JOIN compensation_order_info coi on fai.id = coi.accident_id
        where coi.accident_type = #{type} and coi.flight_no = #{no} and coi.flight_date = #{date}
        <if test="id != null">
            and coi.id != #{id}
        </if>
    </select>


    <select id="pages" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO" databaseId="mysql">
        SELECT * from (
        SELECT OI.id,
        OI.accident_id ,
        OI.accident_no,
        OI.order_no,
        MAX(OI.choice_segment ) choice_segment,
        OI.flight_no ,
        OI.flight_date ,
        MAX(OI.choice_segment_ch) choice_segment_ch,
        MAX(get_cityNameAnd3Code(OI.service_city)) service_city,
        <!--状态-->
        MAX(OI.status) status,
        MAX(OI.compensate_type) compensateType,
        MAX(fo.accident_type) accidentType,
        MAX(fo.fc_type) fcType,
        <!-- 计划/实际执行 、 计划/实际金额、冻结人数-->
        COUNT(if(pi.receive_status = 1,true,null))
        + COUNT(if((pi.receive_status = 1 and pi.with_baby='1'),true,null))
        actualCarryOutNum,
        COUNT(DISTINCT pi.pax_id) +COUNT(if(pi.with_baby='1',true,null))  as planCarryOutNum,
        COUNT(if(pi.switch_off=1,true,null)) as frozenNum,
        SUM(if(pi.receive_status = 1,current_amount,0)) actualCompensateMoney,
        max(oi.sum_money) as planCompensateMoney,
        max(concat(ue.name,ue.job_number)) created_by,
        max(OI.created_time) created_time,
        case
        when get_audit_count(#{dto.userId},OI.id)=0 then 'N'
        when get_audit_count(#{dto.userId},OI.id)>0 then 'Y'
        end as toExamine,
        cai.task_id taskId,uc.id userId
        FROM compensation_order_info OI
        LEFT JOIN flight_accident_info fo on fo.id = OI.accident_id
        LEFT JOIN compensation_pax_info pi on oi.id = pi.order_id
        LEFT JOIN uc_user uc on uc.id = OI.created_by
        LEFT JOIN uc_employee ue ON uc.employee_id = ue.id
        LEFT JOIN compensation_audit_info cai on oi.id = cai.order_id

        WHERE 1=1

        <if test="dto.orderNo != null and dto.orderNo != ''">
            and   OI.order_no = #{dto.orderNo}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and   OI.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
            and  OI.flight_date  BETWEEN #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.orgCity != null and dto.orgCity != ''">
            and  fo.segment like concat('%',#{dto.orgCity},'-%')
        </if>
        <if test="dto.dstCity != null and dto.dstCity != ''">
            and  fo.segment like concat('%-',#{dto.dstCity},'%')
        </if>
        <if test="dto.status != null and dto.status.size()>0">
            and OI.status IN
            <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="dto.accidentType != null and dto.accidentType != ''">
            and #{dto.accidentType} = fo.accident_type
        </if>
        <if test="dto.fcType != null and dto.fcType != ''">
            and  fo.fc_type in
            <foreach collection="dto.fcTypeList" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <if test="dto.compensateType != null and dto.compensateType != ''">
            and #{dto.compensateType} = OI.compensate_type
        </if>

        <if test="dto.serviceCity != null and dto.serviceCity != ''">
            and OI.service_city in
            <foreach collection="dto.serviceCityList" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            <!--and (uc.employee_code = #{dto.createdBy} or uc.name like "%"#{dto.createdBy}"%")-->
            and (ue.job_number = #{dto.createdBy} or uc.name like concat('%',concat(#{dto.createdBy},'%')))
        </if>

        <!--           行权限-->
        <if test="dto.accidentTypes != null and dto.accidentTypes.size() >0">
            and fo.accident_type in(
            select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
            <foreach collection="dto.accidentTypes" item="type"  open="(" separator="," close=")">
                #{type}
            </foreach>
            )
        </if>
        <if test="dto.workStations != null and dto.workStations.size() >0">
            and OI.service_city in
            <foreach collection="dto.workStations" item="station"  open="(" separator="," close=")">
                #{station}
            </foreach>
        </if>
        GROUP BY OI.id,OI.accident_id ,OI.accident_no,OI.order_no,OI.flight_no ,OI.flight_date,cai.task_id,fo.STD
        ORDER BY OI.flight_date DESC ,fo.STD,MAX(oi.status) ASC
        ) AS DT
    </select>

    <select id="getSameTypeOrders" resultType="com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderAddTipsVO" databaseId="mysql">
        select
        coi.id orderId,coi.accident_id,coi.order_no,
        coi.status,
        coi.choice_segment_ch, coi.sum_money,
        concat(ue.name,ue.job_number) created_by,
        (select COUNT(pi.pax_id) +COUNT(if(pi.with_baby='1',true,null)) FROM compensation_pax_info pi where order_id=coi.id) sumPersons
        from flight_accident_info fai
        LEFT JOIN compensation_order_info coi on fai.id = coi.accident_id
        where coi.accident_type = #{type} and coi.flight_no = #{no} and coi.flight_date = #{date}
    </select>


    <select id="findPaxReceiveRecord" resultType="com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO" databaseId="oracle">
        SELECT
        cao.apply_user,
        cao.apply_code,
        cao.telephone,
        cpr.trans_amount applyAmount,
        (SELECT SUM (cpi.CURRENT_AMOUNT) FROM compensation_pax_info cpi
        WHERE cpi.PAX_NAME = #{paxName} AND cpi.ID_NO = #{idNo}
        AND cpi.ORDER_ID=#{orderId} ) applySumAmount,
        cao.apply_way applyType,
        cao.COMPENSATE_TYPE payType,
        cao.COMPENSATE_SUB_TYPE paySubType,
        nvl(cpr.trans_msg ,cpr.err_code_des) failReason,
        cao.created_time applyTime,
        cpr.return_serial_no paySerialNumber,
        cpr.pay_status payStatus,
        cpr.pay_return_time payTime,
        cpr.PAX_RECEIVE_STATE paxReceiveState
        from compensation_apply_order cao
        LEFT JOIN  compensation_pay_record cpr on cao.id = cpr.apply_id
        LEFT JOIN  compensation_pax_info cpi on cpr.apply_pax_id = cpi.id
        where cpi.pax_id = #{paxId} and cpi.order_id = #{orderId}
        order by cao.created_time desc
    </select>

    <select id="findPaxReceiveRecord" resultType="com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO" databaseId="mysql">
        SELECT
        cao.apply_user,
        cao.apply_code,
        cao.telephone,
        cpr.trans_amount applyAmount,
        (SELECT SUM (cpi.CURRENT_AMOUNT) FROM compensation_pax_info cpi
        WHERE cpi.PAX_NAME = #{paxName} AND cpi.ID_NO = #{idNo}
        AND cpi.ORDER_ID=#{orderId} ) applySumAmount,
        cao.apply_way applyType,
        cpr.pay_type payType,
        ifnull(cpr.trans_msg ,cpr.err_code_des) failReason,
        cao.created_time applyTime,
        cpr.return_serial_no paySerialNumber,
        cpr.pay_status payStatus,
        cpr.pay_start_time payTime,
        cpr.PAX_RECEIVE_STATE paxReceiveState
        from compensation_apply_order cao
        LEFT JOIN  compensation_pay_record cpr on cao.id = cpr.apply_id
        LEFT JOIN  compensation_pax_info cpi on cpr.apply_pax_id = cpi.id
        where cpi.pax_id = #{paxId} and cpi.order_id = #{orderId}
        order by cao.created_time desc
    </select>

    <select id="selectOneById" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO">
        SELECT
        coi.ID ,
        coi.ACCIDENT_ID ,
        coi.ACCIDENT_NO ,
        coi.SOURCE ,
        coi.ORDER_NO ,
        coi.COMPENSATE_TYPE ,
        coi.compensate_sub_type,
        coi.compensate_standard,
        coi.commodity_id,
        get_cityNameAnd3Code(coi.SERVICE_CITY) serviceCity,
        coi.CHOICE_SEGMENT ,
        coi.CHOICE_SEGMENT_CH ,
        coi.REMARK ,
        coi.ENSURE_TYPE ,
        coi.STATUS ,
        coi.FLIGHT_ID ,
        coi.EXPIRY_DATE ,
        coi.SUM_MONEY ,
        coi.FLIGHT_NO ,
        coi.FLIGHT_DATE ,
        coi.ISS_USER ,
        coi.CLOSE_USER ,
        coi.CLOSE_TIME ,
        coi.ACCIDENT_TYPE ,
        coi.ACCIDENT_SUB_TYPE ,
        coi.FULL_SEGMENT ,
        coi.CREATED_BY,
        coi.UPDATED_BY,
        coi.CREATED_TIME ,
        coi.UPDATED_TIME,
        coi.BELONG_AIRLINE,
        coi.BELONG_AIRLINE_ABBR,
        coi.GRANT_BANKROLL,
        coi.input_source inputSource,
        coi.TEMPORARY_COMPENSATION,
        coi.SOURCE_TENANT_CODE
        from
        compensation_order_info coi
        where 1=1
        <if test="orderId != null and orderId != ''">
         and    coi.id = #{orderId}
        </if>

        <if test="orderNo != null and orderNo != ''">
         and   coi.order_No = #{orderNo}
        </if>


    </select>

    <update id="updateOverdueOrder">
        update compensation_order_info set STATUS = #{status} , CLOSE_USER = 'system',CLOSE_TIME = SYSDATE
        where 1=1
        and id in
        <foreach collection="orderIds" item="orderId" open="(" separator="," close=")">
            #{orderId}
        </foreach>

    </update>

    <update id="updateOrderCreatedBy">
        UPDATE COMPENSATION_ORDER_INFO coi SET coi.CREATED_BY = #{currentUserId}
        WHERE coi.ID = #{compensationId}
    </update>

    <select id="findCloseAccidentOrder" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO">
        select * from
        (
        select  coi.ACCIDENT_ID,coi.accident_no,coi.ACCIDENT_TYPE ,count(*) num1,coi.tenant_Id,
        count( case when coi.status = '5' or coi.status = '6' or coi.status = '7' or coi.status = '8' or coi.status = '9'  then 1 else null end ) num2
        from compensation_order_info coi
        left join flight_accident_info co  on co.accident_no = coi.accident_no and co.accident_status != '3' and co.accident_status != '4'
        left join BAGGAGE_ACCIDENT_INFO ba  on ba.accident_no = coi.accident_no and  ba.accident_status != '3' and ba.accident_status != '4'
        left join OVER_BOOK_ACCIDENT_INFO ob  on ob.accident_no = coi.accident_no and  ob.accident_status != '3' and ob.accident_status != '4'
        left join COMPLAINT_ACCIDENT_INFO ca  on ca.accident_id = coi.accident_no and  ca.accident_status != '3' and ca.accident_status != '4'

        where 1=1
        and (CO.ACCIDENT_NO is not null or ba.ACCIDENT_NO is not null or  ob.ACCIDENT_NO is not null
        or  ca.accident_id is not null)
        GROUP BY  coi.ACCIDENT_ID,coi.accident_no,coi.ACCIDENT_TYPE,coi.tenant_Id
        ) cd
        where
        cd.num1 = cd.num2
    </select>
    <select id="findOverdueOrder" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO" databaseId="mysql">

        select * from compensation_order_info
        where status in ('3','4')
        and  DATE_ADD(DATE_FORMAT(FLIGHT_DATE, '%Y-%m-%d'), INTERVAL 1 YEAR) &lt;  DATE_FORMAT(now(), '%Y-%m-%d')


    </select>


    <select id="findOverdueOrder" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO"  databaseId="oracle">
        select * from compensation_order_info
        where status in ('3','4')
        and TO_CHAR(add_months(TO_DATE(FLIGHT_DATE, 'YYYY-MM-DD'),12*1),'YYYY-MM-DD') &lt; TO_CHAR(SYSDATE,'YYYY-MM-DD')
    </select>


    <select id="getCompensationOrderInfo"
            resultType="com.swcares.aps.compensation.model.irregularflight.vo.GeneralCompensationOrderInfoDetailVO">
        SELECT
        OI.id,
        OI.BELONG_AIRLINE AS belongAirline,
        OI.TEMPORARY_COMPENSATION AS temporaryCompensation,
        OI.BELONG_AIRLINE_ABBR AS belongAirlineAbbr,
        OI.RELEASE_TIME AS releaseTime,
        OI.RELEASE AS release,
        OI.COMMODITY_ID AS commodityID,
        OI.STANDARD_SELECT_TYPE AS standardSelectType,
        OI.REMARK AS remark,
        OI.input_source inputSource,
        OI.CLOSE_USER AS closeUser,
        OI.CLOSE_TIME AS closeTime,
        OI.COMPENSATE_SUB_TYPE as compensateSubType,
        OI.accident_id,
        OI.accident_no,
        OI.order_no,
        OI.choice_segment choiceSegment,
        OI.flight_no,
        OI.flight_date,
        OI.choice_segment_ch choiceSegmentCh,
        OI.service_city  serviceCity,
        OI.status status,
        OI.source source,
        OI.compensate_type compensateType,
        OI.accident_type AS accidentType,
        OI.COMPENSATE_STANDARD as compensationStandard,
        OI.ACCIDENT_SUB_TYPE AS fcType,
        uc.ID as userId,
        COUNT( decode( pi.receive_status, 1, 'true', NULL ) ) + COUNT( decode( pi.receive_status, 1, decode( pi.with_baby, '1', 'true', NULL ), NULL ) ) actualCarryOutNum,
        COUNT( DISTINCT pi.pax_id ) + COUNT( decode( pi.with_baby, '1', 'true', NULL ) ) AS planCarryOutNum,
        COUNT( decode( pi.switch_off, 1, 'true', NULL ) ) AS frozenNum,
        SUM( decode( pi.receive_status, 1, current_amount, 0 ) ) actualCompensateMoney,
        oi.sum_money  AS planCompensateMoney,
        OI.CREATED_BY AS created_by,
        OI.CREATED_TIME AS created_time,
        case
        when get_audit_count(#{userId},OI.id)=0 then 'N'
        when get_audit_count(#{userId},OI.id)>0 then 'Y'
        end as toExamine,
        cai.task_id taskId
        FROM
        compensation_order_info OI
        LEFT JOIN compensation_pax_info pi ON oi.id = pi.order_id
        LEFT JOIN compensation_audit_info cai ON oi.id = cai.order_id
        LEFT JOIN uc_employee ue ON  OI.CREATED_BY = concat(ue.name ,ue.JOB_NUMBER)
        LEFT JOIN UC_USER uc ON uc.EMPLOYEE_ID = ue.id
        WHERE
        OI.ID = #{id}
        GROUP BY
        OI.id,
        uc.ID,
        OI.BELONG_AIRLINE,
        OI.TEMPORARY_COMPENSATION,
        OI.BELONG_AIRLINE_ABBR,
        OI.RELEASE_TIME,
        OI.CLOSE_USER,
        OI.CLOSE_TIME,
        OI.COMPENSATE_SUB_TYPE,
        OI.RELEASE,
        OI.COMMODITY_ID,
        OI.STANDARD_SELECT_TYPE,
        OI.input_source,
        OI.REMARK,
        OI.status,
        OI.compensate_type,
        OI.service_city,
        OI.accident_id,
        OI.accident_no,
        OI.order_no,
        OI.flight_no,
        OI.flight_date,
        cai.task_id,
        OI.CHOICE_SEGMENT,
        OI.ACCIDENT_TYPE,
        OI.ACCIDENT_SUB_TYPE,
        OI.COMPENSATE_STANDARD,
        OI.CREATED_TIME,
        OI.CREATED_BY,
        oi.sum_money,
        OI.source,
        OI.choice_segment_ch
    </select>

    <select id="getTenantIdByCode" resultType="java.lang.Long">
        select id FROM SYS_TENANT where TENANT_CODE = #{tenantCode}
    </select>

    <select id="getTenantById" resultType="map">
        select TENANT_CODE,COMPANY_NAME,TENANT_NAME,ID FROM SYS_TENANT where ID = #{id}
    </select>

    <select id="findNeedRefreshWorkflowUserInfoOrder" resultType="com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO">
        SELECT * FROM
            (
                SELECT o.*,
                     (SELECT count(*) FROM COMPENSATION_AUDIT_INFO t WHERE t.ORDER_ID = o.ID ) AS all_amount
                FROM compensation_order_info o
                WHERE o.status = '2' ) tmp
            WHERE tmp.all_amount = 0
    </select>


    <update id="updateLuggageBusinessReview">

        update compensation_order_info set LUGGAGE_BUSINESS_REVIEW = '1', LUGGAGE_BUSINESS_REVIEW_USER = #{userId} ,LUGGAGE_BUSINESS_REVIEW_TIME = SYSDATE
        where 1=1
        <!--复核操作可针对；领取状态为已领取且支付状态为支付成功且补偿单类型为异常行李-->
        and ACCIDENT_TYPE = '2'
        and (LUGGAGE_BUSINESS_REVIEW != '1' OR LUGGAGE_BUSINESS_REVIEW IS null)
        and id = #{orderId}
    </update>
</mapper>
