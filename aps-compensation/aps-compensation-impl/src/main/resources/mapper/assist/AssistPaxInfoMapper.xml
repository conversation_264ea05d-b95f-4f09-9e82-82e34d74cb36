<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.assist.mapper.AssistPaxListMapper">
    <select id="getPaxList" resultType="com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO" databaseId="mysql">
        SELECT DISTINCT cpi.pax_name,cpi.id_no,cpi.is_child,coi.flight_no,coi.flight_date,
               case cpi.with_baby
                   when 1 then 'Y'
                   else 'N'
                   end as withBaby,
               (select sum(c1.current_amount) from compensation_pax_info c1
                left join compensation_order_info c2 on c1.order_id=c2.id
                where c2.flight_no = #{dto.flightNo} and c2.flight_date = #{dto.flightDate} and c2.`status` = '4'
                and c1.receive_status = '0'
                and c1.switch_off = '0'
                and c1.id_no = cpi.id_no
               ) as planCompensateMoney,
               (select group_concat(c3.tkt_no) from
                    (select distinct c1.tkt_no,c1.id_no from compensation_pax_info c1
                    left join compensation_order_info c2 on c1.order_id=c2.id
                    where c2.flight_no = #{dto.flightNo} and c2.flight_date = #{dto.flightDate} and c2.`status` = '4'
                    and c1.receive_status = '0'
                    and c1.switch_off = '0') as c3
               where c3.id_no=cpi.id_no group by c3.id_no
               )as tkt_no,
               (select min(c1.tkt_issue_date) from compensation_pax_info c1
                left join compensation_order_info c2 on c1.order_id=c2.id
                where c2.flight_no = #{dto.flightNo} and c2.flight_date = #{dto.flightDate} and c1.id_no = cpi.id_no
               )as tktDate,
               if(instr(#{dto.mainClass},cpi.main_class) or instr(#{dto.mainClass},cpi.sub_class),1,0)as isBClass
        from compensation_pax_info cpi
        left join compensation_order_info coi on cpi.order_id = coi.id
        where coi.flight_no = #{dto.flightNo} and coi.flight_date = #{dto.flightDate} and coi.`status` = '4'
        and cpi.receive_status = '0'
        and cpi.switch_off = '0'
        <if test="dto.name != null and dto.name !='' ">
            and cpi.pax_name like concat("%",#{dto.name},"%")
        </if>
        group by cpi.pax_name,cpi.id_no,withBaby,is_child,cpi.current_amount,cpi.main_class,cpi.sub_class
        order by convert(left(pax_name,1)  using gbk) asc,isBClass desc,tktDate
    </select>

    <select id="getPaxList" resultType="com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO" databaseId="oracle">
        SELECT
            t.pax_name,
            t.id_no,
            t.is_child,
            max(t.flight_no) flight_no,
            max(t.flight_date) flight_date,
            t.withBaby,
            t.planCompensateMoney,
            max(t.tkt_no) tkt_no,
            max(t.tktDate) tktDate,
            t.isBClass
        FROM
            (
                SELECT DISTINCT
                    cpi.pax_name,
                    cpi.id_no,
                    cpi.is_child,
                    coi.flight_no,
                    coi.flight_date,
                    CASE
                        cpi.with_baby
                        WHEN '1' THEN 'Y'
                        ELSE 'N'
                        END as withBaby,
                    (
                        SELECT
                            sum( c1.current_amount )
                        FROM
                            compensation_pax_info c1
                                LEFT JOIN compensation_order_info c2 ON c1.order_id = c2.id
                        WHERE
                            c2.flight_no = #{dto.flightNo}
                          AND c2.flight_date = #{dto.flightDate}
                          AND c2.status = '4'
                          AND c1.receive_status = '0'
                          AND c1.switch_off = '0'
                          AND c1.id_no = cpi.id_no
                          AND c2.COMPENSATE_TYPE = '1'
                          AND c2.source='1'
                    ) AS planCompensateMoney,
                    (
                        SELECT
                            to_char(wm_concat(c3.tkt_no))
                        FROM
                            (
                                SELECT DISTINCT
                                    c1.tkt_no,
                                    c1.id_no
                                FROM
                                    compensation_pax_info c1
                                        LEFT JOIN compensation_order_info c2 ON c1.order_id = c2.id
                                WHERE
                                    c2.flight_no = #{dto.flightNo}
                                  AND c2.flight_date = #{dto.flightDate}
                                  AND c2.status = '4'
                                  AND c1.receive_status = '0'
                                  AND c1.switch_off = '0'
                                  AND c2.source='1'
                            )  c3
                        WHERE
                            c3.id_no = cpi.id_no
                        GROUP BY
                            c3.id_no
                    ) AS tkt_no,
                    (
                        SELECT
                            min( c1.tkt_issue_date )
                        FROM
                            compensation_pax_info c1
                                LEFT JOIN compensation_order_info c2 ON c1.order_id = c2.id
                        WHERE
                            c2.flight_no = #{dto.flightNo}
                          AND c2.flight_date =  #{dto.flightDate}
                          AND c1.id_no = cpi.id_no
                          AND c2.source='1'
                    ) AS tktDate,
                    CASE
                    WHEN instr(#{dto.mainClass}, cpi.main_class )>0 OR instr(#{dto.mainClass}, cpi.sub_class )>0 then '1'
                    ELSE '0'
                    END AS isBClass
            FROM
		compensation_pax_info cpi
		LEFT JOIN compensation_order_info coi ON cpi.order_id = coi.id
        WHERE
            coi.flight_no = #{dto.flightNo}
          AND coi.flight_date = #{dto.flightDate}
          AND coi.status = '4'
          AND cpi.receive_status = '0'
          AND cpi.switch_off = '0'
          AND coi.COMPENSATE_TYPE = '1'
          AND coi.source='1'
        <if test="dto.name != null and dto.name !='' ">
            and cpi.pax_name like concat(concat('%',#{dto.name}),'%')
        </if>
        ORDER BY
            FN_GETPY(SUBSTR(cpi.pax_name, 0, 1)) asc,
            isBClass DESC,
            tktDate
            )  t

        GROUP BY
            t.pax_name,
            t.id_no,
            t.withBaby,
            t.is_child,
            t.planCompensateMoney,
            t.isBClass
        ORDER BY  FN_GETPY(SUBSTR(t.pax_name, 0, 1)) ASC,
         t.isBClass DESC,
         max(t.tktDate)
    </select>

    <select id="findPaxAmount" resultType="com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO" databaseId="mysql">
        SELECT count(*) receivedNum,cpi.pax_name,cpi.id_no FROM compensation_pax_info cpi
        LEFT JOIN compensation_apply_pax cap on cpi.id = cap.pax_info_id
        LEFT JOIN compensation_apply_order cao on cao.id = cap.apply_id
        where cao.telephone = #{phone}
        and cao.apply_way = '2'  and DATE_SUB(CURDATE(), INTERVAL 6 MONTH) &lt;= cao.created_time
        group by cpi.pax_name,cpi.id_no
    </select>

    <select id="findPaxAmount" resultType="com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO" databaseId="oracle">
        SELECT count(*) receivedNum,cpi.pax_name,cpi.id_no FROM compensation_pax_info cpi
        LEFT JOIN compensation_apply_pax cap on cpi.id = cap.pax_info_id
        LEFT JOIN compensation_apply_order cao on cao.id = cap.apply_id
        where cao.telephone = #{phone}
          and cao.apply_way = '2'  and add_months(sysdate, -6) &lt;= cao.created_time
        group by cpi.pax_name,cpi.id_no
    </select>

</mapper>
