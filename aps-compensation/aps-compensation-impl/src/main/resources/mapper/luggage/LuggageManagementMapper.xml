<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageManagementMapper">
    <!-- 唐康-->
    <select id="findLuggageStockDetailed" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO" databaseId="mysql">
        SELECT
        executor,
        status,
        amount,
        stock,
        created_time,
        reason
        FROM luggage_stock_details
        <where>
                luggage_id=#{dto.luggageId}
            <if test="dto.status != null and dto.status != ''">
                and  status = #{dto.status}
            </if>
            <if test="dto.executor != null and dto.executor != ''">
                and  executor like concat('%',#{dto.executor},'%')
            </if>
            <if test="dto.executionStartTime != null and dto.executionStartTime != ''">
                and DATE_FORMAT(created_time,'%Y-%m-%d')  BETWEEN #{dto.executionStartTime} and #{dto.executionEndTime}
            </if>
       </where>
        order by created_time desc
    </select>
    <select id="findLuggageStockDetailed" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO" databaseId="oracle">
        SELECT
        executor,
        status,
        amount,
        stock,
        created_time,
        reason
        FROM luggage_stock_details
        <where>
            luggage_id=#{dto.luggageId}
            <if test="dto.status != null and dto.status != ''">
                and  status = #{dto.status}
            </if>
            <if test="dto.executor != null and dto.executor != ''">
                and  executor like concat(concat('%',#{dto.executor}),'%')
            </if>
            <if test="dto.executionStartTime != null and dto.executionStartTime != ''">
                and TO_CHAR(created_time ,'yyyy-mm-dd')  BETWEEN #{dto.executionStartTime} and #{dto.executionEndTime}
            </if>
        </where>
        order by created_time desc
    </select>

    <update id="updateLuggageStockDetailed">
             update luggage_management lm
             <set>
                 <if test="dto.amount != null  ">
                     lm.stock=lm.stock + (#{dto.amount})
                 </if>
             </set>
             where id=#{dto.luggageId}
                <if test="dto.amount != null and dto.amount &lt; 0  ">
                      and lm.stock >= ABS(#{dto.amount})
                 </if>
    </update>

    <select id="getLuggageCompensationReportDetail" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO" databaseId="oracle">
        SELECT
        coi.ID ,
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        bai.pod orgCity,
        bai.poa dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT bai.IS_DOMESTIC FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) regionType,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        bai.tkt_no,
        bai.baggage_no pkgNo,
        cmi.AMOUNT compensateNum,
        lm.LUGGAGE_NAME luggageName,
        lm.BRAND luggageBrand,
        lm.PRICE luggagePrice,
        lm."SIZES" luggageSize,
        NVL2(cei.EXPRESS_TYPE, '2',DECODE(cpi.RECEIVE_STATUS,'1','1',null)) expressType,
        cei.EXPRESS_NO,
        cfi.STD
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN BAGGAGE_ACCIDENT_INFO bai ON coi.ACCIDENT_ID  = bai.ID
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_MATERIAL_INFO cmi ON coi.ID = cmi.ORDER_ID
        LEFT JOIN LUGGAGE_MANAGEMENT lm ON lm.LUGGAGE_NO = cmi.MATERIAL_NO
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cmi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_EXPRESS_INFO cei ON coi.ORDER_NO  = cei.ORDER_NO
        <where>
            coi.status in (4,5,6) AND coi.accident_type = '2' AND coi.COMPENSATE_TYPE = '3'  <!-- 事故单类型为异常行李并且补偿方式为实物补偿 -->
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and bai.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.luggageNo != null and dto.luggageNo != ''">
                and bai.baggage_no = #{dto.luggageNo}
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 1">
                and cpi.RECEIVE_STATUS = '1' and cei.EXPRESS_TYPE IS NULL
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 2">
                and cei.EXPRESS_TYPE IS NOT NULL
            </if>
            <if test="dto.luggageName != null and dto.luggageName != ''">
                and lm.LUGGAGE_NAME = #{dto.luggageName}
            </if>
            <if test="dto.brand != null and dto.brand.size() > 0">
                and lm.BRAND in
                <foreach collection="dto.brand" item="brand"  open="(" separator="," close=")">
                    #{brand}
                </foreach>
            </if>
            <if test="dto.size != null and dto.size.size() > 0">
                and lm."SIZES" in
                <foreach collection="dto.size" item="size"  open="(" separator="," close=")">
                    #{size}
                </foreach>
            </if>
            <if test="dto.expressNo != null and dto.expressNo != ''">
                and cei.EXPRESS_NO = #{dto.expressNo}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,NLSSORT(cpi.PAX_NAME,'NLS_SORT = SCHINESE_PINYIN_M')
    </select>


    <select id="getLuggageCompensationReportDetail" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO" databaseId="mysql">
        SELECT
        coi.ID ,
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        bai.pod orgCity,
        bai.poa dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT bai.IS_DOMESTIC FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) regionType,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        bai.tkt_no,
        bai.baggage_no pkgNo,
        cmi.AMOUNT compensateNum,
        lm.LUGGAGE_NAME luggageName,
        lm.BRAND luggageBrand,
        lm.PRICE luggagePrice,
        lm."SIZES" luggageSize,
        if(cei.EXPRESS_TYPE is null,if(cpi.RECEIVE_STATUS == '1','1','2'),if(cei.EXPRESS_TYPE == '1','2','2')) expressType,
        cei.EXPRESS_NO,
        cfi.STD
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN BAGGAGE_ACCIDENT_INFO bai ON coi.ACCIDENT_ID  = bai.ID
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_MATERIAL_INFO cmi ON coi.ID = cmi.ORDER_ID
        LEFT JOIN LUGGAGE_MANAGEMENT lm ON lm.LUGGAGE_NO = cmi.MATERIAL_NO
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cmi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_EXPRESS_INFO cei ON coi.ORDER_NO  = cei.ORDER_NO
        <where>
            coi.status in (4,5,6) AND coi.accident_type = '2' AND coi.COMPENSATE_TYPE = '3'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and bai.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.luggageNo != null and dto.luggageNo != ''">
                and bai.baggage_no = #{dto.luggageNo}
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 1">
                and cpi.RECEIVE_STATUS = '1' and cei.EXPRESS_TYPE IS NULL
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 2">
                and cei.EXPRESS_TYPE IS NOT NULL
            </if>
            <if test="dto.luggageName != null and dto.luggageName != ''">
                and lm.LUGGAGE_NAME = #{dto.luggageName}
            </if>
            <if test="dto.brand != null and dto.brand.size() > 0">
                and lm.BRAND in
                <foreach collection="dto.brand" item="brand"  open="(" separator="," close=")">
                    #{brand}
                </foreach>
            </if>
            <if test="dto.size != null and dto.size.size() > 0">
                and lm."SIZES" in
                <foreach collection="dto.size" item="size"  open="(" separator="," close=")">
                    #{size}
                </foreach>
            </if>

            <if test="dto.expressNo != null and dto.expressNo != ''">
                and cei.EXPRESS_NO = #{dto.expressNo}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,CONVERT(cpi.PAX_NAME USING GBK)
    </select>


    <select id="getLuggageCompensationReport"
            resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO" databaseId="oracle">
        SELECT
        coi.ID ,
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        bai.pod orgCity,
        bai.poa dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT fpd.DICT_VALUE FROM BD_AIRPORT_INFO bai
        LEFT JOIN FLT_PASSENGER_DICTIONARY fpd ON bai.IS_DOMESTIC = fpd.DICT_KEY
        WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY
        AND fpd.DICT_TYPE = 'FlightTypeEnum') regionType,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        bai.tkt_no,
        bai.baggage_no pkgNo,
        cmi.AMOUNT compensateNum,
        lm.LUGGAGE_NAME luggageName,
        lm.BRAND luggageBrand,
        lm.PRICE luggagePrice,
        lm."SIZES" luggageSize,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = NVL2(cei.EXPRESS_TYPE, '2',DECODE(cpi.RECEIVE_STATUS,'1','1',null))
        AND sdd.DICT_TYPE = 'baggage_receive_type') expressType,
        cei.EXPRESS_NO,
        cfi.STD
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN BAGGAGE_ACCIDENT_INFO bai ON coi.ACCIDENT_ID  = bai.ID
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_MATERIAL_INFO cmi ON coi.ID = cmi.ORDER_ID
        LEFT JOIN LUGGAGE_MANAGEMENT lm ON lm.LUGGAGE_NO = cmi.MATERIAL_NO
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cmi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_EXPRESS_INFO cei ON coi.ORDER_NO  = cei.ORDER_NO
        <where>
            coi.status in (4,5,6) AND coi.accident_type = '2' AND coi.COMPENSATE_TYPE = '3'  <!-- 事故单类型为异常行李并且补偿方式为实物补偿 -->
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and bai.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.luggageNo != null and dto.luggageNo != ''">
                and bai.baggage_no = #{dto.luggageNo}
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 1">
                and cpi.RECEIVE_STATUS = '1' and cei.EXPRESS_TYPE IS NULL
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 2">
                and cei.EXPRESS_TYPE IS NOT NULL
            </if>
            <if test="dto.luggageName != null and dto.luggageName != ''">
                and lm.LUGGAGE_NAME = #{dto.luggageName}
            </if>
            <if test="dto.brand != null and dto.brand.size() > 0">
                and lm.BRAND in
                <foreach collection="dto.brand" item="brand"  open="(" separator="," close=")">
                    #{brand}
                </foreach>
            </if>
            <if test="dto.size != null and dto.size.size() > 0">
                and lm."SIZES" in
                <foreach collection="dto.size" item="size"  open="(" separator="," close=")">
                    #{size}
                </foreach>
            </if>
            <if test="dto.expressNo != null and dto.expressNo != ''">
                and cei.EXPRESS_NO = #{dto.expressNo}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,NLSSORT(cpi.PAX_NAME,'NLS_SORT = SCHINESE_PINYIN_M')
    </select>



    <select id="getLuggageCompensationReport"
            resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO" databaseId="mysql">
        SELECT
        coi.ID ,
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        bai.pod orgCity,
        bai.poa dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT fpd.DICT_VALUE FROM BD_AIRPORT_INFO bai
        LEFT JOIN FLT_PASSENGER_DICTIONARY fpd ON bai.IS_DOMESTIC = fpd.DICT_KEY
        WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY
        AND fpd.DICT_TYPE = 'FlightTypeEnum') regionType,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        bai.tkt_no,
        bai.baggage_no pkgNo,
        cmi.AMOUNT compensateNum,
        lm.LUGGAGE_NAME luggageName,
        lm.BRAND luggageBrand,
        lm.PRICE luggagePrice,
        lm."SIZES" luggageSize,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = if(cei.EXPRESS_TYPE is null,if(cpi.RECEIVE_STATUS == '1','1','2'),if(cei.EXPRESS_TYPE == '1','2','2'))
        AND sdd.DICT_TYPE = 'baggage_receive_type') expressType,
        cei.EXPRESS_NO,
        cfi.STD
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN BAGGAGE_ACCIDENT_INFO bai ON coi.ACCIDENT_ID  = bai.ID
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_MATERIAL_INFO cmi ON coi.ID = cmi.ORDER_ID
        LEFT JOIN LUGGAGE_MANAGEMENT lm ON lm.LUGGAGE_NO = cmi.MATERIAL_NO
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cmi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_EXPRESS_INFO cei ON coi.ORDER_NO  = cei.ORDER_NO
        <where>
            coi.status in (4,5,6) AND coi.accident_type = '2' AND coi.COMPENSATE_TYPE = '3'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and bai.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.luggageNo != null and dto.luggageNo != ''">
                and bai.baggage_no = #{dto.luggageNo}
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 1">
                and cpi.RECEIVE_STATUS = '1' and cei.EXPRESS_TYPE IS NULL
            </if>
            <if test="dto.expressType != null and dto.expressType != '' and dto.expressType == 2">
                and cei.EXPRESS_TYPE IS NOT NULL
            </if>
            <if test="dto.luggageName != null and dto.luggageName != ''">
                and lm.LUGGAGE_NAME = #{dto.luggageName}
            </if>
            <if test="dto.brand != null and dto.brand.size() > 0">
                and lm.BRAND in
                <foreach collection="dto.brand" item="brand"  open="(" separator="," close=")">
                    #{brand}
                </foreach>
            </if>
            <if test="dto.size != null and dto.size.size() > 0">
                and lm."SIZES" in
                <foreach collection="dto.size" item="size"  open="(" separator="," close=")">
                    #{size}
                </foreach>
            </if>

            <if test="dto.expressNo != null and dto.expressNo != ''">
                and cei.EXPRESS_NO = #{dto.expressNo}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,CONVERT(cpi.PAX_NAME USING GBK)
    </select>
</mapper>