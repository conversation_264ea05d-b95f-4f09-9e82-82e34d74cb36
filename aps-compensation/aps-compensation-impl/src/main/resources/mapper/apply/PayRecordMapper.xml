<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.apply.mapper.PayRecordMapper">

    <select id="page" resultType="com.swcares.aps.compensation.model.apply.vo.PayRecordDOVO">
        select * from compensation_pay_record
    </select>

    <update id="doUpdateById">
        UPDATE compensation_pay_record
        SET  pay_status = #{payRecordDO.payStatus}
        <if test="payRecordDO.returnSerialNo !=null and payRecordDO.returnSerialNo != ''">
            ,return_serial_no = #{payRecordDO.returnSerialNo}
        </if>
        <if test="payRecordDO.transDate !=null and payRecordDO.transDate != ''">
            ,trans_date = #{payRecordDO.transDate}
        </if>
        <if test="payRecordDO.errCode !=null and payRecordDO.errCode != ''">
            ,err_code = #{payRecordDO.errCode}
        </if>

        <if test="payRecordDO.transCode !=null and payRecordDO.transCode != ''">
            ,trans_code = #{payRecordDO.transCode}
        </if>
        <if test="payRecordDO.transMsg !=null and payRecordDO.transMsg != ''">
            ,trans_msg = #{payRecordDO.transMsg}
        </if>
        <if test="payRecordDO.transSubCode !=null and payRecordDO.transSubCode != ''">
            ,trans_sub_code = #{payRecordDO.transSubCode}
        </if>
        <if test="payRecordDO.transSubMsg !=null and payRecordDO.transSubMsg != ''">
            ,trans_sub_msg = #{payRecordDO.transSubMsg}
        </if>
        <if test="payRecordDO.paxReceiveState !=null and payRecordDO.paxReceiveState != ''">
            ,pax_receive_state=#{payRecordDO.paxReceiveState}
        </if>
        <if test="payRecordDO.payTypeApiVersion !=null and payRecordDO.payTypeApiVersion != ''">
            ,pay_type_api_version = #{payRecordDO.payTypeApiVersion}
        </if>
        <if test="payRecordDO.wxReceivePackageInfo !=null and payRecordDO.wxReceivePackageInfo != ''">
            ,wx_receive_package_info = #{payRecordDO.wxReceivePackageInfo}
        </if>
        <if test="payRecordDO.payReturnTime !=null">
            ,pay_return_time = #{payRecordDO.payReturnTime}
        </if>
        <if test="payRecordDO.payStartTime !=null">
            ,pay_start_time = #{payRecordDO.payStartTime}
        </if>
        <if test="payRecordDO.errCodeDes !=null">
            ,err_code_des = #{payRecordDO.errCodeDes}
        </if>

        WHERE id = #{payRecordDO.id}
    </update>

</mapper>
