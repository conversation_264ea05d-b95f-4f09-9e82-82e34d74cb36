<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper">

    <select id="page" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyOrderVO">
        select * from compensation_apply_order
    </select>

    <select id="findApplyAuthPax" resultType="com.swcares.aps.compensation.model.apply.vo.AuthPaxVO">
        select t.order_id as orderId,t.switch_off as switchOff,t.receive_status as receiveStatus,t.current_amount as money
        from compensation_pax_info t LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id = #{dto.paxId}
        and t2.id = #{dto.orderId}
        and t2.status = '4'
        ORDER BY t.switch_off desc
    </select>

    <select id="queryRecordCount" resultType="int">
        SELECT
        COUNT(*)
        FROM
        compensation_apply_order cao
        WHERE 1 = 1
        <if test="way != null and way != ''">
            AND cao.apply_way = #{way}
        </if>
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>

        <if test="dto.flightNo != null and dto.flightNo != ''">
            AND cao.FLIGHT_NO = #{dto.flightNo}
        </if>

        <if test="dto.flightDate != null and dto.flightDate != ''">
            AND cao.FLIGHT_DATE = #{dto.flightDate}
        </if>
    </select>

    <select id="queryRecord" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="mysql">
        SELECT
        flightNo,
        flightDate,
        segmentCh,
        accidentType,
        applyAmount,
        recordId,
        (case when successNum>0 and successNum!=applyStatusNum then '4'
        when 	successNum = applyStatusNum then '2'
        when failNum = applyStatusNum then '3'
        else '1' end )AS status
<!--        ( CASE WHEN INSTR( pay_status, '3' ) THEN '3' WHEN INSTR( pay_status, '2' ) THEN '2' ELSE '1' END ) AS status-->
        FROM
        (
        SELECT
        cfi.flight_no AS flightNo,
        cfi.flight_date AS flightDate,
        cfi.segment_ch AS segmentCh,
        coi.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        count(cap.apply_pax_status) applyStatusNum,
        count(case when cap.apply_pax_status=2 then 1 else null end) successNum,
        count(case when cap.apply_pax_status=3 then 1 else null end) failNum,
        GROUP_CONCAT(cap.apply_pax_status) AS pay_status,
        cpr.pax_receive_state paxReceiveState
        FROM
        compensation_apply_order AS cao
        LEFT JOIN compensation_apply_pax AS cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record AS cpr ON cpr.apply_id = cao.id AND cap.pax_info_id = cpr.apply_pax_id
        LEFT JOIN compensation_flight_info AS cfi ON cfi.order_id = cap.order_id
        LEFT JOIN compensation_order_info AS coi ON coi.id = cfi.order_id
        WHERE cao.apply_way='0'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        GROUP BY
        cfi.flight_no,
        cfi.flight_date,
        cfi.segment_ch,
        coi.accident_type,
        cao.apply_amount,
        cao.id,
        cfi.std,
        cao.created_time
        ORDER BY
        cfi.flight_date DESC,
        cfi.std ASC,
        cao.created_time DESC
        ) AS t
    </select>

    <select id="queryRecord" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="oracle">
        SELECT
        cao.flight_no AS flightNo,
        cao.flight_date AS flightDate,
        cao.segment_ch AS segmentCh,
        cao.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        cap.apply_pax_status status,
        cap.apply_pax_status as applyPaxStatus,
        (case when cap.apply_pax_status=2 then 3
        when cap.apply_pax_status=3 then 2
        else cap.apply_pax_status end ) pageSort,
        cpr.pax_receive_state paxReceiveState
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record cpr ON cpr.apply_id = cao.id AND cap.pax_info_id = cpr.apply_pax_id
        WHERE cao.apply_way='0'


        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        order by pageSort asc ,CAO.CREATED_TIME desc
    </select>

    <select id="queryReplaceRecord" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO">
         SELECT
            cao.flight_no AS flightNo,
            cao.flight_date AS flightDate,
            cao.segment_ch AS segmentCh,
            cao.accident_type AS accidentType,
            cao.apply_amount AS applyAmount,
            cao.id AS recordId,
            cao.apply_status AS applyStatus,
            cap.apply_pax_status AS status,
            cap.apply_pax_status AS applyPaxStatus
        FROM
            compensation_apply_order cao
            LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id

        WHERE cao.apply_way='1'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        ORDER BY (CASE WHEN applyPaxStatus =2 THEN 3 WHEN applyPaxStatus =3 THEN 2 ELSE applyPaxStatus END ) asc
        ,CAO.CREATED_TIME desc

    </select>

    <select id="replaceFilterRecord" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="mysql">
        SELECT
        cao.flight_no AS flightNo,
        cao.flight_date AS flightDate,
        cao.segment_ch AS segmentCh,
        cao.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        cao.apply_status AS applyStatus,
        cap.apply_pax_status AS applyPaxStatus,
        cpr.pax_receive_state AS paxReceiveState
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record cpr ON cpr.apply_id = cao.id AND cap.pax_info_id = cpr.apply_pax_id
        WHERE cao.apply_way='1'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            AND cao.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.flightStartDate != null and dto.flightStartDate !=  ''">
            <if test="dto.flightEndDate != null and dto.flightEndDate !=  ''">
                and cao.flight_date BETWEEN #{dto.flightStartDate} AND #{dto.flightEndDate}
            </if>
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime !=  ''">
            <if test="dto.createdEndTime != null and dto.createdEndTime != ''">
                and DATE_FORMAT(cao.created_time,'%Y-%m-%d') BETWEEN #{dto.createdStartTime} AND #{dto.createdEndTime}
            </if>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentType)">
            and cao.accident_type in
            <foreach item="item" collection="dto.accidentType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.applyStatus)">
            and cao.apply_status in
            <foreach item="item" collection="dto.applyStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.applyPaxStatus)">
            and cap.apply_pax_status in
            <foreach item="item" collection="dto.applyPaxStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY (CASE WHEN applyPaxStatus =2 THEN 3 WHEN applyPaxStatus =3 THEN 2 ELSE applyPaxStatus END ) asc
        ,CAO.CREATED_TIME desc
    </select>

    <select id="replaceFilterRecord" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="oracle">
        SELECT
        cao.flight_no AS flightNo,
        cao.flight_date AS flightDate,
        cao.segment_ch AS segmentCh,
        cao.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        cao.apply_status AS applyStatus,
        cap.apply_pax_status AS status,
        cap.apply_pax_status AS applyPaxStatus,
        cpr.pax_receive_state AS paxReceiveState
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record cpr ON cpr.apply_id = cao.id AND cap.pax_info_id = cpr.apply_pax_id
        WHERE cao.apply_way='1'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            AND cao.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.flightStartDate != null and dto.flightStartDate !=  ''">
            <if test="dto.flightEndDate != null and dto.flightEndDate !=  ''">
                and cao.flight_date BETWEEN #{dto.flightStartDate} AND #{dto.flightEndDate}
            </if>
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime !=  ''">
            <if test="dto.createdEndTime != null and dto.createdEndTime != ''">
                and to_char(cao.created_time,'yyyy-MM-dd') BETWEEN #{dto.createdStartTime} AND #{dto.createdEndTime}
            </if>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentType)">
            and cao.accident_type in
            <foreach item="item" collection="dto.accidentType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.applyStatus)">
            and cao.apply_status in
            <foreach item="item" collection="dto.applyStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.applyPaxStatus)">
            and cap.apply_pax_status in
            <foreach item="item" collection="dto.applyPaxStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        ORDER BY (CASE WHEN applyPaxStatus =2 THEN 3 WHEN applyPaxStatus =3 THEN 2 ELSE applyPaxStatus END ) asc
        ,CAO.CREATED_TIME desc
    </select>

    <select id="getReceive" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="mysql">

        SELECT t.* ,
        (case when successNum>0 and successNum!=applyStatusNum then '4'
        when 	successNum = applyStatusNum then '2'
        when failNum = applyStatusNum then '3'
        else '1' end )AS status
        FROM(

        SELECT
        cfi.flight_no AS flightNo,
        cfi.flight_date AS flightDate,
        cfi.segment_ch AS segmentCh,
        coi.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        count(cap.apply_pax_status) applyStatusNum,
        count(case when cap.apply_pax_status=2 then 1 else null end) successNum,
        count(case when cap.apply_pax_status=3 then 1 else null end) failNum
        FROM
        compensation_apply_order AS cao
        LEFT JOIN compensation_apply_pax AS cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record AS cpr ON cpr.apply_id = cao.id
        LEFT JOIN compensation_flight_info AS cfi ON cfi.order_id = cap.order_id
        LEFT JOIN compensation_order_info AS coi ON coi.id = cfi.order_id
        WHERE cao.apply_way='0'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            AND cfi.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.flightStartDate != null and dto.flightStartDate !=  ''">
            <if test="dto.flightEndDate != null and dto.flightEndDate !=  ''">
                and cfi.flight_date BETWEEN #{dto.flightStartDate} AND #{dto.flightEndDate}
            </if>
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime !=  ''">
            <if test="dto.createdEndTime != null and dto.createdEndTime != ''">
                and DATE_FORMAT(cao.created_time,'%Y-%m-%d') BETWEEN #{dto.createdStartTime} AND #{dto.createdEndTime}
            </if>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentType)">
            and coi.accident_type in
            <foreach item="item" collection="dto.accidentType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP by cfi.flight_no,
        cfi.flight_date,
        cfi.segment_ch,
        coi.accident_type,
        cao.apply_amount ,
        cao.id,
        cfi.flight_date,
        cfi.std,
        cao.created_time
        ORDER BY cfi.flight_date DESC,cfi.std ASC,cao.created_time DESC
        )t
    </select>

    <select id="getReceive" resultType="com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO" databaseId="oracle">

        SELECT
        cao.flight_no AS flightNo,
        cao.flight_date AS flightDate,
        cao.segment_ch AS segmentCh,
        cao.accident_type AS accidentType,
        cao.apply_amount AS applyAmount,
        cao.id AS recordId,
        cap.apply_pax_status status,
        cap.apply_pax_status as applyPaxStatus,
        cpr.pax_receive_state AS paxReceiveState,
        (case when cap.apply_pax_status=2 then 3
        when cap.apply_pax_status=3 then 2
        else cap.apply_pax_status end ) pageSort
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
        LEFT JOIN compensation_pay_record cpr ON cpr.apply_id = cao.id
        WHERE cao.apply_way='0'
        <if test="dto.name != null and dto.name != ''">
            AND cao.apply_user = #{dto.name}
        </if>
        <if test="dto.idCard != null and dto.idCard != ''">
            AND cao.id_no = #{dto.idCard}
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            AND cao.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.flightStartDate != null and dto.flightStartDate !=  ''">
            <if test="dto.flightEndDate != null and dto.flightEndDate !=  ''">
                and cao.flight_date BETWEEN #{dto.flightStartDate} AND #{dto.flightEndDate}
            </if>
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime !=  ''">
            <if test="dto.createdEndTime != null and dto.createdEndTime != ''">
                and to_char(cao.created_time,'yyyy-MM-dd') BETWEEN #{dto.createdStartTime} AND #{dto.createdEndTime}
            </if>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.accidentType)">
            and cao.accident_type in
            <foreach item="item" collection="dto.accidentType" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.applyStatus)">
            and cap.apply_pax_status in
            <foreach item="item" collection="dto.applyStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        order by pageSort asc ,CAO.CREATED_TIME desc
    </select>

    <select id="getApplyInfoDetails" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO">
         SELECT DISTINCT
            cao.apply_code AS applyCode,
            cao.get_money_way AS getMoneyWay,
            cao.get_money_account AS getMoneyAccount,
            cao.created_time AS createdTime,
            cao.flight_date AS flightData,
            cao.flight_no AS flightNo,
            cao.std AS std,
            cao.sta AS sta,
            cao.segment_ch AS segmentCh,
            cao.segment AS segment,
            cpr.pax_receive_state AS paxReceiveState
        FROM
            compensation_apply_order cao
            left join compensation_pay_record cpr on cpr.apply_id = cao.id
        WHERE
            cao.apply_way='0' and cao.id = #{recordId}
            <if test="idCard != null and idCard != ''">
                and cao.id_no = #{idCard}
            </if>
    </select>

    <select id="getReplaceApplyInfoDetails" resultType="com.swcares.aps.compensation.model.apply.vo.ReplaceInfoDetailsVO">

        SELECT DISTINCT
            cao.apply_code AS applyCode,
            cao.apply_user AS applyUser,
            cao.id_no AS idNo,
            cao.telephone AS telephone,
            cao.get_money_way AS getMoneyWay,
            cao.get_money_account AS getMoneyAccount,
            cao.created_time AS createdTime,
            cao.accident_type AS accidentType,
            cao.flight_date AS flightData,
            cao.flight_no AS flightNo,
            cao.std AS std,
            cao.sta AS sta,
            cao.segment_ch AS segmentCh,
            cao.segment AS segment,
            cao.apply_status AS applyStatus,
            cap.apply_pax_status status,
            cap.apply_pax_status as applyPaxStatus,
            cao.collect_identity_card_photo AS photoId,
            cpr.pax_receive_state AS paxReceiveState
        FROM
            compensation_apply_order cao
            LEFT JOIN compensation_apply_pax cap ON cap.apply_id = cao.id
            LEFT JOIN compensation_pay_record cpr on cpr.apply_id = cao.id
        WHERE
            cao.apply_way='1' and cao.id = #{recordId}
            and cao.id_no = #{idCard}
    </select>

    <select id="getCompensateDetails" resultType="com.swcares.aps.compensation.model.apply.vo.CompensateDetailsVO">
         SELECT
            cao.apply_user AS applyUser,
            cao.telephone AS telephone,
            cao.id_no AS idNo,
            cpi.current_amount AS currentAmount,
            cpi.with_baby AS withBaby,
            cpi.tkt_no AS tktNo,
            cpi.main_class as mainClass,
            cpi.sub_class as subClass,
            coi.accident_type as accidentType,
            cap.apply_pax_status as payStatus,
            cap.apply_pax_error as errCodeDes,
            cpi.pax_name as paxName,
            cpi.is_child as isChild,
            cpr.pax_receive_state AS paxReceiveState,
            cpr.pay_type_api_version AS payTypeApiVersion,
            (select BAGGAGE_NO from BAGGAGE_ACCIDENT_INFO where ID = coi.ACCIDENT_ID) pkgNo
        FROM
            compensation_apply_order cao
            LEFT JOIN compensation_apply_pax cap ON cap.apply_id = cao.id
            LEFT JOIN compensation_pax_info cpi ON cpi.id = cap.pax_info_id
            left join compensation_order_info coi on coi.id = cpi.order_id
            left join compensation_pay_record cpr on (cpr.apply_pax_id= cap.pax_info_id and cao.id = cpr.apply_id)
        WHERE
            cao.apply_way='0' and cao.id = #{recordId}
            and cao.id_no = #{idCard}

         ORDER BY coi.created_time desc ,cpi.tkt_issue_date desc
    </select>

    <select id="getReplaceCompensateDetails" resultType="com.swcares.aps.compensation.model.apply.vo.ReplaceCompensateDetailsVO">
        SELECT
	    cpi.pax_name AS paxName,
	    cpi.id_no AS idNo,
	    cpi.tkt_no AS tktNo,
	    cap.pax_identity_card_photo AS photoId,
	    cpi.current_amount AS currentAmount,
	    cpi.with_baby AS withBaby,
	    cpi.main_class AS mainClass,
	    cpi.sub_class AS subClass,
        coi.accident_type AS accidentType,
	    cap.apply_pax_status AS receiveStatus,
	    cap.apply_pax_error AS errCodeDes,
	    cpi.is_child AS isChild,
        cpr.pax_receive_state AS paxReceiveState,
        cpr.pay_type_api_version AS payTypeApiVersion,
        (select BAGGAGE_NO from BAGGAGE_ACCIDENT_INFO where ID = coi.ACCIDENT_ID) pkgNo
        FROM
	    compensation_apply_order cao
	    LEFT JOIN compensation_apply_pax cap ON cap.apply_id = cao.id
	    LEFT JOIN compensation_pax_info cpi ON cpi.id = cap.pax_info_id
	    LEFT JOIN compensation_order_info coi ON coi.id = cpi.order_id
	    LEFT JOIN compensation_pay_record cpr ON ( cpr.apply_pax_id = cap.pax_info_id AND cao.id = cpr.apply_id )
        WHERE
        cao.apply_way='1' and cao.id = #{recordId}
        <if test="idCard != null and idCard != ''">
            and cao.id_no = #{idCard}
        </if>
        ORDER BY coi.created_time desc ,cpi.tkt_issue_date desc
    </select>



    <select id="webPage" resultType="com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxPageVO" databaseId="mysql">
        select DISTINCT
        o.id ,o.apply_code ,
        o.apply_status applyStatus,
        f.flight_no,f.flight_date,o.apply_user,
        o.telephone,o.apply_cust_num,o.apply_amount,o.created_time,
        o.get_money_way getMoneyWay,
        fa.accident_type as accidentType,
        af.task_id,ad.updated_time as auditTime,o.quick_pay,o.payment_waiting_period as paymentWaitingPeriod
        from compensation_apply_order o
        LEFT JOIN compensation_pay_record p on o.id = p.apply_id
        LEFT JOIN compensation_flight_info f on p.order_id = f.order_id
        LEFT JOIN compensation_order_info d on p.order_id = d.id
        LEFT JOIN flight_accident_info fa on d.accident_id = fa.id
        LEFT JOIN workflow_auditor_id_info af on af.business_value = o.id and project='apply-impl' and business='apply'
        LEFT JOIN compensation_apply_adt_record ad on o.id = ad.apply_id and ad.pax_info_id = -1

        where 1=1 and o.apply_way='1'
        <if test="dto.name != null and dto.name != ''">
            and o.apply_user like "%"  #{dto.name} "%"
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and f.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''
            and dto.flightEndDate != null and dto.flightEndDate != ''">
            and f.flight_date BETWEEN  #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime != ''
            and dto.createdEndTime != null and dto.createdEndTime != ''">
            and DATE_FORMAT(o.created_time,'%Y-%m-%d') BETWEEN  #{dto.createdStartTime} and #{dto.createdEndTime}
        </if>

        <if test="dto.flightNo != null and dto.flightNo != ''">
            and  f.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.accidentType !=null and dto.accidentType.length >0">
            and fa.accident_type in
            <foreach collection="dto.accidentType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        <if test="dto.applyStatus != null and dto.applyStatus.length >0 ">
            and o.apply_status in
            <foreach item="item" collection="dto.applyStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        order by f.flight_date DESC ,f.flight_no ,o.apply_status  ASC

    </select>

    <select id="webPage" resultType="com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxPageVO" databaseId="oracle">
        select DISTINCT
        o.id ,o.apply_code ,o."SOURCE",
        o.apply_status applyStatus,
        f.flight_no,f.flight_date,o.apply_user,
        o.telephone,o.apply_cust_num,o.apply_amount,o.created_time,
        o.get_money_way getMoneyWay,
        d.accident_type as accidentType,
        af.task_id,ad.updated_time as auditTime,o.quick_pay,o.payment_waiting_period as paymentWaitingPeriod
        from compensation_apply_order o
        LEFT JOIN compensation_pay_record p on o.id = p.apply_id
        LEFT JOIN compensation_flight_info f on p.order_id = f.order_id
        LEFT JOIN compensation_order_info d on p.order_id = d.id
        LEFT JOIN workflow_auditor_id_info af on af.business_value = o.id and project='apply-impl' and business='apply'
        LEFT JOIN compensation_apply_adt_record ad on o.id = ad.apply_id and ad.pax_info_id = -1

        where 1=1 and o.apply_way='1'
        <if test="dto.name != null and dto.name != ''">
            and o.apply_user like concat(concat('%',#{dto.name}),'%')
        </if>
        <if test="dto.flightNo != null and dto.flightNo != ''">
            and f.flight_no = #{dto.flightNo}
        </if>
        <if test="dto.flightStartDate != null and dto.flightStartDate != ''
            and dto.flightEndDate != null and dto.flightEndDate != ''">
            and f.flight_date BETWEEN  #{dto.flightStartDate} and #{dto.flightEndDate}
        </if>
        <if test="dto.createdStartTime != null and dto.createdStartTime != ''
            and dto.createdEndTime != null and dto.createdEndTime != ''">
            and TO_CHAR(o.created_time,'yyyy-MM-dd') BETWEEN  #{dto.createdStartTime} and #{dto.createdEndTime}
        </if>

        <if test="dto.flightNo != null and dto.flightNo != ''">
            and  f.flight_no = #{dto.flightNo}
        </if>

        <if test="dto.accidentType !=null and dto.accidentType.length >0">
            and d.accident_type in
            <foreach collection="dto.accidentType" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>

        </if>
        <if test="dto.applyStatus != null and dto.applyStatus.length >0 ">
            and o.apply_status in
            <foreach item="item" collection="dto.applyStatus" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

        order by f.flight_date DESC ,f.flight_no ,o.apply_status  ASC

    </select>

    <select id="findApplyOrderInfo" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyOrderDetailsVO">
        select  DISTINCT
        o.id ,o.apply_code ,
        d.accident_type accidentType,
        o.apply_status applyStatus,
        o.created_time,
        o.get_money_way getMoneyWay,
        o.apply_user,o.get_money_account,o.open_bank_name,o.apply_user accountHolder,
        f.flight_no,f.flight_date,f.std,f.etd,d.choice_segment_ch segmentCh,
        o.payment_waiting_period as paymentWaitingPeriod,ad.updated_time as auditTime,
        o.quick_pay,
        p.pax_receive_state paxReceiveState
        from compensation_apply_order o
        LEFT JOIN compensation_pay_record p on o.id = p.apply_id
        LEFT JOIN compensation_flight_info f on p.order_id = f.order_id
        LEFT JOIN compensation_order_info d on p.order_id = d.id
        LEFT JOIN flight_accident_info fa on d.accident_id = fa.id
        LEFT JOIN compensation_apply_adt_record ad on o.id = ad.apply_id and ad.pax_info_id = -1
        where 1=1 and o.apply_way='1'
        and o.id = #{id}
    </select>

    <select id="findSubstituteCollarPaxInfo" resultType="com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxInfoVO" databaseId="mysql">
        select
        o.id applyId,o.apply_user as applyUser,
        <!--'1' as idType, -->
        'NI' as idType,
        o.telephone,o.id_no,o.collect_identity_card_photo as photoId,
        ad.audit_status as applyStatus,
        get_user_name(ad.auditor_id) as auditResult,
        ifnull(ad.updated_time,ad.created_time) as auditTime ,
        crg.value rejectReason
        from compensation_apply_order o
        LEFT JOIN compensation_apply_adt_record ad on o.id = ad.apply_id and ad.pax_info_id = -1
        LEFT JOIN COMPENSATION_DATA_CONFIG crg on ad.reject_reason = crg.id
        where 1=1 and o.apply_way='1'
        and o.id = #{id}
    </select>

    <select id="findSubstituteCollarPaxInfo" resultType="com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxInfoVO" databaseId="oracle">
        select
        o.id applyId,o.apply_user as applyUser,
        <!-- '1' as idType,  -->
        'NI' as idType,
        o.telephone,o.id_no,o.collect_identity_card_photo as photoId,
        ad.audit_status as applyStatus,


        (select CONCAT(ue.name,ue.JOB_NUMBER)
        FROM UC_USER uu LEFT JOIN UC_EMPLOYEE ue on UU.EMPLOYEE_ID = ue."ID"
        WHERE uu.id =  ad.auditor_id) as auditResult,

        nvl(ad.updated_time,ad.created_time) as auditTime ,
        crg.value rejectReason
        from compensation_apply_order o
        LEFT JOIN compensation_apply_adt_record ad on o.id = ad.apply_id and ad.pax_info_id = -1
        LEFT JOIN COMPENSATION_DATA_CONFIG crg on ad.reject_reason = crg.id
        where 1=1 and o.apply_way='1'
        and o.id = #{id}
    </select>

    <select id="findPassengerPaxInfo" resultType="com.swcares.aps.compensation.model.apply.vo.PassengerPaxInfoVO" databaseId="mysql">
        select
        cao.id applyId,
        pi.pax_name applyUser,
        max(pi.id_type)  idType,
        max(pi.main_class) mainClass,MAX(pi.sub_class) subClass ,
        MAX(pi.with_baby) withBaby,MAX(pi.is_child) isChild,
        max(p.pax_identity_card_photo) as photoId ,pi.id_no,
        max(ad.audit_status) as applyStatus,
        max(get_user_name(ad.auditor_id)) as auditResult,
        max(ifnull(ad.updated_time,ad.created_time)) as auditTime ,
        max(crg.value) as rejectReason,
        max(p.order_id),
        max(p.pax_info_id) as paxId,
        max(pi.pax_id) as paxInfoId,
        (case when count(DISTINCT pi.tkt_no)>1 then GROUP_CONCAT(pi.tkt_no SEPARATOR ' / ')
        else max(pi.tkt_no)
        end )	AS tktNo
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax p on p.apply_id = cao.id
        LEFT JOIN compensation_pax_info pi on p.pax_info_id = pi.id
        LEFT JOIN compensation_apply_adt_record ad on p.apply_id = ad.apply_id  and ad.pax_info_id = p.pax_info_id
        LEFT JOIN COMPENSATION_DATA_CONFIG crg on ad.reject_reason = crg.id
        where 1=1
        and cao.id = #{id}
        GROUP BY cao.id,pi.pax_name,pi.id_no
        ORDER BY max(p.id) asc
    </select>

    <select id="findPassengerPaxInfo" resultType="com.swcares.aps.compensation.model.apply.vo.PassengerPaxInfoVO" databaseId="oracle">
        select
        cao.id applyId,
        pi.pax_name applyUser,
        max(pi.id_type)  idType,
        max(pi.main_class) mainClass,MAX(pi.sub_class) subClass ,
        MAX(pi.with_baby) withBaby,MAX(pi.is_child) isChild,
        max(p.pax_identity_card_photo) as photoId ,pi.id_no,
        max(ad.audit_status) as applyStatus,


        max((select CONCAT(ue.name,ue.JOB_NUMBER)
        FROM UC_USER uu LEFT JOIN UC_EMPLOYEE ue on UU.EMPLOYEE_ID = ue."ID"
        WHERE uu.id =  ad.auditor_id)) as auditResult,

        max(nvl(ad.updated_time,ad.created_time)) as auditTime ,
        max(crg.value) as rejectReason,
        max(p.order_id),
        max(p.pax_info_id) as paxId,
        max(pi.pax_id) as paxInfoId,
        (case when count(DISTINCT pi.tkt_no)>1 THEN listagg(pi.tkt_no,' / ') within group(order by pi.tkt_no)
        else max(pi.tkt_no)
        end )	AS tktNo
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax p on p.apply_id = cao.id
        LEFT JOIN compensation_pax_info pi on p.pax_info_id = pi.id
        LEFT JOIN compensation_apply_adt_record ad on p.apply_id = ad.apply_id  and ad.pax_info_id = p.pax_info_id
        LEFT JOIN COMPENSATION_DATA_CONFIG crg on ad.reject_reason = crg.id
        where 1=1
        and cao.id = #{id}
        GROUP BY cao.id,pi.pax_name,pi.id_no
        ORDER BY max(p.id) asc
    </select>

    <select id="findCompensationInfo" resultType="com.swcares.aps.compensation.model.apply.vo.PassengerCompensationOrderVO" databaseId="mysql">
        SELECT
        DISTINCT
        cap.pax_info_id paxId,
        cpi.pax_id as paxInfoId,
        coi.order_no as orderNo,
        coi.accident_id as accidentId,
        coi.id orderId,
        cpi.tkt_no AS tktNo,
        cpi.pax_name,
        cpi.id_no,
        cpi.segment as choiceSegment,
        cpi.segment_ch as choiceSegmentCh,
        fai.accident_type AS accidentType,
        cap.apply_pax_status AS applyPaxStatus,
        ifnull(cpr.err_code_des,cap.apply_pax_error) AS errCodeDes,
        cpr.pay_status AS pay_status,
        cpi.current_amount AS currentAmount,coi.created_time,
        cpi.with_baby AS withBaby,
        cpr.pay_start_time as payTime,
        cao.quick_pay,cpi.pax_id paxInfoId,
        cpr.pax_receive_state AS paxReceiveState
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cap.apply_id = cao.id
        LEFT JOIN compensation_pax_info cpi ON cpi.id = cap.pax_info_id
        LEFT JOIN compensation_order_info coi ON coi.id = cpi.order_id
        LEFT JOIN flight_accident_info fai ON coi.accident_id = fai.id
        LEFT JOIN compensation_pay_record cpr ON ( cpr.apply_pax_id = cap.pax_info_id AND cao.id = cpr.apply_id )
        WHERE 1=1 and coi.id is not null
        and cao.apply_way='1'
        and cao.id = #{id}
        ORDER BY cpi.pax_id, coi.created_time desc
    </select>

    <select id="findCompensationInfo" resultType="com.swcares.aps.compensation.model.apply.vo.PassengerCompensationOrderVO" databaseId="oracle">
        SELECT
        DISTINCT
        cap.pax_info_id paxId,
        cpi.pax_id as paxInfoId,
        coi.order_no as orderNo,
        coi.accident_id as accidentId,
        coi.id orderId,
        cpi.tkt_no AS tktNo,
        cpi.pax_name,
        cpi.id_no,
        cpi.segment as choiceSegment,
        cpi.segment_ch as choiceSegmentCh,
        coi.accident_type AS accidentType,
        cap.apply_pax_status AS applyPaxStatus,
        nvl(cpr.err_code_des,cap.apply_pax_error) AS errCodeDes,
        cpr.pay_status AS pay_status,
        cpi.current_amount AS currentAmount,coi.created_time,
        cpi.with_baby AS withBaby,
        cpr.pay_return_time as payTime,
        cao.quick_pay,cpi.pax_id paxInfoId,
        cpr.pax_receive_state AS paxReceiveState
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax cap ON cap.apply_id = cao.id
        LEFT JOIN compensation_pax_info cpi ON cpi.id = cap.pax_info_id
        LEFT JOIN compensation_order_info coi ON coi.id = cpi.order_id
        LEFT JOIN flight_accident_info fai ON coi.accident_id = fai.id
        LEFT JOIN compensation_pay_record cpr ON ( cpr.apply_pax_id = cap.pax_info_id AND cao.id = cpr.apply_id )
        WHERE 1=1 and coi.id is not null
        and cao.apply_way='1'
        and cao.id = #{id}
        ORDER BY cpi.pax_id, coi.created_time desc
    </select>
    <update id="updQuickPay">
        update compensation_apply_order set quick_pay =#{status}
        where id =  #{id}
    </update>

    <update id="updQuickPayStatus" >
        update compensation_apply_order set quick_pay =#{status}
        where id =  #{id} and tenant_id = #{tenantId}
    </update>

    <select id="findPaxBaseInfoByApplyId" resultType="com.swcares.aps.compensation.model.apply.dto.ApplyPaxBaseInfoDTO" databaseId="mysql">
        select
        cao.id applyId,
        pi.pax_name applyUser,
        max(pi.main_class) mainClass,MAX(pi.sub_class) subClass ,
        MAX(pi.with_baby) withBaby,MAX(pi.is_child) isChild,
        max(p.pax_identity_card_photo) as photoId ,pi.id_no,
        max(p.order_id),max(p.pax_info_id) as paxId,
        max(coi.service_city) as serviceCity,
        (case when count(DISTINCT pi.tkt_no)>1 then GROUP_CONCAT(pi.tkt_no SEPARATOR ' / ')
        else max(pi.tkt_no)
        end )	AS tktNo
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax p on p.apply_id = cao.id
        LEFT JOIN compensation_pax_info pi on p.pax_info_id = pi.id
        LEFT JOIN compensation_order_info coi on pi.order_id =coi.id
        where
        cao.id = #{id}
        GROUP BY cao.id,pi.pax_name,pi.id_no
        ORDER BY cao.id asc
    </select>

    <select id="findPaxBaseInfoByApplyId" resultType="com.swcares.aps.compensation.model.apply.dto.ApplyPaxBaseInfoDTO" databaseId="oracle">
        select
        cao.id applyId,
        pi.pax_name applyUser,
        max(pi.main_class) mainClass,MAX(pi.sub_class) subClass ,
        MAX(pi.with_baby) withBaby,MAX(pi.is_child) isChild,
        max(p.pax_identity_card_photo) as photoId ,pi.id_no,
        max(p.order_id),max(p.pax_info_id) as paxId,
        max(coi.service_city) as serviceCity,
        (case when count(DISTINCT pi.tkt_no)>1 then listagg(pi.tkt_no,' / ') within group(order by pi.tkt_no)
        else max(pi.tkt_no)
        end )	AS tktNo
        FROM
        compensation_apply_order cao
        LEFT JOIN compensation_apply_pax p on p.apply_id = cao.id
        LEFT JOIN compensation_pax_info pi on p.pax_info_id = pi.id
        LEFT JOIN compensation_order_info coi on pi.order_id =coi.id
        where
        cao.id = #{id}
        GROUP BY cao.id,pi.pax_name,pi.id_no
        ORDER BY cao.id asc
    </select>

    <select id="findUnpaidOrderInfo" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO" databaseId="mysql">
        SELECT distinct R.ID recordId,r.TRANS_AMOUNT transAmount,r.APPLY_PAX_ID applyPaxId,r.ORDER_ID orderId,
        O.ID , O.APPLY_USER ,O.TELEPHONE ,O.APPLY_AMOUNT ,
        R.PAY_STATUS ,O.GET_MONEY_ACCOUNT ,O.GET_MONEY_WAY ,
        O.ID_NO ,O.OPEN_BANK_NAME ,O.APPLY_CODE ,R.PAY_START_TIME,R.PAY_TYPE_API_VERSION,
        O.QUICK_PAY
        FROM COMPENSATION_APPLY_ORDER O
        LEFT JOIN COMPENSATION_PAY_RECORD R on o."ID" = R.APPLY_ID
        WHERE
        (O.APPLY_WAY = 0 OR (O.APPLY_WAY = 1 AND O.APPLY_STATUS = 2 AND O.QUICK_PAY > 0))
        AND O.GET_MONEY_WAY != 2
        AND r.SWITCH_OFF = '0'
        AND ((R.PAY_STATUS in (0,3)) OR
        (R.PAY_STATUS = 2  AND DATE_FORMAT(O.CREATED_TIME, '%Y-%m-%d')>= date_sub(DATE_FORMAT(now(), '%Y-%m-%d'),Interval 1 day) ))
        --数据来源: 1机场/2航司
        --是否有资金授权 1 授权 0 不授权
        and oi.id is not null
        --  机场端查询：赔偿单数据来源=机场 并且 是否有资金授权 = 0未授权
        and oi.source = '1' and  oi.GRANT_BANKROLL = '0'
    </select>

    <select id="findUnpaidOrderInfo" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO" databaseId="oracle">
        SELECT distinct O.accident_Type, O.TENANT_ID, O.COMPENSATE_TYPE, R.TRANS_DATE transDate,
        O.COMPENSATE_SUB_TYPE, R.ID recordId,r.TRANS_AMOUNT transAmount,r.APPLY_PAX_ID applyPaxId,r.ORDER_ID orderId,
        O.ID , O.APPLY_USER ,O.TELEPHONE ,O.APPLY_AMOUNT ,
        R.PAY_STATUS ,O.GET_MONEY_ACCOUNT ,O.COMPENSATE_SUB_TYPE ,
        O.ID_NO ,O.OPEN_BANK_NAME ,O.APPLY_CODE ,R.PAY_START_TIME,oi.source,R.PAY_TYPE_API_VERSION,
        O.QUICK_PAY
        FROM COMPENSATION_APPLY_ORDER O
        LEFT JOIN COMPENSATION_PAY_RECORD R on o."ID" = R.APPLY_ID
        LEFT JOIN COMPENSATION_ORDER_INFO oi on oi."ID" = R.order_ID
        WHERE
        (O.APPLY_WAY = 0 OR (O.APPLY_WAY = 1 AND O.APPLY_STATUS = 2 AND O.QUICK_PAY > 0))
        AND O.COMPENSATE_SUB_TYPE in(1, 2)
        AND r.SWITCH_OFF = '0'
        AND ((R.PAY_STATUS in (0, 3)) )
        --数据来源: 1机场/2航司
        --是否有资金授权 1 授权 0 不授权
        and oi.id is not null
        --  机场端查询：赔偿单数据来源=1机场 并且 是否有资金授权 = 0未授权
        and oi.source = '1' and  oi.GRANT_BANKROLL = '0'
    </select>

    <update id="updApplyOrderPaxStatus">
        UPDATE compensation_apply_pax set APPLY_PAX_STATUS = #{payStatus}
        <if test="errorStr!=null and errorStr!=''">
            ,APPLY_PAX_ERROR = #{errorStr}
        </if>
        WHERE  APPLY_CODE =  #{applyCode}
    </update>


    <update id="updOrderPaxStatus">
        UPDATE COMPENSATION_PAX_INFO set RECEIVE_STATUS = #{receiveStatus}
        <if test="receiveTime!=null and receiveTime!=''">
            ,RECEIVE_TIME = TO_DATE(#{receiveTime}, 'yyyy-mm-dd hh24:mi:ss')
        </if>

        WHERE id = #{paxInfoId}
        AND ORDER_ID =  #{orderId}
    </update>

    <select id="findFlightByApplyId" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO">
        SELECT DISTINCT
            cfi.flight_no AS flightNo,
            cfi.flight_date AS flightData,
            cfi.segment_ch AS segmentCh
        FROM
            compensation_apply_order cao
                LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
                LEFT JOIN compensation_flight_info cfi ON cfi.order_id = cap.order_id
        WHERE
            cap.apply_id = #{applyId}
    </select>

    <select id="findFlightById" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO">
        SELECT
            cfi.flight_no AS flightNo,
            cfi.flight_date AS flightData,
            cfi.segment_ch AS segmentCh
        FROM
            compensation_apply_order cao
                LEFT JOIN compensation_apply_pax cap ON cao.id = cap.apply_id
                LEFT JOIN compensation_flight_info cfi ON cfi.order_id = cap.order_id
        WHERE
            cao.id = #{id} AND ROWNUM = 1
    </select>

    <select id="getOverTimeActOrder" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyBehalfOrderVO">
        SELECT DISTINCT O.tenant_id as tenantId,O.ID,O.APPLY_CODE AS applyCode,
        O.AUDIT_TIME AS auditTime ,nvl(o.PAYMENT_WAITING_PERIOD,0) AS paymentWaitingPeriod
        FROM COMPENSATION_APPLY_ORDER  O
        LEFT JOIN COMPENSATION_PAY_RECORD R on o."ID" = R.APPLY_ID
        WHERE APPLY_WAY=1 AND APPLY_STATUS=2 AND R.PAY_STATUS=0 AND (QUICK_PAY IS NULL OR QUICK_PAY=0)
        and O.AUDIT_TIME is not null
    </select>

    <select id="getApplyPaxOrderStatus" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyPaxOrderInfoQueryVO">
        select o.STATUS orderStatus, p.SWITCH_OFF from COMPENSATION_ORDER_INFO o
        LEFT JOIN COMPENSATION_PAX_INFO p on o.ID = p.ORDER_ID
        where o.ID = #{orderId}
        and p.ID = #{paxInfoId}


    </select>



</mapper>
