<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.apply.mapper.ApplyPaxMapper">

    <select id="page" resultType="com.swcares.aps.compensation.model.apply.vo.ApplyPaxDOVO">
        select * from compensation_apply_pax
    </select>

    <select id="authPaxFind" resultType="com.swcares.aps.compensation.model.apply.vo.AuthPaxVO">
        select t.order_id as orderId,t.switch_off as switchOff,t.receive_status as receiveStatus,t2.status compensationStatus
        from compensation_pax_info t
        LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id_no = #{dto.idNo}
        and t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        and (t2.status = '4' or t2.status = '5')
        and (t2.compensate_type ='1' or t2.compensate_type ='2')
        <choose>
            <!--机场端查询，没有进行资金授权的赔偿单（0否1是）-->
            <when test="dto.grantBankroll!=null and dto.grantBankroll!='' and dto.grantBankroll == '1'.toString() ">
                and (T2.GRANT_BANKROLL = '0' or  T2.GRANT_BANKROLL is null)
            </when>
            <otherwise>
                and t2.source = '1'
            </otherwise>
        </choose>
        ORDER BY t.switch_off desc
    </select>

    <select id="findCompensationOrderFlight" resultType="com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO" databaseId="oracle">
        select t3.flight_date,t3.flight_no,t3.std,t3.sta,t.segment as segment,t.segment_ch as segmentCH,t3.segment_ch as segmentCHFlight
        from compensation_pax_info t
        LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id_no = #{dto.idNo}
        and t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        and t2.status = '4'
        and t.switch_off = '0'
        and t.receive_status ='0'
        and rownum = 1
        and (t2.compensate_type ='1' or t2.compensate_type ='2')
        <!--机场端查询，没有资金授权的赔偿单0否1是-->
        and (T2.GRANT_BANKROLL = '0' or  T2.GRANT_BANKROLL is null)
        order by t3.segment desc
    </select>

    <select id="findCompensationOrder" resultType="com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO">
        select t.id as paxId,t.pax_name,t.id_type,t.id_no,t.is_child,t.with_baby,t.telephone,t.tkt_no,
        t.main_class,t.sub_class,t2.id as orderId,t2.order_no,t.current_amount as planCompensateMoney,
        <!--        (select t4.fc_type from flight_accident_info t4 where t4.id = t2.accident_id) as accidentType  因为异常行李事故单不在flight_accident_info表，不能这样查-->
        t2.accident_type accidentType,t2.compensate_sub_type as  compensateSubType ,t2.compensate_type as compensateType,
        t.PKG_NO pkgNo,t2.INPUT_SOURCE inputSource
        from compensation_pax_info t
        LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id_no = #{dto.idNo}
        and t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        and t2.status = '4'
        and t.switch_off = '0'
        and t.receive_status ='0'
        and (t2.compensate_type ='1' or t2.compensate_type ='2')
        <choose>
            <!--机场端查询，没有进行资金授权的赔偿单（0否1是）-->
            <when test="dto.grantBankroll!=null and dto.grantBankroll!='' and dto.grantBankroll == '1'.toString() ">
                and (T2.GRANT_BANKROLL = '0' or  T2.GRANT_BANKROLL is null)
            </when>
            <otherwise>
                and t2.source = '1'
            </otherwise>
        </choose>
        order by accidentType asc,t2.created_time desc
    </select>

    <!--    <select id="authBehalfPaxFind" resultType="int">-->
    <!--        select count(1) from flight_pax_info t LEFT JOIN foc_flight_info t2-->
    <!--        on t.flight_no = t2.flight_no and t.flight_date = t2.flight_date-->
    <!--        where t.id_no = #{dto.idNo}-->
    <!--        and t.pax_name = #{dto.paxName}-->
    <!--        and t2.flight_date = #{dto.flightDate}-->
    <!--        and t2.flight_no = #{dto.flightNo}-->
    <!--    </select>-->

    <select id="findCompensationOrderBehalf" resultType="com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO">
        select t.id as paxId,t.pax_name,t.id_type,t.id_no,t.is_child,t.with_baby,t.telephone,t.tkt_no,
        t.main_class,t.sub_class,t2.id as orderId,t2.order_no,t.current_amount as planCompensateMoney,
        t2.accident_type as accidentType,t2.compensate_sub_type as  compensateSubType ,t2.compensate_type as compensateType,
        t.PKG_NO pkgNo
        from compensation_pax_info t
        LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id_no = #{dto.idNo}
        and t.pax_name = #{dto.paxName}
        and t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        and t2.status = '4'
        and t.switch_off = '0'
        and t.receive_status ='0'
        and (t2.compensate_type ='1' or t2.compensate_type ='2')
        <!--机场端查询，没有资金授权的赔偿单0否1是-->
        and (T2.GRANT_BANKROLL = '0' or  T2.GRANT_BANKROLL is null)
        order by accidentType asc,t2.created_time desc
    </select>

    <select id="authPaxFindBehalf" resultType="com.swcares.aps.compensation.model.apply.vo.AuthPaxVO">
        select t.order_id as orderId,t.switch_off as switchOff,t.receive_status as receiveStatus
        from compensation_pax_info t
        LEFT JOIN compensation_order_info t2 on t.order_id = t2.id
        LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id
        where t.id_no = #{dto.idNo}
        and t.pax_name = #{dto.paxName}
        and t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        and t2.status = '4'
        and (t2.compensate_type ='1' or t2.compensate_type ='2')
        <!--机场端查询，没有资金授权的赔偿单0否1是-->
        and (T2.GRANT_BANKROLL = '0' or  T2.GRANT_BANKROLL is null)
        ORDER BY t.switch_off desc
    </select>

<!--    <select id="findPaxFlight" resultType="int">-->
<!--        select count(1) from flight_pax_info t-->
<!--        LEFT JOIN foc_flight_info t2 on t.flight_date = t2.flight_date and t.flight_no = t2.flight_no-->
<!--        where t.id_no = #{dto.idNo}-->
<!--        and t.pax_name = #{dto.paxName}-->
<!--        and t2.flight_date = #{dto.flightDate}-->
<!--        and t2.flight_no = #{dto.flightNo}-->
<!--    </select>-->

    <select id="findCompensationOrderFlightBehalf" resultType="com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO" databaseId="mysql">
        select
        t3.flight_date,
        t3.flight_no,
        t3.std,
        t3.sta,
        t3.segment as segment,
        t3.segment_ch as segmentCH
        from compensation_flight_info t3
        where t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}
        order by t3.segment desc LIMIT 1
    </select>

    <select id="findCompensationOrderFlightBehalf" resultType="com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO" databaseId="oracle">
        select * FROM( select
        t3.flight_date,
        t3.flight_no,
        t3.std,
        t3.sta,
        t3.segment as segment,
        t3.segment_ch as segmentCH
        from compensation_flight_info t3
        where t3.flight_date = #{dto.flightDate}
        and t3.flight_no = #{dto.flightNo}

        order by t3.segment desc) WHERE   rownum = 1
    </select>

    <update id="updateApplyPaxStatus">
        update compensation_apply_pax t
        set t.apply_pax_status = #{dto.statusId}
        <if test="dto.errorStr!=null and dto.errorStr!=''">
            ,t.apply_pax_error = #{dto.errorStr}
        </if>
        where t.pax_info_id = #{dto.paxInfoId}
        and t.apply_id = #{dto.applyId}
    </update>

    <update id="updApplyOrderPaxStatus">
        UPDATE compensation_apply_pax
        SET apply_pax_status =  #{dto.applyPaxStatus}
        <if test="dto.applyPaxError!=null and dto.applyPaxError!=''">
            ,apply_pax_error =  #{dto.applyPaxError}
        </if>
        WHERE order_id = #{dto.orderId} AND pax_info_id = #{dto.paxInfoId} AND apply_id = #{dto.applyId}
    </update>

</mapper>
