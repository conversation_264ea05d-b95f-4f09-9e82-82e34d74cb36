<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper">
    <select id="findBaggageAccidentList" resultType="com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO">
        select
        b.id as accidentId,
        b.pax_flight_no,
        b.pax_segment,
        b.pax_flight_date,
        GET_CITYNAMEAND3CODE(b.serve_segment) serve_segment,
        b.pax_name,
        b.ID_NO,
        b.ID_TYPE as idType,
        b.type as type ,
        b.accident_no,
        b.accident_status as accidentStatus  ,
        b.baggage_no,
        b.CREATED_BY  AS created_by,
        b.created_time,
        CASE
        when to_char(sysdate,'yyyy-MM-dd') &gt;= TO_CHAR(b.over_time,'yyyy-MM-dd') THEN 'Y'
        ELSE 'N'
        END AS overTime,
        b.ACCIDENT_SOURCE as source,
        b.belong_airline as belongAirline,
        b.belong_airline_abbr as belongAirlineAbbr,
        b.reason_type as reasonType,
        (select count(*) from compensation_order_info c  where  c.accident_no=b.accident_no )as compensationNumber
        from baggage_accident_info b
        <where>
            <if test="dto.paxFlightNo != null and dto.paxFlightNo != ''">                       <!--出发航站 pc端筛选条件-->
                and b.pax_flight_no=#{dto.paxFlightNo}
            </if>

            <if test="dto.pod != null and dto.pod != ''">                       <!--出发航站 pc端筛选条件-->
                and INSTR(b.pod, #{dto.pod}) &gt; 0
            </if>

            <if test="dto.poa != null and dto.poa != ''">                        <!--到达航站 pc端筛选条件-->
                and  INSTR(b.poa, #{dto.poa}) &gt; 0
            </if>

            <if test="dto.startFlightDate != null and dto.startFlightDate != ''">
<!--                and DATE_FORMAT(b.pax_flight_date,'%Y-%m-%d')  between #{dto.startFlightDate} and #{dto.endFlightDate}-->
                and b.pax_flight_date  between #{dto.startFlightDate} and #{dto.endFlightDate}
            </if>

            <if test="dto.serveSegment != null and dto.serveSegment != ''">      <!--服务航站 H5端筛选条件-->
                and b.serve_segment=#{dto.serveSegment}
            </if>

            <if test="dto.paxName != null and dto.paxName != ''">
                and b.pax_name like concat(concat('%',#{dto.paxName}),'%')
                <!--and b.pax_name like #{dto.paxName} -->
            </if>

            <if test="dto.types != null and dto.types != ''">
                and b.type in
                <foreach collection="dto.typesSplits" item="type" open="(" separator="," close=")">
                    #{type}
                </foreach>
            </if>

            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and b.accident_no=#{dto.accidentNo}
            </if>

            <if test="dto.accidentStatus != null and dto.accidentStatus != ''">
                and  b.accident_status in
                <foreach collection="dto.accidentStatusSplits" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.idNo != null and dto.idNo != '' and dto.idType == 1">
                and b.id_no=#{dto.idNo}
            </if>

            <if test="dto.idNo != null and dto.idNo != '' and dto.idType == 3">
                and b.baggage_no=#{dto.idNo}
            </if>
            <if test="dto.idNo != null and dto.idNo != '' and dto.idType == 2">
<!--                and b.tkt_no like concat('%',#{dto.tktNo},'%')-->
                and b.tkt_no like #{dto.idNo}
            </if>

            <if test="dto.accidentSource != null and dto.accidentSource != ''">
                and b.accident_source = #{dto.accidentSource}
            </if>
            
            <if test="@cn.hutool.core.util.ArrayUtil@isNotEmpty(dto.belongAirline)">
                and  b.belong_airline in
                <foreach collection="dto.belongAirline" item="item" separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.reasonType != null and dto.reasonType != ''">
                and b.reason_type = #{dto.reasonType}
            </if>
        </where>
        order by b.pax_flight_date desc,b.pax_flight_no,b.accident_status,b.id
    </select>

    <select id="findBaggageDetail" resultMap="BaseResultMap" databaseId="oracle">
        select
        <!--事故信息如下-->
        b.id as accidentId,
        b.accident_no,
        b.signature,
        b.accident_status ,
        b.INPUT_SOURCE,
        b.type ,
        b.serve_segment,
        b.damage_type,
        b.damage_part,
        b.damage_degree,
        b.baggage_brand,
        b.lost_weight,
        b.lost_type,
        b.reminder_time,
        CASE
        when to_char(sysdate,'yyyy-MM-dd') &gt;= TO_CHAR(b.over_time,'yyyy-MM-dd') THEN 'Y'
        ELSE 'N'
        END AS over_time,
        NVL2(trim(b.lost_time),b.lost_time, (TRUNC(SYSDATE) - TRUNC(b.todo_time)) )as lost_time ,
        b.confirm_lost_time,
        b.lost_amount,
        b.declaration_type,
        b.created_by as created_by1,
        to_char(b.created_time,'YYYY-MM-DD hh24:mi:ss') as created_time1 ,
        b.TOVOID_BY,
        to_char(b.TOVOID_TIME,'YYYY-MM-DD hh24:mi:ss') TOVOID_TIME ,
        b.CLOSE_USER,
        to_char(b.CLOSE_TIME ,'YYYY-MM-DD hh24:mi:ss') CLOSE_TIME,
        b.accident_reason,
        b.collect_identity_pax_imgs,
        b.collect_identity_voucher_imgs,
        nvl2(b.collect_identity_baggage_imgs,b.collect_identity_baggage_imgs,'') as collectIdentityBaggagePhotos ,
        c.accident_type,
        c.accident_sub_type,
        b.belong_airline,
        b.belong_airline_abbr,
        b.accident_source,
        b.reason_type,
        <!--航班信息如下-->
        b.pax_flight_no,
        b.pax_flight_date,
        b.pax_segment,
        b.std,
        b.etd,
        b.PLANE_CODE,
        b.FLT_CODE,
        <!--旅客信息如下-->
        b.pax_name,
        b.ID_TYPE ,
        b.id_no,
        b.tkt_no,
        b.phone,
        b.pax_id,
        <!--行李信息如下-->
        b.baggage_no,
        b.baggage_flight_date,
        b.baggage_flight_no,
        b.baggage_segment,
        b.stopover_segment,
        b.alternate_terminal,
        b.baggage_type ,
        b.overweight_no,
        <!--补偿单信息如下-->
        (select COUNT(1) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = c.ID GROUP BY cpi.ORDER_ID) as
        planCarryOutNum,
        (select COUNT(1) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = c.ID and cpi.RECEIVE_STATUS = '1' GROUP
        BY cpi.ORDER_ID) as actualCarryOutNum,
        c.sum_money,
        (select sum(cpi.CURRENT_AMOUNT) FROM COMPENSATION_PAX_INFO cpi WHERE cpi.ORDER_ID = c.ID and
        cpi.RECEIVE_STATUS = '1' GROUP BY cpi.ORDER_ID) as trueCurrentAmount,
        c.compensate_type,
        c.order_no,
        c.status ,
        c.id as order_id,
        c.SERVICE_CITY as serviceCity,
        c.CREATED_BY as created_by2,
        c.CREATED_TIME as created_time2,
        c.temporary_compensation as temporaryCompensation,
        c.REMARK as compensationRemark,
        c.compensate_sub_type,
        c.compensate_standard,
        c.commodity_id
        <!--快递信息如下-->
        <!--e.addressee,
        e.phone phone2,
        e.express_type ,
        e.address,
        e.express_company,
        e.express_no,
        e.express_goods,
        e.order_no orderNo2,
        e.created_by as created_by3,
        e.created_time as created_time3-->

        FROM baggage_accident_info b
        LEFT JOIN compensation_order_info c on b.id = c.accident_id
        <!--LEFT JOIN compensation_express_info e on c.ORDER_NO = e.ORDER_NO-->
        WHERE
        b.id = #{dto.accidentId}
    </select>

    <resultMap id="BaseResultMap" type="com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVO">
        <!--事故信息如下-->
        <result column="belong_airline" property="belongAirline" jdbcType="VARCHAR" />
        <result column="belong_airline_abbr" property="belongAirlineAbbr" jdbcType="VARCHAR" />
        <result column="signature" property="signature" jdbcType="VARCHAR" />
        <result column="accident_source" property="accidentSource" jdbcType="VARCHAR" />
        <result column="reason_type" property="reasonType" jdbcType="VARCHAR" />
        <result column="accidentId" property="accidentId" jdbcType="VARCHAR" />
        <result column="accident_no" property="accidentNo" jdbcType="VARCHAR" />
        <result column="accident_status" property="accidentStatus" jdbcType="VARCHAR" />
        <result column="input_source" property="inputSource" jdbcType="VARCHAR" />
        <result column="type" property="type" jdbcType="VARCHAR" />
        <result column="serve_segment" property="serveSegment" jdbcType="VARCHAR" />
        <result column="damage_type" property="damageType" jdbcType="VARCHAR" />           <!--1、破损类型-->
        <result column="damage_part" property="damagePart" jdbcType="VARCHAR" />           <!--1、破损类型-->
        <result column="damage_degree" property="damageDegree" jdbcType="VARCHAR" />       <!--1、破损类型-->
        <result column="baggage_brand" property="baggageBrand" jdbcType="VARCHAR" />       <!--1、破损类型-->
        <result column="lost_weight" property="lostWeight" jdbcType="VARCHAR" />           <!--2、少收类型--><!--2、丢失类型--><!--2、内件缺失类型-->
        <result column="lost_type" property="lostType" jdbcType="VARCHAR" />               <!--2、少收类型-->
        <result column="reminder_time" property="reminderTime" jdbcType="VARCHAR" />       <!--2、少收类型-->
        <result column="lost_time" property="lostTime" jdbcType="VARCHAR" />               <!--3、丢失类型-->
        <result column="over_time" property="overTime" jdbcType="VARCHAR" />
        <result column="confirm_lost_time" property="confirmLostTime" jdbcType="VARCHAR" /> <!--3、丢失类型-->
        <result column="lost_amount" property="lostAmount" jdbcType="VARCHAR" />           <!--4、内件缺失类型-->
        <result column="declaration_type" property="declarationType" jdbcType="VARCHAR" /> <!--4、内件缺失类型-->
        <result column="created_by1" property="createdBy" jdbcType="VARCHAR" />
        <result column="created_time1" property="createdTime" jdbcType="VARCHAR" />
        <result column="tovoid_by" property="tovoidBy" jdbcType="VARCHAR" />
        <result column="tovoid_time" property="tovoidTime" jdbcType="VARCHAR" />
        <result column="close_user" property="closeUser" jdbcType="VARCHAR" />
        <result column="close_time" property="closeTime" jdbcType="VARCHAR" />
        <result column="accident_reason" property="accidentReason" jdbcType="VARCHAR" />
        <result column="collect_identity_pax_imgs" property="collectIdentityPaxPhotos" jdbcType="VARCHAR" />
        <result column="collect_identity_voucher_imgs" property="collectIdentityVoucherPhotos" jdbcType="VARCHAR" />
        <result column="collectIdentityBaggagePhotos" property="collectIdentityBaggagePhotos" jdbcType="VARCHAR" />
        <!--航班信息如下-->
        <result column="pax_flight_no" property="paxFlightNo" jdbcType="VARCHAR" />
        <result column="pax_flight_date" property="paxFlightDate" jdbcType="VARCHAR" />
        <result column="pax_segment" property="paxSegment" jdbcType="VARCHAR" />
        <result column="std" property="std" jdbcType="VARCHAR" />
        <result column="etd" property="etd" jdbcType="VARCHAR" />
        <result column="plane_code" property="planeCode" jdbcType="VARCHAR" />
        <result column="flt_code" property="fltCode" jdbcType="VARCHAR" />
        <!--旅客信息如下-->
        <result column="pax_name" property="paxName" jdbcType="VARCHAR" />
        <result column="id_type" property="idType" jdbcType="VARCHAR" />
        <result column="id_no" property="idNo" jdbcType="VARCHAR" />
        <result column="tkt_no" property="tktNo" jdbcType="VARCHAR" />
        <result column="phone" property="phone" jdbcType="VARCHAR" />
        <result column="pax_id" property="paxId" jdbcType="VARCHAR" />
        <!--行李信息如下-->
        <result column="baggage_no" property="baggageNo" jdbcType="VARCHAR" />
        <result column="baggage_flight_date" property="baggageFlightDate" jdbcType="VARCHAR" />
        <result column="baggage_flight_no" property="baggageFlightNo" jdbcType="VARCHAR" />
        <result column="baggage_segment" property="baggageSegment" jdbcType="VARCHAR" />
        <result column="stopover_segment" property="stopoverSegment" jdbcType="VARCHAR" />
        <result column="alternate_terminal" property="alternateTerminal" jdbcType="VARCHAR" />
        <result column="baggage_type" property="baggageType" jdbcType="VARCHAR" />
        <result column="overweight_no" property="overweightNo" jdbcType="VARCHAR" />
<!--        <collection property="baggageExpressList" ofType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageExpressVO">-->
<!--            <result column="addressee" property="addressee" jdbcType="VARCHAR" />-->
<!--            <result column="phone2" property="phone" jdbcType="VARCHAR" />-->
<!--            <result column="address" property="address" jdbcType="VARCHAR" />-->
<!--            <result column="express_company" property="expressCompany" jdbcType="VARCHAR" />-->
<!--            <result column="express_type" property="expressType" jdbcType="VARCHAR" />-->
<!--            <result column="express_no" property="expressNo" jdbcType="VARCHAR" />-->
<!--            <result column="express_goods" property="expressGoods" jdbcType="VARCHAR" />-->
<!--            <result column="orderNo2" property="orderNo" jdbcType="VARCHAR" />-->
<!--            <result column="created_by3" property="createdBy" jdbcType="VARCHAR" />-->
<!--            <result column="created_time3" property="createdTime" jdbcType="VARCHAR" />-->
<!--        </collection>-->
        <collection property="baggageCompensationList" ofType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageCompensationVO">
            <result column="order_id" property="orderId" jdbcType="VARCHAR" />
            <result column="order_no" property="orderNo" jdbcType="VARCHAR" />
            <result column="status" property="status" jdbcType="VARCHAR" />
            <result column="compensate_type" property="compensateType" jdbcType="VARCHAR" />
            <result column="compensate_sub_type" property="compensateSubType" jdbcType="VARCHAR" />
            <result column="compensate_standard" property="compensateStandard" jdbcType="VARCHAR" />
            <result column="temporaryCompensation" property="temporaryCompensation" jdbcType="BOOLEAN" />
            <result column="serviceCity" property="serviceCity" jdbcType="VARCHAR" />
            <result column="commodity_id" property="commodityId" jdbcType="VARCHAR" />
            <result column="sum_money" property="planCurrentAmount" jdbcType="VARCHAR" />
            <result column="created_by2" property="createdBy" jdbcType="VARCHAR" />
            <result column="created_time2" property="createdTime" jdbcType="VARCHAR" />
            <result column="accident_type" property="accidentType" jdbcType="VARCHAR" />
            <result column="accident_sub_type" property="accidentSubType" jdbcType="VARCHAR" />
            <result column="trueCurrentAmount" property="trueCurrentAmount" jdbcType="VARCHAR" />
            <result column="actualCarryOutNum" property="actualCarryOutNum" jdbcType="VARCHAR" />
            <result column="planCarryOutNum" property="planCarryOutNum" jdbcType="VARCHAR" />
            <result column="compensationRemark" property="compensationRemark" jdbcType="VARCHAR" />
        </collection>

    </resultMap>

    <select id="findNo" resultType="String">
        select b.match_accident_no from baggage_accident_info b where b.id = #{dto.accidentId}
    </select>

    <select id="findMatchResult" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO">
        select b.id as accidentId,
        b.accident_no,  b.accident_status, b.type
        from baggage_accident_info b
        where
        b.accident_no in
        <foreach collection="dto.accidentNos" item="accidentNo" open="(" separator="," close=")">
           #{accidentNo}
        </foreach>
    </select>


    <update id="updBaggageAccidentStatusBatch">
        UPDATE baggage_accident_info SET accident_status = #{accidentStatus}
        where 1=1
        <if test="accidentNo != null and accidentNo.size() >0">
            and accident_no in
            <foreach collection="accidentNo" item="items" open="(" separator="," close=")">
                #{items}
            </foreach>
        </if>
    </update>

    <select id="selectUndeliveredAccidentDropdown" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO">
        SELECT
        ba.id as accidentId,
        ba.accident_no as accidentNo,
        ba.PAX_NAME as passengerName,
        ba.TYPE as accidentType,
        ba.BAGGAGE_NO as baggageNo
        FROM
        baggage_accident_info ba
        WHERE
        ba.PAX_FLIGHT_NO = #{dto.flightNo}
        AND ba.PAX_FLIGHT_DATE = #{dto.flightDate}
        AND ba.ACCIDENT_STATUS != '4'
        AND ba.TYPE != '23'
        AND NOT EXISTS (
        SELECT 1
        FROM baggage_transport_info bti
        WHERE bti.ACCIDENT_NO = ba.accident_no)
        AND NOT EXISTS (
        SELECT 1
        FROM BAGGAGE_TRANSPORT_ACCIDENT_REL btar
        WHERE btar.ACCIDENT_NO = ba.ACCIDENT_NO  )
        <if test="dto.mergedId != null and dto.mergedId.size() >0">
            union all
            SELECT
            ba.id as accidentId,
            ba.accident_no as accidentNo,
            ba.PAX_NAME as passengerName,
            ba.TYPE as accidentType,
            ba.BAGGAGE_NO as baggageNo
            FROM
            baggage_accident_info ba
            WHERE
            ba.accident_no in
            <foreach collection="dto.mergedId" item="items" open="(" separator="," close=")">
                 #{items}
            </foreach>
        </if>
    </select>

    <select id="existTransport" resultType="int">
        SELECT
        COUNT(1)
        FROM
        BAGGAGE_TRANSPORT_INFO bti
        LEFT JOIN
        baggage_transport_accident_rel btar ON bti.ID = btar.TRANSPORT_ID
        WHERE
        bti.ACCIDENT_NO =#{accidentNo} OR btar.ACCIDENT_NO =#{accidentNo}
    </select>
</mapper>
