<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper">

    <select id="toMatch" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO" >
        SELECT bai.accident_no,
        bai.baggage_no,
        bai.pax_name,
        bai.serve_segment,
        bai.type,
        bai.accident_status accidentStatus,
        CASE
        WHEN trim(bai.match_accident_no) IS NULL THEN  'N'
        ELSE 'Y'
        END whetherMatch
        FROM baggage_accident_info bai
        where bai.pax_flight_no = #{dto.flightNo}
        and bai.pax_flight_date = #{dto.flightDate}
        and bai.type = #{dto.type}
        and bai.baggage_no = #{dto.baggageNo}
<!--        and bai.serve_segment = #{dto.serveSegment}-->
        and (bai.accident_status = '1' or bai.accident_status = '2' or bai.accident_status = '3')
    </select>

    <select id="getBaggageAccidentNumber" resultType="java.lang.Integer" databaseId="mysql">
        select count(*)
        from baggage_accident_info bai 
        <where>
            bai.accident_status != 0
            and current_timestamp() &lt; adddate(bai.created_time,interval 1 year)
            <choose>
                <when test="dto.idNo != null and dto.idNo != ''">
                   and bai.id_no = #{dto.idNo}
                </when>
                <when test="dto.tktNo != null and dto.tktNo != ''">
                    and bai.tkt_no = #{dto.tktNo}
                </when>
                <otherwise>
                    and bai.baggage_no = #{dto.bagTag}
                </otherwise>
            </choose>
        </where>
    </select>

    <update id="changeStatus">
        update baggage_accident_info set accident_status = #{status}
        <if test="status == '3'.toString() "><!--事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）-->
            ,CLOSE_USER = #{userNo}, CLOSE_TIME = #{dateTime}
        </if>
        <if test="status == '4'.toString() "><!--事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）-->
            ,TOVOID_BY = #{userNo}, TOVOID_TIME = #{dateTime}
        </if>
        where id=#{id}
    </update>


    <select id="getBaggageAccidentNumber" resultType="java.lang.Integer" databaseId="oracle">
        select count(*)
        from baggage_accident_info bai
        <where>
            bai.accident_status != 0
            and SYSDATE &lt; add_months(bai.CREATED_TIME ,12)
            <choose>
                <when test="dto.idNo != null and dto.idNo != ''">
                    and bai.id_no = #{dto.idNo}
                </when>
                <when test="dto.tktNo != null and dto.tktNo != ''">
                    and bai.tkt_no = #{dto.tktNo}
                </when>
                <otherwise>
                    and bai.baggage_no = #{dto.bagTag}
                </otherwise>
            </choose>
        </where>
    </select>

    <select id="findById" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO">
        SELECT
        b.id,
        b.accident_no,
        b.signature,
        b.accident_status accidentStatus,
        b.accident_reason,
        b.type,
        b.REASON_TYPE reasonType,
        b.serve_segment,
        b.pax_flight_no,
        b.sta,
        b.pax_flight_date,
        b.pax_segment,
        b.poa,
        b.pod,
        b.std,
        b.etd,
        b.baggage_no,
        b.baggage_flight_no,
        b.baggage_flight_date,
        b.baggage_segment,
        b.stopover_segment,
        b.baggage_type,
        b.overweight_no,
        b.damage_type,
        b.damage_part,
        b.damage_degree,
        b.baggage_brand,
        b.lost_weight,
        b.lost_type,
        b.reminder_time,
        b.id_no,
        b.alternate_terminal,
        b.lost_amount,
        b.collect_identity_pax_imgs paxImg,
        b.pax_name,
        b.pax_id,
        b.declaration_type declarationType,
        b.collect_identity_voucher_imgs voucherImg,
        b.collect_identity_baggage_imgs baggageImg,
        b.id_type,
        b.tkt_no,
        b.phone,
        b.created_by,
        b.created_time,
        b.match_accident_no,
        b.todo_time,
        b.input_source inputSource
        FROM baggage_accident_info b
        WHERE
        b.id = #{id}
    </select>

    <update id="toBoundBaggageAccident" >
        UPDATE BAGGAGE_ACCIDENT_INFO bai SET match_accident_no = #{toBoundNo} WHERE
        bai.accident_no IN
        <foreach collection="beBoundNos" item="items" open="(" separator="," close=")">
            #{items}
        </foreach>
    </update>


    <select id="getExistAccidentNumber"  resultType="java.lang.Integer">
        <!--需判断该事故单下旅客是否已经存在相同日期相同航班相同事故类型的事故单 .不统计作废状态-->
        select count(1)
        from BAGGAGE_ACCIDENT_INFO
        where ACCIDENT_STATUS !=4
        and PAX_FLIGHT_NO = #{dto.flightNo}
        and PAX_FLIGHT_DATE =#{dto.flightDate}
        and "TYPE" =#{dto.type}
        and PAX_NAME =#{dto.paxName}
        and ID_NO =#{dto.idNo}
        and ID_TYPE=#{dto.idType}
        <if test="dto.accidentNo != null and dto.accidentNo != ''">
            and ACCIDENT_NO != #{dto.accidentNo}
        </if>
    </select>

    <select id="getExistCompensationNumber"  resultType="java.lang.Integer">
        <!--//当前航班下相同补偿单类型下是否存在该补偿单中存在旅客的其他补偿单（包含全部状态）-->
        select count(DISTINCT OI.id)
        from
        COMPENSATION_ORDER_INFO oi
        LEFT JOIN COMPENSATION_PAX_INFO pi on OI.id = PI.ORDER_ID
        where OI.FLIGHT_NO = #{dto.flightNo}
        and OI.FLIGHT_DATE =#{dto.flightDate}
        and OI.ACCIDENT_TYPE=#{dto.accidentType}
        and pi.PAX_NAME =#{dto.paxName}
        and pi.ID_NO =#{dto.idNo}
        and pi.ID_TYPE=#{dto.idType}
        <if test="dto.orderNo != null and dto.orderNo != ''">
            and OI.ORDER_NO != #{dto.orderNo}
        </if>
    </select>


    <select id="getOvertimeAccident" resultType="com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO">
        select *
        from BAGGAGE_ACCIDENT_INFO
        where  to_char(sysdate,'yyyy-MM-dd') >= TO_CHAR(over_time,'yyyy-MM-dd')
        <!-- 机场端/航司端需要区分数据来源，进行执行任务， 事故来源 1机场/2航司-->
        and ACCIDENT_SOURCE ='1'

        <!--少收超时提醒发送状态，0-已发送，1-发送失败-->
        and (OVER_MSG_STATUS ='1' or OVER_MSG_STATUS is null)
  </select>





</mapper>
