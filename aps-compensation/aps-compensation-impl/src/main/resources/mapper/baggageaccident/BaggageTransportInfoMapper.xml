<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportInfoMapper">

    <sql id="baggageTransportListRef" >
        <where>
            <if test="query.createdStartTime != null">
                AND TRUNC(bti.updated_time) &gt;= TO_DATE(#{query.createdStartTime},'YYYY-MM-DD')
            </if>
            <if test="query.createdEndTime != null">
                AND TRUNC(bti.updated_time) &lt;= TO_DATE(#{query.createdEndTime},'YYYY-MM-DD')
            </if>
            <if test="query.flightStartDate != null">
                AND TO_DATE(bai.pax_flight_date,'YYYY-MM-DD') &gt;=TO_DATE(#{query.flightStartDate},'YYYY-MM-DD')
            </if>
            <if test="query.flightEndDate != null">
                AND TO_DATE(bai.pax_flight_date,'YYYY-MM-DD') &lt;=TO_DATE(#{query.flightEndDate},'YYYY-MM-DD')
            </if>
            <if test="query.receiverName != null and query.receiverName != ''">
                AND bti.receiver_name LIKE '%' ||  #{query.receiverName} || '%'
            </if>
            <if test="query.lastUpdatedBy != null and query.lastUpdatedBy!= ''">
                AND bti.updated_by LIKE '%' ||  #{query.lastUpdatedBy} || '%'
            </if>
            <if test="query.transportStartDate != null">
                AND TRUNC(bti.transport_date) &gt;= TO_DATE(#{query.transportStartDate},'YYYY-MM-DD')
            </if>
            <if test="query.transportEndDate != null">
                AND TRUNC(bti.transport_date) &lt;= TO_DATE(#{query.transportEndDate},'YYYY-MM-DD')
            </if>
            <if test="query.flightNo != null and query.flightNo != ''">
                AND bai.pax_flight_no = #{query.flightNo}
            </if>
            <if test="query.accidentNo != null and query.accidentNo != ''">
                AND bai.accident_no = #{query.accidentNo}
            </if>
            <if test="query.accidentType != null and query.accidentType != ''">
                AND bai.type = #{query.accidentType}
            </if>
            <if test="query.belongAirline != null and query.belongAirline.size() > 0">
                AND bai.belong_airline IN
                <foreach collection="query.belongAirline" item="airline" open="(" separator="," close=")">
                    #{airline}
                </foreach>
            </if>
            <if test="query.compensationStation != null and query.compensationStation != ''">
                AND bai.pax_segment like '%'|| #{query.compensationStation} || '-%'
            </if>
            <if test="query.transportNumber != null and query.transportNumber != ''">
                AND bti.transport_number = #{query.transportNumber}
            </if>
            <if test="query.createdBy != null and query.createdBy != ''">
                AND bti.updated_by LIKE CONCAT('%', #{query.createdBy}, '%')
            </if>

            <if test="query.transportCompany != null and query.transportCompany.size() > 0">
                AND bti.transport_company IN
                <foreach collection="query.transportCompany" item="company" open="(" separator="," close=")">
                    #{company}
                </foreach>
            </if>
            <if test="query.auditStatus != null and query.auditStatus.size() > 0">
                AND bti.audit_status IN
                <foreach collection="query.auditStatus" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="query.id != null and query.id != ''">
                AND bti.id = CAST(#{query.id} AS NUMBER)
            </if>
            <if test="query.reviewed != null and query.reviewed != ''">
                AND bti.reviewed = #{query.reviewed}
            </if>
        </where>
    </sql>
    <select id="queryBaggageTransportList" resultType="com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO">
    WITH transport_accidents AS (
        SELECT
            bai.tenant_id,
            bai.accident_no,
            bai.type as accident_type,
            bai.belong_airline,
            bti.transport_number,
            bti.receiver_name,
            bai.pax_flight_date as flight_date,
            bai.pax_flight_no as flight_no,
            bai.pax_segment as segment,
            bai.serve_segment as input_airport,
            bti.receiver_phone1,
            bti.receiver_phone2,
            bti.updated_by as last_updated_by,
            bti.updated_time as last_updated_time,
            bti.transport_company,
            bti.transport_cost,
            bti.id as id,
            bai.id AS accident_id,
            bti.TRANSPORT_DATE as  transportDate,
            bai.accident_status as accidentStatus,
            bti.audit_status as auditStatus,
            1 as mainAccident,
            <choose>
                <when test="query.jobNumber != null and query.jobNumber != ''">
                    CASE WHEN EXISTS (
                        SELECT 1 FROM workflow_auditor_id_info waii2
                        INNER JOIN uc_user uu ON uu.id = waii2.auditor_id
                        INNER JOIN uc_employee ue ON ue.id = uu.employee_id
                        WHERE waii2.BUSINESS_VALUE = CAST(bti.id AS VARCHAR2(50))
                        AND ue.job_number = #{query.jobNumber}
                    ) THEN 1 ELSE 0 END as auditFlag,
                    (
                        SELECT waii3.task_id FROM workflow_auditor_id_info waii3
                        INNER JOIN uc_user uu3 ON uu3.id = waii3.auditor_id
                        INNER JOIN uc_employee ue3 ON ue3.id = uu3.employee_id
                        WHERE waii3.BUSINESS_VALUE = CAST(bti.id AS VARCHAR2(50))
                        AND ue3.job_number = #{query.jobNumber}
                        AND ROWNUM = 1
                    ) as taskId
                </when>
                <otherwise>
                    0 as auditFlag,
                    NULL as taskId
                </otherwise>
            </choose>,
            bti.reviewed,
            bti.reviewed_by as reviewedBy,
            bti.reviewed_time as reviewedTime
        FROM BAGGAGE_TRANSPORT_INFO bti
        LEFT JOIN BAGGAGE_ACCIDENT_INFO bai ON bti.accident_id = bai.id
        <include refid="baggageTransportListRef"/>
    ),
        related_transport AS (
        SELECT
        bai.tenant_id,
        bai.accident_no,
        bai.type AS accident_type,
        bai.belong_airline,
        bti.transport_number,
        bti.receiver_name,
        bai.pax_flight_date AS flight_date,
        bai.pax_flight_no AS flight_no,
        bai.pax_segment AS segment,
        bai.serve_segment AS input_airport,
        bti.receiver_phone1,
        bti.receiver_phone2,
        bti.updated_by AS last_updated_by,
        bti.updated_time AS last_updated_time,
        bti.transport_company,
        bti.transport_cost,
        bti.id as id,
        bai.id AS accident_id,
        bti.TRANSPORT_DATE as  transportDate,
        bai.accident_status as accidentStatus,
        bti.audit_status as auditStatus,
        0 as mainAccident,
        <choose>
            <when test="query.jobNumber != null and query.jobNumber != ''">
                CASE WHEN EXISTS (
                    SELECT 1 FROM workflow_auditor_id_info waii2
                    INNER JOIN uc_user uu ON uu.id = waii2.auditor_id
                    INNER JOIN uc_employee ue ON ue.id = uu.employee_id
                    WHERE waii2.BUSINESS_VALUE = CAST(bti.id AS VARCHAR2(50))
                    AND ue.job_number = #{query.jobNumber}
                ) THEN 1 ELSE 0 END as auditFlag,
                (
                    SELECT waii3.task_id FROM workflow_auditor_id_info waii3
                    INNER JOIN uc_user uu3 ON uu3.id = waii3.auditor_id
                    INNER JOIN uc_employee ue3 ON ue3.id = uu3.employee_id
                    WHERE waii3.BUSINESS_VALUE = CAST(bti.id AS VARCHAR2(50))
                    AND ue3.job_number = #{query.jobNumber}
                    AND ROWNUM = 1
                ) as taskId
            </when>
            <otherwise>
                0 as auditFlag,
                NULL as taskId
            </otherwise>
        </choose>,
        bti.reviewed,
        bti.reviewed_by as reviewedBy,
        bti.reviewed_time as reviewedTime
        FROM BAGGAGE_TRANSPORT_ACCIDENT_REL btar
        INNER JOIN BAGGAGE_TRANSPORT_INFO bti ON btar.transport_id = bti.id
        INNER JOIN BAGGAGE_ACCIDENT_INFO bai ON bai.accident_no = btar.accident_no
        <include refid="baggageTransportListRef"/>
        )
        SELECT * FROM transport_accidents
        UNION ALL
        SELECT * FROM related_transport
        ORDER BY 7 DESC, 17 DESC, 14 DESC
    </select>

    <select id="checkTransportAuditPermission" resultType="com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO">
        SELECT
        waii.*
        FROM workflow_auditor_id_info waii
        INNER JOIN uc_user uu ON uu.id = waii.auditor_id
        INNER JOIN uc_employee ue ON ue.id = uu.employee_id
        WHERE waii.BUSINESS_VALUE = #{transportId}
        AND ue.job_number = #{jobNumber}
    </select>

</mapper>