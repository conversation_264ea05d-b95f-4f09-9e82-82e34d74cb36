<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.compensation.mapper.CompensationOrderQueriesMapper">

    <select id="compensationBaseInfoPage" resultType="com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO" databaseId="mysql">
        SELECT coi.id,
        coi.accident_id ,
        coi.accident_no,
        coi.order_no,
        coi.compensate_type,
        coi.service_city,
        coi.choice_segment ,
        coi.choice_segment_ch,
        coi.remark,
        coi.ensure_type,
        coi.status,
        coi.expiry_date,
        coi.sum_money,
        coi.flight_no ,
        coi.flight_date,
        coi.iss_user,
        coi.close_user,
        coi.close_time,
        coi.accident_type,
        coi.accident_sub_type,
        coi.full_segment,

        coi.CREATED_BY AS created_by,
        coi.created_time created_time

        FROM compensation_order_info coi

       <where>
           <if test="dto.orderNo != null and dto.orderNo != ''">
                coi.order_no = #{dto.orderNo}
           </if>
           <if test="dto.flightNo != null and dto.flightNo != ''">
               and   coi.flight_no = #{dto.flightNo}
           </if>
           <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
               and  coi.flight_date  between #{dto.flightStartDate} and #{dto.flightEndDate}
           </if>
           <if test="dto.orgCity != null and dto.orgCity != ''">
               and  coi.full_segment like concat('%',#{dto.orgCity},'-%')
           </if>
           <if test="dto.dstCity != null and dto.dstCity != ''">
               and  coi.full_segment like concat('%-',#{dto.dstCity},'%')
           </if>
           <if test="dto.accidentType != null and dto.accidentType != ''">
               and #{dto.accidentType} = coi.accident_type
           </if>

           <if test="dto.accidentId != null">
               and coi.accident_id=#{dto.accidentId}
           </if>

           <if test="dto.accidentNo != null and dto.accidentNo != ''">
               and coi.accident_no=#{dto.accidentNo}
           </if>

           <if test="dto.compensateType != null and dto.compensateType != ''">
               and #{dto.compensateType} = coi.compensate_type
           </if>

           <if test="dto.serviceCity != null and dto.serviceCity != ''">
               and coi.service_city = #{dto.serviceCity}
           </if>
           <if test="dto.createdBy != null and dto.createdBy != ''">
               and (uc.employee_code = #{dto.createdBy} or uc.name like "%"#{dto.createdBy}"%")
           </if>

           <if test="dto.status != null and dto.status.size()>0">
               and  coi.status in
               <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                   #{item}
               </foreach>
           </if>

           <if test="dto.accidentSubType != null and dto.accidentSubType.size() > 0" >
               and coi.accident_sub_type in
               <foreach collection="dto.accidentSubType" item="item" open="(" separator="," close=")">
                   #{item}
               </foreach>
           </if>

           <!-- 行权限-->
           <if test="dto.accidentTypes != null and dto.accidentTypes.size() >0">
               and coi.accident_type in(
               select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
               <foreach collection="dto.accidentTypes" item="type"  open="(" separator="," close=")">
                   #{type}
               </foreach>
               )
           </if>
           <if test="dto.workStations != null and dto.workStations.size() >0">
               and coi.service_city in
               <foreach collection="dto.workStations" item="station"  open="(" separator="," close=")">
                   #{station}
               </foreach>
           </if>
       </where>
    </select>

    <select id="compensationBaseInfoPage" resultType="com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO" databaseId="oracle">
        SELECT coi.id,
        coi.accident_id ,
        coi.accident_no,
        coi.order_no,
        coi.compensate_type,
        coi.service_city,
        coi.choice_segment ,
        coi.choice_segment_ch,
        coi.remark,
        coi.ensure_type,
        coi.status,
        coi.expiry_date,
        coi.sum_money,
        coi.flight_no ,
        coi.flight_date,
        coi.iss_user,
        coi.close_user,
        coi.close_time,
        coi.accident_type,
        coi.accident_sub_type,
        coi.full_segment,
        coi.created_by AS created_by,
        coi.created_time created_time
        FROM compensation_order_info coi
        <where>
            <!-- 异常行李补偿单,驳回状态补偿单需要进行私有化操作，仅发起人可以编辑  -->
<!--            (coi.status != '1'  or (coi.status = '1' and coi.created_by = #{dto.userId}))-->
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.order_no = #{dto.orderNo}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and   coi.flight_no = #{dto.flightNo}
            </if>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and  coi.flight_date  between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.orgCity != null and dto.orgCity != ''">
                and  coi.full_segment like concat(concat('%',#{dto.orgCity}),'-%')
            </if>
            <if test="dto.dstCity != null and dto.dstCity != ''">
                and  coi.full_segment like concat(concat('%-',#{dto.dstCity}),'%')
            </if>
            <if test="dto.accidentType != null and dto.accidentType != ''">
                and #{dto.accidentType} = coi.accident_type
            </if>

            <if test="dto.accidentId != null">
                and coi.accident_id=#{dto.accidentId}
            </if>

            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.accident_no=#{dto.accidentNo}
            </if>

            <if test="dto.compensateType != null and dto.compensateType != ''">
                and #{dto.compensateType} = coi.compensate_type
            </if>


            <if test="dto.createdBy != null and dto.createdBy != ''">
                and coi.created_by like concat(concat('%',#{dto.createdBy}),'%')
            </if>

            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.service_city in
                <foreach collection="dto.serviceCity" item="serviceCity" open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>

            <if test="dto.status != null and dto.status.size()>0">
                and  coi.status in
                <foreach collection="dto.status" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="dto.accidentSubType != null and dto.accidentSubType.size() > 0" >
                and coi.accident_sub_type in
                <foreach collection="dto.accidentSubType" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <!-- 行权限-->
            <if test="dto.accidentTypes != null and dto.accidentTypes.size() >0">
                and coi.accident_type in(
                select sda.dict_item from sys_dictionary_data sda where  sda.item_value in
                <foreach collection="dto.accidentTypes" item="type"  open="(" separator="," close=")">
                    #{type}
                </foreach>
                )
            </if>
            <if test="dto.workStations != null and dto.workStations.size() >0">
                and coi.service_city in
                <foreach collection="dto.workStations" item="station"  open="(" separator="," close=")">
                    #{station}
                </foreach>
            </if>
        </where>

    </select>

<!--    <select id="findCompensationOrderCashInfo" resultType="com.swcares.aps.compensation.model.compensation.dto.CompensationOrderCashInfo">-->
<!--        select-->
<!--        coi.sum_money,-->
<!--        coi.order_no,-->
<!--        coi.status,-->
<!--        coi.choice_segment ,-->
<!--        coi.service_city,-->
<!--        coi.compensate_type,-->
<!--        coi.ensure_type,-->
<!--        coi.remark,-->
<!--        coi.created_time,-->
<!--        coi.created_by-->
<!--        from compensation_order_info coi-->
<!--    </select>-->

    <select id="selectPaxName" resultType="String">
        select bai.pax_name
        from
        compensation_order_info coi
        left join
        baggage_accident_info bai on coi.accident_id=bai.id
        <where>
            coi.id =#{compensationId}
        </where>
    </select>

    <select id="selectCompensationStandard" resultType="com.swcares.aps.compensation.model.compensation.dto.CompensationStandardInfoDTO">
        select
        crr.class_type,
        crr.cps_num,
        crr.adult_std,
        crr.child_std
        from
        compensation_rule_record crr
        where
        order_id =#{compensationId}
    </select>

    <select id="selectIsCustom" resultType="String">
        select crr.is_custom
        from
        compensation_order_info coi
        left join
        compensation_rule_record crr
        on coi.id = crr.order_id
        <where>
            coi.id =#{compensationId}
        </where>
    </select>

    <select id="selectCpsNum" resultType="String">
        select crr.cps_num,crr.CREATED_TIME
        from
        compensation_rule_record crr where
        crr.order_id = #{orderId}
        ORDER  BY crr.CREATED_TIME DESC
    </select>


    <select id="getCashBusinessCostsDetail" resultType="com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO" databaseId="oracle">
        SELECT coi.SOURCE,
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.ORG_CITY_AIRP) orgCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.DST_CITY_AIRP) dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT bai.IS_DOMESTIC FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) regionType,
        cfi.AC_TYPE ,
        coi.ACCIDENT_TYPE ,
        coi.ACCIDENT_SUB_TYPE ,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        cpi.TKT_NO ,
        cpi.CURRENT_AMOUNT compensateAmount,
        cpi.RECEIVE_STATUS ,
        cao.APPLY_WAY RECEIVE_WAY,
        cao.APPLY_STATUS ,
        cao.APPLY_CODE ,
        cpr.PAY_STATUS ,
        cpr.PAY_TYPE ,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.GET_MONEY_ACCOUNT ,
        cpr.PAY_START_TIME payTime,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cpi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_PAY_RECORD cpr ON cpr.APPLY_PAX_ID = cpi.ID
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        <where>
            coi.status in (4,5,6) AND coi.COMPENSATE_TYPE = '1'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and to_char(cpr.PAY_START_TIME,'yyyy-mm-dd') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP  in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.accidentType != null and dto.accidentType != ''">
                and coi.ACCIDENT_TYPE = #{dto.accidentType}
            </if>
<!--            <if test="dto.accidentType != null and dto.accidentType.size() > 0">-->
<!--                and coi.ACCIDENT_TYPE in-->
<!--                <foreach collection="dto.accidentType" item="accidentType"  open="(" separator="," close=")">-->
<!--                    #{accidentType}-->
<!--                </foreach>-->
<!--            </if>-->
            <if test="dto.source != null and dto.source != ''">
                and coi.source = #{dto.source}
            </if>
            <if test="dto.accidentSubType != null and dto.accidentSubType.size() >0">
                and coi.ACCIDENT_SUB_TYPE in
                <foreach collection="dto.accidentSubType" item="subType"  open="(" separator="," close=")">
                    #{subType}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.receiveStatus != null and dto.receiveStatus.size() >0">
                and cpi.RECEIVE_STATUS in
                <foreach collection="dto.receiveStatus" item="status"  open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.receiveWay != null and dto.receiveWay != ''">
                and cao.APPLY_WAY in
                <foreach item="item" index="index" collection="dto.receiveWay.split(',')" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="dto.applyStatus != null and dto.applyStatus.size() >0">
                and cao.APPLY_STATUS in
                <foreach collection="dto.applyStatus" item="applyStatus"  open="(" separator="," close=")">
                    #{applyStatus}
                </foreach>
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and cpi.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.payStatus != null and dto.payStatus.size() >0">
                and cpr.PAY_STATUS in
                <foreach collection="dto.payStatus" item="payStatus"  open="(" separator="," close=")">
                    #{payStatus}
                </foreach>
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,NLSSORT(cpi.PAX_NAME,'NLS_SORT=SCHINESE_PINYIN_M')
    </select>

    <select id="getCashBusinessCostsDetail" resultType="com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO" databaseId="mysql">
        SELECT
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.ORG_CITY_AIRP) orgCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.DST_CITY_AIRP) dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT bai.IS_DOMESTIC FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) regionType,
        cfi.AC_TYPE ,
        coi.ACCIDENT_TYPE ,
        coi.ACCIDENT_SUB_TYPE ,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        cpi.TKT_NO ,
        cpi.CURRENT_AMOUNT compensateAmount,
        cpi.RECEIVE_STATUS ,
        cpi.RECEIVE_WAY ,
        cao.APPLY_STATUS ,
        cao.APPLY_CODE ,
        cpr.PAY_STATUS ,
        cpr.PAY_TYPE ,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.GET_MONEY_ACCOUNT ,
        cpr.PAY_START_TIME payTime,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cpi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_PAY_RECORD cpr ON cpr.APPLY_PAX_ID = cpi.ID
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN BD_AIRPORT_INFO bai ON coi.SERVICE_CITY = bai.AIRPORT_3CODE
        <where>
            coi.status in (4,5,6) AND coi.COMPENSATE_TYPE = '1'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and DATE_FORMAT(cpr.PAY_START_TIME,'%Y-%m-%d') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity != ''">
                and cpi.ORG_CITY_AIRP = #{dto.orgCity}
            </if>
            <if test="dto.dstCity != null and dto.dstCity != ''">
                and cpi.DST_CITY_AIRP = #{dto.dstCity}
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity != ''">
                and coi.SERVICE_CITY = #{dto.serviceCity}
            </if>
            <if test="dto.accidentType != null and dto.accidentType != ''">
                and coi.ACCIDENT_TYPE = #{dto.accidentType}
            </if>
            <if test="dto.accidentSubType != null and dto.accidentSubType.size() >0">
                and coi.ACCIDENT_SUB_TYPE in
                <foreach collection="dto.accidentSubType" item="subType"  open="(" separator="," close=")">
                    #{subType}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.receiveStatus != null and dto.receiveStatus.size() >0">
                and cpi.RECEIVE_STATUS in
                <foreach collection="dto.receiveStatus" item="status"  open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.receiveWay != null and dto.receiveWay != ''">
                and cao.APPLY_WAY = #{dto.receiveWay}
            </if>
            <if test="dto.applyStatus != null and dto.applyStatus.size() >0">
                and cao.APPLY_STATUS in
                <foreach collection="dto.applyStatus" item="applyStatus"  open="(" separator="," close=")">
                    #{applyStatus}
                </foreach>
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and cpi.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.payStatus != null and dto.payStatus.size() >0">
                and cpr.PAY_STATUS in
                <foreach collection="dto.payStatus" item="payStatus"  open="(" separator="," close=")">
                    #{payStatus}
                </foreach>
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,CONVERT(cpi.PAX_NAME USING GBK)
    </select>


    <select id="getCashPayDetail" resultType="com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO" databaseId="oracle">
        SELECT
        cpr.PAY_START_TIME payTime,
        cpr.PAY_TYPE,
        cpr.TRANS_AMOUNT payAmount,
        cpr.PAY_STATUS ,
        cpr.ERR_CODE_DES payFailReason,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.APPLY_CODE,
        cao.GET_MONEY_ACCOUNT ,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_PAY_RECORD cpr
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN COMPENSATION_ORDER_INFO coi ON cpr.ORDER_ID = coi.ID
        <where>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and to_char(cpr.PAY_START_TIME,'yyyy-mm-dd')   between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY cpr.PAY_START_TIME DESC
    </select>

    <select id="getCashPayDetail" resultType="com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO" databaseId="mysql">
        SELECT
        cpr.PAY_START_TIME payTime,
        cpr.PAY_TYPE,
        cpr.TRANS_AMOUNT payAmount,
        cpr.PAY_STATUS ,
        cpr.ERR_CODE_DES payFailReason,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.APPLY_CODE,
        cao.GET_MONEY_ACCOUNT ,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_PAY_RECORD cpr
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN COMPENSATION_ORDER_INFO coi ON cpr.ORDER_ID = coi.ID
        <where>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and DATE_FORMAT(cpr.PAY_START_TIME,'%Y-%m-%d %H:%i:%s') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY cpr.PAY_START_TIME DESC
    </select>


    <select id="getCashBusinessCostsDetailReport"
            resultType="com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO" databaseId="oracle">
        SELECT
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.ORG_CITY_AIRP) orgCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.DST_CITY_AIRP) dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT fpd.DICT_VALUE FROM BD_AIRPORT_INFO bai
        LEFT JOIN FLT_PASSENGER_DICTIONARY fpd ON bai.IS_DOMESTIC = fpd.DICT_KEY
                                WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY
                                AND fpd.DICT_TYPE = 'FlightTypeEnum') regionType,
        cfi.AC_TYPE ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = coi.ACCIDENT_TYPE AND sdd.DICT_TYPE = 'accident_type') accidentType ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = coi.ACCIDENT_SUB_TYPE AND sdd.DICT_TYPE in ('baggage_type','fc_type') ) accidentSubType ,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        cpi.TKT_NO ,
        cpi.CURRENT_AMOUNT compensateAmount,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpi.RECEIVE_STATUS AND sdd.DICT_TYPE = 'receive_status') receiveStatus,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cao.APPLY_WAY AND sdd.DICT_TYPE = 'apply_way') receiveWay,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cao.APPLY_STATUS  AND sdd.DICT_TYPE = 'apply_status') applyStatus,
        cao.APPLY_CODE ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_STATUS  AND sdd.DICT_TYPE = 'pay_status') payStatus,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_TYPE  AND sdd.DICT_TYPE = 'payment_amount_type') payType,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.GET_MONEY_ACCOUNT ,
        cpr.PAY_START_TIME payTime,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cpi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_PAY_RECORD cpr ON cpr.APPLY_PAX_ID = cpi.ID
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        <where>
            coi.status in (4,5,6) AND coi.COMPENSATE_TYPE = '1'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and to_char(cpr.PAY_START_TIME,'yyyy-mm-dd') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity.size() > 0">
                and cpi.ORG_CITY_AIRP in
                <foreach collection="dto.orgCity" item="orgCity"  open="(" separator="," close=")">
                    #{orgCity}
                </foreach>
            </if>
            <if test="dto.dstCity != null and dto.dstCity.size() > 0">
                and cpi.DST_CITY_AIRP  in
                <foreach collection="dto.dstCity" item="dstCity"  open="(" separator="," close=")">
                    #{dstCity}
                </foreach>
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
                and coi.SERVICE_CITY in
                <foreach collection="dto.serviceCity" item="serviceCity"  open="(" separator="," close=")">
                    #{serviceCity}
                </foreach>
            </if>
            <if test="dto.accidentType != null and dto.accidentType != ''">
                and coi.ACCIDENT_TYPE = #{dto.accidentType}
            </if>
            <if test="dto.accidentSubType != null and dto.accidentSubType.size() >0">
                and coi.ACCIDENT_SUB_TYPE in
                <foreach collection="dto.accidentSubType" item="subType"  open="(" separator="," close=")">
                    #{subType}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.receiveStatus != null and dto.receiveStatus.size() >0">
                and cpi.RECEIVE_STATUS in
                <foreach collection="dto.receiveStatus" item="status"  open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.receiveWay != null and dto.receiveWay != ''">
                and cao.APPLY_WAY = #{dto.receiveWay}
            </if>
            <if test="dto.applyStatus != null and dto.applyStatus.size() >0">
                and cao.APPLY_STATUS in
                <foreach collection="dto.applyStatus" item="applyStatus"  open="(" separator="," close=")">
                    #{applyStatus}
                </foreach>
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and cpi.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.payStatus != null and dto.payStatus.size() >0">
                and cpr.PAY_STATUS in
                <foreach collection="dto.payStatus" item="payStatus"  open="(" separator="," close=")">
                    #{payStatus}
                </foreach>
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,NLSSORT(cpi.PAX_NAME,'NLS_SORT=SCHINESE_PINYIN_M')
    </select>


    <select id="getCashBusinessCostsDetailReport" resultType="com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO" databaseId="mysql">
        SELECT
        coi.FLIGHT_DATE ,
        coi.FLIGHT_NO ,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.ORG_CITY_AIRP) orgCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = cpi.DST_CITY_AIRP) dstCity,
        (SELECT CONCAT(bai.AIRPORT_NAME,bai.AIRPORT_3CODE) FROM BD_AIRPORT_INFO bai WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY) serviceCity,
        (SELECT fpd.DICT_VALUE FROM BD_AIRPORT_INFO bai
        LEFT JOIN FLT_PASSENGER_DICTIONARY fpd ON bai.IS_DOMESTIC = fpd.DICT_KEY
        WHERE bai.AIRPORT_3CODE = coi.SERVICE_CITY
        AND fpd.DICT_TYPE = 'FlightTypeEnum') regionType,
        cfi.AC_TYPE ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = coi.ACCIDENT_TYPE AND sdd.DICT_TYPE = 'accident_type') accidentType ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = coi.ACCIDENT_SUB_TYPE AND sdd.DICT_TYPE in ('baggage_type','fc_type') ) accidentSubType ,
        coi.ORDER_NO ,
        coi.ACCIDENT_NO ,
        cpi.PAX_NAME ,
        cpi.TKT_NO ,
        cpi.CURRENT_AMOUNT compensateAmount,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpi.RECEIVE_STATUS AND sdd.DICT_TYPE = 'receive_status') receiveStatus,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cao.APPLY_WAY AND sdd.DICT_TYPE = 'apply_way') receiveWay,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cao.APPLY_STATUS  AND sdd.DICT_TYPE = 'apply_status') applyStatus,
        cao.APPLY_CODE ,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_STATUS  AND sdd.DICT_TYPE = 'pay_status') payStatus,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_TYPE  AND sdd.DICT_TYPE = 'payment_amount_type') payType,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.GET_MONEY_ACCOUNT ,
        cpr.PAY_START_TIME payTime,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_ORDER_INFO coi
        LEFT JOIN COMPENSATION_PAX_INFO cpi ON coi.ID = cpi.ORDER_ID
        LEFT JOIN COMPENSATION_FLIGHT_INFO cfi ON cpi.ORDER_ID = cfi.ORDER_ID
        LEFT JOIN COMPENSATION_PAY_RECORD cpr ON cpr.APPLY_PAX_ID = cpi.ID
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN BD_AIRPORT_INFO bai ON coi.SERVICE_CITY = bai.AIRPORT_3CODE
        <where>
            coi.status in (4,5,6) AND coi.COMPENSATE_TYPE = '1'
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                and coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and DATE_FORMAT(cpr.PAY_START_TIME,'%Y-%m-%d') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.flightNo != null and dto.flightNo != ''">
                and coi.FLIGHT_NO = #{dto.flightNo}
            </if>
            <if test="dto.orgCity != null and dto.orgCity != ''">
                and cpi.ORG_CITY_AIRP = #{dto.orgCity}
            </if>
            <if test="dto.dstCity != null and dto.dstCity != ''">
                and cpi.DST_CITY_AIRP = #{dto.dstCity}
            </if>
            <if test="dto.serviceCity != null and dto.serviceCity != ''">
                and coi.SERVICE_CITY = #{dto.serviceCity}
            </if>
            <if test="dto.accidentType != null and dto.accidentType != ''">
                and coi.ACCIDENT_TYPE = #{dto.accidentType}
            </if>
            <if test="dto.accidentSubType != null and dto.accidentSubType.size() >0">
                and coi.ACCIDENT_SUB_TYPE in
                <foreach collection="dto.accidentSubType" item="subType"  open="(" separator="," close=")">
                    #{subType}
                </foreach>
            </if>
            <if test="dto.orderNo != null and dto.orderNo != ''">
                and coi.ORDER_NO = #{dto.orderNo}
            </if>
            <if test="dto.accidentNo != null and dto.accidentNo != ''">
                and coi.ACCIDENT_NO = #{dto.accidentNo}
            </if>
            <if test="dto.receiveStatus != null and dto.receiveStatus.size() >0">
                and cpi.RECEIVE_STATUS in
                <foreach collection="dto.receiveStatus" item="status"  open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
            <if test="dto.receiveWay != null and dto.receiveWay != ''">
                and cao.APPLY_WAY = #{dto.receiveWay}
            </if>
            <if test="dto.applyStatus != null and dto.applyStatus.size() >0">
                and cao.APPLY_STATUS in
                <foreach collection="dto.applyStatus" item="applyStatus"  open="(" separator="," close=")">
                    #{applyStatus}
                </foreach>
            </if>
            <if test="dto.paxName != null and dto.paxName != ''">
                and cpi.PAX_NAME = #{dto.paxName}
            </if>
            <if test="dto.tktNo != null and dto.tktNo != ''">
                and cpi.TKT_NO = #{dto.tktNo}
            </if>
            <if test="dto.payStatus != null and dto.payStatus.size() >0">
                and cpr.PAY_STATUS in
                <foreach collection="dto.payStatus" item="payStatus"  open="(" separator="," close=")">
                    #{payStatus}
                </foreach>
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY coi.FLIGHT_DATE DESC,cfi.STD ,CONVERT(cpi.PAX_NAME USING GBK)
    </select>
    <select id="getCashPayDetailReport"
            resultType="com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO" databaseId="oracle">
        SELECT
        cpr.PAY_START_TIME payTime,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_TYPE  AND sdd.DICT_TYPE = 'payment_amount_type') payType ,
        cpr.TRANS_AMOUNT payAmount,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_STATUS  AND sdd.DICT_TYPE = 'pay_status') payStatus,
        cpr.ERR_CODE_DES payFailReason,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.APPLY_CODE,
        cao.GET_MONEY_ACCOUNT ,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_PAY_RECORD cpr
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN COMPENSATION_ORDER_INFO coi ON cpr.ORDER_ID = coi.ID
        <where>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and to_char(cpr.PAY_START_TIME,'yyyy-mm-dd') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY cpr.PAY_START_TIME DESC
    </select>

    <select id="getCashPayDetailReport" resultType="com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO" databaseId="mysql">
        SELECT
        cpr.PAY_START_TIME payTime,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_TYPE  AND sdd.DICT_TYPE = 'payment_amount_type') payType
        cpr.TRANS_AMOUNT payAmount,
        (SELECT sdd.ITEM_VALUE  FROM SYS_DICTIONARY_DATA sdd
        WHERE sdd.DICT_ITEM = cpr.PAY_STATUS  AND sdd.DICT_TYPE = 'pay_status') payStatus,
        cpr.ERR_CODE_DES payFailReason,
        cao.APPLY_USER ,
        cao.OPEN_BANK_NAME ,
        cao.APPLY_CODE,
        cao.GET_MONEY_ACCOUNT ,
        cpr.RETURN_SERIAL_NO paySerialNumber
        FROM COMPENSATION_PAY_RECORD cpr
        LEFT JOIN COMPENSATION_APPLY_ORDER cao ON cpr.APPLY_ID = cao.ID
        LEFT JOIN COMPENSATION_ORDER_INFO coi ON cpr.ORDER_ID = coi.ID
        <where>
            <if test="dto.flightStartDate != null and dto.flightStartDate != ''">
                coi.flight_date between #{dto.flightStartDate} and #{dto.flightEndDate}
            </if>
            <if test="dto.payStartDate != null and dto.payStartDate != ''">
                and DATE_FORMAT(cpr.PAY_START_TIME,'%Y-%m-%d %H:%i:%s') between  #{dto.payStartDate} and #{dto.payEndDate}
            </if>
            <if test="dto.payType != null and dto.payType.size() >0">
                and cpr.PAY_TYPE in
                <foreach collection="dto.payType" item="payType"  open="(" separator="," close=")">
                    #{payType}
                </foreach>
            </if>
            <if test="dto.applyCode != null and dto.applyCode != ''">
                and cao.APPLY_CODE = #{dto.applyCode}
            </if>
        </where>
        ORDER BY cpr.PAY_START_TIME DESC
    </select>

</mapper>
