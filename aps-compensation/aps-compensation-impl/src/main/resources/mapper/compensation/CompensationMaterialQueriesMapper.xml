<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialQueriesMapper">

    <select id="findCompensationLuggageList" resultType="com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO">
       	select
       	coi.id,
	   	coi.status,
	   	coi.flight_no ,
	   	coi.flight_date,
	   	bai.pax_segment,
	   	coi.service_city,
	   	bai.pax_name,
	   	coi.order_no,
	   	coi.compensate_type ,
	   	cmi.amount,
	   	coi.sum_money,
		coi.CREATED_BY createdId,
		coi.CREATED_BY AS created_by
		from
	   	compensation_order_info coi
       	left join baggage_accident_info      bai on coi.accident_id = bai.id
       	left join compensation_material_info cmi on coi.order_no = cmi.order_no
	   <where>
		   coi.compensate_type = '3'
           <if test="dto.flightNo != null and dto.flightNo != ''">
			  and coi.flight_no =#{dto.flightNo}
           </if>

		   <if test="dto.startDate != null and dto.startDate != '' and dto.endDate != null and dto.endDate != ''">
			   and coi.flight_date  between #{dto.startDate} and #{dto.endDate}
		   </if>

		   <if test="dto.serviceCity != null and dto.serviceCity.size() > 0">
			   and coi.service_city in
			   <foreach collection="dto.serviceCity" item="serviceCity" open="(" separator="," close=")">
				   #{serviceCity}
			   </foreach>
		   </if>

		   <if test="dto.status != null and dto.status != ''">
			   and coi.status in
			   <foreach collection="dto.StatusSplits" item="type" open="(" separator="," close=")">
				   #{type}
			   </foreach>
		   </if>
       </where>
       order by coi.flight_date desc,coi.flight_no,status
   </select>

	<select id="findCompensationLuggageDetailInfo" resultType="com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailVO">
		select
		<!--补偿单信息如下-->
		coi.id as compensationId,
		coi.sum_money,
		coi.order_no,
		coi.status,
		coi.service_city,
		coi.ensure_type,
		coi.compensate_type,
		coi.compensate_sub_type,
		coi.compensate_standard,
		coi.commodity_id,
		coi.accident_id,
		coi.accident_no,
		coi.accident_sub_type accidentSubType,
		coi.accident_type,
		bai.type type,
		coi.created_by,
		coi.input_source inputSource,
		<!--航班信息如下-->
		coi.flight_no,
		coi.flight_date,
		bai.pax_segment,
		bai.std,
		bai.etd,
		<!--补偿旅客信息如下-->
		bai.pax_name,
		bai.id_type,
		bai.id_no,
		bai.tkt_no,
		bai.phone,
		bai.baggage_no,
		bai.pax_id,
		<!--补偿实物与成本信息如下-->
		cmi.material_brand,
		cmi.material_name,
		cmi.material_size,
		cmi.material_univalent,
		cmi.amount,
		coi.remark,
		cmi.material_id as materialId,
		lm.stock,
		cpi.receive_status as receiveStatus
		from
		compensation_order_info coi
        left join baggage_accident_info      bai on coi.accident_id = bai.id
        left join compensation_material_info cmi on coi.id = cmi.order_id
		left join compensation_pax_info cpi on cpi.order_id=cmi.order_id and cpi.pax_id=cmi.pax_id
		left join LUGGAGE_MANAGEMENT lm  on cmi.MATERIAL_ID = lm.id

        <where>
			<if test="dto.id != null and dto.id != ''">
				coi.id = #{dto.id}
			</if>
		</where>
	</select>

</mapper>
