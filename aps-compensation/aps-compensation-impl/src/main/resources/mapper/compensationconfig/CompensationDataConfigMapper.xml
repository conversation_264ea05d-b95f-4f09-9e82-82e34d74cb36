<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.swcares.aps.compensation.impl.dataconfig.mapper.CompensationDataConfigMapper">
    <select id="existsBySubTypeCode" resultType="int">
        SELECT   COUNT(1)
        FROM compensation_data_config WHERE   deleted = 0 and sub_type = #{subType}
    </select>
</mapper>