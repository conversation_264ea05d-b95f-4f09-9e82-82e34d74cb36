<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.compensation.impl.dataconfig.mapper.ReplaceConfigMapper">
    <select id="rejectReasonPages" resultType="com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO">
        select * from compensation_data_config where type=#{dto.type}
        <if test="dto.subType !=null and dto.subType !=''">
            and sub_type=#{dto.subType}
        </if>
        and deleted=0 order by updated_time desc
    </select>

    <select id="getAllRejectReason" resultType="com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO">
        select * from compensation_data_config where type=#{param.type} and sub_type=#{param.subType} and deleted=0 order by updated_time desc
    </select>
</mapper>
