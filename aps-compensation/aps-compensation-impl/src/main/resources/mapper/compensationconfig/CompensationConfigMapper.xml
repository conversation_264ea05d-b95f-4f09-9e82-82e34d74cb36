<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.swcares.aps.compensation.impl.dataconfig.mapper.CompensationConfigMapper">
    <select id="getCompensationConfigByType" resultType="com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO">
        select cdc.id, cdc.type , cdc.sub_type subType, cdc.value ,cdc.AIR_CODE,cdc.CODE_CN,
        cdc.description, cdc.type_label typeLabel, cdc.is_sys isSys, cdc.deleted,
        cdc.CREATED_BY ,
        cdc.CREATED_TIME ,
        cdc.UPDATED_BY ,
        cdc.UPDATED_TIME
        from compensation_data_config cdc
        where cdc.deleted = 0 and cdc.type = #{type}
    </select>
</mapper>