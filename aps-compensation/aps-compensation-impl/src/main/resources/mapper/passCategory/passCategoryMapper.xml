<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.usercenter.impl.passengerCategory.mapper.PassengerCategoryMapper">
    
    <resultMap id="PassengerCategoryMap" type="com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo">
        <result property="type" column="type" />
        <result property="sysDictionaryDataId" column="sysDictionaryDataId"/>
        <collection property="categorys" ofType="com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorysVo">
            <result property="category" column="category"/>
            <result property="code" column="code"/>
        </collection>
    </resultMap>

    <select id="sentPage" resultType="com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo" databaseId="oracle">
        select *
        from PASSENGER_CATEGORY_CONFIGURE pcc
        <where>
            <if test="dto.sysDictionaryDataId != null and dto.sysDictionaryDataId != ''">
                and pcc.SYS_DICTIONARY_DATA_ID = #{dto.sysDictionaryDataId}
            </if>

            <if test="dto.code != null and dto.code != ''">
                and pcc.code like concat(concat('%', #{dto.code}),'%')
            </if>

            <if test="dto.type != null and dto.type != ''">
                and pcc.type = #{dto.type}
            </if>

            <if test="dto.category != null and dto.category != ''">
                and pcc.category like concat(concat('%', #{dto.category}),'%')
            </if>

            <if test="dto.state != null and dto.state != ''">
                and pcc.state = #{dto.state}
            </if>

            <if test="dto.airCode != null and dto.airCode != ''">
                and pcc.air_code= #{dto.airCode}
            </if>
        </where>
        order by pcc.CREATED_TIME desc
    </select>

    <select id="passengerCategory" resultMap="PassengerCategoryMap" parameterType="com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto">
        SELECT
        pcc1."TYPE",
        pcc1.SYS_DICTIONARY_DATA_ID as sysDictionaryDataId,
        pcc2.CATEGORY,
        pcc2.CODE
        FROM
        ( SELECT
              "TYPE",
              SYS_DICTIONARY_DATA_ID
          FROM
              PASSENGER_CATEGORY_CONFIGURE
            <where>
              state = 1
                <if test="airCode != null and airCode != ''">
                    and air_code= #{airCode}
              </if>
              <if test="sysDictionaryDataId != null and sysDictionaryDataId != ''">
                    and SYS_DICTIONARY_DATA_ID = #{sysDictionaryDataId}
              </if>

                <if test="type != null and type != ''">
                    and "TYPE" = #{type}
                </if>
            </where>
          GROUP BY
              "TYPE",
              SYS_DICTIONARY_DATA_ID ) pcc1
        LEFT JOIN
            PASSENGER_CATEGORY_CONFIGURE pcc2
                ON pcc1."TYPE" = pcc2."TYPE"
            AND pcc1.SYS_DICTIONARY_DATA_ID = pcc2.SYS_DICTIONARY_DATA_ID
        group by
            pcc1."TYPE",
            pcc1.SYS_DICTIONARY_DATA_ID,
            pcc2.CATEGORY,
            pcc2.CODE
    </select>
</mapper>
