########################################## nacos配置  ###################################
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.17.231:8848
        namespace: airport
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        refresh-enabled: true
        file-extension: yml
        namespace: airport
        group: airport
        shared-configs:
          - data-id: application-bash-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
            group: airport
logging:
  config: classpath:logback.xml
  level:
    com.swcares: info
    org.springframework: warn
