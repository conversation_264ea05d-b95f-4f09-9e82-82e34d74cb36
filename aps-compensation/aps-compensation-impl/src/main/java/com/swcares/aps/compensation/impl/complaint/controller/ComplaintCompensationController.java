package com.swcares.aps.compensation.impl.complaint.controller;

import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintCompensationService;
import com.swcares.aps.compensation.model.complaint.dto.ComplaintCompensationCreateDto;
import com.swcares.aps.compensation.model.complaint.dto.FastCreateComplaintInfoDto;
import com.swcares.aps.compensation.model.complaint.dto.PassengerSelectInfoDto;
import com.swcares.aps.compensation.model.complaint.vo.*;
import com.swcares.aps.compensation.model.irregularflight.vo.GeneralCompensationOrderInfoDetailVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/complaint/compensation")
@Api(tags = "旅客投诉补偿统一接口")
@ApiVersion(value = "旅客投诉补偿统一接口 v1.0.1")
public class ComplaintCompensationController extends BaseController {

    @Autowired
    private ComplaintCompensationService complaintCompensationService;

    @PostMapping("/select")
    @ApiOperation(value = "投诉旅客选择查询")
    public BaseResult<List<ComplaintPassengerListVo>> select(@RequestBody @Valid PassengerSelectInfoDto passengerSelectInfoDto){
        return ok(complaintCompensationService.select(passengerSelectInfoDto));
    }

    @PostMapping("/getFrequency")
    @ApiOperation(value = "获取补偿次数")
    public BaseResult<List<CompensationCountByPassengerInfoVo>> getFrequency(@RequestBody PassengerQueryDTO passengerQueryDTO){
        return ok(complaintCompensationService.getFrequency(passengerQueryDTO));
    }

    @PostMapping("/countList")
    @ApiOperation(value = "点击补偿次数用于查询旅客补偿总次数列表")
    public BaseResult<CompensationCountListVo> countList(@RequestBody @Valid PassengerSelectInfoDto passengerSelectInfoDto){
        return ok(complaintCompensationService.countList(passengerSelectInfoDto));
    }

    @PostMapping("/create")
    @ApiOperation(value = "保存补偿单草稿/生成补偿单")
    public BaseResult<Long> create(@RequestBody @Valid ComplaintCompensationCreateDto complaintCompensationCreateDto){
        return ok(complaintCompensationService.create(complaintCompensationCreateDto));
    }

    @PostMapping("/fast/create")
    @ApiOperation(value = "保存补偿单草稿/生成补偿单")
    public BaseResult<Long> fastCreate(@RequestBody @Valid FastCreateComplaintInfoDto fastCreateComplaintInfoDto){
        return ok(complaintCompensationService.fastCreate(fastCreateComplaintInfoDto));
    }

    @PostMapping("/fast/check")
    @ApiOperation(value = "补偿单校验-fast")
    public BaseResult<ComplaintCompensationCreateVo> fastCheck(@RequestBody @Valid FastCreateComplaintInfoDto fastCreateComplaintInfoDto){
        return ok(complaintCompensationService.fastCheck(fastCreateComplaintInfoDto));
    }

    @PostMapping("/check")
    @ApiOperation(value = "补偿单校验")
    public BaseResult<ComplaintCompensationCreateVo> check(@RequestBody @Valid ComplaintCompensationCreateDto complaintCompensationCreateDto){
        return ok(complaintCompensationService.check(complaintCompensationCreateDto));
    }

    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getCompensationType")
    @ApiOperation(value = "获取补偿方式")
    public BaseResult<List<CompensationTypeVo>> getCompensationType(String belongAirline,String accidentType){
        // TODO:补偿方式的字典不支持，后续需要创建表结构来存储
        List<CompensationTypeVo> result = new ArrayList<>();
        CompensationTypeVo compensationTypeVo = new CompensationTypeVo();
        compensationTypeVo.setCompensationType("现金补偿");
        compensationTypeVo.setValue("1");
        compensationTypeVo.setRole(true);
        List<CompensationTypeVo> children = getCompensationTypeVos();
        compensationTypeVo.setChildren(children);

        CompensationTypeVo compensationTypeVo2 = new CompensationTypeVo();
        compensationTypeVo2.setCompensationType("实物");
        compensationTypeVo2.setValue("3");
        compensationTypeVo2.setRole(accidentType.equals("2") ? true : false);
        List<CompensationTypeVo> children2 = getCompensationTypeVos2(accidentType);
        compensationTypeVo2.setChildren(children2);

        CompensationTypeVo compensationTypeVo3 = new CompensationTypeVo();
        compensationTypeVo3.setCompensationType("虚拟");
        compensationTypeVo3.setValue("2");
        compensationTypeVo3.setRole(false);
        compensationTypeVo3.setChildren(null);

        result.add(compensationTypeVo);
        result.add(compensationTypeVo2);
        result.add(compensationTypeVo3);
        return ok(result);
    }

    private static @NotNull List<CompensationTypeVo> getCompensationTypeVos() {
        List<CompensationTypeVo> children = new ArrayList<>();
        for (int i = 1; i < 4; i++) {
            CompensationTypeVo compensationTypeVo1 = new CompensationTypeVo();
            if (i == 1){compensationTypeVo1.setCompensationType("微信");compensationTypeVo1.setRole(true);}
            if (i == 2){compensationTypeVo1.setCompensationType("银联");compensationTypeVo1.setRole(true);}
            if (i == 3){compensationTypeVo1.setCompensationType("数字人民币");compensationTypeVo1.setRole(false);}
            compensationTypeVo1.setValue(String.valueOf(i));
            children.add(compensationTypeVo1);
        }
        return children;
    }
    private static @NotNull List<CompensationTypeVo> getCompensationTypeVos2(String accidentType) {
        List<CompensationTypeVo> children = new ArrayList<>();
        for (int i = 1; i < 3; i++) {
            CompensationTypeVo compensationTypeVo1 = new CompensationTypeVo();
            if (i == 1){compensationTypeVo1.setCompensationType("箱包");compensationTypeVo1.setRole((accidentType.equals("2") ? true : false));}
            if (i == 2){compensationTypeVo1.setCompensationType("纪念品");compensationTypeVo1.setRole((accidentType.equals("2") ? true : false));}
            compensationTypeVo1.setValue(String.valueOf(i));
            children.add(compensationTypeVo1);
        }
        return children;
    }
    @ApiImplicitParam(name = "belongAirline",value = "归属航司",required = true)
    @GetMapping("/getCompensationAmount")
    @ApiOperation(value = "标准补偿金额")
    public BaseResult<String> getCompensationAmount(String belongAirline){
        // TODO:补偿金额的规则，展示没有设计，经和夏阳讨论，目前先返回写死的值，后续做赔偿金额规则时获取
        return ok("200");
    }

    @GetMapping("/detail/{id}")
    @ApiOperation(value = "补偿单id查询补偿单")
    public BaseResult<GeneralCompensationOrderInfoDetailVO> detail(@PathVariable(value = "id") Long id){
        return ok(complaintCompensationService.getCompensationOrderInfo(id));
    }
}
