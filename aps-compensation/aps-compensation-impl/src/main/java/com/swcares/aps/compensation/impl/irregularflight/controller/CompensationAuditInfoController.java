package com.swcares.aps.compensation.impl.irregularflight.controller;

import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


/**
 * ClassName：com.swcares.compensation.controller.AuditInfoController <br>
 * Description：赔偿单-待审核记录 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/audit/info")
@Api(tags = "赔偿单-待审核记录接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
@Slf4j
public class CompensationAuditInfoController extends BaseController {
    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @GetMapping("/findReviewer")
    @ApiOperation(value = "条件查询可选审核人，前端调用")
    public BaseResult<Object> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo,@ApiParam(value = "赔偿单id")Long orderId) {
        return ok(compensationAuditInfoService.findReviewer(orgId, userInfo,taskId,orderId));
    }

    @PostMapping("/cpcFindReviewerIds")
    @ApiOperation(value = "条件查询审核人id，cpc调用")
    public BaseResult<Object> findReviewerIds(@RequestBody @ApiParam(value = "查询参数") List<String> positions) {
        return ok(compensationAuditInfoService.findReviewerIds(positions));
    }

    @GetMapping("/cpcFindReviewer")
    @ApiOperation(value = "条件查询可选审核人，cpc调用")
    public BaseResult<Object> getReviewer(@ApiParam(value = "部门id") String deptIds, @ApiParam(value = "审核人id") String userIds,@ApiParam(value = "角色id") String roleIds) {
        return ok(compensationAuditInfoService.getReviewer(deptIds, userIds,roleIds));
    }


    @PostMapping("/saveReviewer")
    @ApiOperation(value = "审核人确认")
    public BaseResult<Object> saveReviewer(@RequestBody CompensationAuditInfoDTO dto) {
        compensationAuditInfoService.saveReviewer(dto);
        return ok();
    }

    @PostMapping("/operation")
    @ApiOperation(value = "审核操作-同意、不同意、驳回")
    public BaseResult<Object> auditOperation(@RequestBody AuditProcessorDTO dto){
        Map<String, Object> objectMap = null;
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        try {
            objectMap = compensationAuditInfoService.auditOperation(dto);
        } catch (Exception e) {
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            } throw new BusinessException(CompensationException.AUDIT_ERROR);
        }
        stopWatch.stop();
        log.info("【aps-compensation-impl】赔偿单执行审核操作耗时：【{}】",stopWatch.prettyPrint());
        return ok(objectMap);
    }

    @GetMapping("/findAuditRecord")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<Object> findAuditRecord(@ApiParam(value = "赔偿单id",required=true)Long orderId,@ApiParam(value = "赔偿单号",required=true)String orderNo) {
        List<CompensationAuditRecordVO> list = new ArrayList<>();
        try {
          list = compensationAuditInfoService.findAuditRecord(orderId,orderNo);
        } catch (Exception e) {
            e.printStackTrace();
            throw new BusinessException(CompensationException.AUDIT_ERROR);
        }
        return ok(list);
    }
}
