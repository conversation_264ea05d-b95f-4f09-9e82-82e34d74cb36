package com.swcares.aps.compensation.impl.baggage.luggage.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageInfoMapper
 * @projectName aps
 * @description:
 * @date 2022/2/9 9:36
 */
@Mapper
public interface LuggageInfoMapper extends BaseMapper<LuggageInfoDO> {

    @Select("SELECT ID, LUGGAGE_NO, BRAND, LUGGAGE_NAME, SIZES, SEGMENT, PRICE, STOCK, COMPENSATE, CREATED_BY, CREATED_TIME, " +
            "DELETE_MARK, UPDATED_BY, UPDATED_TIME " +
            "FROM LUGGAGE_MANAGEMENT where id = #{id}")
    LuggageInfoDO findById(String id);

    /**
    * @title findSegment
    * @description 根据三字码查询航站信息
    * <AUTHOR>
    * @date 2022/2/10 13:39
    * @param codes
    * @return java.util.List<java.lang.String>
    */
    List<String> findSegment(String[] codes);

    /**
    * @title findLuggageList
    * @description 分页查询箱包信息列表
    * <AUTHOR>
    * @date 2022/2/9 11:23
    * @param dto
    * @return java.util.List<com.swcares.aps.luggage.model.vo.LuggageInfoVO>
    */
    IPage<LuggageInfoVO> findLuggageList(@Param("dto") LuggageInfoPageDTO dto, Page<LuggageInfoVO> page);

    /**
    * @title findCreatedBy
    * @description 根据employeeId查询创建人信息（姓名+工号）
    * <AUTHOR>
    * @date 2022/2/9 11:26
    * @param uId
    * @return java.lang.String
    */
    @InterceptorIgnore(tenantLine = "true")
    String findCreatedBy(@Param("id") Long uId);

    /**
    * @title removeById
    * @description 通过ID软删除箱包（即是修改已删除标识）
    * <AUTHOR>
    * @date 2022/2/9 13:41
    * @param id
    * @return void
    */
    void removeById(Long id);

    /**
    * @title updateLuggage
    * @description 根据id修改箱包信息
    * <AUTHOR>
    * @date 2022/2/9 13:49
    * @param dto
    * @return void
    */
    void updateLuggage(@Param("dto") LuggageInfoDTO dto);

    /**
    * @title findTodayAmount
    * @description 通过日期查找今日箱包数量（每日上限999）
    * <AUTHOR>
    * @date 2022/2/9 13:59
    * @param today
    * @return java.lang.Integer
    */
    Integer findTodayAmount(String today);

    /**
    * @title hasExistLuggage
    * @description 同航站通过箱包品牌，名称规格，尺寸，单价，库存判断是否存在该箱包
    * <AUTHOR>
    * @date 2022/2/9 14:51
    * @param dto
    * @return java.lang.Integer
    */
    Integer hasExistLuggage(@Param("dto") LuggageInfoDTO dto);

    /**
    * @title updateCompensate
    * @description 根据箱包id和操作数量（增加传正数，减少负数）修改箱包的已赔偿数量
    * <AUTHOR>
    * @date 2022/2/10 14:05
    * @param id
    * @param number
    * @return void
    */
    @InterceptorIgnore(tenantLine = "true")
    void updateCompensate(@Param("id") Long id,@Param("num") Long number);

    /**
     * Title：findLuggageStockDetails <br>
     * Description： 根据id 查询所有箱包库存管理记录信息<br>
     * author：唐康 <br>
     * date：2022/02/09 14:37 <br>
     * @param dto
     * @return
     */
    List<FindLuggageStockInfoVO> findLuggageStockDetails(@Param("dto")LuggageInfoDTO dto);

    /**
    * @Author: 谭睿
    * @Description: 通过箱包单号查询库存数量
    * @DateTime: 2022/2/10 14:43
    * @Params: [luggageNo]
    * @Return java.lang.Integer
    */
    @InterceptorIgnore(tenantLine = "true")
    @Select("select lm.stock from luggage_management lm where lm.luggage_no = #{luggageNo}")
    Long selectLuggageStock(String luggageNo);

    List<LuggageInfoVO> getLuggageManagementList(@Param("brand") String[] brand);

}
