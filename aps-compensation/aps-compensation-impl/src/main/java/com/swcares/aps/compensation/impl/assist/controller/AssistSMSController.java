package com.swcares.aps.compensation.impl.assist.controller;

import com.swcares.aps.compensation.impl.assist.service.AssisPaxCompensationService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @title: AssistPassengerController
 * @projectName aps
 * @description: 乘机人查询领取（复用王磊代码）
 * @date 2022/1/20 10:24
 */

@RestController
@RequestMapping("/assist/sms")
@Api(tags = "短信验证接口")
@ApiVersion(value = "现金协助补偿接口")
public class AssistSMSController extends BaseController {

    @Autowired
    private AssisPaxCompensationService assisPaxCompensationService;

    @GetMapping("/sendSMSCode")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<Object> sendSMSCode(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        assisPaxCompensationService.sendSMSCode(phoneNum);
        return ok();
    }

    @GetMapping("/verificationSMSCode")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<Object> verificationSMSCode(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        assisPaxCompensationService.verificationSMSCode(phoneNum,authCode);
        return ok();
    }

}
