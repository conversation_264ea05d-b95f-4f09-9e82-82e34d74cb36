package com.swcares.aps.compensation.impl.irregularflight.controller;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationRuleRecordService;
import com.swcares.aps.compensation.model.baggage.accident.dto.VerifyAlikePaxOrderDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationSyntheticalSaveDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StopWatch;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.controller.CompensationOrderInfoController <br>
 * Description：赔偿单信息 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/order/info")
@Api(tags = "赔偿单信息接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
@Slf4j
public class CompensationOrderInfoController extends BaseController {
    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    private CompensationRuleRecordService compensationRuleRecordService;

    @PostMapping("/save")
    @ApiOperation(value = "新建赔偿单信息记录")
    public BaseResult<Object> save(@Validated @RequestBody CompensationSyntheticalSaveDTO dto) {
        Map<String,Object> dataMap = new HashMap<>();
        try {
            dto.getOrderInfoDTO().setAccidentType(dto.getAccidentInfoDTO().getAccidentType());
            dto.getOrderInfoDTO().setAccidentSubType(dto.getAccidentInfoDTO().getFcType());
            dto.getOrderInfoDTO().setFullSegment(dto.getAccidentInfoDTO().getSegment());
            dataMap = compensationOrderInfoService.saveOrderInfoAndAudit(dto);
        }catch (Exception e){
            e.printStackTrace();
            log.error("【CompensationOrderInfo】新建赔偿单保存出错,请求参数{}，错误信息{}", JSONUtil.toJsonStr(dto),e);
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }

        return ok(dataMap);
    }


    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除赔偿单信息记录")
    public BaseResult<Object> orderDelete(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        boolean deleted = compensationOrderInfoService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改赔偿单信息记录")
    public BaseResult<Object> update(@RequestBody CompensationOrderInfoDTO dto) {
        CompensationOrderInfoDO compensationOrderInfo = ObjectUtils.copyBean(dto, CompensationOrderInfoDO.class);
        boolean updated = compensationOrderInfoService.updateById(compensationOrderInfo);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/findOrderDetails/{id}")
    @ApiOperation(value = "通过ID查询赔偿单信息")
    public BaseResult<CompensationOrderDetailsVO> findOrderDetails(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(compensationOrderInfoService.findById(id));
    }

    @PostMapping("/pages")
    @ApiOperation(value = "条件分页查询赔偿单信息记录")
    public PagedResult<List<CompensationOrderInfoExamineVO>> pages(@RequestBody CompensationOrderInfoPagedDTO dto) {
        String userId = UserContext.getUserId()+"";
        dto.setUserId(userId);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        IPage<CompensationOrderInfoExamineVO> result = compensationOrderInfoService.pages(dto);
        stopWatch.stop();
        log.info("【aps-compensation-impl】赔偿单分页查询耗时：【{}】",stopWatch.prettyPrint());
        return ok(result);
    }

    @GetMapping("/findOrderRule/{id}")
    @ApiOperation(value = "通过ID查询赔偿单补偿标准信息")
    public BaseResult<List<CompensationStandardVO>> findOrderRule(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(compensationRuleRecordService.getCompensationRuleByOrderId(id));
    }

    @PutMapping("/takeEffect/{id}")
    @ApiOperation(value = "通过ID进行发放操作")
    public BaseResult<Object> takeEffect(@PathVariable @ApiParam(value = "主键id", required = true) Long id){
        String userId = UserContext.getUserId()+"";
        int column = compensationOrderInfoService.takeEffect(id,userId);
        if(column<1){
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        log.info("工号：【{}】，进行赔偿发放，赔偿单id：【{}】",UserContext.getUser().getUsername(),id);
        return ok();
    }


    @PutMapping("/close/{id}")
    @ApiOperation(value = "通过ID进行关闭操作")
    public BaseResult<Object> close(@PathVariable @ApiParam(value = "主键id", required = true) Long id){
        String userId = UserContext.getUserId()+"";
        int column = compensationOrderInfoService.close(id,userId);
        if(column<1){
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        log.info("工号：【{}】，执行关闭赔偿单，赔偿单id：【{}】",UserContext.getUser().getUsername(),id);
        return ok();
    }

    @GetMapping("/findOrderEchoInfo/{id}")
    @ApiOperation(value = "通过ID查询赔偿单编辑回显信息")
    public BaseResult<CompensationOrderEditEchoVO> getOrderEditEchoById(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(compensationOrderInfoService.getOrderEditEchoById(id));
    }

    @GetMapping("/findPaxReceiveRecord")
    @ApiOperation(value = "通过旅客PaxID-查领取记录")
    public BaseResult<List<PaxReceiveRecordVO>> findPaxReceiveRecord( @ApiParam(value = "orderId", required = true) Long orderId, @ApiParam(value = "paxId", required = true) String paxId) {
        return ok(compensationOrderInfoService.findPaxReceiveRecord(orderId,paxId));
    }

    @GetMapping("/findSameTypeOrders")
    @ApiOperation(value = "通过航班日期，航班号查询同类型赔偿单")
    public BaseResult<Object> getSameTypeOrders(@ApiParam(value = "航班日期", required = true) String flightDate,@ApiParam(value = "航班号", required = true)String flightNo,@ApiParam(value = "事故单类型", required = true)String accidentType,@ApiParam(value = "事故单Id")Long orderId) {
        return ok(compensationOrderInfoService.getSameTypeOrders(flightDate, flightNo, accidentType, orderId));
    }


}
