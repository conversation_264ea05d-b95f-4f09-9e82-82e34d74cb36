package com.swcares.aps.compensation.impl.apply.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.compensation.mapper.ApplyOrderMapper <br>
 * Description：航延补偿申领单信息表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
public interface ApplyOrderMapper extends BaseMapper<ApplyOrderDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-11-25 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<ApplyOrderVO> page(@Param("dto") ApplyOrderPagedDTO dto, Page<ApplyOrderVO> page);

    /**
     * Title：findApplyAuthPax <br>
     * Description：保存前验证赔偿单用户是否可用 <br>
     * author：王磊 <br>
     * date：2021/12/2 10:36 <br>
     * @param dto <br>
     * @return <br>
     */
    AuthPaxVO findApplyAuthPax(@Param("dto") AuthPaxOrderDTO dto);

    /**
     * Title：queryRecord <br>
     * Description：本人申领记录查询 <br>
     * author：于琦海 <br>
     * date：2021/12/7 13:42 <br>
     * @param dto ApplyQueryRecordDTO
     * @return List<ReceivingRecordVO>
     */
    IPage<ReceivingRecordVO> queryRecord(@Param("dto") ApplyQueryRecordDTO dto, Page<ReceivingRecordVO> page);

    /**
     * Title：queryReplaceRecord <br>
     * Description：代领申领记录查询 <br>
     * author：于琦海 <br>
     * date：2021/12/7 13:42 <br>
     * @param dto ApplyQueryRecordDTO
     * @param page Page<ReceivingRecordVO>
     * @return IPage<ReceivingRecordVO>
     */
    IPage<ReceivingRecordVO> queryReplaceRecord(@Param("dto") ApplyQueryRecordDTO dto, Page<ReceivingRecordVO> page);

    /**
     * Title：replaceFilterRecord <br>
     * Description：代人领取申领单筛选查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param dto ReplaceFilterRecordDTO
     * @param page Page<ReceivingRecordVO>
     * @return IPage<ReceivingRecordVO>
     */
    IPage<ReceivingRecordVO> replaceFilterRecord(@Param("dto") ReplaceFilterRecordDTO dto,
                                                 Page<ReceivingRecordVO> page);

    /**
     * Title：queryRecordCount <br>
     * Description：查询是否含有数据 <br>
     * author：于琦海 <br>
     * date：2021/12/7 13:42 <br>
     * @param dto ApplyQueryRecordDTO
     * @return int
     */
    int queryRecordCount(@Param("dto") ApplyQueryRecordDTO dto,String way);

    /**
     * Title：queryRecord <br>
     * Description：领取记录查询 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return List<ReceivingRecordVO>
     * @param dto
     */
    IPage<ReceivingRecordVO> getReceive(@Param("dto") ApplyGetReceiveDTO dto, Page<ReceivingRecordVO> page);

    /**
     * Title：getApplyInfoDetails <br>
     * Description：获取本人申领单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return ApplyInfoDetailsVO
     * @param recordId
     */
    ApplyInfoDetailsVO getApplyInfoDetails(String recordId,String idCard);

    /**
     * Title：getApplyInfoDetails <br>
     * Description：获取代领申领单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return ReplaceInfoDetailsVO
     * @param recordId
     */
    ReplaceInfoDetailsVO getReplaceApplyInfoDetails(String recordId,String idCard);

    /**
     * Title：getCompensateDetails <br>
     * Description：补偿单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return List<CompensateDetailsVO>
     * @param recordId
     */
    List<CompensateDetailsVO> getCompensateDetails(String recordId,String idCard);

    /**
     * Title：getCompensateDetails <br>
     * Description：旅客代领补偿单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return List<ReplaceCompensateDetailsVO>
     * @param recordId
     */
    List<ReplaceCompensateDetailsVO> getReplaceCompensateDetails(String recordId,String idCard);



    /**
     * @title webPage
     * @description web代领审核列表
     * <AUTHOR>
     * @date 2022/1/17 13:22
     * @param dto
     * @param page
     * @return IPage<SubstituteCollarPaxPageVO>
     */
    IPage<SubstituteCollarPaxPageVO> webPage(@Param("dto") SubstituteCollarPaxPageDTO dto, Page<SubstituteCollarPaxPageVO> page);


    /**
     * @title findApplyOrderInfo
     * @description  查-代领旅客详情-申领单信息
     * <AUTHOR>
     * @date 2022/1/17 17:15
     * @param id
     * @return ApplyOrderDetailsVO
     */
    ApplyOrderDetailsVO findApplyOrderInfo(Long id);

    /**
     * @title findSubstituteCollarPaxInfo
     * @description 代领人信息
     * <AUTHOR>
     * @date 2022/1/17 17:40
     * @param id
     * @return SubstituteCollarPaxInfoVO
     */
    SubstituteCollarPaxInfoVO findSubstituteCollarPaxInfo(Long id);

    /**
     * @title findPassengerPaxInfo
     * @description 申领人信息
     * <AUTHOR>
     * @date 2022/1/17 18:55
     * @param id
     * @return List<PassengerPaxInfoVO>
     */
    List<PassengerPaxInfoVO> findPassengerPaxInfo(Long id);

    /**
     * @title findCompensationInfo
     * @description 乘机人赔偿单信息列表
     * <AUTHOR>
     * @date 2022/1/18 9:46
     * @param id
     * @return List<PassengerCompensationOrderVO>
     */
    List<PassengerCompensationOrderVO> findCompensationInfo(Long id);

    /**
     * @title updQuickPayStatus
     * @description 修改为【1快速支付，2=等待期满后的快速支付】
     * <AUTHOR>
     * @date 2022/1/18 14:49
     * @param id
     * @return boolean
     */
    boolean updQuickPay(@Param("id") Long id,@Param("status") String status);

    @InterceptorIgnore(tenantLine = "true")
    boolean updQuickPayStatus(@Param("id") Long id, @Param("status") String status, @Param("tenantId")long tenantId);


    /**
     * @title findPaxBaseInfoByApplyId
     * @description 获取乘机人基本信息
     * <AUTHOR> @date 2022/2/23 10:24
     * @param id
     * @return List<ApplyPaxBaseInfoDTO>
     */
    List<ApplyPaxBaseInfoDTO> findPaxBaseInfoByApplyId(Long id);

    /**
     * @title findFlightByApplyId
     * @description 通过申领单id获取航班数据
     * <AUTHOR>
     * @date 2022/7/28 9:49
     * @param applyId
     * @return com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO
     */
    ApplyInfoDetailsVO findFlightByApplyId(String applyId);

    /**
     * @title findFlightById
     * @description 通过申领单id获取航班数据
     * <AUTHOR>
     * @date 2022/7/28 9:49
     * @param id
     * @return com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO
     */
    ApplyInfoDetailsVO findFlightById(String id);

    /**
     * @title findUnpaidOrderInfo
     * @description 定时任务查询支付订单
     * <AUTHOR>
     * @date 2022/7/25 16:59

     * @return java.util.List<com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO>
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ApplyOrderPayVO> findUnpaidOrderInfo();


    /***
     * @title updOrderPaxStatus
     * @description 更新保障单旅客领取状态
     * <AUTHOR>
     * @date 2022/7/26 11:13
     * @param paxInfoId
     * @param orderId
     * @param receiveStatus
     * @param receiveTime
     * @return boolean
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean updOrderPaxStatus(@Param("paxInfoId") Long paxInfoId,@Param("orderId") Long orderId,@Param("receiveStatus") String receiveStatus,@Param("receiveTime") String receiveTime);


    /***
     * @title updOrderPaxStatus
     * @description 更新申领单旅客领取状态
     * <AUTHOR>
     * @date 2022/10/24 11:13
     * @param applyCode
     * @param payStatus
     * @param errorStr
     * @return boolean
     */
    boolean updApplyOrderPaxStatus(@Param("applyCode") String applyCode,@Param("payStatus") String payStatus,@Param("errorStr") String errorStr);


    /**
     * @title getOverTimeActOrder
     * @description 获取代领审核订单是否超时
     * <AUTHOR>
     * @date 2022/10/25 15:35

     * @return com.swcares.aps.compensation.model.apply.vo.ApplyBehalfOrderVO
     */
    @InterceptorIgnore(tenantLine = "true")
    List<ApplyBehalfOrderVO> getOverTimeActOrder();


    /***
     * @title getApplyPaxOrderStatus
     * @description 根据订单，旅客id查赔偿单和旅客冻结状态
     * <AUTHOR>
     * @date 2022/10/26 11:48
     * @param orderId
     * @param paxInfoId
     * @return
     */
    ApplyPaxOrderInfoQueryVO getApplyPaxOrderStatus(@Param("orderId") Long orderId,@Param("paxInfoId") Long paxInfoId);

}
