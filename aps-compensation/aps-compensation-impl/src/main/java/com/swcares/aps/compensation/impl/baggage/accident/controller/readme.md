ALTER TABLE COMP_TRAVELSKY_TEST_APT.BAGGAGE_ACCIDENT_INFO ADD AUDIT_STATUS VARCHAR2(1) NULL;
COMMENT ON COLUMN COMP_TRAVELSKY_TEST_APT.BAGGAGE_ACCIDENT_INFO.AUDIT_STATUS IS '审核状态';


INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODEL_CODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE)
VALUES(8, 'transportation', 'baggage-impl', 'transportationAudit');


-- 添加字段
ALTER TABLE BAGGAGE_TRANSPORT_INFO ADD (
TRANSPORT_AIRPORT VARCHAR2(100),
BELONG_AIRLINE VARCHAR2(50),
AIRPORT_AUDITOR VARCHAR2(100),
AIRLINE_AUDITOR VARCHAR2(100)
);

```sql
-- 为运输单表添加复核相关字段
ALTER TABLE BAGGAGE_TRANSPORT_INFO ADD (
    REVIEWED NUMBER(1) DEFAULT 0,
    REVIEWED_BY VARCHAR2(100),
    REVIEWED_TIME TIMESTAMP
);

-- 添加字段注释
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.REVIEWED IS '是否已复核(0-未复核，1-已复核)';
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.REVIEWED_BY IS '复核人工号';
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.REVIEWED_TIME IS '复核时间';
```

-- 添加字段注释
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.TRANSPORT_AIRPORT IS '运输机场';
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.BELONG_AIRLINE IS '归属航司';
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.AIRPORT_AUDITOR IS '机场审核人';
COMMENT ON COLUMN BAGGAGE_TRANSPORT_INFO.AIRLINE_AUDITOR IS '航司审核人';


INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODE_NODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE, NODE_KEY, NODE_BUSINESS_TYPE)
VALUES(110, 'transportation', 'baggage-impl', 'transportationAudit', 'submitter', 'submitter');

INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODE_NODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE, NODE_KEY, NODE_BUSINESS_TYPE)
VALUES(111, 'transportation', 'baggage-impl', 'transportationAudit', 'Activity_01', 'common');

INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODE_NODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE, NODE_KEY, NODE_BUSINESS_TYPE)
VALUES(112, 'transportation', 'baggage-impl', 'transportationAudit', 'Activity_02', 'common');

INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODE_NODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE, NODE_KEY, NODE_BUSINESS_TYPE)
VALUES(113, 'transportation', 'baggage-impl', 'transportationAudit', 'SyncProcess_Activity_3', 'sync');

INSERT INTO COMP_TRAVELSKY_TEST_APT.WORKFLOW_MODE_NODE_INFO
(ID, BUSINESS, PROJECT, MODEL_CODE, NODE_KEY, NODE_BUSINESS_TYPE)
VALUES(114, 'transportation', 'baggage-impl', 'transportationAudit', 'end', 'end')