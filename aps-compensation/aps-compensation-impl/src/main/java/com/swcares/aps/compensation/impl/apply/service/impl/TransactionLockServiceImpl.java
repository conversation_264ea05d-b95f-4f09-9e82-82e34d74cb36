package com.swcares.aps.compensation.impl.apply.service.impl;

import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfOrderService;
import com.swcares.aps.compensation.impl.apply.service.ApplyCaptchaService;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.apply.service.TransactionLockService;
import com.swcares.aps.compensation.impl.assist.service.AssistApplyOrderService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.CompensationBehalfOrderInfoDTO;
import com.swcares.aps.compensation.model.apply.dto.CompensationOrderInfoDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName：TransactionLockServiceImpl
 * @Description：三种领取方式的并发建单控制逻辑实现
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 夏阳
 * @Date： 2022/9/20 14:13
 * @version： v1.0
 */
@Service
@Slf4j
public class TransactionLockServiceImpl implements TransactionLockService {

    private static final String APPLY_PAX_LOOK = "APPLY_PAX_LOOK";

    @Autowired
    private ApplyOrderService applyOrderService;

    @Autowired
    private ApplyBehalfOrderService applyBehalfOrderService;

    @Autowired
    private AssistApplyOrderService assistApplyOrderService;

    @Autowired
    private Redisson redisson;

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ApplyCaptchaService applyCaptchaService;

    @Override
    public void saveApplyNormalOrder(ApplyOrderDTO dto) {

        Set<String> paxIds = new HashSet<>();
        if(ObjectUtils.isNotEmpty(dto.getOrderInfoVOS())){
            for (CompensationOrderInfoDTO c: dto.getOrderInfoVOS()) {
                paxIds.add(c.getPaxId());
            }
        }

        RLock multiLock = this.getMultiLock(paxIds);
        multiLock.lock(ApplyConstants.APPLY_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
        try {
            applyOrderService.saveApplyOrder(dto);
        } finally {
            multiLock.unlock();
        }
    }

    @Override
    public void saveApplyBehalfOrder(ApplyBehalfOrderDTO dto) {
        RLock multiLock = this.getMultiLock(getPaxIds(dto.getOrderInfoVOS()));
        multiLock.lock(ApplyConstants.APPLY_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
        try {
            applyBehalfOrderService.saveBehalfApplyOrder(dto);
        } finally {
            multiLock.unlock();
        }
    }

    @Override
    public BaseResult<Object> saveApplyAssistOrder(ApplyBehalfOrderDTO dto) {
        RLock multiLock = this.getMultiLock(getPaxIds(dto.getOrderInfoVOS()));
        multiLock.lock(ApplyConstants.APPLY_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
        try {
            //判断短信验证码是否校验成功,避免绕过短信
            applyCaptchaService.verifyTokenCaptchaPassByTenant();
            return assistApplyOrderService.saveAssistApply(dto);
        } finally {
            multiLock.unlock();
        }
    }

 private Set<String> getPaxIds(List<CompensationBehalfOrderInfoDTO> list){
        Set<String> paxIds = new HashSet<>();
        if(ObjectUtils.isNotEmpty(list)){
            for (CompensationBehalfOrderInfoDTO c: list) {
                paxIds.add(c.getPaxId());
            }
        }
        return paxIds;
    }

    private RLock getMultiLock(Set<String> paxIds) {
        RLock[] paxArray = new RLock[paxIds.size()];
        int i = 0;
        for (String paxId : paxIds) {
            log.info("【aps-compensation-impl】申领时事务锁序列值:{},旅客id:{}", i, paxId);
            paxArray[i] = redisson.getLock(APPLY_PAX_LOOK +"_"+ paxId);
            i++;
        }
        return redisson.getMultiLock(paxArray);
    }
}
