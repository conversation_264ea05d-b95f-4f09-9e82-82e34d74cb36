package com.swcares.aps.compensation.impl.overbook.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.overbook.vo.*;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：OverBookAccidentInfoService
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 11:27
 * @version： v1.0
 */
public interface OverBookAccidentInfoService  extends IService<OverBookAccidentInfoDO> {
    /**
     * @title getPcList
     * @description PC超售事故单信息列表查询
     * <AUTHOR>
     * @date 2024/5/30 15:18
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.overbook.vo.OverBookPcListVO>
     */
    IPage<OverBookPcListVO> getPcList(OverBookPcSearchDTO dto);

    /**
     * @title getPcDetail
     * @description PC超售事故单详情
     * <AUTHOR>
     * @date 2024/5/30 15:18
     * @param accidentId
     * @return com.swcares.aps.compensation.model.overbook.vo.OverBookPcDetailsVO
     */
    OverBookPcDetailsVO getPcDetail(Long accidentId);

    /**
     * @title getCompensateNumList
     * @description 领取次数
     * <AUTHOR>
     * @date 2024/6/3 16:09
     * @param dto
     * @return java.util.List<com.swcares.aps.compensation.model.overbook.vo.CompensationNumListVO>
     */
    List<CompensationNumListVO> getCompensateNumList(OverBookPaxSearchDTO dto);
    //H5
    /**
     * @title getH5List
     * @description 超售事故单信息列表查询
     * <AUTHOR>
     * @date 2024/5/30 15:18
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.overbook.vo.OverBookH5ListVO>
     */
    IPage<OverBookH5ListVO> getH5List(OverBookH5SearchDTO dto);

    /**
     * @title overBookDetailInfo
     * @description 事故单详情信息
     * <AUTHOR>
     * @date 2024/5/30 15:18
     * @param accidentId
     * @return com.swcares.aps.compensation.model.overbook.vo.OverBookH5DetailsVO
     */
    OverBookH5DetailsVO overBookDetailInfo(Long accidentId);

    /**
     * @title findSelectedPax
     * @description 航班验证
     * <AUTHOR>
     * @date 2024/5/30 15:18
     * @param dto
     * @return com.swcares.aps.compensation.model.overbook.vo.OverBookPaxVO
     */
    List<OverBookPaxVO> findSelectedPax(OverBookVerifyDTO dto);

    /***
     * @title verifyAlikeOrder
     * @description 保存校验事故单||补偿单，是否存在相同类型的订单
     * <AUTHOR>
     * @date 2024/6/5 9:32
     * @param dto
     * @return void
     */
    String verifyAlikeOrder(VerifyAlikeOrderDTO dto);
    /**
     * @title saveOverBook
     * @description 保存事故单
     * <AUTHOR>
     * @date 2024/5/31 13:47
     * @param overBookSaveDTO
     * @return void
     */
    Map<String,Long> saveOverBook(OverBookSaveDTO overBookSaveDTO) throws IOException;

    /***
     * @title saveOverBookCompensationOrder
     * @description 保存事故单的补偿单
     * <AUTHOR>
     * @date 2024/6/6 15:46
     * @param compensationSaveDTO
     * @return void
     */
    Long saveOverBookCompensationOrder(OverBookCompensationSaveDTO compensationSaveDTO);
    /**
     * @title toVoidOverBook
     * @description 作废
     * <AUTHOR>
     * @date 2024/5/30 16:16
     * @param accidentId
     * @return void
     */
    void toVoidOverBook(Long accidentId);

    /**
     * @title delOverBook
     * @description 删除
     * <AUTHOR>
     * @date 2024/5/30 16:17
     * @param accidentId
     * @return void
     */
    void delOverBook(Long accidentId);

    public boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus);
}
