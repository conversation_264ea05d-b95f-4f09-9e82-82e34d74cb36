package com.swcares.aps.compensation.impl.privilege.mapper;

import com.swcares.aps.compensation.model.privilege.dto.SearchCooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname CooperativeCustomerMapper
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:11
 * @Version 1.0
 */
@Mapper
public interface CooperativeCustomerMapper extends BaseMapper<CooperativeCustomer> {

    List<CooperativeCustomer> getCooperativeCustomerList(@Param("dto") SearchCooperativeCustomerDTO searchCriteria);
}
