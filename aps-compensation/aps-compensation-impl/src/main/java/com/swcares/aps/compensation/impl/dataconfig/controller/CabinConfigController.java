package com.swcares.aps.compensation.impl.dataconfig.controller;

import com.swcares.aps.compensation.impl.dataconfig.service.CabinConfigService;
import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：CabinConfigController
 * @Description：按租户配置舱位信息
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 15:39
 * @version： v1.0
 */
@Slf4j
@RestController
@RequestMapping("/compensation/cabin/")
@Api(tags = "赔付系统舱位配置")
@ApiVersion(value = "赔付系统业务配置 v1.0")
public class CabinConfigController extends BaseController {

    @Autowired
    private CabinConfigService cabinConfigService;

    @PostMapping("/list")
    @ApiOperation(value = "加载当前用户所在航司的仓位信息")
    public BaseResult<CabinConfigVO> loadCabinByAirline(){
        return ok(cabinConfigService.loadCabinByAirline(UserContext.getCurrentUser().getTenantCode()));
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建或者修改仓位信息")
    public BaseResult<Object> createCabinConfig(String businessCabin, String economyCabin) {
        LoginUserDetails user = UserContext.getCurrentUser();
        log.info("【舱位信息设置】用户【{}】提交的公务舱信息【{}】, 经济舱信息【{}】", user.getEmployeeName() + user.getId(),
                businessCabin, economyCabin);
        cabinConfigService.createCabinConfig(businessCabin, economyCabin);
        return ok();
    }

}
