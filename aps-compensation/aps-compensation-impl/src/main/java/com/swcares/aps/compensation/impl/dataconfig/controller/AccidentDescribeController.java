package com.swcares.aps.compensation.impl.dataconfig.controller;

import com.swcares.aps.compensation.impl.dataconfig.service.AccidentDescribeService;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/baggage/accidentDescribe")
@Api(tags = "异常行李事故说明维护接口")
@ApiVersion(value = "异常行李事故说明维护接口 v1.0")
public class AccidentDescribeController extends BaseController {

    @Autowired
    private AccidentDescribeService accidentDescribeService;


    @GetMapping("/getAllAccidentDescribeSelect")
    @ApiOperation(value = "获取所有事故说明")
    public BaseResult<List<CompensationConfigVO>> getAllAccidentDescribeSelect(String airCode) {
        return ok(accidentDescribeService.getAll(airCode));
    }

    @PostMapping("/delete")
    @ApiOperation(value = "刪除事故说明记录")
    public BaseResult<Object> delete(@RequestBody CompensationConfigDeleteDTO dto) {
        if(dto.getId()==null){
            throw new BusinessException(CommonErrors.PARAM_VALIDATE_ERROR);
        }
        dto.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        boolean deleted=accidentDescribeService.delete(dto);
        if(!deleted){
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改事故说明记录")
    public BaseResult<Object>  update(@RequestBody CompensationConfigDTO dto) {
        if(dto.getId()==null){
            throw new BusinessException(CommonErrors.PARAM_VALIDATE_ERROR);
        }
        dto.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        boolean updated = accidentDescribeService.update(dto);
        if(!updated){
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/create")
    @ApiOperation(value = "添加事故说明记录")
    public BaseResult<Object>  create(@RequestBody CompensationConfigDTO dto) {
        dto.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        dto.setCreatedBy(String.valueOf(UserContext.getUserId()));
        boolean created = accidentDescribeService.create(dto);
        if(!created){
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
}
