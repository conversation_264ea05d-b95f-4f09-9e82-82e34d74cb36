package com.swcares.aps.compensation.impl.compensation.service;


import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationOrderQueriesMapper;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.mapper.FlightAccidentInfoMapper;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationStandardInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationSumMoneyDetailDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAbnormalBaggageDetailExtInfo;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAbnormalFlightDetailExtInfo;
import com.swcares.aps.compensation.model.compensation.vo.CompensationDetailExtInfoVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * @ClassName：CommpensationRepresentation
 * @Description：补偿单VO转换
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 11:23
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationRepresentationService {
    @Autowired
    FlightAccidentInfoMapper flightAccidentInfoMapper;

    @Autowired
    BaggageAccidentMapper baggageAccidentMapper;

    @Autowired
    CompensationOrderQueriesMapper compensationOrderQueriesMapper;

    @Autowired
    private WorkflowApi workflowApi;

    private final static String COMPENSATE_TYPE_BAGGAGE_DICT_ID ="2"; //异常行李
    private final static String COMPENSATE_TYPE_FLIGHT_DICT_ID ="1"; //不正常航班
    private final static String COMPENSATE_TYPE_COMPLAINT_DICT_ID ="4"; //旅客投诉

    public CompensationDetailExtInfoVO ofCashCompensationDetailExtInfoVO(AccidentCompensationDTO accidentCompensationDTO) {
        if (accidentCompensationDTO == null) {
            return null;
        }
        //TODO 根据实际需要ETL VO（具体需求出来后再做）

        //是否自定义现金属性查询
        List<String> isCustoms = compensationOrderQueriesMapper.selectIsCustom(accidentCompensationDTO.getId());
        String isCustom = isCustoms.get(0);
        if(COMPENSATE_TYPE_FLIGHT_DICT_ID.equals(accidentCompensationDTO.getAccidentType())){
            CompensationAbnormalFlightDetailExtInfo compensationAbnormalFlightDetailExtInfo = ObjectUtils.copyBean(accidentCompensationDTO, CompensationAbnormalFlightDetailExtInfo.class);

            String format = accidentCompensationDTO.getCreatedTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            compensationAbnormalFlightDetailExtInfo.setCompensationCreatedTime(format.replaceAll("T"," "));

            //已补偿数
            int hasCompensate = 0;
            //所有补偿数
            int allCompensate = 0;
            //塞补偿总金额详情
            List<CompensationPaxInfoDO> compensationPaxInfo1 = compensationAbnormalFlightDetailExtInfo.getCompensationPaxInfo();
            int adultNum =0;
            int childNum = 0;
            int babyNum = 0;
            for (CompensationPaxInfoDO c:compensationPaxInfo1) {
                if(StringUtils.isNotEmpty(c.getWithBaby()) && "1".equals(c.getWithBaby())){
                    babyNum++;
                }
                if(StringUtils.isNotEmpty(c.getIsChild()) && "1".equals(c.getIsChild())){
                    childNum++;
                }
                if( !("C".equals(c.getSex())) && "0".equals(c.getIsChild())){
                    adultNum++;
                }
                //计算已赔偿人数，婴儿也算
                if(ReceiveStatusEnum.RECEIVED.getKey().equals(c.getReceiveStatus())){
                    hasCompensate++;
                    if(StringUtils.isNotEmpty(c.getWithBaby()) && "1".equals(c.getWithBaby())){
                        hasCompensate++;
                    }
                }
            }
            allCompensate = compensationAbnormalFlightDetailExtInfo.getCompensationPaxInfo().size() + babyNum;
            compensationAbnormalFlightDetailExtInfo.setHasCompensate(hasCompensate);
            compensationAbnormalFlightDetailExtInfo.setAllCompensate(allCompensate);

            CompensationSumMoneyDetailDTO compensationSumMoneyDetailDTO = new CompensationSumMoneyDetailDTO();
            compensationSumMoneyDetailDTO.setAllCompensate(allCompensate);
            compensationSumMoneyDetailDTO.setAdultNum(adultNum);
            compensationSumMoneyDetailDTO.setChildNum(childNum);
            compensationSumMoneyDetailDTO.setBabyNum(babyNum);

            compensationSumMoneyDetailDTO.setSumMoney(accidentCompensationDTO.getSumMoney());
            compensationAbnormalFlightDetailExtInfo.setCompensationSumMoneyDetailDTO(compensationSumMoneyDetailDTO);

            //塞补偿标准
            List<CompensationStandardInfoDTO> compensationStandardInfo =compensationOrderQueriesMapper.selectCompensationStandard(accidentCompensationDTO.getId());
            Double childPercent = Double.valueOf((childNum/adultNum));
            for (CompensationStandardInfoDTO c:compensationStandardInfo) {
                c.setChildPercent(childPercent);
            }
            compensationAbnormalFlightDetailExtInfo.setCompensationStandardInfo(compensationStandardInfo);


            //塞不正常航班事故信息
            LambdaQueryWrapper<FlightAccidentInfoDO> flightWrapper = Wrappers.lambdaQuery();
            flightWrapper.eq(FlightAccidentInfoDO::getId,accidentCompensationDTO.getAccidentId());
            FlightAccidentInfoDO flightAccidentInfoDO = flightAccidentInfoMapper.selectOne(flightWrapper);
            compensationAbnormalFlightDetailExtInfo.setFlightAccidentInfoDO(flightAccidentInfoDO);
            compensationAbnormalFlightDetailExtInfo.setIsCustom(isCustom);
            compensationAbnormalFlightDetailExtInfo.getFlightAccidentInfoDO().setAccidentType(accidentCompensationDTO.getAccidentType());//事故类型数据字典转换

            return compensationAbnormalFlightDetailExtInfo;
        }

        if(COMPENSATE_TYPE_BAGGAGE_DICT_ID.equals(accidentCompensationDTO.getAccidentType())){

            CompensationAbnormalBaggageDetailExtInfo compensationAbnormalBaggageDetailExtInfo = ObjectUtils.copyBean(accidentCompensationDTO, CompensationAbnormalBaggageDetailExtInfo.class);
            String format = accidentCompensationDTO.getCreatedTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            compensationAbnormalBaggageDetailExtInfo.setCompensationCreatedTime(format.replaceAll("T"," "));
            String paxName = null;
            paxName =compensationOrderQueriesMapper.selectPaxName(accidentCompensationDTO.getId());
            compensationAbnormalBaggageDetailExtInfo.setPaxName(paxName);

            LambdaQueryWrapper<BaggageAccidentInfoDO> baggageWrapper= Wrappers.lambdaQuery();
            baggageWrapper.eq(BaggageAccidentInfoDO::getId,accidentCompensationDTO.getAccidentId());
            BaggageAccidentInfoDO baggageAccidentInfoDO = baggageAccidentMapper.selectOne(baggageWrapper);
            compensationAbnormalBaggageDetailExtInfo.setBaggageAccidentInfoDO(baggageAccidentInfoDO);
            compensationAbnormalBaggageDetailExtInfo.setIsCustom(isCustom);

            //查旅客自定义的补偿金额

            List<String> cpsNum =compensationOrderQueriesMapper.selectCpsNum(accidentCompensationDTO.getId());
            if(CollectionUtils.isNotEmpty(cpsNum)){
                compensationAbnormalBaggageDetailExtInfo.setCpsNum(cpsNum.get(0));
            }
            return compensationAbnormalBaggageDetailExtInfo;
        }
        if (COMPENSATE_TYPE_COMPLAINT_DICT_ID.equals(accidentCompensationDTO.getAccidentType())){
            // 旅客信息返回，解冻冻结的前置条件
           return ObjectUtils.copyBean(accidentCompensationDTO, CompensationDetailExtInfoVO.class);
        }
        return null;

    }

    /**
     * @title checkIsSponsor
     * @description 检验当前查看详情的用户是否为发起人
     * <AUTHOR>
     * @date 2022/10/24 15:18
     * @param compensationDetailExtInfoVO
     * @return void
     */
    public void checkIsSponsor(CompensationDetailExtInfoVO compensationDetailExtInfoVO) {
        //判断当前节点用户，是否是发起人
        if(!CompensateStatusEnum.DRAFT.getKey().equals(compensationDetailExtInfoVO.getStatus())){
            Long userId = UserContext.getUserId();
            CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(compensationDetailExtInfoVO.getId().toString()).build()).getData();
            log.info("【箱包补偿单详情信息】当前节点的下一审核任务【{}】", JSONUtil.toJsonStr(currentTaskActivityVO));
            if(ObjectUtils.isNotEmpty(currentTaskActivityVO.getCurrentTaskActivityDTOS())){
                CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
                compensationDetailExtInfoVO.setIsSponsor(ApplyWorkflowNodeBusiTypeEnum.SUBMITTER.getType().equals(currentTaskActivityDTO.getNodeKey())
                        && userId.toString().equals(compensationDetailExtInfoVO.getCreatedBy()));
            }
        }
    }
}
