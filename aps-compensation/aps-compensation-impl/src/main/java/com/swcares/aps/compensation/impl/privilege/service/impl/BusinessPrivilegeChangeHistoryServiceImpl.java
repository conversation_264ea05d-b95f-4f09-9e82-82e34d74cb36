package com.swcares.aps.compensation.impl.privilege.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeChangeHistoryMapper;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeChangeHistoryService;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeChangeHistory;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeChangeHistoryServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 15:14
 * @Version 1.0
 */
@Service
public class BusinessPrivilegeChangeHistoryServiceImpl extends ServiceImpl<BusinessPrivilegeChangeHistoryMapper, AirlineBusinessPrivilegeChangeHistory>
        implements BusinessPrivilegeChangeHistoryService {
}
