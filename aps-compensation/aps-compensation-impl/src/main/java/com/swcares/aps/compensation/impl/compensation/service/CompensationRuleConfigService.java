package com.swcares.aps.compensation.impl.compensation.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationRuleConfigMapper;
import com.swcares.aps.compensation.model.rools.entity.CompensationRuleConfig;
import org.springframework.stereotype.Service;

@Service
public class CompensationRuleConfigService extends ServiceImpl<CompensationRuleConfigMapper, CompensationRuleConfig> {
}
