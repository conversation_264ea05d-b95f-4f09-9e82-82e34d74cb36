package com.swcares.aps.compensation.impl.baggage.accident.enums;

/**
 * @ClassName：BaggageAccidentExpressTypeEnum
 * @Description： 异常行李快递信息添加方式
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/28 10:38
 * @version： v1.0
 */
public enum BaggageAccidentExpressTypeEnum {
    ADD_MANUALLY("1", "手动添加"),
    OFFLINE_GRANT("2", "赔偿单线下发放");

    private BaggageAccidentExpressTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaggageAccidentExpressTypeEnum build(String key) {
        return build(key, true);
    }

    public static BaggageAccidentExpressTypeEnum build(String key, boolean throwEx) {
        BaggageAccidentExpressTypeEnum typeEnum = null;
        for (BaggageAccidentExpressTypeEnum element : BaggageAccidentExpressTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + BaggageAccidentExpressTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
