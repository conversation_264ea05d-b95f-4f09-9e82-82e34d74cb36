package com.swcares.aps.compensation.impl.complaint.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.basic.data.remoteapi.FltPassengerDataSourceServiceFactory;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintAccidentInfoMapper;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintCompensationInfoMapper;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapper;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.complaint.dto.*;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.*;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService <br>
 * Description：旅客投诉事故单统一方法类 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @Date 2024/5/27 11:17 <br>
 */
@Service
@Slf4j
public class ComplaintAccidentService extends BaseServiceImpl<ComplaintAccidentInfoMapper, ComplaintAccidentInfoEntity> {
    @Autowired
    FltPassengerDataSourceServiceFactory fltPassengerDataSourceServiceFactory;

    @Autowired
    ComplaintAccidentInfoMapper complaintAccidentInfoMapper;

    @Autowired
    PassengerAccidentInfoService passengerAccidentInfoService;

    @Autowired
    ComplaintCompensationInfoMapper complaintCompensationInfoMapper;
    @Autowired
    ComplaintCompensationService complaintCompensationService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    BusinessPrivilegeMapper businessPrivilegeMapper;
    @Autowired
    ComplaintAccidentService complaintAccidentService;

    @Autowired
    private Redisson redisson;

    private static final String COMPLAINT_ACCIDENT_SAVE_LOCK_PREFIX = "complaint_accident_save_lock_prefix";

    public Long create(ComplaintAccidentCreateDto complaintAccidentCreateDto){
        PassengerSelectInfoDto dto = complaintAccidentCreateDto.getPassengerSelectInfo();
        log.info("【旅客投诉-事故单创建】：航班号【{}】，航班日期【{}】,旅客数量【{}】】，申请人工号【{}】", dto.getFlightNo() , dto.getFlightDate(),complaintAccidentCreateDto.getPassengerComplaintInfoList().size() ,ApsUserUtils.getCreatedBy());
        String lockKey = COMPLAINT_ACCIDENT_SAVE_LOCK_PREFIX + dto.getFlightNo() + dto.getFlightDate() + complaintAccidentCreateDto.getPassengerComplaintInfoList().size();
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            locked = r.tryLock(0, 10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【旅客投诉-事故单创建】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【旅客投诉-事故单创建】lockKey: {},加锁对象：航班号【{}】，航班日期【{}】，旅客数量【{}】，获取锁失败，说明该单子已经被创建了~",lockKey, dto.getFlightNo(), dto.getFlightDate(), complaintAccidentCreateDto.getPassengerComplaintInfoList().size());
            throw new BusinessException(BaggageAccidentException.ACCIDENT_DUPLICATE_SUBMISSION);
        }
        log.info("【旅客投诉-事故单创建】lockKey: {},加锁对象：航班号【{}】，航班日期【{}】，旅客数量【{}】，获取锁成功，执行业务逻辑~", lockKey,dto.getFlightNo(), dto.getFlightDate(), complaintAccidentCreateDto.getPassengerComplaintInfoList().size());
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        return  complaintAccidentService.doCreate(complaintAccidentCreateDto);
    }
    /**
     * Title: 保存事故单草稿/生成事故单/包含事故单下一步
     *
     * @param complaintAccidentCreateDto ComplaintAccidentCreateDto
     * @return int
     * <AUTHOR>
     * @date 2024/5/28 09:53
     * @since 2024/5/28
     */
    @Transactional(rollbackFor = Exception.class)
    public Long doCreate(ComplaintAccidentCreateDto complaintAccidentCreateDto) {
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = insertAccident(complaintAccidentCreateDto.getPassengerSelectInfo(), complaintAccidentCreateDto.getPassengerCompensationInfoDto(), complaintAccidentCreateDto.getFlag(),complaintAccidentCreateDto.getId());
        // 吃异常，换方式
        //passengerAccidentInfoService.saveBatch(fetchPassenger(complaintAccidentCreateDto.getPassengerComplaintInfoList(), complaintAccidentInfoEntity));
        List<PassengerAccidentInfoEntity> passengerAccidentInfoEntities = fetchPassenger(complaintAccidentCreateDto.getPassengerComplaintInfoList(), complaintAccidentInfoEntity);
        if (passengerAccidentInfoEntities.size() > 0 && passengerAccidentInfoEntities != null){
            for (PassengerAccidentInfoEntity passengerAccidentInfoEntity : passengerAccidentInfoEntities) {
                passengerAccidentInfoService.save(passengerAccidentInfoEntity);
            }
        }
        if (AccidentStatusEnum.TODO.getValue().equals(String.valueOf(complaintAccidentCreateDto.getFlag()))) {
            businessDataPushHandler.dataStore(complaintAccidentInfoEntity.getId(), BusinessDataSyncConstant.BUSINESS_COMPLAINT, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }
        return complaintAccidentInfoEntity.getId();
    }

    /**
     * Title: 保存事故单统一方法
     *
     * @param passengerSelectInfo          PassengerSelectInfoDto
     * @param passengerCompensationInfoDto PassengerCompensationInfoDto
     * @param flag                         String
     * <AUTHOR>
     * @date 2024/5/28 10:25
     * @since 2024/5/28
     */
    public ComplaintAccidentInfoEntity insertAccident(PassengerSelectInfoDto passengerSelectInfo, PassengerCompensationInfoDto passengerCompensationInfoDto, String flag,Long id ) {
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = new ComplaintAccidentInfoEntity();
        if (id != null) {
            ComplaintAccidentInfoEntity accidentInfoEntity = getById(id);
            complaintAccidentInfoEntity.setAccidentId(accidentInfoEntity.getAccidentId());
            removeById(id);
        }else{
             complaintAccidentInfoEntity.setAccidentId(CompensationOrderNoUtils.getOrderNumber(UserContext.getCurrentUser().getTenantCode(), CompensationOrderNoUtils.complaintAccidentCode));
        }
        // 事故单状态（0草稿、1待处理、2处理中、3已结案、4作废）和其他的统一
        if (AccidentStatusEnum.DRAFT.getValue().equals(String.valueOf(flag))) {
            complaintAccidentInfoEntity.setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
        } else {
            complaintAccidentInfoEntity.setAccidentStatus(AccidentStatusEnum.TODO.getValue());
        }
        complaintAccidentInfoEntity.setAccidentSource("1");
        complaintAccidentInfoEntity.setFlightNo(passengerSelectInfo.getFlightNo());
        complaintAccidentInfoEntity.setFlightDate(DateUtil.parseDate(passengerSelectInfo.getFlightDate()));
        complaintAccidentInfoEntity.setSegment(passengerSelectInfo.getSegment());
        complaintAccidentInfoEntity.setSegmentCh(passengerSelectInfo.getSegmentCh());
        complaintAccidentInfoEntity.setBelongAirline(passengerSelectInfo.getFlightNo().substring(0, 2));
        AirlineBusinessPrivilege businessPrivilege = businessPrivilegeMapper.getBusinessPrivilegeBetweenCustomer(complaintAccidentInfoEntity.getBelongAirline(), UserContext.getCurrentUser().getTenantCode());
        complaintAccidentInfoEntity.setBelongAirlineAbbr(businessPrivilege.getGrantorName());
        complaintAccidentInfoEntity.setAccidentType(CompensationAccidentTypeEnum.COMPLAINT.getKey());
        complaintAccidentInfoEntity.setAccidentSubType(passengerCompensationInfoDto.getAccidentSubType());
        complaintAccidentInfoEntity.setReasonType(passengerCompensationInfoDto.getReasonType());
        complaintAccidentInfoEntity.setCreatedUser(ApsUserUtils.getCreatedBy());
        complaintAccidentInfoEntity.setCreatedTime(DateUtil.date());
        complaintAccidentInfoEntity.setContactInfo(passengerCompensationInfoDto.getContactInfo());
        complaintAccidentInfoEntity.setComplaintDep(passengerCompensationInfoDto.getComplaintDep());
        complaintAccidentInfoEntity.setComplaintChannel(passengerCompensationInfoDto.getComplaintChannel());
        complaintAccidentInfoEntity.setFilesUrl(passengerCompensationInfoDto.getFileUrl());
        complaintAccidentInfoEntity.setAccidentDes(passengerCompensationInfoDto.getAccidentDes());
        complaintAccidentInfoEntity.setComplaintDep(passengerCompensationInfoDto.getComplaintDep());
        int insert = complaintAccidentInfoMapper.insert(complaintAccidentInfoEntity);
        log.info("保存事故单-旅客投诉事故单实体：{}", complaintAccidentInfoEntity);
        return insert > 0 ? complaintAccidentInfoEntity : null;
    }

    /**
     * Title: 接口获取旅客详情存快照使用
     *
     * @param passengerComplaintInfoList List<PassengerComplaintDto>
     * @param complaintAccidentInfoEntity 事故单信息
     * @return List<PassengerAccidentInfoEntity>
     * <AUTHOR>
     * @date 2024/5/28 11:57
     * @since 2024/5/28
     */
    public List<PassengerAccidentInfoEntity> fetchPassenger(List<PassengerComplaintDto> passengerComplaintInfoList, ComplaintAccidentInfoEntity complaintAccidentInfoEntity) {
        List<PassengerAccidentInfoEntity> result = new ArrayList<>();
        passengerComplaintInfoList.forEach(passengerComplaintDto -> {
            PassengerQueryDTO dto = new PassengerQueryDTO();
            dto.setTktNo(passengerComplaintDto.getTicketNumber());
            dto.setFlightNo(complaintAccidentInfoEntity.getFlightNo());
            dto.setFlightDate(DateUtil.formatDate(complaintAccidentInfoEntity.getFlightDate()));
            PassengerBasicDataService passengerService = fltPassengerDataSourceServiceFactory.getPassengerService();
            List<PassengerBasicInfoVO> passengerInfo = passengerService.getPassengerInfo(dto);
            if (passengerInfo != null && passengerInfo.size() > 0) {
                PassengerAccidentInfoEntity passengerAccidentInfoEntity = new PassengerAccidentInfoEntity();
                passengerAccidentInfoEntity.setAccidentPrimaryId(complaintAccidentInfoEntity.getId());
                passengerAccidentInfoEntity.setPaxName(passengerInfo.get(0).getPaxName());
                passengerAccidentInfoEntity.setIdNo(passengerInfo.get(0).getIdNo());
                passengerAccidentInfoEntity.setIdType(passengerInfo.get(0).getIdType());
                passengerAccidentInfoEntity.setTktNo(passengerInfo.get(0).getTktNo());
                passengerAccidentInfoEntity.setSegment(passengerInfo.get(0).getSegment());
                passengerAccidentInfoEntity.setMainClass(passengerInfo.get(0).getMainClass());
                passengerAccidentInfoEntity.setSubClass(passengerInfo.get(0).getSubClass());
                passengerAccidentInfoEntity.setCheckIn(passengerInfo.get(0).getCheckStatus());
                passengerAccidentInfoEntity.setIsBaby(passengerInfo.get(0).getWithBaby());
                passengerAccidentInfoEntity.setIsCancel(passengerInfo.get(0).getIsCancel());
                passengerAccidentInfoEntity.setIsChild(passengerInfo.get(0).getIsChild());
                passengerAccidentInfoEntity.setPnr(passengerInfo.get(0).getPnr());
                passengerAccidentInfoEntity.setCancelTime(DateUtil.parse(passengerInfo.get(0).getCancelTime()));
                passengerAccidentInfoEntity.setTktIssueTime(DateUtil.parse(passengerInfo.get(0).getTktDate()));
                result.add(passengerAccidentInfoEntity);
            }
        });
        return result;
    }


    /**
     * Title: 生成事故单的表单验证
     *
     * @param complaintAccidentCreateDto ComplaintAccidentCreateDto
     * @return ComplaintAccidentCreateCheckVo
     * <AUTHOR>
     * @date 2024/5/28 13:46
     * @since 2024/5/28
     */
    public ComplaintAccidentCreateCheckVo check(ComplaintAccidentCreateDto complaintAccidentCreateDto) {
        ComplaintAccidentCreateCheckVo complaintAccidentCreateCheckVo = new ComplaintAccidentCreateCheckVo();
        List<ComplaintAccidentInfoEntity> complaintAccidentInfoEntities = complaintCompensationInfoMapper.getCheckAccidentInfo(complaintAccidentCreateDto.getPassengerComplaintInfoList(),complaintAccidentCreateDto.getId());
        if (complaintAccidentInfoEntities == null || complaintAccidentInfoEntities.size() == 0) {
            complaintAccidentCreateCheckVo.setFlag(false);
            return complaintAccidentCreateCheckVo;
        }
        complaintAccidentCreateCheckVo.setFlag(true);
        complaintAccidentCreateCheckVo.setComplaintAccidentCheckInfoVos(JSON.parseArray(JSON.toJSONString(complaintAccidentInfoEntities), ComplaintAccidentCheckInfoVo.class));
        // tips query
        List<FrequencyVo> frequency = complaintAccidentInfoMapper.frequency(complaintAccidentCreateDto.getPassengerComplaintInfoList(),complaintAccidentCreateDto.getId());
        if (frequency != null && frequency.size() > 0) {
            StringBuilder msg = new StringBuilder("该事故单下");
            frequency.forEach(result -> msg.append(result.getPaxName() + "旅客已有" + result.getFrequency() + "张事故单,"));
            complaintAccidentCreateCheckVo.setMsg(msg.append("是否新建事故单？").toString());
        }
        return complaintAccidentCreateCheckVo;
    }

    /**
     * Title: 投诉旅客事故单列表查询接口
     *
     * @param complaintAccidentQueryDto ComplaintAccidentQueryDto
     * @return IPage<ComplaintAccidentQueryVo>
     * <AUTHOR>
     * @date 2024/5/28 16:26
     * @since 2024/5/28
     */
    public IPage<ComplaintAccidentQueryVo> pageQuery(ComplaintAccidentQueryDto complaintAccidentQueryDto) {
        Page<ComplaintAccidentQueryVo> page = complaintAccidentQueryDto.createPage();
        page.setOptimizeCountSql(false);
        return complaintAccidentInfoMapper.pageQuery(complaintAccidentQueryDto, page);
    }


    /**
     * Title: operate accident
     *
     * @param id   accident primary id
     * @param flag operate flag 0:delete 1:invalid
     * @return void
     * <AUTHOR>
     * @date 2024/5/29 10:25
     * @since 2024/5/29
     */
    public Boolean operate(Long id, String flag) {
        ComplaintAccidentInfoEntity byId = getById(id);
        if (byId == null) {
            return false;
        }
        // 0:draft status can delete
        if ("0".equals(flag) && "0".equals(byId.getAccidentStatus())) {
            return removeById(id);
        }
        // 2 ：pending status can invalid
        if ("1".equals(flag) && "1".equals(byId.getAccidentStatus())) {
            byId.setAccidentStatus(CompensationAccidentTypeEnum.COMPLAINT.getKey());
            byId.setInvalidUser(ApsUserUtils.getCreatedBy());
            byId.setInvalidTime(new Date());
            boolean b = saveOrUpdate(byId);
            //作废，同步事故单
            businessDataPushHandler.dataStore(id, BusinessDataSyncConstant.BUSINESS_COMPLAINT, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);

            return b;
        }
        return false;
    }


    /**
     * Title: draft detail info
     *
     * @param id accident primary key
     * @return ComplaintDraftVo
     * <AUTHOR>
     * @date 2024/5/29 11:38
     * @since 2024/5/29
     */
    public ComplaintDraftVo draft(Long id) {
        ComplaintDraftVo complaintDraftVo = new ComplaintDraftVo();
        complaintDraftVo.setComplainAccidentInfoEntity(getById(id));
        List<PassengerAccidentInfoEntity> list = passengerAccidentInfoService.list(
                Wrappers.lambdaQuery(PassengerAccidentInfoEntity.class)
                        .eq(PassengerAccidentInfoEntity::getAccidentPrimaryId, id));
        if (list != null && list.size() > 0) {
            List<ComplaintPassengerListVo> accidentConcatPassengerVos = new ArrayList<>();
            PassengerSelectInfoDto passengerSelectInfoDto = new PassengerSelectInfoDto();
            passengerSelectInfoDto.setFlightDate(DateUtil.formatDate(complaintDraftVo.getComplainAccidentInfoEntity().getFlightDate()));
            passengerSelectInfoDto.setFlightNo(complaintDraftVo.getComplainAccidentInfoEntity().getFlightNo());
            passengerSelectInfoDto.setSegment(complaintDraftVo.getComplainAccidentInfoEntity().getSegment());
            passengerSelectInfoDto.setCompensateType(complaintDraftVo.getComplainAccidentInfoEntity().getAccidentType());
            List<CompensationCountByPassengerInfoVo> compensationCountByPassengerInfoVos =
                    complaintCompensationInfoMapper.selectCompensationCountByPassengerInfo(passengerSelectInfoDto);
            Map<String, String> compensationCountMap = compensationCountByPassengerInfoVos.stream()
                    .collect(Collectors.toMap(CompensationCountByPassengerInfoVo::getTicketNumber, CompensationCountByPassengerInfoVo::getFrequency));
            list.forEach(passengerAccidentInfoEntity -> {
                ComplaintPassengerListVo complaintPassengerListVo = new ComplaintPassengerListVo();
                BeanUtils.copyProperties(passengerAccidentInfoEntity, complaintPassengerListVo);
                if (compensationCountMap != null && compensationCountMap.size() > 0) {
                    complaintPassengerListVo.setFrequency(Integer.valueOf(compensationCountMap.get(complaintPassengerListVo.getTktNo()) != null ? compensationCountMap.get(complaintPassengerListVo.getTktNo()) : "0"));
                } else {
                    complaintPassengerListVo.setFrequency(0);
                }
                complaintPassengerListVo.setTktIssueTime(passengerAccidentInfoEntity.getTktIssueTime()!=null ?DateUtil.format(passengerAccidentInfoEntity.getTktIssueTime(), DatePattern.NORM_DATETIME_PATTERN):null);
                complaintPassengerListVo.setCancelTime(passengerAccidentInfoEntity.getTktIssueTime()!=null ?DateUtil.format(passengerAccidentInfoEntity.getCancelTime(), DatePattern.NORM_DATETIME_PATTERN):null);
                accidentConcatPassengerVos.add(complaintPassengerListVo);
            });
            complaintDraftVo.setPassengerComplaintDtoList(complaintCompensationService.passengerInfoSort(accidentConcatPassengerVos));
        }
        return complaintDraftVo;
    }

    /**
     * Title: accident info detail
     *
     * @param id accident primary key
     * @return ComplaintAccidentDetailVo
     * <AUTHOR>
     * @date 2024/5/29 13:22
     * @since 2024/5/29
     */
    public ComplaintAccidentDetailVo detail(Long id) {
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = getById(id);
        ComplaintAccidentDetailVo complaintAccidentDetailVo = new ComplaintAccidentDetailVo();
        complaintAccidentDetailVo.setComplaintAccidentInfoEntity(complaintAccidentInfoEntity);
        List<PassengerAccidentInfoEntity> list = passengerAccidentInfoService.list(
                Wrappers.lambdaQuery(PassengerAccidentInfoEntity.class)
                        .eq(PassengerAccidentInfoEntity::getAccidentPrimaryId, id));
        if (list != null && list.size() > 0) {
            List<ComplaintPassengerListVo> accidentConcatPassengerVos = new ArrayList<>();
            PassengerSelectInfoDto passengerSelectInfoDto = new PassengerSelectInfoDto();
            passengerSelectInfoDto.setFlightDate(DateUtil.formatDate(complaintAccidentInfoEntity.getFlightDate()));
            passengerSelectInfoDto.setFlightNo(complaintAccidentInfoEntity.getFlightNo());
            passengerSelectInfoDto.setSegment(complaintAccidentInfoEntity.getSegment());
            passengerSelectInfoDto.setCompensateType(complaintAccidentInfoEntity.getAccidentType());
            List<CompensationCountByPassengerInfoVo> compensationCountByPassengerInfoVos =
                    complaintCompensationInfoMapper.selectCompensationCountByPassengerInfo(passengerSelectInfoDto);
            Map<String, String> compensationCountMap = compensationCountByPassengerInfoVos.stream()
                    .collect(Collectors.toMap(CompensationCountByPassengerInfoVo::getTicketNumber, CompensationCountByPassengerInfoVo::getFrequency));
            list.forEach(passengerAccidentInfoEntity -> {
                ComplaintPassengerListVo complaintPassengerListVo = new ComplaintPassengerListVo();
                BeanUtils.copyProperties(passengerAccidentInfoEntity, complaintPassengerListVo);
                if (compensationCountMap != null && compensationCountMap.size() > 0) {
                    complaintPassengerListVo.setFrequency(Integer.valueOf(compensationCountMap.get(complaintPassengerListVo.getTktNo()) != null ? compensationCountMap.get(complaintPassengerListVo.getTktNo()) : "0"));
                } else {
                    complaintPassengerListVo.setFrequency(0);
                }
                complaintPassengerListVo.setTktIssueTime(passengerAccidentInfoEntity.getTktIssueTime()!=null ?DateUtil.format(passengerAccidentInfoEntity.getTktIssueTime(), DatePattern.NORM_DATETIME_PATTERN):null);
                complaintPassengerListVo.setCancelTime(passengerAccidentInfoEntity.getTktIssueTime()!=null ?DateUtil.format(passengerAccidentInfoEntity.getCancelTime(), DatePattern.NORM_DATETIME_PATTERN):null);
                accidentConcatPassengerVos.add(complaintPassengerListVo);
            });
            complaintAccidentDetailVo.setAccidentConcatPassengerVos(complaintCompensationService.passengerInfoSort(accidentConcatPassengerVos));
        }
        // fetch relationship of compensation
        complaintAccidentDetailVo.setAccidentConcatCompensationInfoVos(complaintCompensationInfoMapper.selectRelationshipOfCompensationList(id));
        return complaintAccidentDetailVo;
    }

    public ComplaintAccidentInfoEntity getAccidentDetail(Long id) {
        return this.getById(id);
    }


    /***
     * @title updAccidentStatusToDo
     * @description 批量更新事故单状态
     * <AUTHOR>
     * @date 2022/9/16 12:16
     * @param accidentNo
     * @param accidentStatus
     * @return boolean
     */
    public boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus){
        return this.baseMapper.updAccidentStatusBatch(accidentNo,accidentStatus);
    }

}
