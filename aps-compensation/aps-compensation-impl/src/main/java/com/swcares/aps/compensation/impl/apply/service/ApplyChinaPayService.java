package com.swcares.aps.compensation.impl.apply.service;


import com.swcares.aps.component.pay.pay.service.chinapay.bean.ChinaPayAuthRequestDTO;

/**
 * @ClassName：ChinaPayService
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2025/5/6 14:27
 * @version： v1.0
 */
public interface ApplyChinaPayService {
    /***
     * @title setTokenChinaPayPass
     * @description 设置银联实名认证通过的标识。用于建单判断
     * <AUTHOR>
     * @date 2025/5/6 14:13
     * @param dto
     * @return void
     */
    public void setTokenChinaPayPass(ChinaPayAuthRequestDTO dto);

    /***
     * @title setTokenChinaPayPass
     * @description 设置银联实名认证通过的标识。用于建单判断
     * <AUTHOR>
     * @date 2025/5/6 14:13
     * @param dto
     * @return void
     */
    public void isTokenChinaPayPass(ChinaPayAuthRequestDTO dto);
}
