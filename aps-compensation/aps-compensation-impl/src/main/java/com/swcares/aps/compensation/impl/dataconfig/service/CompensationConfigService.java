package com.swcares.aps.compensation.impl.dataconfig.service;

import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：DamageTypeService <br>
 * Description： 异常行李配置类<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/8 <br>
 * @version v1.0 <br>
 */
public interface CompensationConfigService {

	/**
	 * @title getDamageTypeDict
	 * @description 查询异常行李配置
	 * <AUTHOR>
	 * @date 2022/3/8 15:24

	 * @return Map<String,List<DamageTypeVO>>
	 */
	Map<String, List<CompensationConfigVO>> getCompensationConfigByType(String type);

	/**
	 * @title getCompensationConfigByTypes
	 * @description 查询配置
	 * <AUTHOR>
	 * @date 2022/3/23 15:47
	 * @param types
	 * @return
	 */
	List<CompensationConfigVO> getCompensationConfigByTypes(List<String> types);
}
