package com.swcares.aps.compensation.impl.apply.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyPaxDOVO;
import com.swcares.aps.compensation.model.apply.vo.AuthPaxVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * ClassName：com.swcares.compensation.mapper.ApplyPaxMapper <br>
 * Description：航延补偿申领旅客信息表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
public interface ApplyPaxMapper extends BaseMapper<ApplyPaxDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-11-25 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<ApplyPaxDOVO> page(@Param("dto") ApplyPaxPagedDTO dto, Page<ApplyPaxDOVO> page);

    /**
     * Title：authPaxFind <br>
     * Description：通过航班号航班时间身份证去查询旅客赔偿单信息 <br>
     * author：王磊 <br>
     * date：2021/11/29 16:04 <br>
     * @param dto <br>
     * @return <br>
     */
   List<AuthPaxVO> authPaxFind(@Param("dto") AuthPaxDTO dto);

   /**
    * Title：findCompensationOrder <br>
    * Description：通过条件查询赔偿单信息 <br>
    * author：王磊 <br>
    * date：2021/11/30 10:09 <br>
    * @param dto <br>
    * @return <br>
    */
    List<CompensationOrderInfoVO> findCompensationOrder(@Param("dto") AuthPaxDTO dto);

    /**
     * Title：findCompensationOrderFlight <br>
     * Description：通过条件查询航班信息 <br>
     * author：王磊 <br>
     * date：2021/11/30 10:09 <br>
     * @param dto <br>
     * @return <br>
     */
    CompensationFlightInfoVO findCompensationOrderFlight(@Param("dto") AuthPaxDTO dto);

    /**
     * Title：authBehalfPaxFind <br>
     * Description：通过条件获取代领人的航班信息 <br>
     * author：王磊 <br>
     * date：2022/1/6 14:04 <br>
     * @param dto <br>
     * @return <br>
     */
//    int authBehalfPaxFind(@Param("dto") AuthBehalfPaxDTO dto);

    /**
     * Title：findCompensationOrder <br>
     * Description：代领通过条件查询赔偿单信息 <br>
     * author：王磊 <br>
     * date：2021/11/30 10:09 <br>
     * @param dto <br>
     * @return <br>
     */
    List<CompensationOrderInfoVO> findCompensationOrderBehalf(@Param("dto") AuthBehalfPaxDTO dto);

    /**
     * Title：authPaxFindBehalf <br>
     * Description：通过条件获取旅客赔偿单状态 <br>
     * author：王磊 <br>
     * date：2022/1/11 10:53 <br>
     * @param dto <br>
     * @return <br>
     */
    List<AuthPaxVO> authPaxFindBehalf(@Param("dto") AuthBehalfPaxDTO dto);

    /**
     * Title：findPaxFlight <br>
     * Description：通过条件查询旅客是否在本航班 <br>
     * author：王磊 <br>
     * date：2022/1/11 10:57 <br>
     * @param dto <br>
     * @return <br>
     */
//    int findPaxFlight(@Param("dto")AuthBehalfPaxDTO dto);

    /**
     * Title：findCompensationOrderFlightBehalf <br>
     * Description：通过条件查询航班信息代领 <br>
     * author：王磊 <br>
     * date：2021/11/30 10:09 <br>
     * @param dto <br>
     * @return <br>
     */
    CompensationFlightInfoVO findCompensationOrderFlightBehalf(@Param("dto") AuthPaxDTO dto);

    /**
     * Title：updateApplyPaxStatus <br>
     * Description：TODO <br>
     * author：王磊 <br>
     * date：2022/1/20 17:35 <br>
     * @param dto
     * @return <br>
     */
    int updateApplyPaxStatus(@Param("dto") UpdateApplyPaxStatusDTO dto);

    @InterceptorIgnore(tenantLine = "true")
    int updApplyOrderPaxStatus(@Param("dto") ApplyOrderPaxStatusUpdDTO dto);
}
