package com.swcares.aps.compensation.impl.dataconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * @ClassName：CabinConfigMapper
 * @Description：按租户配置舱位信息
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 15:39
 * @version： v1.0
 */
public interface CabinConfigMapper extends BaseMapper<CabinConfigDO> {
}
