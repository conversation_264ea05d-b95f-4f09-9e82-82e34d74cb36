package com.swcares.aps.compensation.impl.baggage.luggage.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageConsumptionDetailPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageConsumptionDetailDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO;
import feign.Param;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface LuggageConsumptionMapper extends BaseMapper<LuggageConsumptionDetailDO> {

	IPage<LuggageConsumptionDetailVO> findDetailed(@Param("dto") LuggageConsumptionDetailPageDTO dto, Page<LuggageConsumptionDetailVO> page);

	@InterceptorIgnore(tenantLine = "true")
	int updateByEntity(@Param("dto") LuggageConsumptionDetailDO entity);

}
