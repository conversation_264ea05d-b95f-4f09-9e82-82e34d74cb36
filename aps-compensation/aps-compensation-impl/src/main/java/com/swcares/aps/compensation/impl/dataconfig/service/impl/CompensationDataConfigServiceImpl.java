package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.dataconfig.mapper.CompensationDataConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.CompensationDataConfigService;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import org.springframework.stereotype.Service;

@Service
public class CompensationDataConfigServiceImpl  extends ServiceImpl<CompensationDataConfigMapper, DataConfigDO> implements CompensationDataConfigService {


}
