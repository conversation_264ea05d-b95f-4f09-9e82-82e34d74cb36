package com.swcares.aps.compensation.impl.dataconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;

import java.util.List;

/**
 * ClassName： DamageTypeMapper
 * Description： @DOTO
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 * <AUTHOR>
 * Date 2022/3/8 14:51
 * @version v1.0
 */
public interface CompensationConfigMapper extends BaseMapper<DataConfigDO> {

	/**
	 * @title getCompensationConfigByType
	 * @description @TODO
	 * <AUTHOR>
	 * @date 2022/3/15 15:45
	 * @param type
	 * @return List<CompensationConfigVO>
	 */
	List<CompensationConfigVO> getCompensationConfigByType(String type);
}
