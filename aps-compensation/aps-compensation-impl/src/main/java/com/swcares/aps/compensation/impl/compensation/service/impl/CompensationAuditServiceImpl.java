package com.swcares.aps.compensation.impl.compensation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAuditService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationRuleRecordMapper;
import com.swcares.aps.compensation.impl.irregularflight.mapper.FlightAccidentInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationRuleRecordDO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：CompensationAuditServiceImpl
 * @Description：补偿单统一审核接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 19:29
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationAuditServiceImpl implements CompensationAuditService {

    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @Autowired
    private IrregularFlightWorkflowService irregularFlightWorkflowService;

    @Autowired
    private FlightAccidentInfoMapper flightAccidentInfoMapper;

    @Autowired
    private CompensationRuleRecordMapper compensationRuleRecordMapper;

    private static final String ACCIDENT_TYPE_FLIGHT = "1";
    private static final String COMPLAINT_TYPE_FLIGHT = "4";
    private static final String COMPENSATE_TYPE_CASH = "1";

    @Override
    public CompensationAuditOperationVO startAuditProcess(AccidentCompensationDTO compensation){
        CompensationSyntheticalSaveDTO request=this.createCompensationSyntheticalSaveDTO(compensation);
        try {
            Map<String, Object> resultMp = irregularFlightWorkflowService.startAuditProcess(request);
            return wrapAuditOperatorVoMp(resultMp);
        } catch (Exception e) {
            log.error("startAuditProcess发起审核流程失败，事故单号："+compensation.getAccidentNo()+",赔偿单号："+compensation.getOrderNo(),e);
            throw new BusinessException(CompensationException.AUDIT_ERROR
                    ,"发起审核流程失败，事故单号："+compensation.getAccidentNo()+",赔偿单号："+compensation.getOrderNo()+"错误原因："+e.getMessage());
        }
    }

    private CompensationSyntheticalSaveDTO createCompensationSyntheticalSaveDTO(AccidentCompensationDTO compensation) {
        CompensationSyntheticalSaveDTO compensationSyntheticalSaveDTO=new CompensationSyntheticalSaveDTO();
        CompensationOrderInfoDTO compensationOrderInfoDTO = BeanUtil.copyProperties(compensation, CompensationOrderInfoDTO.class);
        compensationSyntheticalSaveDTO.setOrderInfoDTO(compensationOrderInfoDTO);

        if(StringUtils.equals(compensationSyntheticalSaveDTO.getOrderInfoDTO().getAccidentType(),ACCIDENT_TYPE_FLIGHT) &&
                StringUtils.equals(compensationSyntheticalSaveDTO.getOrderInfoDTO().getCompensateType(),COMPENSATE_TYPE_CASH)){

            FlightAccidentInfoDO flightAccidentInfoDO = flightAccidentInfoMapper.selectById(compensationOrderInfoDTO.getAccidentId());
            FlightAccidentInfoDTO flightAccidentInfoDTO = BeanUtil.copyProperties(flightAccidentInfoDO, FlightAccidentInfoDTO.class);
            compensationSyntheticalSaveDTO.setAccidentInfoDTO(flightAccidentInfoDTO);

            LambdaQueryWrapper<CompensationRuleRecordDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(CompensationRuleRecordDO::getOrderId, compensationOrderInfoDTO.getId())
                    .eq(CompensationRuleRecordDO::getAccidentId, compensationOrderInfoDTO.getAccidentId());
            List<CompensationRuleRecordDO> compensationRuleRecordDOS = compensationRuleRecordMapper.selectList(lambdaQueryWrapper);

            List<CompensationRuleRecordDTO> dtoList=new ArrayList<>(compensationRuleRecordDOS.size());
            compensationRuleRecordDOS.forEach(t->{
                CompensationRuleRecordDTO compensationRuleRecordDTO = BeanUtil.copyProperties(t, CompensationRuleRecordDTO.class);
                dtoList.add(compensationRuleRecordDTO);
            });
            compensationSyntheticalSaveDTO.setRuleRecordDTO(dtoList);
            if(ObjectUtils.isNotEmpty(compensation.getCompensationPaxInfo())){
                List<CompensationPaxInfoDTO> compensationPaxInfoDTOs = ObjectUtils.copyBeans(compensation.getCompensationPaxInfo(), CompensationPaxInfoDTO.class);
                compensationSyntheticalSaveDTO.setPaxInfoDTO(compensationPaxInfoDTOs);
            }
        }
        if(StringUtils.equals(compensationSyntheticalSaveDTO.getOrderInfoDTO().getAccidentType(),COMPLAINT_TYPE_FLIGHT) &&
                StringUtils.equals(compensationSyntheticalSaveDTO.getOrderInfoDTO().getCompensateType(),COMPENSATE_TYPE_CASH)){

            compensationSyntheticalSaveDTO.setComplaintAccidentInfoEntity(compensation.getComplaintAccidentInfoEntity());

            LambdaQueryWrapper<CompensationRuleRecordDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(CompensationRuleRecordDO::getOrderId, compensationOrderInfoDTO.getId())
                    .eq(CompensationRuleRecordDO::getAccidentId, compensationOrderInfoDTO.getAccidentId());
            List<CompensationRuleRecordDO> compensationRuleRecordDOS = compensationRuleRecordMapper.selectList(lambdaQueryWrapper);

            List<CompensationRuleRecordDTO> dtoList=new ArrayList<>(compensationRuleRecordDOS.size());
            compensationRuleRecordDOS.forEach(t->{
                CompensationRuleRecordDTO compensationRuleRecordDTO = BeanUtil.copyProperties(t, CompensationRuleRecordDTO.class);
                dtoList.add(compensationRuleRecordDTO);
            });
            compensationSyntheticalSaveDTO.setRuleRecordDTO(dtoList);
            if(ObjectUtils.isNotEmpty(compensation.getCompensationPaxInfo())){
                List<CompensationPaxInfoDTO> compensationPaxInfoDTOs = ObjectUtils.copyBeans(compensation.getCompensationPaxInfo(), CompensationPaxInfoDTO.class);
                compensationSyntheticalSaveDTO.setPaxInfoDTO(compensationPaxInfoDTOs);
            }
        }

        if(compensationSyntheticalSaveDTO.getOrderInfoDTO().getCurrentAmount()==null
                && CollectionUtils.isNotEmpty(compensationSyntheticalSaveDTO.getRuleRecordDTO())
                && compensationSyntheticalSaveDTO.getRuleRecordDTO().get(0).getCpsNum()!=null){
            compensationSyntheticalSaveDTO.getOrderInfoDTO()
                    .setCurrentAmount(compensationSyntheticalSaveDTO.getRuleRecordDTO().get(0).getCpsNum());
        }
        return compensationSyntheticalSaveDTO;
    }

    @Override
    public CompensationAuditOperationVO submitterAuditProcess(AccidentCompensationDTO compensation) {
        CompensationSyntheticalSaveDTO request=this.createCompensationSyntheticalSaveDTO(compensation);
        try {
            Map<String, Object> resultMp = irregularFlightWorkflowService.submitterAuditProcess(request);
            return wrapAuditOperatorVoMp(resultMp);
        } catch (Exception e) {
            log.error("【aps-compensation】发起审核流程失败。赔偿单id[{}],异常信息：{}", compensation.getId(),e.getMessage());
            throw new BusinessException(CompensationException.AUDIT_ERROR,"发起审核流程失败");
        }
    }

    @Override
    public CompensationAuditReviewerVO findReviewer(Long orgId, String userInfo, String taskId, Long orderId) {
        Map<String, Object> reviewerMp = compensationAuditInfoService.findReviewer(orgId, userInfo, taskId, orderId);
        List<CompensationReviewerInfoVO> reviewerInfoVos  =(List<CompensationReviewerInfoVO> ) reviewerMp.get("userInfo");
        CompensationAuditReviewerVO vo=new CompensationAuditReviewerVO(taskId,reviewerInfoVos);
        return vo;
    }

    @Override
    public List<CompensationAuditRecordVO> findAuditRecord(Long orderId, String orderNo) {
        return compensationAuditInfoService.findAuditRecord(orderId,orderNo);
    }

    @Override
    public void saveReviewer(CompensationAuditInfoDTO compensationAuditInfoDTO) {
        compensationAuditInfoService.saveReviewer(compensationAuditInfoDTO);
    }

    @Override
    public CompensationAuditOperationVO auditOperation(AuditProcessorDTO auditProcessorDTO) throws Exception{
        Map<String, Object> resultMp = compensationAuditInfoService.auditOperation(auditProcessorDTO);

        CompensationAuditOperationVO vo = wrapAuditOperatorVoMp(resultMp);
        return vo;
    }

    @NotNull
    private CompensationAuditOperationVO wrapAuditOperatorVoMp(Map<String, Object> resultMp) {
        return new CompensationAuditOperationVO(String.valueOf(resultMp.get("orderId")),
                    (Boolean) resultMp.get("isCancel"),
                    (String)resultMp.get("taskId"),
                    resultMp.get("orderAuditorList")==null? Collections.EMPTY_LIST:resultMp.get("orderAuditorList"),
                    (Boolean)resultMp.get("isPrompt"));
    }
}
