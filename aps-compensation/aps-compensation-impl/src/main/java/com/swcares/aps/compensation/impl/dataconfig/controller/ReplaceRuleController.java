package com.swcares.aps.compensation.impl.dataconfig.controller;

import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRuleService;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRuleDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.controller.ReplaceRuleController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/replace/rule")
@Api(tags = "代领人领取规则接口")
@ApiVersion(value = "代领设置 v1.0")
public class ReplaceRuleController extends BaseController {
    @Autowired
    private ReplaceRuleService replaceRuleService;

    @GetMapping("/getRule")
    @ApiOperation(value = "获取当前配置的代领人领取规则")
    public BaseResult<Map<String,ReplaceRuleDTO>> getRule() {
        return ok(replaceRuleService.getRule());
    }

    @PostMapping("/saveBaseRule")
    @ApiOperation(value = "创建或者修改基础规则")
    public BaseResult<Object> saveBaseRule(@Validated @RequestBody ReplaceBaseRuleDTO dto) {

        boolean created = replaceRuleService.saveBaseRule(dto);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }


    @PostMapping("/savePayWaitPeriod")
    @ApiOperation(value = "创建或者修改支付等待期")
    public BaseResult<Object> savePayWaitPeriod(@Validated @RequestBody ReplacePayPeriodRuleDTO dto) {

        boolean created = replaceRuleService.savePayWaitPeriod(dto);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
}
