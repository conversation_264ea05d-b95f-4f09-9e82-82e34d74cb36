package com.swcares.aps.compensation.impl.irregularflight.workflow.process;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.IrregularflightWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.process <br>
 * Description：取消节点<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 14:52 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class IrregularFlightCancelNodeProcess implements NodeNoticeProcess {

    @Autowired
    private IrregularFlightWorkflowService irregularFlightWorkflowService;

    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        NodeNoticeProcessResult nodeNoticeProcessResult = new NodeNoticeProcessResult();
        try {
            nodeNoticeProcessResult.setData(irregularFlightWorkflowService.cancelWorkflow(noticeDTO));
        }catch (Exception e){
            e.printStackTrace();
            log.error("【aps-compensation-impl】审核取消节点-处理异常！，noticeDTO:{"+JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(CompensationException.AUDIT_ERROR,"Cancel流程异常");

        }

        return nodeNoticeProcessResult;
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.IRREGULARFLIGHT_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.CANCEL.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.BAGGAGE_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.BAGGAGE_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.CANCEL.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.COMPLAINT_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.CANCEL.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.OVERBOOK_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.CANCEL.getType())){
            result=true;
        }
        return result;
    }


}
