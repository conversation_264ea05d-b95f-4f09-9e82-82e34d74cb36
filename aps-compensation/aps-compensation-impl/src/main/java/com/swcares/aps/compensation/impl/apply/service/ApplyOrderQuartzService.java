package com.swcares.aps.compensation.impl.apply.service;

import com.swcares.aps.compensation.model.apply.dto.ApplyOrderPaxStatusUpdDTO;
import com.swcares.aps.compensation.model.apply.dto.OrderPaxStatusPayUpdDTO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * ClassName：com.swcares.scgsi.flight.service <br>
 * Description：申领单-服务 <br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月08日 20:34 <br>
 * @version v1.0 <br>
 */
public interface ApplyOrderQuartzService {


    PayRecordDO findById(Long id);

    /**
     * Title： findUnpaidOrderInfo <br>
     * Description： 查询待支付订单集合 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:01 <br>
     * @param
     * @return
     */
    List<ApplyOrderPayVO> findUnpaidOrderInfo();

    /**
     * @title saveRecord
     * @description 保存并更新支付记录表
     * <AUTHOR>
     * @date 2022/7/26 10:49
     * @param payRecordDO
     * @return void
     */
    int updateById(PayRecordDO payRecordDO);

    /***
     * @title updatePayRecordById
     * @description 按主键id修改
     * <AUTHOR>
     * @date 2022/10/27 14:10
     * @param payRecordDO
     * @return void
     */
    void updatePayRecordById(PayRecordDO payRecordDO);

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单旅客领取状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @return
     */
    int updApplyOrderPaxStatus(ApplyOrderPaxStatusUpdDTO dto);

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新赔偿单旅客领取状态 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @return
     */
    boolean updOrderPaxStatus(OrderPaxStatusPayUpdDTO dto);

    /**
     * Title：updApplyOrderStatus <br>
     * Description： 更新申领单领取时间 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 20:02 <br>
     * @param applyCode   支付订单号
     * @param receieveTime   领取时间
     * @return
     */
    void updApplyOrderInfo(String applyCode, LocalDateTime receieveTime);

    void changeStatus(PayRecordDO aoTransRecord,ApplyOrderPayVO orderInfo);

}
