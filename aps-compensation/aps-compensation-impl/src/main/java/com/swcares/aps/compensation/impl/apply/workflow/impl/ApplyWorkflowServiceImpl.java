package com.swcares.aps.compensation.impl.apply.workflow.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyStatusEnum;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWayEnum;
import com.swcares.aps.compensation.impl.apply.service.ApplyAuditService;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.workflow.NodeNoticeProcessProxy;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.WorkflowAuditorVerifyDTO;
import com.swcares.aps.component.workflow.entity.WorkflowModelCodeInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.enums.AuditStatusEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.component.workflow.service.WorkflowModelCodeInfoService;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：ApplyWorkflowServiceImpl <br>
 * Description：申领单工作流 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/18 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class ApplyWorkflowServiceImpl implements ApplyWorkflowService {

    @Autowired
    private WorkflowModelCodeInfoService workflowModelCodeInfoService;

    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;

    @Autowired
    private NodeNoticeProcessProxy nodeNoticeProcessProxy;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    private ApplyOrderService applyOrderService;

    @Autowired
    private ApplyAuditService applyAuditService;

    @Autowired
    private Redisson redisson;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;

    private static final String REDISSON_PRE_KEY="APPLY_AUDIT_";

    private static final Long REDISSON_KEY_REDIS_LOCK_TIME=100L;

    private Map<String,String> APPLY_WAY_TO_RECEIVE_TYPE_MAP=new HashMap<>(3);

    public ApplyWorkflowServiceImpl(){
        APPLY_WAY_TO_RECEIVE_TYPE_MAP.put(ApplyWayEnum.SELF.getWay(),"101");
        APPLY_WAY_TO_RECEIVE_TYPE_MAP.put(ApplyWayEnum.REPLACE.getWay(),"102");
        APPLY_WAY_TO_RECEIVE_TYPE_MAP.put(ApplyWayEnum.ASSIST.getWay(),"103");
    }

    /**
     * @title startWorkflow
     * @description 启动工作流流程
     * <AUTHOR> @date 2022/2/22 16:18
     * @param applyOrderId  申领单唯一标识ID
     * @param operatorUserId 当前操作用户ID
     * @return void
     */
    @Override
    public void startWorkflow(String applyOrderId,String operatorUserId) throws Exception {

        RLock lock = redisson.getLock(REDISSON_PRE_KEY+applyOrderId);
        Object logParams=null;

        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                log.info("【aps-apply-impl】-申领单工作流-发起审核begin:{},{}",applyOrderId,operatorUserId);
                ApplyOrderDO applyOrder = applyOrderService.getById(applyOrderId);
                if(applyOrder==null){
                    log.error("【aps-apply-impl】-申领单工作流-发起审核异常，申领单不存在,applyOrderId:{},operatorUserId:{}",applyOrderId,operatorUserId);
                    throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyOrderId+"不存在");
                }

                String applyStatus = applyOrder.getApplyStatus();
                if(StringUtils.isNotEmpty(applyStatus)){
                    if(StringUtils.equalsIgnoreCase(applyStatus, ApplyStatusEnum.AUDITING.getDictId())
                            || StringUtils.equalsIgnoreCase(applyStatus,ApplyStatusEnum.APPROVED.getDictId())
                            || StringUtils.equalsIgnoreCase(applyStatus,ApplyStatusEnum.REJECT.getDictId())){
                        log.error("【aps-apply-impl】-申领单工作流-发起审核审核异常，申领单状态异常不能发起审核,param:{},{}",JSONUtil.toJsonStr(applyOrder),operatorUserId);
                        throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyOrderId+"状态异常,不能发起工作流");
                    }
                }

                Long tenantId = TenantHolder.getTenant();
                String tenantCode = compensationOrderInfoService.getTenantCodeById(tenantId);
                WorkflowModelCodeInfoDO workflowModelCodeInfoDO=workflowModelCodeInfoService
                        .findByProjectAndBusiness(ApsProjectEnum.APPLY_IMPL.getProjectType(), ApplyConstants.APPLY_WORKFLOW_BUSINESS);
                workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
                StartProcessParamsDTO startProcessParams=new StartProcessParamsDTO();
                startProcessParams.setAssignee("userId:"+operatorUserId);
                startProcessParams.setBusinessKey(applyOrderId);
                startProcessParams.setProcDefKey(workflowModelCodeInfoDO.getModelCode());


                /**设置系统标记*/
                NodeExtVarsDTO nodeExtVars = createNodeExtVarsDTO();
                startProcessParams.setExtVars(JSONUtil.parseObj(nodeExtVars));
                logParams=startProcessParams;

                /**调用workflow启动审核流程*/
                BaseResult<CurrentTaskActivityVO> currentTaskActivityVOBaseResult = workflowApi.startProcess(startProcessParams);
                log.info("【aps-apply-impl】-申领单工作流-调用workflow接口,param:{},result:{}", JSONUtil.toJsonStr(startProcessParams),
                        JSONUtil.toJsonStr(currentTaskActivityVOBaseResult));

                //数据落库开启审核流程
                applyAuditService.openAudit(applyOrder,operatorUserId);

                NodeNoticeDTO nodeNoticeDTO = createNodeNoticeDTO(nodeExtVars, currentTaskActivityVOBaseResult);
                nodeNoticeProcessProxy.process(nodeNoticeDTO);


            }else{
                log.error("【aps-apply-impl】-申领单工作流-发起审核异常，获取分布式锁失败,applyOrderId:{},operatorUserId:{}",applyOrderId,operatorUserId);
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyOrderId+"获取分布式锁失败");
            }
        }catch (DecoderHandlerException handlerException){
            handlerDecoderException(handlerException,logParams);

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }finally {
            lock.unlock();
        }

    }

    @Override
    public void submitterWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception {

        RLock lock = redisson.getLock(REDISSON_PRE_KEY+nodeNoticeDTO.getBusiKey());
        Object logParams=null;
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                log.info("【aps-apply-impl】-申领单工作流-submitter发起审核begin:{}",JSONUtil.toJsonStr(nodeNoticeDTO));

                String applyOrderId=nodeNoticeDTO.getBusiKey();
                ApplyOrderDO applyOrder = applyOrderService.getById(applyOrderId);
                if(applyOrder==null){
                    log.error("【aps-apply-impl】-申领单工作流-submitter发起审核异常，申领单不存在,applyOrderId:{}",applyOrderId);
                    throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyOrderId+"不存在");
                }


                //调用cpc流程
                CompleteProcessParamsDTO processParams = new CompleteProcessParamsDTO();
                processParams.setBusinessKey(applyOrderId);
                processParams.setTaskId(nodeNoticeDTO.getTaskId());
                processParams.setUserId(nodeNoticeDTO.getAssignees().stream().collect(Collectors.joining(",")));

                /**设置业务数据*/
                ApplyWorkflowStartBusiDTO busiData=new ApplyWorkflowStartBusiDTO();
                busiData.setReceivetype(APPLY_WAY_TO_RECEIVE_TYPE_MAP.get(applyOrder.getApplyWay()));
                List<ApplyPaxBaseInfoDTO> baseInfoDTOS = applyOrderService.findPaxBaseInfoByApplyId(applyOrder.getId());
                busiData.setServiceCity(baseInfoDTOS.get(0).getServiceCity());
                processParams.setBusiData(JSONUtil.parseObj(busiData));


                processParams.setOptionCode(AuditStatusEnum.SUBMIT.getKey());

                /**设置系统标记*/
                NodeExtVarsDTO nodeExtVars = createNodeExtVarsDTO();
                processParams.setExtVars(JSONUtil.parseObj(nodeExtVars));

                logParams=processParams;
                /**调用cpc启动审核流程*/
                BaseResult<CurrentTaskActivityVO> currentTaskActivityVOBaseResult = workflowApi.completeTask(processParams);
                log.info("【aps-apply-impl】-申领单工作流-submitter调用workflow接口,param:{},result:{}", JSONUtil.toJsonStr(processParams),
                        JSONUtil.toJsonStr(currentTaskActivityVOBaseResult));

                NodeNoticeDTO nextNodeNoticeDTO = createNodeNoticeDTO(nodeExtVars, currentTaskActivityVOBaseResult);
                nodeNoticeProcessProxy.process(nextNodeNoticeDTO);
            }else{
                log.error("【aps-apply-impl】-申领单工作流-submitter发起审核，获取分布式锁失败：{}",JSONUtil.toJsonStr(nodeNoticeDTO));
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单获取分布式锁失败");
            }
        }catch (DecoderHandlerException handlerException){
            handlerDecoderException(handlerException,logParams);

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }finally {
            lock.unlock();
        }

    }


    /**
     * @title auditWorkflowProcess
     * @description 节点扭转操作，前端审核操作
     * <AUTHOR> @date 2022/2/22 16:02
     * @param applyAudit
     * @return void
     */
    @Override
    public void auditWorkflowProcess(ApplyAuditDTO applyAudit) throws Exception  {

        RLock lock = redisson.getLock(REDISSON_PRE_KEY+applyAudit.getApplyId());
        Object logParams=null;
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                log.info("【aps-apply-impl】-申领单工作流-进行审核操作begin:{}",JSONUtil.toJsonStr(applyAudit));
                //校验参数
                verifyAuditParam(applyAudit);

                //获取当前处理节点
                logParams=applyAudit.getApplyId();
                BaseResult<CurrentTaskActivityVO> currentTaskBaseResult = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(applyAudit.getApplyId()).build());

                CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskBaseResult.getData().getCurrentTaskActivityDTOS().get(0);

                if(!currentTaskActivityDTO.getIsEndActivity()){ //调用cpc流程
                    CompleteProcessParamsDTO processParams = new CompleteProcessParamsDTO();
                    processParams.setBusinessKey(applyAudit.getApplyId());
                    processParams.setTaskId(currentTaskActivityDTO.getTaskId());
                    processParams.setUserId("userId:"+applyAudit.getAuditorUserId());

                    //设置业务数据
                    processParams.setOptionCode(AuditStatusEnum.AGREE.getKey());


                    //调用CPC系统
                    NodeExtVarsDTO nodeExtVars = createNodeExtVarsDTO();
                    processParams.setExtVars(JSONUtil.parseObj(nodeExtVars));

                    logParams=processParams;
                    currentTaskBaseResult = workflowApi.completeTask(processParams);
                    log.info("【aps-apply-impl】-申领单工作流-auditProcess调用cpc接口,param:{},result:{}", JSONUtil.toJsonStr(processParams),
                            JSONUtil.toJsonStr(currentTaskBaseResult));

                }

                //调用本地操作数据库
                applyAuditService.processOperatorAudit(applyAudit);

                NodeNoticeDTO nextNodeNoticeDTO = createNodeNoticeDTO(createNodeExtVarsDTO(), currentTaskBaseResult);
                nodeNoticeProcessProxy.process(nextNodeNoticeDTO);

                //事故单类型对应的业务code
                ApplyOrderDO applyOrderDO = applyOrderService.getById(applyAudit.getApplyId());
                String businessCode = AccidentTypeBusinessCodeEnum.build(applyOrderDO.getAccidentType()).getValue();
                businessDataPushHandler.dataStore(applyOrderDO.getId(), businessCode, BusinessDataSyncConstant.DATA_TYPE_APPLY);

            }else{
                log.error("【aps-apply-impl】-申领单工作流-进行审核操作，获取分布式锁失败：{}",JSONUtil.toJsonStr(applyAudit));
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单获取分布式锁失败");
            }
        }catch (DecoderHandlerException handlerException){
            handlerDecoderException(handlerException,logParams);

        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }finally {
            lock.unlock();
        }

    }

    @NotNull
    private NodeNoticeDTO createNodeNoticeDTO(NodeExtVarsDTO nodeExtVars, BaseResult<CurrentTaskActivityVO> currentTaskActivityVOBaseResult) {
        CurrentTaskActivityVO nextTask = currentTaskActivityVOBaseResult.getData();
        NodeNoticeDTO nextNodeNoticeDTO = new NodeNoticeDTO();
        nextNodeNoticeDTO.setExtVars(nodeExtVars);
        nextNodeNoticeDTO.setAssignees(nextTask.getCurrentTaskActivityDTOS().get(0).getAssignees());
        nextNodeNoticeDTO.setModelCode(nextTask.getModelCode());
        nextNodeNoticeDTO.setBusiKey(nextTask.getBusiKey());
        nextNodeNoticeDTO.setNodeKey(nextTask.getCurrentTaskActivityDTOS().get(0).getNodeKey());
        nextNodeNoticeDTO.setNodeName(nextTask.getCurrentTaskActivityDTOS().get(0).getNodeName());
        nextNodeNoticeDTO.setOptionCode(nextTask.getPreOptionCode());
        nextNodeNoticeDTO.setTaskId(nextTask.getCurrentTaskActivityDTOS().get(0).getTaskId());
        return nextNodeNoticeDTO;
    }

    @Override
    public void automaticWorkflowProcess(NodeNoticeDTO noticeDTO) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY+noticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                applyAuditService.processAutomaticAudit(noticeDTO.getBusiKey());

            }else{
                log.error("【aps-apply-impl】-申领单工作流-自动审核节点审核操作，获取分布式锁失败：{}",JSONUtil.toJsonStr(REDISSON_PRE_KEY));
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单获取分布式锁失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }finally {
            lock.unlock();
        }

    }

    @Override
    public void endWorkflowProcess(NodeNoticeDTO noticeDTO) throws Exception{
        RLock lock = redisson.getLock(REDISSON_PRE_KEY+noticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                String applyOrderId=noticeDTO.getBusiKey();
                applyAuditService.finishAudit(applyOrderId);
            }else{
                log.error("【aps-apply-impl】-申领单工作流-END审核节点审核操作，获取分布式锁失败：{}",JSONUtil.toJsonStr(REDISSON_PRE_KEY));
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单获取分布式锁失败");
            }
        }catch (Exception e){
            e.printStackTrace();
            throw e;
        }finally {
            lock.unlock();
        }

    }



    private void verifyAuditParam(ApplyAuditDTO applyAudit) {
        ApplyUserAuditDTO applyUserAudit = applyAudit.getApplyUserAudit();
        if(StringUtils.equalsIgnoreCase(applyUserAudit.getAuditType(),ApplyConstants.APPLY_AUDIT_REJECT)
                && StringUtils.isBlank(applyUserAudit.getRejectReasonId())){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，参数错误，没有传入拒绝原由,param:{}",JSONUtil.toJsonStr(applyAudit));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"请传入审核拒绝原由");
        }

        List<ApplyPaxInfoAuditDTO> applyPaxInfoS = applyAudit.getApplyPaxInfoS();
        for(ApplyPaxInfoAuditDTO applyPaxInfoAudit:applyPaxInfoS){
            if(StringUtils.equalsIgnoreCase(applyPaxInfoAudit.getAuditType(),ApplyConstants.APPLY_AUDIT_REJECT)
                    && StringUtils.isBlank(applyPaxInfoAudit.getRejectReasonId())){
                log.error("【aps-apply-impl】-申领单工作流-审核异常，参数错误，没有传入拒绝原由,param:{}",JSONUtil.toJsonStr(applyAudit));
                throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"请传入审核拒绝原由");
            }
        }

        ApplyOrderDO applyOrder = applyOrderService.getById(applyAudit.getApplyId());
        if(applyOrder==null){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单不存在,param:{}",JSONUtil.toJsonStr(applyAudit));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyAudit.getApplyId()+"不存在");
        }

        if(!StringUtils.equalsIgnoreCase(applyOrder.getApplyStatus(), ApplyStatusEnum.AUDITING.getDictId())){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单状态异常,不为审核中不能进行审核,param:{}",JSONUtil.toJsonStr(applyAudit));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单"+applyAudit.getApplyId()+"当前状态不为审核中,不能进行审核");
        }

        List<ApplyPaxBaseInfoDTO> applyPaxBaseInfoDTOS = applyOrderService.findPaxBaseInfoByApplyId(applyOrder.getId());
        if(CollectionUtils.isEmpty(applyPaxBaseInfoDTOS)){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单不存在乘客信息,param:{}",JSONUtil.toJsonStr(applyAudit));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单乘客信息不完全");
        }
        List<String> collectOne = applyPaxBaseInfoDTOS.stream().map(ApplyPaxBaseInfoDTO::getPaxId).collect(Collectors.toList());
        List<String> collectTwo = applyAudit.getApplyPaxInfoS().stream().map(ApplyPaxInfoAuditDTO::getPaxInfoId).collect(Collectors.toList());
        if(collectOne.size()!=collectTwo.size()){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单乘客信息传参错误,param:{},pax:{}",JSONUtil.toJsonStr(applyAudit),JSONUtil.toJsonStr(collectOne));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单乘客信息不完全");
        }

        Optional<String> any = collectTwo.stream().filter(t -> !collectOne.contains(t)).findAny();
        if(any.isPresent()){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单乘客信息传参错误,param:{},pax:{}",JSONUtil.toJsonStr(applyAudit),JSONUtil.toJsonStr(collectOne));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"申领单乘客信息不完全");
        }

        WorkflowAuditorVerifyDTO auditorVerifyDTO = WorkflowAuditorVerifyDTO.builder().business(ApplyConstants.APPLY_WORKFLOW_BUSINESS)
                .project(ApsProjectEnum.APPLY_IMPL.getProjectType())
                .businessValue(applyAudit.getApplyId())
                .verifyAuditorId(applyAudit.getAuditorUserId())
                .build();
        boolean result=workflowAuditorIdInfoService.checkAuditorId(auditorVerifyDTO);
        if(!result){
            log.error("【aps-apply-impl】-申领单工作流-审核异常，申领单乘客信息传参错误,param:{},审核权限不够",JSONUtil.toJsonStr(applyAudit));
            throw new BusinessException(ApplyErrors.AUDIT_PARAM_ERROR,"用户"+applyAudit.getAuditorUserId()+"无权限审核该申领单");
        }

        //TODO 判断原因ID是否存在
    }

    private NodeExtVarsDTO createNodeExtVarsDTO() {
        /**设置系统标记*/
        NodeExtVarsDTO nodeExtVars = new NodeExtVarsDTO();
        nodeExtVars.setProject(ApsProjectEnum.APPLY_IMPL.getProjectType());
        nodeExtVars.setBusiness(ApplyConstants.APPLY_WORKFLOW_BUSINESS);
        return nodeExtVars;
    }


    private void handlerDecoderException(DecoderHandlerException handlerException, Object params) {
        log.info("【aps-apply-impl】-申领单工作流-调用workflow接口反馈异常,param:{},result:{}", JSONUtil.toJsonStr(params),handlerException.getCode()+":"+handlerException.getMsg());
        log.error("【aps-apply-impl】-申领单工作流-流调用workflow接口反馈异常,param:{},result:{}",JSONUtil.toJsonStr(params),handlerException.getCode()+":"+handlerException.getMsg());
        if(WorkflowUtils.isExecutedTask(handlerException.getCode())
                || WorkflowUtils.taskNotExist(handlerException.getCode())){
            throw new BusinessException(ApplyErrors.CPC_TASK_ID_ERROR);
        }else {
            throw new BusinessException(ApplyErrors.CPC_AUDIT_ERROR);
        }
    }
}
