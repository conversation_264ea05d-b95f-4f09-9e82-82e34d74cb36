package com.swcares.aps.compensation.impl.baggage.luggage.controller;

import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageManagementService;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockOpService;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindLuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageManagementController
 * @projectName aps
 * @description: 箱包
 * @date 2022/2/8 13:56
 */
@RestController
@RequestMapping("/luggage")
@Api(tags = "箱包信息以及库存管理")
@ApiVersion(value = "箱包管理v1.0")
public class LuggageManagementController extends BaseController {

    @Autowired
    private LuggageManagementService luggageManagementService;

    @Autowired
    private LuggageStockOpService luggageStockOpService;

    @GetMapping("/findSegment")
    @ApiOperation(value = "通过三字码查询航站信息")
    public BaseResult<Object> findSegment(@RequestParam(value = "code") String code){
        return ok(luggageManagementService.findSegment(code));
    }

    @PostMapping("/findLuggage")
    @ApiOperation(value = "箱包信息列表查询")
    public PagedResult<List<LuggageInfoVO>> findLuggageList(@RequestBody LuggageInfoPageDTO dto){
        return ok(luggageManagementService.findLuggageList(dto));
    }

    @PostMapping("/save")
    @ApiOperation(value = "新增箱包信息")
    public BaseResult<Object> saveLuggage(@RequestBody LuggageInfoDTO dto){
        luggageManagementService.saveLuggage(dto);
        return ok();
    }

    @DeleteMapping("/remove")
    @ApiOperation(value = "通过id删除箱包信息")
    public BaseResult<Object> removeLuggage(@RequestParam("id") @ApiParam(value = "箱包id") Long id){
        luggageManagementService.removeLuggage(id);
        return ok();
    }

    @PutMapping("/update")
    @ApiOperation(value = "通过id修改箱包信息")
    public BaseResult<Object> updateLuggage(@RequestBody LuggageInfoDTO dto){
        luggageManagementService.updateLuggage(dto);
        return ok();
    }

    //唐康
    @PostMapping("/stock/detailed")
    @ApiOperation(value = "通过id查询箱包库存明细")
    public PagedResult<List<FindLuggageStockInfoVO>> findLuggageStockDetailed(@RequestBody FindLuggageStockInfoDTO dto){
        return ok(luggageManagementService.findLuggageStockDetailed(dto));
    }

    @PutMapping("/stock/update")
    @ApiOperation(value = "箱包库存数量修改-增加/减少")
    public BaseResult<Object> updateLuggageStock(@RequestBody LuggageStockInfoDTO dto){
        dto.setOperatorId(UserContext.getCurrentUser().getEmployeeId());
        luggageStockOpService.updateLuggageStock(dto);
        return ok();
    }

    @PostMapping("/getLuggageCompensationReportDetail")
    @ApiOperation(value = "箱包补偿报表列表详情")
    public PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReportDetail(@RequestBody LuggageCompensatePageDTO dto){
        return ok(luggageManagementService.getLuggageCompensationReportDetail(dto));
    }

    @PostMapping("/getLuggageCompensationReport")
    @ApiOperation(value = "箱包补偿报表导出")
    public PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReport(@RequestBody LuggageCompensatePageDTO dto){
        return ok(luggageManagementService.getLuggageCompensationReport(dto));
    }

    @GetMapping("/getLuggageManagementList")
    @ApiOperation(value = "获取箱包列表,箱包品牌和箱包尺寸")
    public BaseResult<List<LuggageInfoVO>> getLuggageManagementList(@RequestParam(value = "brand" ,required = false)@ApiParam(value = "箱包品牌")List<String> brand){
        List<LuggageInfoVO> luggageManagementList = luggageManagementService.getLuggageManagementList(brand);
        return ok(luggageManagementList);
    }

}

