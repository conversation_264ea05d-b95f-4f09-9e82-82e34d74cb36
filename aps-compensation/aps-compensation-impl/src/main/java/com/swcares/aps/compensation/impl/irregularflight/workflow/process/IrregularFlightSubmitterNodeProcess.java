package com.swcares.aps.compensation.impl.irregularflight.workflow.process;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.AuditStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.IrregularflightWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationSyntheticalSaveDTO;
import com.swcares.aps.component.workflow.NodeNoticeProcess;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.process <br>
 * Description：Submitter通知节点，传入业务参数至工作流引擎，真正开始整个流程的扭转 <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 02月14日 14:52 <br>
 * @version v1.0 <br>
 */
@Component
@Slf4j
public class IrregularFlightSubmitterNodeProcess implements NodeNoticeProcess {

    @Autowired
    private IrregularFlightWorkflowService irregularFlightWorkflowService;




    @Override
    public NodeNoticeProcessResult process(NodeNoticeDTO noticeDTO) {
        NodeNoticeProcessResult nodeNoticeProcessResult = new NodeNoticeProcessResult();
        try {
            log.info("执行审核提交节点，判断节点是是否经过驳回后重新提交,busiKey:[{}],noticeDTO:[{}]",noticeDTO.getBusiKey(), JSON.toJSONString(noticeDTO));
            if(AuditStatusEnum.REJECT.getKey().equals(noticeDTO.getOptionCode())
                    && !(noticeDTO.getExtVars().getExtVars() instanceof CompensationSyntheticalSaveDTO)){
                //审批驳回后-修改赔偿单状态
                log.info("驳回到发起人，并修改补偿单状态,busiKey:[{}],noticeDTO:[{}]",noticeDTO.getBusiKey(), JSON.toJSONString(noticeDTO));
                irregularFlightWorkflowService.updCompensateStatusByAuditStatus(noticeDTO);
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("orderId", noticeDTO.getBusiKey());
                resultMap.put("isCancel", false);
                resultMap.put("taskId", noticeDTO.getTaskId());
                resultMap.put("orderAuditorList", new ArrayList<>());
                resultMap.put("isPrompt", false);
                nodeNoticeProcessResult.setData(resultMap);
            }else{
                //提交节点执行
                nodeNoticeProcessResult.setData(irregularFlightWorkflowService.submitterWorkflow(noticeDTO));
            }
        }catch (Exception e){
            e.printStackTrace();
            log.error("【aps-compensation-impl】提交申领单流程出错，noticeDTO:{"+JSONUtil.toJsonStr(noticeDTO)+"}",e);
            throw new BusinessException(CompensationException.AUDIT_ERROR,"Submitter流程异常");

        }
        return nodeNoticeProcessResult;
    }

    @Override
    public boolean canProcess(NodeNoticeDTO noticeDTO) {
        boolean result=false;
        JSONObject jsonObject = JSONUtil.parseObj(noticeDTO.getExtVars());
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.IRREGULARFLIGHT_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.BAGGAGE_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.BAGGAGE_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.COMPLAINT_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }
        if(StringUtils.equalsIgnoreCase(extVarsDTO.getProject(), ApsProjectEnum.COMPENSATION_IMPL.getProjectType())
                && StringUtils.equalsIgnoreCase(extVarsDTO.getBusiness(), CompensationConstant.OVERBOOK_WORKFLOW_BUSINESS)
                && StringUtils.equalsIgnoreCase(noticeDTO.getNodeBusinessType(), IrregularflightWorkflowNodeBusiTypeEnum.SUBMITTER.getType())){
            result=true;
        }
        return result;
    }
}
