package com.swcares.aps.compensation.impl.baggage.accident.service.handler;

import cn.hutool.core.date.LocalDateTimeUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;

/**
 * ClassName： ChangeDoToProcessStatusHandlerServiceImpl
 * Description： 待处理状态更改为处理中服务类
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/23 14:33
 * @version v1.0
 */
@Service
@Slf4j
public class ChangeToDoToProcessStatusHandlerServiceImpl implements BaggageAccidentStatusHandlerService {

    private BaggageAccidentMapper baggageAccidentMapper;

    @Autowired
    public ChangeToDoToProcessStatusHandlerServiceImpl(BaggageAccidentMapper baggageAccidentMapper){
        this.baggageAccidentMapper=baggageAccidentMapper;
    }

    @Override
    public boolean support(String sourceStatus, String targetStatus) {
        return AccidentStatusEnum.TODO.getValue().equals(sourceStatus)
                && AccidentStatusEnum.PROCESS.getValue().equals(targetStatus);
    }

    @Override
    public void check(BaggageAccidentInfoDTO accidentInfo, String targetStatus) {

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String t1 = accidentInfo.getTodoTime().format(formatter);
        SimpleDateFormat sdf =  new SimpleDateFormat( "yyyy-MM-dd" );
        Date date= null;
        try {
            date = sdf.parse(t1);
        } catch (ParseException e) {
            log.error("【事故单修改状态 待处理->处理中 失败】时间转换异常,异常【{}】",e.getMessage());
            e.printStackTrace();
        }
        LocalDateTime todoTime = LocalDateTimeUtil.of(date);
        LocalDateTime now = LocalDateTime.now();
        Duration duration = LocalDateTimeUtil.between(todoTime,now);

        LambdaUpdateWrapper<BaggageAccidentInfoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BaggageAccidentInfoDO::getAccidentNo,accidentInfo.getAccidentNo())
                .set(BaggageAccidentInfoDO::getLostTime,String.valueOf(duration.toDays()+1));
        baggageAccidentMapper.update(null,lambdaUpdateWrapper);
    }
}
