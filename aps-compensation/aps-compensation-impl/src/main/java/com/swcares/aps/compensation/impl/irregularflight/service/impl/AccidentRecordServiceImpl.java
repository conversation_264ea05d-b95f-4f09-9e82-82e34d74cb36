package com.swcares.aps.compensation.impl.irregularflight.service.impl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.irregularflight.mapper.AccidentRecordMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.AccidentRecordService;
import com.swcares.aps.compensation.model.irregularflight.entity.AccidentRecordDO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.service.impl.AccidentRecordServiceImpl <br>
 * Description：事故单将操作记录表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class AccidentRecordServiceImpl extends ServiceImpl<AccidentRecordMapper, AccidentRecordDO> implements AccidentRecordService {

}
