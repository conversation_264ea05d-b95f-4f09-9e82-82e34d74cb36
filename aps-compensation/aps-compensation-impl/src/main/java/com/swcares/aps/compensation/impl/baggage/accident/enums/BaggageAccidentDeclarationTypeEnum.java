package com.swcares.aps.compensation.impl.baggage.accident.enums;

/**
 * @ClassName：BaggageAccidentDeclarationTypeEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/22 18:56
 * @version： v1.0
 */
public enum BaggageAccidentDeclarationTypeEnum {
    ALL_DECLARATION("1", "全部申报"),
    PARTIAL_DECLARATION("2", "部分申报"),
    NO_DECLARATION("3", "未申报");

    private BaggageAccidentDeclarationTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaggageAccidentDeclarationTypeEnum build(String key) {
        return build(key, true);
    }

    public static BaggageAccidentDeclarationTypeEnum build(String key, boolean throwEx) {
        BaggageAccidentDeclarationTypeEnum typeEnum = null;
        for (BaggageAccidentDeclarationTypeEnum element : BaggageAccidentDeclarationTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + BaggageAccidentDeclarationTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
