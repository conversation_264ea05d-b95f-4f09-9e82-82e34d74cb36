package com.swcares.aps.compensation.impl.complaint.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.complaint.dto.ComplaintAccidentQueryDto;
import com.swcares.aps.compensation.model.complaint.dto.PassengerComplaintDto;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintAccidentQueryVo;
import com.swcares.aps.compensation.model.complaint.vo.FrequencyVo;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public interface ComplaintAccidentInfoMapper extends BaseMapper<ComplaintAccidentInfoEntity> {

     List<FrequencyVo> frequency(@Param("dto") List<PassengerComplaintDto> passengerComplaintInfoList,@Param("id") Long id);

    IPage<ComplaintAccidentQueryVo> pageQuery(@Param("dto") ComplaintAccidentQueryDto complaintAccidentQueryDto, Page<ComplaintAccidentQueryVo> page);

    @InterceptorIgnore(tenantLine = "true")
    boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus);
}
