package com.swcares.aps.compensation.impl.irregularflight.controller;

import com.swcares.aps.basic.data.businessimpl.model.dto.CompensationPaxSearchDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.*;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationImplFltPaxDataService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;


@RestController
@RequestMapping("/compensation/fltPaxData")
@Api(tags = "赔付-航班旅客接口")
public class CompensationFltPaxDataController extends BaseController {
    @Autowired
    private CompensationBasicDataService compensationBasicDataService;
    @Autowired
    private CompensationImplFltPaxDataService compensationImplFltPaxDataService;

    @PostMapping("/findPassengers")
    BaseResult<List<PassengerBasicInfoVO>> getPassengers(@RequestBody PassengerQueryDTO dto){
        return ok(compensationBasicDataService.getPassengerInfo(dto));
    }

    @PostMapping("/getFlightList")
    BaseResult<List<FlightBasicnfoVO>> getFlightBasicInfo(@RequestBody FlightBaseQueryDTO dto){
        return ok(compensationBasicDataService.getFlightBasicInfo(dto));
    }


    @GetMapping("/terminal/info/list")
    public BaseResult<Object> getTerminal(){
        return ok(compensationImplFltPaxDataService.getTerminal());
    }


    @GetMapping("/findFlight")
    public BaseResult<FlightFindVO> getFlight(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment){
        return ok(compensationBasicDataService.getFlight(date, flightNo, choiceSegment));
    }

    @GetMapping("/findSegment")
    BaseResult<List<SegmentFindVO>> getSegment(@RequestParam(value = "date", required = true) String date, @RequestParam(value = "flightNo", required = true) String flightNo){
        return ok(compensationBasicDataService.findFltSegment(date, flightNo));
    }


    @GetMapping("/findAlternateAndStop")
    BaseResult<String> getAlternateAndStop(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo){
        return ok(compensationImplFltPaxDataService.getAlternateAndStop(date, flightNo));
    }

    @GetMapping("/getFltAirStation")
    public BaseResult<Set<String>> getFltAirStation(@RequestParam(value = "date", required = true)String date, @RequestParam(value = "flightNo", required = true)  String flightNo, @RequestParam(value = "choiceSegment", required = true)  String choiceSegment){
        return ok(compensationImplFltPaxDataService.getFltAirStation(date, flightNo, choiceSegment));
    }
}
