package com.swcares.aps.compensation.impl.compensation.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.compensation.dto.CompensationLuggageReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderQueriesService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationRepresentationService;
import com.swcares.aps.compensation.model.compensation.vo.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：CompensationOrderSearchController
 * @Description：补偿单查询接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 10:41
 * @version： v1.0
 */
@RestController
@RequestMapping("/compensation/query")
@Api(tags = "补偿单查询类接口")
@ApiVersion(value = "补偿单查询类接口 v1.0.1")
@Slf4j
public class CompensationOrderQueriesController extends BaseController {

    private CompensationOrderQueriesService compensationOrderQueriesService;

    private CompensationRepresentationService compensationRepresentationService;

    @Autowired
    public CompensationOrderQueriesController(CompensationOrderQueriesService compensationOrderQueriesService,
                                              CompensationRepresentationService compensationRepresentationService){
        this.compensationOrderQueriesService=compensationOrderQueriesService;
        this.compensationRepresentationService=compensationRepresentationService;
    }

    /**
     * @title compensationBaseInfoPage
     * @description 条件分页查询赔偿单最基本信息记录
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param request
     * @return
     */
    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询赔偿单最基本信息记录")
    public PagedResult<List<CompensationBaseInfoVO>> compensationBaseInfoPage(@RequestBody CompensationBaseInfoRequestDTO request) {
        IPage<CompensationBaseInfoVO> result = compensationOrderQueriesService.compensationBaseInfoPage(CompensationBaseInfoPageDTO.of(request));
        return ok(result);
    }

    /**
     * @title compensationDetailSearch
     * @description 条件查询补偿单详情信息
     * <AUTHOR>
     * @date 2022/3/16 9:28
     * @param orderId
     * @return
     */
    @GetMapping("/detail/{orderId}")
    @ApiOperation(value = "条件查询补偿单详情信息")
    public BaseResult<CompensationDetailExtInfoVO> compensationDetailSearch(@PathVariable String  orderId){
        AccidentCompensationDTO accidentCompensationDTO = compensationOrderQueriesService.compensationDetailSearch(orderId);
        CompensationDetailExtInfoVO compensationDetailExtInfoVO = compensationRepresentationService.ofCashCompensationDetailExtInfoVO(accidentCompensationDTO);
        compensationRepresentationService.checkIsSponsor(compensationDetailExtInfoVO);
        if(compensationDetailExtInfoVO!=null){
            Map<String, String> loginUserCanAuditOrderIdToTaskIdMp = compensationOrderQueriesService
                    .getLoginUserCanAuditOrderIdToTaskIdMp(Arrays.asList(orderId));
            if(loginUserCanAuditOrderIdToTaskIdMp.containsKey(orderId)){
                compensationDetailExtInfoVO.setToExamine("Y");
                compensationDetailExtInfoVO.setTaskId(loginUserCanAuditOrderIdToTaskIdMp.get(orderId));
            }else{
                compensationDetailExtInfoVO.setToExamine("N");
            }
        }
        return ok(compensationDetailExtInfoVO);
    }

    /**
     * @title compensationAuditInfoSearch
     * @description 条件查询补偿单审核人信息
     * <AUTHOR>
     * @date 2022/3/16 9:29
     * @param request
     * @return
     */
    @PostMapping("/audit/info")
    @ApiOperation(value = "条件查询补偿单审核人信息")
    public BaseResult<List<CompensationAuditInfoVO>> compensationAuditInfoSearch(@RequestBody CompensationAuditInfoRequestDTO request){
        List<CompensationAuditInfoVO> result=compensationOrderQueriesService.compensationAuditInfoSearch(CompensationAuditInfoDTO.of(request));
        return ok(result);
    }

    @PostMapping("/findCashCompensationList")
    @ApiOperation(value = "查询现金补偿单列表")
    public PagedResult<List<Object>> findCashCompensationList(@RequestBody CompensationBaseInfoRequestDTO request){
        IPage<Object> result = compensationOrderQueriesService.findCashCompensationList(CompensationBaseInfoPageDTO.of(request));
        return ok(result);
    }

    /**
     * @title getCashBusinessCostsDetail
     * @description 查询现金业务成本明细详情
     * <AUTHOR>
     * @date 2022/7/25 12:37
     * @param dto
     * @return PagedResult<List<CompensationCashReportDTO>>
     */
    @PostMapping("/getCashBusinessCostsDetail")
    @ApiOperation(value = "查询现金业务明细详情")
    public PagedResult<List<CashBusinessCostsDetailVO>> getCashBusinessCostsDetail(@RequestBody CompensationCashReportDTO dto){
        return ok(compensationOrderQueriesService.getCashBusinessCostsDetail(dto));
    }

    /**
     * @title getCashBusinessCostsDetailReport
     * @description 查询现金业务明细详情（用于报表导出）
     * <AUTHOR>
     * @date 2022/10/17 11:10
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO>>
     */
    @PostMapping("/getCashBusinessCostsDetailReport")
    @ApiOperation(value = "查询现金业务明细详情（用于报表导出）")
    public PagedResult<List<CashBusinessCostsDetailVO>> getCashBusinessCostsDetailReport(@RequestBody CompensationCashReportDTO dto){
        return ok(compensationOrderQueriesService.getCashBusinessCostsDetailReport(dto));
    }



    /**
     * @title getCashPayDetail
     * @description 查询现金支付明细报表
     * <AUTHOR>
     * @date 2022/7/25 20:29
     * @param dto
     * @return PagedResult<List<CashBusinessCostsDetailVO>>
     */
    @PostMapping("/getCashPayDetail")
    @ApiOperation(value = "查询现金支付明细报表")
    public PagedResult<List<CashPayDetailVO>> getCashPayDetail(@RequestBody CompensationCashReportDTO dto){
        return ok(compensationOrderQueriesService.getCashPayDetail(dto));
    }


    /**
     * @title getCashPayDetailReport
     * @description 查询现金支付明细报表(用于报表导出)
     * <AUTHOR>
     * @date 2022/10/17 11:11
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO>>
     */
    @PostMapping("/getCashPayDetailReport")
    @ApiOperation(value = "查询现金支付明细报表(用于报表导出)")
    public PagedResult<List<CashPayDetailVO>> getCashPayDetailReport(@RequestBody CompensationCashReportDTO dto){
        return ok(compensationOrderQueriesService.getCashPayDetailReport(dto));
    }





    @PostMapping("/luggageBusinessReview")
    @ApiOperation(value = "复核操作")
    public BaseResult<Object> luggageBusinessReview(@RequestBody LuggageBusinessReviewDTO dto) {
        //复核操作可针对；领取状态为已领取且支付状态为支付成功且补偿单类型为异常行李且当前操作用户有此功能权限的，可以进行复核操作
        return ok(compensationOrderQueriesService.luggageBusinessReview(dto));
    }



    /**
     * @title getLuggageBusinessCostsDetail
     * @description 查询异常行李业务成本明细详情
     * @param dto
     * @return PagedResult<List<CompensationCashReportDTO>>
     */
    @PostMapping("/getLuggageBusinessCostsDetail")
    @ApiOperation(value = "查询异常行李业务明细详情")
    public PagedResult<List<LuggageBusinessCostsDetailVO>> getLuggageBusinessCostsDetail(@RequestBody CompensationLuggageReportDTO dto){
        return ok(compensationOrderQueriesService.getLuggageBusinessCostsDetail(dto));
    }

    /**
     * @title getLuggageBusinessCostsDetailExport
     * @description 查询现金业务明细详情（用于报表导出）
     * @param dto
     * @return com.swcares.baseframe.common.base.PagedResult<java.util.List<com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO>>
     */
    @PostMapping("/getLuggageBusinessCostsDetailExport")
    @ApiOperation(value = "查询异常行李业务明细详情（用于报表导出）")
    public PagedResult<List<LuggageBusinessCostsDetailVO>> getLuggageBusinessCostsDetailExport(@RequestBody CompensationLuggageReportDTO dto){
        return ok(compensationOrderQueriesService.getLuggageBusinessCostsDetail(dto));
    }

}
