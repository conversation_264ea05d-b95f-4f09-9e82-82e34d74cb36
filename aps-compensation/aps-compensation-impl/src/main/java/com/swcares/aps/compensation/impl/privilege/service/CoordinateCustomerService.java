package com.swcares.aps.compensation.impl.privilege.service;

import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.BasicDataDispatchDTO;

/**
 * <AUTHOR> <PERSON>
 * @Classname CoordinateCustomerService
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/27 14:40
 * @Version 1.0
 */
public interface CoordinateCustomerService {
    public void saveOrUpdate(CustomerDTO dispatchDTO);
}
