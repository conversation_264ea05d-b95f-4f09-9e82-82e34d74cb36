package com.swcares.aps.compensation.impl.compensation.service.strategy;

import cn.hutool.core.date.DateUtil;
import com.beust.jcommander.internal.Lists;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageInputSourceTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageManagementService;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.compensation.service.CreateCompensationOrderDOService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.AbstractMaterialDO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName：BaggageMaterialCompensationServiceImpl
 * @Description：异常行李实物补偿单创建编辑服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 17:01
 * @version： v1.0
 */
@Service("baggageMaterialCompensationService")
public class BaggageMaterialCompensationServiceImpl implements CompensationAddAndEditService<CompensationMaterialAddCommandDTO, CompensationMaterialEditCommandDTO> {

    private final static String MATERIAL_TYPE_LUGGAGE ="11"; //实物类型 1,箱包

    //实物验证方法
    private Map<String, Function<String, AbstractMaterialDO>> materialVerifyMp =new HashMap<>();

    private CompensationBasicDataService compensationBasicDataService;

    private AccidentCompensationDomainService accidentCompensationDomainService;

    private BaggageAccidentService baggageAccidentService;

    private LuggageManagementService luggageManagementService;

    private CreateCompensationOrderDOService createCompensationOrderDOService;

    @Autowired
    public BaggageMaterialCompensationServiceImpl(CompensationBasicDataService compensationBasicDataService,
                                              BaggageAccidentService baggageAccidentService,
                                              AccidentCompensationDomainService accidentCompensationDomainService,
                                              LuggageManagementService luggageManagementService,
                                              CreateCompensationOrderDOService createCompensationOrderDOService){
        this.compensationBasicDataService=compensationBasicDataService;
        this.baggageAccidentService=baggageAccidentService;
        this.accidentCompensationDomainService=accidentCompensationDomainService;
        this.luggageManagementService=luggageManagementService;
        this.createCompensationOrderDOService=createCompensationOrderDOService;
    }

    @PostConstruct
    private void init(){
        materialVerifyMp.put(MATERIAL_TYPE_LUGGAGE,luggageManagementService::findById);

    }
    /**
     * @title add
     * @description 添加异常行李实物补偿单
     * <AUTHOR>
     * @date 2022/3/14 11:08
     * @param command
     * @return 补偿单ID
     */
    @Override
    public String add(CompensationMaterialAddCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);
        String compensationId = accidentCompensationDomainService.save(compensation);
        return compensationId;
    }

    /**
     * @title edit
     * @description 添加异常行李实物补偿单
     * <AUTHOR>
     * @date 2022/3/14 11:09
     * @param command
     * @return 补偿单ID
     */
    @Override
    public String edit(CompensationMaterialEditCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);
        accidentCompensationDomainService.edit(compensation,command.getCompensationId());
        return command.getCompensationId();
    }

    @Override
    public CompensationAuditOperationVO reSubmit(CompensationMaterialEditCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);
        CompensationAuditOperationVO result = accidentCompensationDomainService.reSubmit(compensation, command.getCompensationId());
        return result;
    }

    /**
     * @title getAccidentCompensationDTO
     * @description 创建事故补偿单领域模型
     * <AUTHOR>
     * @date 2022/3/14 11:09
     * @param command
     * @return
     */
    @NotNull
    private AccidentCompensationDTO getAccidentCompensationDTO(CompensationMaterialAddCommandDTO command) {

        BaggageAccidentInfoDO accident = baggageAccidentService.getById(command.getAccident().getAccidentId());

        if(accident==null){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,command.getAccident().getAccidentId()+"事故单不存在");
        }
        List<CompensationMaterialInfoDO> compensationMaterialInfos =verifyAndGetCompensationMaterial(command.getMaterials(),accident);

        CompensationPaxInfoDO compensationPaxInfoDO = getCompensationPaxInfoDOByInputSource(command,accident);

        CompensationOrderInfoDO compensationOrderInfoDO = getCompensationOrderInfoDOByInputSource(command,accident);

        return AccidentCompensationDTO.of(compensationOrderInfoDO, Lists.newArrayList(compensationPaxInfoDO), null, compensationMaterialInfos,null);
    }

    @NotNull
    private CompensationPaxInfoDO getCompensationPaxInfoDOByInputSource(CompensationMaterialAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        if(BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(accident.getInputSource())){
            return getManualInputCompensationPaxInfoDO(command,accident);
        } //旅客数据输入的来源=SYSTEM（系统查询）
        else if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(accident.getInputSource())){
            return getCompensationPaxInfoDO(command,accident);
        }else {
            return null;
        }
    }

    /**
     * @title getCompensationPaxInfoDO
     * @description 根据航班旅客信息创建补偿单旅客实体信息
     * <AUTHOR>
     * @date 2022/3/14 9:38
     * @param accident
     * @return 补偿单旅客实体信息
     */
    @NotNull
    private CompensationPaxInfoDO getCompensationPaxInfoDO(CompensationMaterialAddCommandDTO command,BaggageAccidentInfoDO accident) {
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setPaxId(accident.getPaxId());
        List<PassengerBasicInfoVO> passengerResult = compensationBasicDataService.getPassengerInfo(queryDTO);
        if(CollectionUtils.isEmpty(passengerResult)){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accident.getAccidentNo()+"事故单的旅客不存在");
        }
        PassengerBasicInfoVO passenger = passengerResult.get(0);

        CompensationPaxInfoDO compensationPaxInfoDO = ObjectUtils.copyBean(passenger, CompensationPaxInfoDO.class);
        compensationPaxInfoDO.setReceiveStatus(ReceiveStatusEnum.UNCLAIMED.getKey());
        compensationPaxInfoDO.setPaxStatus(passenger.getCheckStatus());
        compensationPaxInfoDO.setPkgNo(passenger.getBagTag());
        compensationPaxInfoDO.setBabyPaxName(passenger.getBabyName());
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
        compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passenger.getTktDate(), dateTimeFormatter));
        compensationPaxInfoDO.setCancelTime(DateUtil.parse(passenger.getCancelTime()));
        compensationPaxInfoDO.setCurrentAmount(command.getCompensationAllMoney());
        return compensationPaxInfoDO;
    }

    @NotNull
    private CompensationPaxInfoDO getManualInputCompensationPaxInfoDO(CompensationMaterialAddCommandDTO command, BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        //获取不到，界面没录入【值机状态、 携带婴儿姓名、购票时间、取消时间】
        CompensationPaxInfoDO compensationPaxInfoDO = new CompensationPaxInfoDO();

        compensationPaxInfoDO.setPaxId(accident.getPaxId());
        compensationPaxInfoDO.setIdNo(accident.getIdNo());
        String segment = extractAirportCodes(accident.getPaxSegment());
        compensationPaxInfoDO.setSegment(segment);
        compensationPaxInfoDO.setSegmentCh(accident.getPaxSegment());
        String[] split = segment.split("-");
        compensationPaxInfoDO.setOrgCityAirp(split[0]);
        compensationPaxInfoDO.setDstCityAirp(split[1]);
        compensationPaxInfoDO.setIdType(accident.getIdType());
        compensationPaxInfoDO.setPaxName(accident.getPaxName());
        compensationPaxInfoDO.setTktNo(accident.getTktNo());

        compensationPaxInfoDO.setReceiveStatus(ReceiveStatusEnum.UNCLAIMED.getKey());
        compensationPaxInfoDO.setPkgNo(accident.getBaggageNo());
        compensationPaxInfoDO.setCurrentAmount(command.getCompensationAllMoney());
//        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
//        compensationPaxInfoDO.setPaxStatus(accident.getCheckStatus());
//        compensationPaxInfoDO.setBabyPaxName(passenger.getBabyName());
//        compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passenger.getTktDate(), dateTimeFormatter));
//        compensationPaxInfoDO.setCancelTime(DateUtil.parse(passenger.getCancelTime()));
        return compensationPaxInfoDO;
    }

    private CompensationOrderInfoDO getCompensationOrderInfoDOByInputSource(CompensationMaterialAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        if(BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(accident.getInputSource())){
            return getManualInputCompensationOrderInfoDO(command,accident);
        } //旅客数据输入的来源=SYSTEM（系统查询）
        else if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(accident.getInputSource())){
            return getCompensationOrderInfoDO(command,accident);
        }else {
            return null;
        }
    }
    /**
     * @title getCompensationOrderInfoDO
     * @description 创建补偿单信息实体
     * <AUTHOR>
     * @date 2022/3/14 11:11
     * @param command
     * @return
     */
    @NotNull
    private CompensationOrderInfoDO getCompensationOrderInfoDO(CompensationMaterialAddCommandDTO command,BaggageAccidentInfoDO accident ) {

        FocFlightInfoDTO focFlightInfoDTO=new FocFlightInfoDTO();
        focFlightInfoDTO.setFlightNo(accident.getPaxFlightNo());
        focFlightInfoDTO.setFlightDate(accident.getPaxFlightDate());
        List<FocFlightInfoVO> flightInfoVOS = compensationBasicDataService.getFocFlightInfo(focFlightInfoDTO);
        if(CollectionUtils.isEmpty(flightInfoVOS)){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accident.getPaxFlightNo()+"_"+accident.getPaxFlightDate()+"航班数据不存在");
        }

//        String collect = flightInfoVOS.stream().map(FocFlightInfoVO::getSegment).collect(Collectors.joining(","));
        command.setServiceCity(command.getServiceCity().substring(command.getServiceCity().length() - 3));
//        if(!collect.contains(command.getServiceCity())){
//            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"服务航站错误");
//        }

        CompensationOrderInfoDO compensationOrderInfoDO = createCompensationOrderDOService.createMaterialInByBaggageAccident(command, accident, flightInfoVOS);
        compensationOrderInfoDO.setSumMoney(command.getCompensationAllMoney());
        // 使用Stream API提取所有对象的id，转换成List<String>
        if(CollectionUtils.isNotEmpty(command.getMaterials())){
            StringBuilder ids = new StringBuilder();
            for(AddMaterialCompensationDTO dto:command.getMaterials()){
                for (int i = 0; i < dto.getAmount().intValue(); i++) {
                    ids.append(dto.getMaterialId()).append(",");
                }
            }
            // 检查并移除最后一个字符如果是逗号的话
            if (ids.length() > 0 && ids.charAt(ids.length() - 1) == ',') {
                ids.deleteCharAt(ids.length() - 1);
            }

            compensationOrderInfoDO.setCommodityId(ids.toString());
        }

        return compensationOrderInfoDO;
    }

    @NotNull
    private CompensationOrderInfoDO getManualInputCompensationOrderInfoDO(CompensationMaterialAddCommandDTO command,BaggageAccidentInfoDO accident ) {

        command.setServiceCity(command.getServiceCity().substring(command.getServiceCity().length() - 3));
        CompensationOrderInfoDO compensationOrderInfoDO = createCompensationOrderDOService.createMaterialInByBaggageAccidentManualInput(command, accident);
        compensationOrderInfoDO.setSumMoney(command.getCompensationAllMoney());
        // 使用Stream API提取所有对象的id，转换成List<String>
        if(CollectionUtils.isNotEmpty(command.getMaterials())){
            StringBuilder ids = new StringBuilder();
            for(AddMaterialCompensationDTO dto:command.getMaterials()){
                for (int i = 0; i < dto.getAmount().intValue(); i++) {
                    ids.append(dto.getMaterialId()).append(",");
                }
            }
            // 检查并移除最后一个字符如果是逗号的话
            if (ids.length() > 0 && ids.charAt(ids.length() - 1) == ',') {
                ids.deleteCharAt(ids.length() - 1);
            }

            compensationOrderInfoDO.setCommodityId(ids.toString());
        }

        return compensationOrderInfoDO;
    }

    /**
     * @title verifyAndGetCompensationMaterial
     * @description 验证并获取补偿单实物列表
     * <AUTHOR>
     * @date 2022/3/22 12:23
     * @param materials
     * @return
     */
    private List<CompensationMaterialInfoDO>  verifyAndGetCompensationMaterial(List<AddMaterialCompensationDTO> materials,
                                                                               BaggageAccidentInfoDO accident) {

        List<CompensationMaterialInfoDO> compensationMaterialInfos=new ArrayList<>(materials.size());
        for (AddMaterialCompensationDTO addMaterial:materials) {
            if(addMaterial.getAmount() == null || addMaterial.getAmount() < 1) {
                throw new BusinessException(CommonErrors.PARAM_VALUE_INVALID_ERROR);
            }
            String materialType = addMaterial.getMaterialType();
            CompensationMaterialInfoDO info = new CompensationMaterialInfoDO();
            if("12".equals(materialType)){
                info.setMaterialType(addMaterial.getMaterialType());
            }
            else{
                String materialId = addMaterial.getMaterialId();
                if(!materialVerifyMp.containsKey(materialType)){
                    throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"实物类型传参错误");
                }
                AbstractMaterialDO materialDO = materialVerifyMp.get(materialType).apply(materialId);
                if(materialDO==null){
                    throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,addMaterial.getMaterialBrand()+"实物不存在");
                }
                if(materialDO.getMaterialStock().longValue()<addMaterial.getAmount().longValue()){
                    throw new BusinessException(CompensationException.LUGGAGE_STOCK_NOT_ENOUGH);
                }
                info.setMaterialId(materialDO.getId());
                info.setMaterialNo(materialDO.getMaterialNo());
                info.setMaterialType(materialDO.getMaterialType());

            }
            info.setAmount(addMaterial.getAmount());
            info.setMaterialBrand(addMaterial.getMaterialBrand());
            info.setMaterialName(addMaterial.getMaterialName());
            info.setMaterialSize(addMaterial.getMaterialSize());
            info.setMaterialUnivalent(addMaterial.getMaterialUnivalent());
            info.setPaxId(accident.getPaxId());
            compensationMaterialInfos.add(info);
        }
        return compensationMaterialInfos;
    }

    private static String extractAirportCodes(String input) {

        //拉萨贡嘎LXA-成都双流CTU 获取LXA-CTU
        String[] split = input.split("-");

        return split[0].substring(split[0].length() - 3) + "-" + split[1].substring(split[1].length() - 3);
    }

}
