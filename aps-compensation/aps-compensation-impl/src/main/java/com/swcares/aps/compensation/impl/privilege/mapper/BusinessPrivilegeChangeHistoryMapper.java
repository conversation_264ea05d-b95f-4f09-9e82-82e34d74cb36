package com.swcares.aps.compensation.impl.privilege.mapper;

import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeChangeHistory;
import com.swcares.baseframe.common.mybatis.base.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeChangeHistoryMapper
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 15:12
 * @Version 1.0
 */
@Mapper
public interface BusinessPrivilegeChangeHistoryMapper extends BaseMapper<AirlineBusinessPrivilegeChangeHistory> {
}
