package com.swcares.aps.compensation.impl.overbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.overbook.constant.OverBookException;
import com.swcares.aps.compensation.impl.overbook.mapper.OverBookConfigMapper;
import com.swcares.aps.compensation.impl.overbook.service.OverBookConfigService;
import com.swcares.aps.compensation.model.overbook.entity.OverBookConfigDO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookConfigVO;
import com.swcares.aps.component.pay.pay.util.chinapay.StringUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName：OverBookConfigServiceImpl
 * @Description：超售配置
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 11:28
 * @version： v1.0
 */
@Slf4j
@Service
public class OverBookConfigServiceImpl extends ServiceImpl<OverBookConfigMapper, OverBookConfigDO> implements OverBookConfigService {

    @Override
    public List<OverBookConfigVO> getConfigInfo() {
        List<OverBookConfigVO> result = new ArrayList<>();
        List<OverBookConfigDO> list = this.list();
        if(ObjectUtils.isNotEmpty(list)){
            list.forEach(d->{
                OverBookConfigVO vo = new OverBookConfigVO();
                BeanUtil.copyProperties(d,vo);
                result.add(vo);
            });
        }
        return result;
    }

    @Override
    public List<OverBookConfigVO> getConfigInfo(String time,String type) {
        double delayHours =StringUtils.isNotEmpty(time)? Double.parseDouble(time):0;
        List<OverBookConfigVO> result = new ArrayList<>();
        LambdaQueryChainWrapper<OverBookConfigDO> wrapper = this.lambdaQuery();
        if(StringUtils.isNotEmpty(type)){
            wrapper.eq(OverBookConfigDO::getType,type);
        }
        List<OverBookConfigDO> list = wrapper.list();

        if(ObjectUtils.isNotEmpty(list)){
            list.forEach(d->{
                OverBookConfigVO vo = new OverBookConfigVO();
                BeanUtil.copyProperties(d,vo);
                if(StringUtils.isNotEmpty(time) && "1".equals(vo.getType())){
                    if(delayHours >= Double.parseDouble(vo.getLeftConditionValue()) && delayHours <  Double.parseDouble(vo.getRightConditionValue())){
                        result.add(vo);
                    }
                }else {
                    result.add(vo);
                }
            });
        }
        return result;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void saveConfigInfo(List<OverBookConfigDO> doList) {
        boolean remove = this.remove(Wrappers.emptyWrapper());
        this.saveBatch(doList);

    }
}
