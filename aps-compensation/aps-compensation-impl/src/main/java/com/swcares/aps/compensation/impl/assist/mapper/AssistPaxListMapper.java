package com.swcares.aps.compensation.impl.assist.mapper;

import com.swcares.aps.compensation.model.assist.dto.FindPaxParamDTO;
import com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO;
import com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistPaxListMapper
 * @projectName aps
 * @description:
 * @date 2022/1/21 14:53
 */
@Mapper
public interface AssistPaxListMapper {

    /**
    * @title getPaxList
    * @description 查询旅客列表参数
    * <AUTHOR>
    * @date 2022/1/21 15:30
    * @param dto
    * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO>
    */
    List<AssistPaxListVO> getPaxList(@Param("dto") FindPaxParamDTO dto);

    /**
    * @title findPaxAmount
    * @description 通过手机号查询半年内协助领取的旅客
    * <AUTHOR>
    * @date 2022/2/15 13:29
    * @param phone
    * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO>
    */
//    List<ReceivedPaxVO> findPaxAmount(@Param("phone")String phone);

    /**
    * @title getPaxName
    * @description 查询该航班下是否存在某个用户
    * <AUTHOR>
    * @date 2022/2/21 21:59
    * @param flightDate
    * @param flightNo
    * @param idNo
    * @return java.lang.String
    */
    @Select("select DISTINCT t.pax_name from compensation_pax_info t " +
            " LEFT JOIN compensation_order_info t2 on t.order_id = t2.id " +
            " LEFT JOIN compensation_flight_info t3 on t2.id = t3.order_id " +
            " where t.id_no = #{idNo} " +
            " and t3.flight_date = #{flightDate} " +
            " and t3.flight_no = #{flightNo}")
    String getPaxName(@Param("flightDate")String flightDate,@Param("flightNo")String flightNo,@Param("idNo")String idNo);
}
