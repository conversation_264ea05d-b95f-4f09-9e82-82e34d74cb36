package com.swcares.aps.compensation.impl.baggage.accident.utils;

import com.swcares.baseframe.common.exception.BusinessException;
import org.apache.commons.lang3.ObjectUtils;

/**
 * @ClassName：AssertBaggageAccidentInfo
 * @Description： 异常行李事故单保存时字段校验断言
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/18 16:24
 * @version： v1.0
 */
public class AssertBaggageAccidentInfo {
    /**
     * @title isNotEmpty
     * @description @TODO
     * <AUTHOR>
     * @date 2022/3/18 17:01
     * @param param
     * @param code
     * @return boolean
     */
    public static boolean isNotEmpty(Object param,int code){
        if(!ObjectUtils.isNotEmpty(param)){
            throw new BusinessException(code);
        }
        return true;
    }

}
