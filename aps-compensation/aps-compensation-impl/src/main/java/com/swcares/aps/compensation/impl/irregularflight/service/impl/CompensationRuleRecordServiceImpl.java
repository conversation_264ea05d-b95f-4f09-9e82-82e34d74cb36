package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationRuleRecordMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationRuleRecordService;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationRuleRecordDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationStandardVO;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.service.impl.CompensationRuleRecordServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-29 <br>
 * @version v1.0 <br>
 */
@Service
public class CompensationRuleRecordServiceImpl extends ServiceImpl<CompensationRuleRecordMapper, CompensationRuleRecordDO> implements CompensationRuleRecordService {

    @Override
    public List<CompensationStandardVO> getCompensationRuleByOrderId(Long orderId) {
        return getBaseMapper().getCompensationRuleByOrderId(orderId);
    }
}
