package com.swcares.aps.compensation.impl.assist.mapper;

import com.swcares.aps.compensation.model.assist.vo.AssistCompensationListVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistCompensationMapper
 * @projectName aps
 * @description:
 * @date 2022/1/20 19:29
 */
@Mapper
public interface AssistCompensationMapper {
    /**
    * @title getOrderByFlightInfo
    * @description 通过航班信息查询是否有补偿单
    * <AUTHOR>
    * @date 2022/1/20 19:31
    * @param flightDate
    * @param flightNo
    * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.AssistCompensationListVO>
    */
    @Select("SELECT coi.id,coi.status FROM compensation_order_info coi " +
            "where coi.flight_date=#{date} and coi.flight_no= #{no}")
    List<AssistCompensationListVO> getOrderByFlightInfo(@Param("date") String flightDate, @Param("no") String flightNo);
}