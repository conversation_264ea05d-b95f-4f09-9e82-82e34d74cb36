package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationPaxInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationPaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.components.encrypt.FieldEncryptor;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.service.impl.CompensationPaxInfoServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Service
@Transactional(rollbackFor = Exception.class)
public class CompensationPaxInfoServiceImpl extends ServiceImpl<CompensationPaxInfoMapper, CompensationPaxInfoDO> implements CompensationPaxInfoService {

    @Autowired
    private FieldEncryptor encryptor;

    @Autowired
    PayRecordService payRecordService;
    @Autowired
    BusinessDataPushHandler businessDataPushHandler;
    @Autowired
    CompensationOrderInfoService compensationOrderInfoService;
    @Override
    public boolean logicRemoveById(Long id) {
        CompensationPaxInfoDO entity = new CompensationPaxInfoDO();
        entity.setId(id);
        LoginUserDetails user = UserContext.getUser();
        return updateById(entity);
    }

    @Override
    public IPage<CompensationPaxInfoVO> page(CompensationPaxInfoPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public List<CompensationChoicePaxVO> findChoicePax(CompensationPaxFrozenDTO paxFrozenDTO) {
        if(StringUtils.isNotEmpty(paxFrozenDTO.getIdNo())){
            paxFrozenDTO.setIdNo(encryptor.encrypt(paxFrozenDTO.getIdNo()));
        }
        //按姓名拼音首字母排序，出票时间倒序，相同时，补偿总次数倒序。
        //是标红置顶，同名同证件 按姓名拼音首字母先后排序，按票号小到大排序。
        List<CompensationChoicePaxVO> choicePaxVOList = baseMapper.findChoicePax(paxFrozenDTO);

        return choicePaxVOList;
    }

    @Override
    public List<PaxCompensationCountVO> getPaxOrderInfo(PaxOrderInfoQueryDTO dto) {
        return getBaseMapper().getPaxOrderInfo(dto);
    }

    @Override
    public Integer freezeOrderPax(FreezeOrderPaxDTO freezeOrderPaxDTO) {
        boolean bool = SqlHelper.retBool(baseMapper.freezeOrderPax(freezeOrderPaxDTO));
        if(!bool) throw new BusinessException(CommonErrors.UPDATE_ERROR);

        LambdaQueryWrapper<CompensationPaxInfoDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(CompensationPaxInfoDO::getOrderId,freezeOrderPaxDTO.getOrderId());
        lambdaQueryWrapper.in(CompensationPaxInfoDO::getPaxId,freezeOrderPaxDTO.getPaxIds());
        List<CompensationPaxInfoDO> paxInfoDOS = getBaseMapper().selectList(lambdaQueryWrapper);
        List<Long> paxInfoIds = paxInfoDOS.stream().map(CompensationPaxInfoDO::getId).collect(Collectors.toList());
        if(paxInfoIds.size() < 1){
            return 0;
        }
        Integer count = 0;
        //冻结
        if("1".equals(freezeOrderPaxDTO.getStatus())){
            //冻结领取中但未支付的旅客。 需要冻结该旅客未支付订单。
            payRecordService.freezePayOrder(Long.valueOf(freezeOrderPaxDTO.getOrderId()),paxInfoIds,freezeOrderPaxDTO.getStatus());
            //批量冻结，统计选择旅客中，是否存在支付中订单。计数返回前端提示。不影响其他未支付的订单冻结
            count = payRecordService.countPayOrder(Long.valueOf(freezeOrderPaxDTO.getOrderId()),paxInfoIds);

        }
        if("0".equals(freezeOrderPaxDTO.getStatus())){
            payRecordService.freezePayOrder(Long.valueOf(freezeOrderPaxDTO.getOrderId()),paxInfoIds,freezeOrderPaxDTO.getStatus());
        }
        CompensationOrderInfoDO orderInfoDO = compensationOrderInfoService.getById(Long.valueOf(freezeOrderPaxDTO.getOrderId()));
        //推送业务数据到协同中心
        businessDataPushHandler.dataStore(Long.valueOf(freezeOrderPaxDTO.getOrderId()),  AccidentTypeBusinessCodeEnum.build(orderInfoDO.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);

        return count;
    }

    @Override
//    public List<ChoosePaxInfoVO> findSelectedPax(ChoosePaxSearchDTO choosePaxSearchDTO) {
//        return baseMapper.findSelectedPax(choosePaxSearchDTO);
//    }
    public List<ChoosePaxInfoVO> findSelectedPax(Long orderId) {
        return baseMapper.findSelectedPax(orderId);
    }

    @Override
    public boolean updatePaxReceiveInfo(List<Long> paxIds, String receiveChannel, String receiveWay, String receiveStatus) {
        UpdateWrapper<CompensationPaxInfoDO> queryWrapper = new UpdateWrapper();
        LambdaUpdateWrapper<CompensationPaxInfoDO> updateWrapper = queryWrapper.lambda().set(CompensationPaxInfoDO::getReceiveChannel, receiveChannel)
                .set(CompensationPaxInfoDO::getReceiveWay, receiveWay)
                .set(CompensationPaxInfoDO::getReceiveStatus, receiveStatus);
        if(ApplyConstants.ALREADY_RECEIVE_STATUS.equals(receiveStatus)){
            updateWrapper.set(CompensationPaxInfoDO::getReceiveTime, LocalDateTime.now());
        }
        updateWrapper.in(CompensationPaxInfoDO::getId,paxIds);
        return this.update(queryWrapper);
    }

    @Override
    public boolean updateReceiveStatus(String compensationPaxId, String key) {
        return SqlHelper.retBool(this.baseMapper.updateReceiveStatus(compensationPaxId,key));
    }

    @Override
    public boolean updateReceiveStatus(String paxId, String compensationId, String receiveStatus) {
        CompensationPaxInfoDTO comDto = new CompensationPaxInfoDTO();
        comDto.setPaxId(paxId);
        comDto.setOrderId(Long.valueOf(compensationId));
        comDto.setReceiveStatus(receiveStatus);
        comDto.setReceiveTime(LocalDateTime.now());
        return SqlHelper.retBool(this.baseMapper.updateReceiveStatusByOrderNo(comDto));
    }
}
