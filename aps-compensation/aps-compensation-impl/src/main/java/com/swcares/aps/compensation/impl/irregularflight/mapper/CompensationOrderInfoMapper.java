package com.swcares.aps.compensation.impl.irregularflight.mapper;
import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.impl.irregularflight.mapper.provide.CompensationOrderInfoProvide;
import com.swcares.aps.compensation.model.compensation.dto.CompensationOrderCashInfo;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.UpdateProvider;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.mapper.CompensationOrderInfoMapper <br>
 * Description：赔偿单信息 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface CompensationOrderInfoMapper extends BaseMapper<CompensationOrderInfoDO> {


    /**
     * @title pages
     * @description 分页查询+行权限
     * <AUTHOR>
     * @date 2021-10-27 10:25
     * @param dto
     * @param page
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO>
     */
    IPage<CompensationOrderInfoExamineVO> pages(@Param("dto") CompensationOrderInfoPagedDTO dto, Page<CompensationOrderInfoVO> page);
    /**
     * Title：findOrderCountByAccidentId <br>
     * Description：通过事故单ID获取赔偿单数量 <br>
     * author：王磊 <br>
     * date：2021/11/1 15:11 <br>
     * @param accidentId <br>
     * @return <br>
     */
    Integer findOrderCountByAccidentId(@Param("accidentId") Long accidentId);


    /**
     * Title： findById<br>
     * Description： 根据赔付单id查信息<br>
     * author：傅欣荣 <br>
     * date：2021/10/29 15:29 <br>
     * @param id
     * @param userId
     * @return com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO
     */
    CompensationOrderDetailsVO findById(@Param("id") Long id,@Param("userId") String userId);

    /**
    * @title upStatusById
    * @description 根据不同情况修改状态（生效，关闭）
    * <AUTHOR>
    * @date 2021/11/9 16:03
    * @param dto 条件封装对象
    * @return int 返回数据改动成功的行数
    */
    @UpdateProvider(type = CompensationOrderInfoProvide.class,method = "update")
    int upStatusById(@Param("dto") CompensationOrderInfoDTO dto);

    /**
    * @title upAccidentStatus
    * @description 事故单结案操作
    * <AUTHOR>
    * @date 2021/12/10 16:45
    * @param orderId
    * @return void
    */
    void upAccidentStatus(@Param("orderId")Long orderId);

    /**
     * Title： <br>
     * Description： 【草稿||驳回】情况下 编辑保存-删除赔偿单 【草稿情况下可能多人操作同一单。避免误删】<br>
     * author：傅欣荣 <br>
     * date：2021/11/10 15:35 <br>
     * @param id
     * @return boolean
     */
    boolean updRemoveOrderByStatus(@Param("id") Long id);


    /**
     * Title： updCompensationOrderStatus<br>
     * Description： 修改赔偿单状态<br>
     * author：傅欣荣 <br>
     * date：2021/11/24 14:51 <br>
     * @param orderId
     * @param status
     * @return boolean
     */
    boolean updCompensationOrderStatus(@Param("id") Long orderId,@Param("status") String status);

    /**
     * Title：findPaxReceiveRecordByPaxId <br>
     * Description： 通过旅客PaxID-查领取记录<br>
     * author：傅欣荣 <br>
     * date：2021/11/29 17:15 <br>
     * @param orderId
     * @param  paxId
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO>
     */
    List<PaxReceiveRecordVO> findPaxReceiveRecord(@Param("orderId") Long orderId ,@Param("paxId") String paxId,@Param("paxName") String paxName,@Param("idNo") String idNo);

    /**
    * @title getSameTypeOrders
    * @description 通过航班日期，航班号以及事故单类型查询同类型赔偿单
    * <AUTHOR>
    * @date 2021/12/13 11:12
    * @param flightDate
    * @param flightNo
    * @param accidentType
    * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderAddTipsVO>
    */
    List<CompensationOrderAddTipsVO> getSameTypeOrders(@Param("date")String flightDate, @Param("no")String flightNo, @Param("type")String accidentType, @Param("id")Long orderId);

    CompensationOrderCashInfo findCompensationOrderCashInfo(String orderId);

    @Select("SELECT DISTINCT CONCAT(CONCAT(cpi.PAX_NAME,','),cpi.ID_NO) from compensation_pax_info cpi WHERE cpi.PAX_ID = #{paxId}")
    String getNameAndNoByPaxId(@Param("paxId") String paxId);


    CompensationOrderInfoDO selectOneById(@Param("orderId") String orderId,@Param("orderNo") String orderNo);

    @InterceptorIgnore(tenantLine = "true")
    List<CompensationOrderInfoDO> findOverdueOrder();

    @InterceptorIgnore(tenantLine = "true")
    boolean updateOverdueOrder(@Param("orderIds") List<Long> orderIds,@Param("status")String status);

    /***
     * @title findCloseAccidentOrder
     * @description 查询事故单下关联赔偿单全部为逾期或关闭的订单。 包含不正常航班、异常行李
     * <AUTHOR>
     * @date 2022/9/16 11:55

     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO>
     */
    @InterceptorIgnore(tenantLine = "true")
    List<CompensationOrderInfoDO> findCloseAccidentOrder();


    /**
     * @title updateOrderCreatedBy
     * @description 修改补偿单提交人（创建人）
     * <AUTHOR>
     * @date 2022/10/27 16:40
     * @param compensationId
     * @param currentUserId
     * @return void
     */
    void updateOrderCreatedBy(@Param("compensationId") String compensationId, @Param("currentUserId") String currentUserId);

    GeneralCompensationOrderInfoDetailVO getCompensationOrderInfo(@Param("id") Long id, @Param("userId") String userId);

    @InterceptorIgnore(tenantLine = "true")
    Long getTenantIdByCode(@Param("tenantCode") String tenantCode);

    @InterceptorIgnore(tenantLine = "true")
    Map<String,Object> getTenantById(@Param("id")Long tenantId);


    @InterceptorIgnore(tenantLine = "true")
    List<CompensationOrderInfoDO> findNeedRefreshWorkflowUserInfoOrder();

    boolean updateLuggageBusinessReview(@Param("orderId") Long orderId, @Param("userId") String userId);
}
