package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.assist.constant.AssistApplyErrors;
import com.swcares.aps.compensation.impl.assist.mapper.AssistCompensationMapper;
import com.swcares.aps.compensation.impl.assist.service.AssisPaxCompensationService;
import com.swcares.aps.compensation.impl.assist.service.AssistCheckService;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO;
import com.swcares.aps.compensation.model.assist.dto.CheckInfoDTO;
import com.swcares.aps.compensation.model.assist.vo.AssistCompensationListVO;
import com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistCheckServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/1/20 13:39
 */
@Service
@Slf4j
public class AssistCheckServiceImpl implements AssistCheckService {

    @Autowired
    private AssisPaxCompensationService assisPaxCompensationService;

    @Autowired
    private ApplyPaxService applyPaxService;
    @Autowired
    private FieldEncryptor fieldEncryptor;

    @Autowired
    private AssistCompensationMapper assistCompensationMapper;
    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Override
    public Object checked(CheckInfoDTO dto) {
        String idNo = dto.getIdNo();
        log.info("【aps-compensation-impl-assist】校验信息:[{}]",dto.toString());
        //判断是否传入了证件号，进行不同校验
        if(ObjectUtils.isEmpty(idNo)){
            return authFlight(dto);
        }
        dto.setIdNo(fieldEncryptor.encrypt(idNo));
        String paxName = assisPaxCompensationService.getPaxName(dto.getFlightDate(),dto.getFlightNo(),dto.getIdNo());
        if(StringUtils.isEmpty(paxName)){
            log.error("【aps-compensation-impl-assist】验证旅客失败,证件号:" + ApsDesensitizedUtil.idCardNum(dto.getIdNo()));
            throw new BusinessException(AssistApplyErrors.PAX_NOT_MATCHING_ERROR);
        }
        AuthPaxDTO paxDTO = new AuthPaxDTO();
        paxDTO.setFlightDate(dto.getFlightDate());
        paxDTO.setFlightNo(dto.getFlightNo());
        paxDTO.setIdNo(idNo);
        //复用 王磊的本人领取校验
        try{

            applyPaxService.authPax(paxDTO);
        }catch (BusinessException b){
            log.error("【aps-compensation-impl-assist】验证旅客失败,证件号:" +ApsDesensitizedUtil.idCardNum(dto.getIdNo()));
            if(b.getCode()==AssistApplyErrors.APPLY_AUTH_PAX_ERROR){
                throw new BusinessException(AssistApplyErrors.PAX_COMPENSATION_NOT_FIND_ERROR,paxName);
            }else if(b.getCode()==AssistApplyErrors.APPLY_AUTH_PAX_ALREADY_RECEIVED){
                throw new BusinessException(AssistApplyErrors.PAX_APPLY_ALREADY_RECEIVED_ERROR,paxName);
            }else if(b.getCode()==AssistApplyErrors.APPLY_AUTH_PAX_ABNORMAL){
                throw new BusinessException(AssistApplyErrors.PAX_APPLY_FROZEN_ERROR,paxName);
            }
            throw b;
        }
        paxDTO.setIdNo(idNo);
        CompensationInfoVO paxCompensation = assisPaxCompensationService.getPaxCompensation(paxDTO);
        List<CompensationOrderInfoVO> orderInfoResultList = new ArrayList<>();

        for(CompensationOrderInfoVO orderInfoVO:paxCompensation.getOrderInfoVOS()){
            //非现金，跳过
            if(!"1".equals(orderInfoVO.getCompensateType())){
                continue;
            }
            orderInfoResultList.add(orderInfoVO);
        }
        if (ListUtils.isEmpty(orderInfoResultList)) {
            log.error("【aps-apply-impl】验证用户失败:" + ApsDesensitizedUtil.idCardNum(dto.getIdNo()));
            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ERROR);
        }
        paxCompensation.setOrderInfoVOS(orderInfoResultList);

        return paxCompensation;
    }

    /**
    * @title authFlight
    * @description 航班查询校验（仅有航班号和日期）
    * @param dto
    * <AUTHOR>
    * @return void
    * @date 2022/1/20 18:56
    */
    private List<AssistPaxListVO> authFlight(CheckInfoDTO dto){
        List<AssistCompensationListVO> list = assistCompensationMapper.getOrderByFlightInfo(dto.getFlightDate(),dto.getFlightNo());
        if(ListUtils.isEmpty(list)){
            log.error("【aps-compensation-impl-assist】验证航班失败:" + dto.getFlightNo()+","+dto.getFlightDate());
            throw new BusinessException(AssistApplyErrors.FLIGHT_NOT_FIND_ERROR);
        }else {
            boolean hasCompensation = false;
            for (AssistCompensationListVO assistCompensationListVO : list) {
                if(assistCompensationListVO.getStatus().equals(CompensateStatusEnum.TAKE_EFFECT.getKey())){
                    hasCompensation = true;
                    break;
                }
            }
            if(!hasCompensation){
                log.error("【aps-compensation-impl-assist】验证航班失败:" + dto.getFlightNo()+","+dto.getFlightDate());
                String[] msg = new String[]{dto.getFlightDate(),dto.getFlightNo()};
                throw new BusinessException(AssistApplyErrors.COMPENSATION_NOT_FIND_ERROR,msg);
            }
        }
        List<AssistPaxListVO> result = assisPaxCompensationService.getPaxCompensationByFlightInfo(dto.getFlightNo(),dto.getFlightDate(),null);
       if(ListUtils.isEmpty(result)){
           log.error("【aps-compensation-impl-assist】验证航班失败:" + dto.getFlightNo()+","+dto.getFlightDate());
           String[] msg = new String[]{dto.getFlightDate(),dto.getFlightNo()};
           throw new BusinessException(AssistApplyErrors.ASSIST_PAX_NOT_FIND_ERROR,msg);
       }
        return result;
    }

}
