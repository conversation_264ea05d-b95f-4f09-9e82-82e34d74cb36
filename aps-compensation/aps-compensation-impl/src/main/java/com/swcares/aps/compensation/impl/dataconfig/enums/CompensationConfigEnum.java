package com.swcares.aps.compensation.impl.dataconfig.enums;

/**
 * ClassName：DamageTypeConfigEnum <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */
public enum CompensationConfigEnum {

	RULE_PAYMENT_WAITING_PERIOD("RULE","PAYMENT_WAITING_PERIOD","支付等待期，单位分钟"),
	RULE_IS_CONTAIN_SELF("RULE","IS_CONTAIN_SELF","是否包含本人 0:不包含 1:包含"),
	RULE_IS_SAME_FLIGHT("RULE","IS_SAME_FLIGHT","是否同航班旅客 0:不是 1:是"),
	RULE_MAX_APPLY_PASSENGER("RULE","MAX_APPLY_PASSENGER","最大可申领取乘机人数"),
	AUDIT_REJECT_REASON("AUDIT_REASON","REJECT_REASON","审核拒绝原因"),
	ACCIDENT_DAMAGE_TYPE("ACCIDENT_DAMAGE","DAMAGE_TYPE","异常行李破损类型"),
	ACCIDENT_DAMAGE_PART("ACCIDENT_DAMAGE","DAMAGE_PART","异常行李破损部位"),
	ACCIDENT_DAMAGE_DEGREE("ACCIDENT_DAMAGE","DAMAGE_DEGREE","异常行李破损程度"),
	BAGGAGE_ACCIDENT_RULE("BAGGAGE_ACCIDENT_RULE","LOST_DEFAULT_ALTER_DAY","异常行李丢失事故提醒时间，单位day");

	private String type;

	private String subType;

	private String description;

	CompensationConfigEnum(String type, String subType, String description) {
		this.type = type;
		this.subType = subType;
		this.description = description;
	}

	public String getType() {
		return type;
	}

	public String getSubType() {
		return subType;
	}

	public String getDescription() {
		return description;
	}
}
