package com.swcares.aps.compensation.impl.compensation.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.CompensationExpressService;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationRuleRecordService;
import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeItemDTO;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName：AccidentCompensationDomainService
 * @Description：事故补偿单领域模型service
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/11 19:53
 * @version： v1.0
 */
@Service
@Slf4j
@Transactional
public class AccidentCompensationDomainService {

    private static final int PROCESS_INSTANCE_NOT_EXIST = 31002;

    private CompensationFactory compensationFactory;

    private CompensationOrderInfoService compensationOrderInfoService;

    private CompensationPaxInfoService compensationPaxInfoService;

    private CompensationMaterialInfoService compensationMaterialInfoService;

    private CompensationFlightInfoService compensationFlightInfoService;

    private CompensationOrderStatusService compensationOrderStatusService;

    private CompensationRuleRecordService compensationRuleRecordService;

    private CompensationExpressService compensationExpressService;

    private CompensationAuditService compensationAuditService;
    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    public AccidentCompensationDomainService(CompensationFactory compensationFactory,
                                             CompensationOrderInfoService compensationOrderInfoService,
                                             CompensationPaxInfoService compensationPaxInfoService,
                                             CompensationMaterialInfoService compensationMaterialInfoService,
                                             CompensationFlightInfoService compensationFlightInfoService,
                                             CompensationOrderStatusService compensationOrderStatusService,
                                             CompensationRuleRecordService compensationRuleRecordService,
                                             CompensationExpressService compensationExpressService,
                                             CompensationAuditService compensationAuditService){
        this.compensationFactory=compensationFactory;
        this.compensationOrderInfoService=compensationOrderInfoService;
        this.compensationPaxInfoService=compensationPaxInfoService;
        this.compensationMaterialInfoService=compensationMaterialInfoService;
        this.compensationFlightInfoService=compensationFlightInfoService;
        this.compensationOrderStatusService=compensationOrderStatusService;
        this.compensationRuleRecordService = compensationRuleRecordService;
        this.compensationExpressService = compensationExpressService;
        this.compensationAuditService=compensationAuditService;
    }

    /**
     * @title save
     * @description 保存补偿单
     * <AUTHOR>
     * @date 2022/3/11 22:32
     * @param compensation
     * @return
     */
    public String save(AccidentCompensationDTO compensation){
        CompensationOrderInfoDO infoDO = ObjectUtils.copyBean(compensation, CompensationOrderInfoDO.class);
        infoDO.setStatus(CompensateStatusEnum.DRAFT.getKey());
        infoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        infoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        infoDO.setCreatedTime(LocalDateTime.now());
        infoDO.setUpdatedTime(LocalDateTime.now());
        //补偿单主表写入数据
        CompensationOrderInfoDO compensationOrder = compensationOrderInfoService.createCompensationOrder(infoDO);

        createOrderDetail(compensation, compensationOrder);

        return String.valueOf(compensationOrder.getId());
    }

    /**
     * @title edit
     * @description 编辑补偿单
     * <AUTHOR>
     * @date 2022/3/11 22:32
     * @param newCompensation compensationId
     * @return
     */
    public void edit(AccidentCompensationDTO newCompensation, String compensationId) {
        AccidentCompensationDTO oldCompensation = compensationFactory.createByOrderIdOld(compensationId);
        if(oldCompensation==null){
            throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,compensationId+"补偿单不存在");
        }
        if((!CompensateStatusEnum.DRAFT.getKey().equals(oldCompensation.getStatus()))
                &&(!CompensateStatusEnum.REJECT.getKey().equals(oldCompensation.getStatus()))){
            throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,compensationId+"非草稿状态/驳回状态不能编辑");
        }

        CompensationOrderInfoDO newInfoDO = ObjectUtils.copyBean(newCompensation, CompensationOrderInfoDO.class);
        newInfoDO.setStatus(CompensateStatusEnum.DRAFT.getKey());
        newInfoDO.setId(oldCompensation.getId());
        newInfoDO.setOrderNo(oldCompensation.getOrderNo());
        newInfoDO.setCreatedBy(oldCompensation.getCreatedBy());
        newInfoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        newInfoDO.setCreatedTime(LocalDateTime.now());
        newInfoDO.setUpdatedTime(LocalDateTime.now());
        compensationOrderInfoService.updateById(newInfoDO);
        deleteOrderDetail(oldCompensation);
        createOrderDetail(newCompensation,newInfoDO);
    }

    /**
     * @title submit
     * @description 提交补偿单
     * <AUTHOR>
     * @date 2022/3/11 22:34
     * @param compensationId
     * @return
     */
    public CompensationAuditOperationVO submit(String compensationId){
        AccidentCompensationDTO compensation = compensationFactory.createByOrderIdOld(compensationId);
        if(compensation==null){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_ERROR,compensationId+"补偿单不存在");
        }
        //事故单类型对应的业务code
        String businessCode = AccidentTypeBusinessCodeEnum.build(compensation.getAccidentType()).getValue();
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(businessCode,compensation.getFlightNo().substring(0, 2));

        //驳回后提交，需要判断当前用户是否是发起人
        if(CompensateStatusEnum.REJECT.getKey().equals(compensation.getStatus()) &&
                !ApsUserUtils.getCreatedBy().equals(compensation.getCreatedBy())){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_AUTHORITY_ERROR);
        }
        if((!CompensateStatusEnum.DRAFT.getKey().equals(compensation.getStatus()))
                &&(!CompensateStatusEnum.REJECT.getKey().equals(compensation.getStatus()))){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_ERROR,compensationId+"非草稿/驳回状态不能提交");
        }
        //驳回提交，不做提交人修改
        if(!CompensateStatusEnum.REJECT.getKey().equals(compensation.getStatus())){
            compensationOrderInfoService.updateCreatedBy(compensationId,ApsUserUtils.getCreatedBy());
        }
        compensationOrderStatusService.changeStatus(compensation,CompensateStatusEnum.AUDIT_PROCESS.getKey());

        //【异常行李、旅客投诉、航班超售-赔偿单提交时】查询资金授权情况
        BusinessPrivilegeItemDTO itemDTO = compensationOrderInfoService.businessPrivilegeByAirlineCode(businessCode,compensation.getFlightNo().substring(0, 2));
        if(itemDTO != null && itemDTO.isGrantBankroll()){
            new LambdaUpdateChainWrapper<>(compensationOrderInfoService.getBaseMapper())
                    .set(CompensationOrderInfoDO::getGrantBankroll,"1")
                    .eq(CompensationOrderInfoDO::getId,compensationId).update();
        }

        //推送业务数据到协同中心
        if (!"0".equals(compensation.getStatus()) && !CompensateStatusEnum.REJECT.getKey().equals(compensation.getStatus())){
            //推送业务数据到协同中心
            businessDataPushHandler.dataStore(compensation.getId(),  AccidentTypeBusinessCodeEnum.build(compensation.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);
        }
        return processWorkflow(compensation);
    }

    /**
     * @title reSubmit
     * @description 重新提交保障单
     * <AUTHOR>
     * @date 2022/5/10 14:45
     * @param compensation
     * @param compensationId
     * @return
     */
    public CompensationAuditOperationVO reSubmit(AccidentCompensationDTO compensation, String compensationId) {
        this.edit(compensation,compensationId);
        compensationOrderStatusService.changeStatus(compensationFactory.createByOrderId(compensationId),CompensateStatusEnum.AUDIT_PROCESS.getKey());


        //推送业务数据到协同中心
        businessDataPushHandler.dataStore(compensation.getId(),  AccidentTypeBusinessCodeEnum.build(compensation.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);

        return processWorkflow(compensation);
    }

    private CompensationAuditOperationVO processWorkflow(AccidentCompensationDTO oldCompensation){
        CurrentTaskActivityVO currentTaskActivityVOBaseResult = null;
        try {
            currentTaskActivityVOBaseResult = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(String.valueOf(oldCompensation.getId())).build()).getData();
            log.info("【aps-compensation】赔偿单id:【{}】,流程待处理结果：【{}】", oldCompensation.getId(), JSONUtil.toJsonStr(currentTaskActivityVOBaseResult));
        }catch (DecoderHandlerException b){
            if(b.getCode()==PROCESS_INSTANCE_NOT_EXIST){
                log.info("【aps-compensation】流程实例不存在，实例id:【{}】",oldCompensation.getId());
            }else{
                throw b;
            }
        }
        if(CompensateStatusEnum.DRAFT.getKey().equals(oldCompensation.getStatus()) && ObjectUtils.isEmpty(currentTaskActivityVOBaseResult)){
            log.info("【aps-compensation】编辑保存赔偿单信息-赔偿单保存成功，赔偿单状态为【审核中】，发起审核流程。赔偿单号[{}]", oldCompensation.getOrderNo());
            return compensationAuditService.startAuditProcess(oldCompensation);
        }
        if(CompensateStatusEnum.REJECT.getKey().equals(oldCompensation.getStatus()) ||
                (CompensateStatusEnum.DRAFT.getKey().equals(oldCompensation.getStatus()) && ObjectUtils.isNotEmpty(currentTaskActivityVOBaseResult))){
            log.info("【aps-compensation】编辑保存赔偿单信息-驳回后再次发起审核流程。赔偿单号[{}]", oldCompensation.getOrderNo());
            return compensationAuditService.submitterAuditProcess(oldCompensation);
        }
        throw  new BusinessException(CompensationException.AUDIT_ERROR,oldCompensation.getOrderNo()+"补偿单状态异常不能发起审核流程");
    }




    /**
     * @title delete
     * @description 删除补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:47
     * @param compensationId
     * @return
     */
    public void delete(String compensationId) {
        AccidentCompensationDTO compensation = compensationFactory.createByOrderId(compensationId);
        if(compensation==null){
            return;
        }
        if(!CompensateStatusEnum.DRAFT.getKey().equals(compensation.getStatus())){
            throw new BusinessException(CompensationException.COMPENSATION_DELETE_ERROR,compensationId+"非草稿状态不能删除");
        }
        compensationOrderInfoService.removeById(compensationId);
        deleteOrderDetail(compensation);
    }
    private void deleteOrderDetail(AccidentCompensationDTO compensation){
        //补偿单旅客表写入数据
        Map<String, Object> map =new HashMap<>(1);
        map.put("order_id",compensation.getId());

        if(CollectionUtils.isNotEmpty(compensation.getCompensationPaxInfo())){
            compensationPaxInfoService.removeByMap(map);
        }

        //补偿单实物表写入数据
        if(CollectionUtils.isNotEmpty(compensation.getCompensationMaterialInfo())){
            compensationMaterialInfoService.removeByMap(map);
        }

        if(compensation.getCompensationFlightInfo()!=null){
            compensationFlightInfoService.removeByMap(map);
        }

        if(CollectionUtils.isNotEmpty(compensation.getCompensationRuleRecordinfo())){
            compensationRuleRecordService.removeByMap(map);
        }
    }

    private void createOrderDetail(AccidentCompensationDTO compensation, CompensationOrderInfoDO compensationOrder) {
        //补偿单旅客表写入数据
        if(CollectionUtils.isNotEmpty(compensation.getCompensationPaxInfo())){
            compensation.getCompensationPaxInfo().forEach(t-> {
                t.setOrderId(compensationOrder.getId());
                t.setUpdatedBy(ApsUserUtils.getCreatedBy());
                t.setCreatedBy(ApsUserUtils.getCreatedBy());
                t.setCreatedTime(LocalDateTime.now());
                t.setUpdatedTime(LocalDateTime.now());
            });
            compensationPaxInfoService.saveBatch(compensation.getCompensationPaxInfo());
        }

        //补偿单实物表写入数据
        if(CollectionUtils.isNotEmpty(compensation.getCompensationMaterialInfo())){
            compensation.getCompensationMaterialInfo().forEach(t->{
                t.setOrderId(compensationOrder.getId());
                t.setOrderNo(compensationOrder.getOrderNo());
                t.setUpdatedBy(ApsUserUtils.getCreatedBy());
                t.setCreatedBy(ApsUserUtils.getCreatedBy());
                t.setCreatedTime(LocalDateTime.now());
                t.setUpdatedTime(LocalDateTime.now());
            });

            compensationMaterialInfoService.saveBatch(compensation.getCompensationMaterialInfo());
        }
        //赔偿单航班信息写入数据
        if(compensation.getCompensationFlightInfo()!=null){
            compensation.getCompensationFlightInfo().setOrderId(compensationOrder.getId());
            compensation.getCompensationFlightInfo().setUpdatedBy(ApsUserUtils.getCreatedBy());
            compensation.getCompensationFlightInfo().setCreatedBy(ApsUserUtils.getCreatedBy());
            compensation.getCompensationFlightInfo().setCreatedTime(LocalDateTime.now());
            compensation.getCompensationFlightInfo().setUpdatedTime(LocalDateTime.now());
            compensationFlightInfoService.save(compensation.getCompensationFlightInfo());
        }

        //补偿单规则写入数据
        if(CollectionUtils.isNotEmpty(compensation.getCompensationRuleRecordinfo())){
            compensation.getCompensationRuleRecordinfo().forEach(t ->{
                t.setAccidentId(compensationOrder.getAccidentId());
                t.setOrderId(compensationOrder.getId());
                t.setCreatedBy(ApsUserUtils.getCreatedBy());
                t.setUpdatedBy(ApsUserUtils.getCreatedBy());
                t.setCreatedTime(LocalDateTime.now());
                t.setUpdatedTime(LocalDateTime.now());
            });
            compensationRuleRecordService.saveBatch(compensation.getCompensationRuleRecordinfo());
        }
    }

    /**
     * @title auditPassToTakeEffect
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:38
     * @param compensationId
     * @return void
     */
    public void auditPassToTakeEffect(String compensationId) {
        AccidentCompensationDTO compensation = compensationFactory.createByOrderId(compensationId);
        if(compensation==null){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_ERROR,compensationId+"补偿单不存在");
        }
        //事故单类型对应的业务code
        String businessCode = AccidentTypeBusinessCodeEnum.build(compensation.getAccidentType()).getValue();
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(businessCode,compensation.getFlightNo().substring(0, 2));

        //机场端：补偿单发放的时候，验证资金授权有无变化，有变化就提示报错；无报错就快照，后续在打款不需要对资金授权做验证。
        BusinessPrivilegeItemDTO itemDTO = compensationOrderInfoService.businessPrivilegeByAirlineCode(businessCode,compensation.getBelongAirline());
        log.info("--【补偿单确认发放功能】赔偿单id：[{}],赔偿单创建资金授权情况：[{}],资金授权信息对象：[{}]",compensationId,compensation.getGrantBankroll(), JSON.toJSONString(itemDTO));
        String grantBankroll = "0";
        if(itemDTO != null && itemDTO.isGrantBankroll()){
            grantBankroll = "1";
        }
        if(!grantBankroll.equals(compensation.getGrantBankroll())){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_GRANT_BANKROLL_ERROR);
        }

        // pass after update release time and release user
        compensationOrderStatusService.changeStatus(compensation,CompensateStatusEnum.TAKE_EFFECT.getKey());
        //推送业务数据到协同中心
        businessDataPushHandler.dataStore(compensation.getId(),  AccidentTypeBusinessCodeEnum.build(compensation.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);


    }

    /**
     * @title closeCompensation
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:55
     * @param compensationId
     * @return void
     */
    public void closeCompensation(String compensationId) {
        AccidentCompensationDTO compensation = compensationFactory.createByOrderIdOld(compensationId);
        if(compensation==null){
            throw new BusinessException(CompensationException.COMPENSATION_SUBMIT_ERROR,compensationId+"补偿单不存在");
        }
        //补偿单从生效状态转换为关闭（5）
        if(CompensateStatusEnum.TAKE_EFFECT.getKey().equals(compensation.getStatus())){
            compensationOrderStatusService.changeStatus(compensation,CompensateStatusEnum.CLOSE.getKey());
        }
        //补偿单从审核通过转换为关闭（8）
        else{
            compensationOrderStatusService.changeStatus(compensation,CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey());
        }
    }

    /**
     * @title mailGrant
     * @description 邮寄发放
     * <AUTHOR>
     * @date 2022/5/7 9:35
     * @param dto
     * @return void
     */
    public boolean mailGrant(CompensationExpressInfoDTO dto) {
        compensationExpressService.saveExpress(dto);
        boolean status = compensationPaxInfoService.updateReceiveStatus(dto.getPaxId(),dto.getCompensationId(), ReceiveStatusEnum.RECEIVED.getKey());
        if(!status){ throw new BusinessException(CommonErrors.UPDATE_ERROR);}
        return status;
    }




}
