package com.swcares.aps.compensation.impl.sms.constant;

/**
 * @ClassName：CompensationSmsConstant
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： tanrui
 * @Date： 2022/11/7 15:42
 * @version： v1.0
 */
public class CompensationSmsConstant {

    public static String SYSTEM_CODE = "compensation_sms";

    //2022/11/14 和王哥沟通后赔付这个值传一个固定的： PAX_COMPENSATION（针对旅客的消息推送）
    public static String BUSINESS_TYPE = "PAX_COMPENSATION";

    //工作人员消息-业务类型，这个和上面BUSINESS_TYPE对应，上面那个是对旅客的，这个是对工作人员的，都是用于消息模块存储数据，进行类型区分的
    public static String STAFF_BUSINESS_TYPE = "2";//服务补偿

    //调用消息模块给公众号推送消息时，模板中需要传这个参数，和王哥沟通后，传中文
    public static String STAFF_OFFICIAL_ACCOUNT_TYPE = "服务补偿";

    //验证码短信发送
    public static String SMS_CODE_BUSINESS_TYPE = "短信验证码";

    /** 旅客小程序推送服务消息时的id，先写死，后续看看要不要挪到配置文件或者数据库；模板id和内容是一一对应的，换模板不仅仅是换模板id，模板内容的代码也要修改 */
    public static final String COMPENSATION_AUDIT_MSG_TEMPLATE_ID = "QWlS2Xw8tisc9ZNF2LgoC_k6sMfHH7WmLyyvtzZCn4g";

    /** 旅客小程序推送服务消息时的clientId,由消息模板颁发 */
    public static final String MINI_CLIENT_ID = "passenger_wx_miniapp";

    /** 旅客小程序推送服务消息时的clientId,由消息模板颁发 */
    public static final String MINI_TITLE = "【服务补偿】旅客领取审核结果通知";

    public static String CLIENT_ID = "employee_wx_official_account";

    //web端URL,需要拼接参数
    public static String WEB_URL_SUFFIX = "/compensation/redress/detail?accidentId=%s&choiceSegment=%s&id=%s&status=%s&orderNo=%s&accidentType=%s&userId=";

    //H5端_不正常航班  拼接补偿单号
    public static String H5_FLIGHT_SUFFIX = "/flightCompensation/compensationDetail?compensationId=%s";

    //H5_异常行李事务补偿  拼接补偿单单号
    public static String H5_LUGGAGE_SUFFIX = "/luggageCompensation/detail?compensationId=%s";

    //H5_现金补偿  后续拼接补偿单单号
    public static String H5_CASH_SUFFIX = "/cashCompensation/detail?compensationId=%s";


    //H5_补偿单详情
    public static String H5_COMPENSATION_SUFFIX = "/listOfCompensationOrders/detail?id=%s&orderNo=%s";

    //短信验证通过标识
    public static String AUTH_TOKEN_SMS_CODE_PASS = "SMS_PASS";

    /** 旅客端领取时，需要发送短信验证码，这儿将发送结果存入redis，避免短信轰炸 */
    public static String AUTH_PHONE_PREFIX = "COMPENSATION:PHONE_VERIFICATION_SMS:";

    /** 旅客端领取时，需要发送短信验证码，这儿将发送结果存入redis，避免短信轰炸，默认过去时间5分钟 */
    public static Long AUTH_PHONE_PREFIX_PAST_DUE = 300L;

    /** 发送短信验证码，token存入redis，避免短信轰炸 */
    public static String AUTH_TOKEN_SMS_PREFIX = "COMPENSATION:TOKEN_VERIFICATION_SMS:";

    /** 短信验证码验证成功后，token存入redis，避免越过短信验证步骤 */
    public static String AUTH_TOKEN_SMS_PASS_PREFIX = "COMPENSATION:TOKEN_VERIFICATION_SMS_PASS:";

    /** 验证短信验证码，token+phone存入redis，值存验证码验证失败次数,避免爆破出正确的验证码 ,  失败次数>=5次,就需要重新获取验证码*/
    public static String AUTH_TOKEN_SMS_CODE_FAIL_PREFIX = "COMPENSATION:TOKEN_VERIFICATION_SMS:";

    /** 验证短信验证码  验证失败次数,避免爆破出正确的验证码 ,  失败次数>=5次,就需要重新获取验证码*/
    public static int AUTH_TOKEN_SMS_CODE_FAIL_NUM = 5;

    public static String CONTACT_CONTENT = "您好，%s%s航班补偿单正在等待您的审核，请您及时处理！";

    public static String CONTACT_TITLE = "%s%s补偿单审核提醒";

    public static String PASSENGER_SMS_CONTENT = "【%s】尊敬的旅客，您的验证码为%s，请在5分钟内输入，感谢您对%s的支持！";

    public static String AUDIT_DISAGREE_TITLE = "补偿单【%s】审核不通过提醒";
    public static String AUDIT_AGREE_END_TITLE = "补偿单【%s】审核通过提醒";
    public static String AUDIT_REJECT_TITLE = "补偿单【%s】审核驳回提醒";
    public static String AUDIT_AGREE_TITLE = "补偿单【%s】待审核提醒";

    public static String ACCIDENT_OVERTIME_TITLE = "事故单【%s】少收时间提醒";

    public static String AUDIT_AGREE_CONTENT = "【%s】【%s】 发起的补偿单(单号:【%s】，补偿金额: 【%s】元，补偿类型:【%s】)正在等待您的审核，请点击前往审核";
    public static String AUDIT_AGREE_END_CONTENT = "您发起的【%s】【%s】 补偿单(单号:【%s】，补偿金额: 【%s】元，补偿类型:【%s】)已审核通过，请前往查看详情";
    public static String AUDIT_DISAGREE_CONTENT = "您发起的【%s】【%s】 补偿单(单号:【%s】，补偿金额: 【%s】元，补偿类型:【%s】)审核不通过，请前往查看详情";
    public static String AUDIT_REJECT_CONTENT = "您参与的【%s】【%s】 补偿单(单号:【%s】，补偿金额: 【%s】元，补偿类型:【%s】)已被驳回，请前往查看详情";

    public static String ACCIDENT_OVERTIME_CONTENT = "【%s】【%s】创建的异常行李少收事故单，少收提醒时间已到";


    //公众号消息推送-待处理模板id
    public static String OFFICIAL_TODO_MODEL = "wWD92xFiVHFdguPC2-tgql9XFBVfW7SXIlxHfECP2Ys";
    //公众号消息推送-结果通知模板id
    public static String OFFICIAL_RESULT_MODEL = "QXq4NwGQJnjC3VHB_yiy517reD7DkjimEHktxouf7NE";
}
