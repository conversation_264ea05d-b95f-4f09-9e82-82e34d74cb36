package com.swcares.aps.compensation.impl.apply.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.apply.dto.PayRecordPagedDTO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.PayRecordDOVO;
import org.apache.ibatis.annotations.Param;

/**
 * ClassName：com.swcares.compensation.mapper.PayRecordMapper <br>
 * Description：航延补偿支付记录表 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
public interface PayRecordMapper extends BaseMapper<PayRecordDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-11-25 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<PayRecordDOVO> page(@Param("dto") PayRecordPagedDTO dto, Page<PayRecordDOVO> page);

    @InterceptorIgnore(tenantLine = "true")
    int doUpdateById(@Param("payRecordDO")PayRecordDO payRecordDO);
}
