package com.swcares.aps.compensation.impl.baggage.accident.service.handler;

import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageAccidentTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import com.swcares.baseframe.common.exception.BusinessException;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName：BaggageAccidentStatusHandlerServiceImpl
 * @Description：待处理状态更改为已结案状态处理服务类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/15 15:22
 * @version： v1.0
 */
@Service
public class ChangeToDoToClosedStatusHandlerServiceImpl implements BaggageAccidentStatusHandlerService {

    @Override
    public boolean support(String sourceStatus, String targetStatus) {
        return AccidentStatusEnum.TODO.getValue().equals(sourceStatus)
                && AccidentStatusEnum.CASE_CLOSED.getValue().equals(targetStatus);
    }

    @Override
    public void check(BaggageAccidentInfoDTO accidentInfo, String targetStatus) {
        //TODO 添加相关逻辑判断，如果逻辑判断不通过，请直接抛businessexception错误；如还有业务处理，请重写BaggageAccidentStatusHandlerService接口的execute实现
        //当异常行李事故单的状态为待处理，且类型不是少收或者多收，不允许待处理状态变更为结案状态
        if(accidentInfo.getType().isEmpty() || accidentInfo.getType().equals(BaggageAccidentTypeEnum.DAMAGE.getValue()) || accidentInfo.getType().equals(BaggageAccidentTypeEnum.INSIDE_LOST.getValue())|| accidentInfo.getType().equals(BaggageAccidentTypeEnum.LOST.getValue())){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_STATUS_VERIFY_FAIL,"当前状态不能修改为"+AccidentStatusEnum.CASE_CLOSED.getLabel()+"状态");
          }
      }
    }

