package com.swcares.aps.compensation.impl.overbook.constant;

/**
 * @ClassName：OverBookConstant
 * @Description：超售常量
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 15:22
 * @version： v1.0
 */
public interface OverBookConstant {

    //凭证类型 证件号 2票号
    public static String SEARCH_TYPE_1 = "1";

    public static String SEARCH_TYPE_2 = "2";


    //0保存草稿 1.只创建事故单 2创建事故单及生成补偿单草稿 3创建事故单及补偿单
    public static String SAVE_TYPE_0 = "0";
    public static String SAVE_TYPE_1 = "1";
    public static String SAVE_TYPE_2 = "2";
    public static String SAVE_TYPE_3 = "3";
}
