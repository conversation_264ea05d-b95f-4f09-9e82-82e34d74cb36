package com.swcares.aps.compensation.impl.baggage.accident.enums;

/**
 * @ClassName：BaggageAccidentExpressTypeEnum
 * @Description： 数据输入的来源
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date： 2025/2/12 10:38
 * @version： v1.0
 */
public enum BaggageInputSourceTypeEnum {
    SYSTEM("SYSTEM", "系统查询"),
    MANUAL_INPUT("MANUAL_INPUT", "人工输入");

    private BaggageInputSourceTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaggageInputSourceTypeEnum build(String key) {
        return build(key, true);
    }

    public static BaggageInputSourceTypeEnum build(String key, boolean throwEx) {
        BaggageInputSourceTypeEnum typeEnum = null;
        for (BaggageInputSourceTypeEnum element : BaggageInputSourceTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + BaggageInputSourceTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
