package com.swcares.aps.compensation.impl.assist.service;

import com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO;
import com.swcares.baseframe.common.base.BaseResult;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistStaffTips
 * @projectName aps
 * @description:    工作人员端提示
 * @date 2022/2/16 9:58
 */
public interface AssistStaffTipsService {
    /**
    * @title sendSMSToStaff
    * @description 通知工作人员端
    * <AUTHOR>
    * @date 2022/2/16 11:11
    * @param phone  代领手机号码
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    */
    BaseResult<Object> sendSMSToStaff(String phone);
}