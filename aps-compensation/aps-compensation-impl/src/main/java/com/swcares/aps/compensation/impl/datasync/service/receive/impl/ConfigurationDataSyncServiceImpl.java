package com.swcares.aps.compensation.impl.datasync.service.receive.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.compensation.service.CompensationRuleConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.CabinConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRuleService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.receive.ConfigurationDataSyncService;
import com.swcares.aps.compensation.impl.passengerCategory.service.impl.PassengerCategoryServiceImpl;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.datasync.dto.BusinessDataSyncDTO;
import com.swcares.aps.compensation.model.rools.entity.CompensationRuleConfig;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessConfigureDataCoordinateDTO;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @ClassName：ConfigurationDataSyncServiceImpl
 * @Description：@TODO
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/10/8 14:25
 * @version： v1.0
 */
@Slf4j
@Service
public class ConfigurationDataSyncServiceImpl implements ConfigurationDataSyncService {

    //业务数据处理方法map
    private Map<String, Consumer<BusinessDataSyncDTO>> configurationDataHandlerMap;

    @PostConstruct
    public void init() {
        configurationDataHandlerMap = new HashMap<>();
        configurationDataHandlerMap.put(BusinessDataSyncConstant.CABIN_TYPE, this::addCabin);
        configurationDataHandlerMap.put(BusinessDataSyncConstant.DATA_TYPE_RULE, this::addRule);
        configurationDataHandlerMap.put(BusinessDataSyncConstant.DATA_TYPE_REPLACE, this::addReplace);
        configurationDataHandlerMap.put(BusinessDataSyncConstant.SERVICE_SUPPORT, this::addServiceSupport);
        // 添加更多的映射
    }
    @Autowired
    ReplaceConfigService replaceConfigService;

    private void addReplace(BusinessDataSyncDTO businessDataSyncDTO) {
        log.info("【机场端接收航司端的数据】收到请求: 【{}】", JSONUtil.toJsonStr(businessDataSyncDTO));
        List<DataConfigDO> dataConfigDos = businessDataSyncDTO.getDataConfigDos();
        if (CollectionUtils.isEmpty(dataConfigDos)) {
            return;
        }
        List<DataConfigDO> configDOS = dataConfigDos.stream().filter(dataConfigDO -> !dataConfigDO.getSubType().equals("REJECT_REASON")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(configDOS)) {
            for (DataConfigDO configDO : configDOS) {
                replaceConfigService.saveOrUpdate(configDO);
            }
        }
        List<DataConfigDO> localRejects = replaceConfigService.list(Wrappers.lambdaQuery(DataConfigDO.class).eq(DataConfigDO::getType, "REJECT_REASON"));
        if (CollectionUtils.isNotEmpty(localRejects)) {
            for (DataConfigDO localReject : localRejects) {
                replaceConfigService.removeById(localReject.getId());
            }
        }
        List<DataConfigDO> rejects = dataConfigDos.stream().filter(dataConfigDO -> dataConfigDO.getSubType().equals("REJECT_REASON")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(rejects)) {
            for (DataConfigDO reject : rejects) {
                replaceConfigService.saveOrUpdate(reject);
            }
        }
    }

    @Autowired
    PassengerCategoryServiceImpl passengerCategoryService;
    private void addServiceSupport(BusinessDataSyncDTO businessDataSyncDTO) {
        log.info("【机场端接收航司端的旅客类别数据】收到请求: 【{}】", JSONUtil.toJsonStr(businessDataSyncDTO));
        List<PassengerCategoryConfigureDepository> passengerCategoryConfigureDepositories = businessDataSyncDTO.getPassengerCategoryConfigureDepositories();
        if (CollectionUtils.isNotEmpty(passengerCategoryConfigureDepositories)){
            for (PassengerCategoryConfigureDepository depository : passengerCategoryConfigureDepositories) {
                passengerCategoryService.saveOrUpdate(depository);
            }
        }
    }

    @Autowired
    CompensationRuleConfigService compensationRuleConfigService;
    private void addRule(BusinessDataSyncDTO businessDataSyncDTO) {
        log.info("【机场端接收航司端的规则数据】收到请求: 【{}】", JSONUtil.toJsonStr(businessDataSyncDTO));
        List<CompensationRuleConfig> compensationRuleConfigs = businessDataSyncDTO.getCompensationRuleConfigs();
        if (CollectionUtils.isNotEmpty(compensationRuleConfigs)){
            for (CompensationRuleConfig compensationRuleConfig : compensationRuleConfigs) {
                compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
            }
        }
    }

    @Autowired
    CabinConfigService cabinConfigService;

    @Override
    public void compensationConfigurationDataSync(BusinessConfigureDataCoordinateDTO businessDataDTO) {
        log.info("【机场端接收航司端的业务数据】收到请求: 【{}】", JSONUtil.toJsonStr(businessDataDTO));
        BusinessDataSyncDTO dataSyncDTO = JSON.parseObject(businessDataDTO.getData(), BusinessDataSyncDTO.class);

        String key = dataSyncDTO.getDataType();
        Consumer<BusinessDataSyncDTO> function = configurationDataHandlerMap.get(key);
        if (function != null) {
            function.accept(dataSyncDTO);
        } else {
            throw new IllegalArgumentException("No method found for business type: " + dataSyncDTO.getBusinessType() + " and data type: " + dataSyncDTO.getDataType());
        }

    }

    private void addCabin(BusinessDataSyncDTO dataSyncDTO){
        CabinConfigDO cabinConfigDO = dataSyncDTO.getCabinConfigDO();
        cabinConfigService.createCabinConfig(cabinConfigDO);
    }

}
