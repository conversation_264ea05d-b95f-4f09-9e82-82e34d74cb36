package com.swcares.aps.compensation.impl.privilege.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;


import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeItemService
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/13 16:30
 * @Version 1.0
 */
public interface BusinessPrivilegeItemService extends IService<AirlineBusinessPrivilegeItem> {
    public int deleteBatch(List<Long> ids);
}
