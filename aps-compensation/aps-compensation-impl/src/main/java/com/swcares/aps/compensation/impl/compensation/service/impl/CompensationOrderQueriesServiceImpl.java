package com.swcares.aps.compensation.impl.compensation.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationOrderQueriesMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationFactory;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderQueriesService;
import com.swcares.aps.compensation.impl.irregularflight.enums.RowPermissionTableName;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationAuditInfoMapper;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationBaseInfoPageDTO;
import com.swcares.aps.compensation.model.compensation.vo.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationAuditInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.component.permission.util.RowPermissionUtil;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @ClassName：CompensationOrderQueriesServiceImpl
 * @Description：补偿单查询服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 13:43
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationOrderQueriesServiceImpl implements CompensationOrderQueriesService {

    private CompensationOrderQueriesMapper compensationOrderQueriesMapper;

    private CompensationAuditInfoMapper compensationAuditInfoMapper;

    private CompensationFactory compensationFactory;

    private BaggageAccidentMapper baggageAccidentMapper;

    @Autowired
    public CompensationOrderQueriesServiceImpl(CompensationOrderQueriesMapper compensationOrderQueriesMapper,
                                               CompensationAuditInfoMapper compensationAuditInfoMapper,
                                               CompensationFactory compensationFactory,
                                               BaggageAccidentMapper baggageAccidentMapper){
        this.compensationOrderQueriesMapper=compensationOrderQueriesMapper;
        this.compensationAuditInfoMapper=compensationAuditInfoMapper;
        this.compensationFactory=compensationFactory;
        this.baggageAccidentMapper = baggageAccidentMapper;
    }

    /**
     * @title compensationBaseInfoPage
     * @description 分页查询最基础信息
     * <AUTHOR>
     * @date 2022/3/11 10:17
     * @param compensationBaseInfoPage
     * @return
     */
    @Override
    public IPage<CompensationBaseInfoVO> compensationBaseInfoPage(CompensationBaseInfoPageDTO compensationBaseInfoPage) {

        /**
         * 字段权限设置
         */
        Map<String, List<String>> rowPermissionValues = RowPermissionUtil
                .getRowValues(RowPermissionTableName.COMPENSATION, RowPermissionTableName.ACCIDENTTYPES, RowPermissionTableName.WORKSTATIONS);
        List<String> accidentTypes = rowPermissionValues.get(RowPermissionTableName.ACCIDENTTYPES);
        List<String> workStations = rowPermissionValues.get(RowPermissionTableName.WORKSTATIONS);
        compensationBaseInfoPage.setAccidentTypes(accidentTypes);
        compensationBaseInfoPage.setWorkStations(workStations);
        //TODO 查询条件数据返回中包含了很多mysql函数，后续考虑将函数逻辑迁移至程序处理（考虑使用拦截器）
        IPage<CompensationBaseInfoVO> page = compensationOrderQueriesMapper.compensationBaseInfoPage(compensationBaseInfoPage, compensationBaseInfoPage.createPage());
        List<CompensationBaseInfoVO> records = page.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            List<String> orderIds = records.stream().map(CompensationBaseInfoVO::getId).map(String::valueOf).collect(Collectors.toList());
            Map<String, String> loginUserCanAuditOrderIdToTaskIdMp = getLoginUserCanAuditOrderIdToTaskIdMp(orderIds);
            records.forEach(t->{
                String orderId = String.valueOf(t.getId());
                if(loginUserCanAuditOrderIdToTaskIdMp.containsKey(orderId)){
                    t.setToExamine("Y");
                    t.setTaskId(loginUserCanAuditOrderIdToTaskIdMp.get(orderId));
                }else{
                    t.setToExamine("N");
                }
            });
        }
        return page;
    }

    @Override
    public Map<String,String> getLoginUserCanAuditOrderIdToTaskIdMp(List<String> orderIds) {
        String userId = String.valueOf(UserContext.getUserId());
        CompensationAuditInfoDTO compensationAuditInfoDTO=new CompensationAuditInfoDTO();
        compensationAuditInfoDTO.setOrderIds(orderIds);
        compensationAuditInfoDTO.setUserId(userId);

        List<CompensationAuditInfoVO> compensationAuditInfoSearchResult = compensationAuditInfoSearch(compensationAuditInfoDTO);
        if(CollectionUtils.isNotEmpty(compensationAuditInfoSearchResult)){
            Map<String, String> result = compensationAuditInfoSearchResult.stream().collect(Collectors.toMap(CompensationAuditInfoVO::getOrderId, CompensationAuditInfoVO::getTaskId));
            return result;
        }
        return Collections.EMPTY_MAP;
    }

    /**
     * @title AccidentCompensation
     * @description 补偿单明细查询
     * <AUTHOR>
     * @date 2022/3/11 10:17
     * @param orderId
     * @return
     */
    @Override
    public AccidentCompensationDTO compensationDetailSearch(String orderId) {
        AccidentCompensationDTO compensation = compensationFactory.createByOrderId(orderId);
        return compensation;
    }


    /**
     * @title compensationAuditInfoSearch
     * @description 条件查询补偿单审核人信息
     * <AUTHOR>
     * @date 2022/3/10 9:34
     * @param compensationAuditInfoDTO
     * @return
     */
    @Override
    public List<CompensationAuditInfoVO> compensationAuditInfoSearch(CompensationAuditInfoDTO compensationAuditInfoDTO) {
        LambdaQueryWrapper<CompensationAuditInfoDO> lambdaQuery = Wrappers.lambdaQuery();
        if(StringUtils.isNotEmpty(compensationAuditInfoDTO.getUserId())){
            lambdaQuery.eq(CompensationAuditInfoDO::getAuditorId,compensationAuditInfoDTO.getUserId());
        }
        if(CollectionUtils.isNotEmpty(compensationAuditInfoDTO.getOrderIds())){
            lambdaQuery.in(CompensationAuditInfoDO::getOrderId,compensationAuditInfoDTO.getOrderIds());
        }
        List<CompensationAuditInfoDO> compensationAuditInfoDOS = compensationAuditInfoMapper.selectList(lambdaQuery);
        final Map<Long, List<CompensationAuditInfoDO>> orderIdMp = Optional.ofNullable(compensationAuditInfoDOS).map(Collection::stream).orElse(Stream.empty())
                .collect(Collectors.groupingBy(CompensationAuditInfoDO::getOrderId));

        List<CompensationAuditInfoVO> result = orderIdMp.keySet().stream().map(orderId -> {
            List<CompensationAuditInfoDO> list = orderIdMp.get(orderId);
            List<String> userIds = list.stream().map(t -> String.valueOf(t.getAuditorId())).collect(Collectors.toList());
            String taskId = list.get(0).getTaskId();
            return new CompensationAuditInfoVO(String.valueOf(orderId), userIds, taskId);
        }).collect(Collectors.toList());
        return result;
    }

    @Override
    public IPage<Object> findCashCompensationList(CompensationBaseInfoPageDTO compensationBaseInfoPageDTO) {
        compensationBaseInfoPageDTO.setUserId(UserContext.getUserId().toString());
        IPage<CompensationBaseInfoVO> baseInfoVOIPage = compensationBaseInfoPage(compensationBaseInfoPageDTO);
        List<CompensationBaseInfoVO> records = baseInfoVOIPage.getRecords();
        List<Object> list = new ArrayList<>();
        if(CollectionUtils.isNotEmpty(records)){
            records.forEach(compensationBaseInfoVO -> {
                String format = compensationBaseInfoVO.getCreatedTime().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
                format = format.replaceAll("T", " ").replaceAll("-", "/");
                if("2".equals(compensationBaseInfoVO.getAccidentType())){
                    BaggageAccidentInfoVO baggageAccidentInfoVO = baggageAccidentMapper.findById(compensationBaseInfoVO.getAccidentId().toString());
                    CompensationAbnormalBaggageExtCashVO compensationAbnormalBaggageExtCashVO =
                            ObjectUtils.copyBean(compensationBaseInfoVO, CompensationAbnormalBaggageExtCashVO.class);
                    compensationAbnormalBaggageExtCashVO.setPaxName(baggageAccidentInfoVO.getPaxName());
                    compensationAbnormalBaggageExtCashVO.setCompensationCreatedTime(format);
                    list.add(compensationAbnormalBaggageExtCashVO);
                }
                else if("1".equals(compensationBaseInfoVO.getAccidentType())){
                    AccidentCompensationDTO accidentCompensationDTO = compensationDetailSearch(compensationBaseInfoVO.getId().toString());
                    List<CompensationPaxInfoDO> compensationPaxInfo = accidentCompensationDTO.getCompensationPaxInfo();
                    CompensationAbnormalFlightExtCashVO compensationAbnormalFlightExtCashVO =
                            ObjectUtils.copyBean(compensationBaseInfoVO, CompensationAbnormalFlightExtCashVO.class);
                    //婴儿人数
                    int babyCount = 0;
                    //已赔偿人数
                    int count = 0;
                    for (CompensationPaxInfoDO compensationPaxInfoDO : compensationPaxInfo) {
                        if(ReceiveStatusEnum.RECEIVED.getKey().equals(compensationPaxInfoDO.getReceiveStatus())) {
                            count++;
                            //若该已赔偿旅客携带婴儿，则数量需要加1
                            if(StringUtils.isNotEmpty(compensationPaxInfoDO.getWithBaby()) && "1".equals(compensationPaxInfoDO.getWithBaby())) count++;
                        }
                        if(StringUtils.isNotEmpty(compensationPaxInfoDO.getWithBaby()) && "1".equals(compensationPaxInfoDO.getWithBaby())) babyCount++;
                    }
                    compensationAbnormalFlightExtCashVO.setAllCompensate(compensationPaxInfo.size()+babyCount);
                    compensationAbnormalFlightExtCashVO.setHasCompensate(count);
                    compensationAbnormalFlightExtCashVO.setCompensationCreatedTime(format);
                    list.add(compensationAbnormalFlightExtCashVO);
                }
            });
        }
        IPage<Object> iPage = new Page<Object>(baseInfoVOIPage.getCurrent(),baseInfoVOIPage.getSize(),list.size());
        iPage.setRecords(list);
        return iPage;
    }

    @Override
    public IPage<CashBusinessCostsDetailVO> getCashBusinessCostsDetail(CompensationCashReportDTO dto) {
        //关闭Mybatis-plus自动优化功能
        return compensationOrderQueriesMapper.getCashBusinessCostsDetail(dto, dto.createPage().setOptimizeCountSql(false));

    }

    @Override
    public IPage<CashPayDetailVO> getCashPayDetail(CompensationCashReportDTO dto) {
        //关闭Mybatis-plus自动优化功能
        return compensationOrderQueriesMapper.getCashPayDetail(dto,dto.createPage().setOptimizeCountSql(false));
    }

    @Override
    public List<CashBusinessCostsDetailVO> getCashBusinessCostsDetailReport(CompensationCashReportDTO dto) {
        return compensationOrderQueriesMapper.getCashBusinessCostsDetailReport(dto);
    }

    @Override
    public IPage<CashPayDetailVO> getCashPayDetailReport(CompensationCashReportDTO dto) {
        return compensationOrderQueriesMapper.getCashPayDetailReport(dto, dto.createPage());
    }
}
