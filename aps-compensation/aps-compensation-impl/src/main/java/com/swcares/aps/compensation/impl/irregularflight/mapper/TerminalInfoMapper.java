package com.swcares.aps.compensation.impl.irregularflight.mapper;

import com.swcares.aps.compensation.model.irregularflight.vo.CityCodeVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName：TerminalInfoMapper
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/7/25 11:18
 * @version： v1.0
 */
@Mapper
public interface TerminalInfoMapper {
    @Select("select CONCAT(airport_name,airport_3code) as cityNameCode from bd_airport_info")
    List<CityCodeVO> getTerminal();
}
