package com.swcares.aps.compensation.impl.baggage.luggage.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.baggage.luggage.constant.LuggageException;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageConsumptionReasonEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageConsumptionStatusEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageStockStatusEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageConsumptionMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageInfoMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageManagementMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageStockSaveInfoMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockOpService;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensateStockDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageConsumptionDetailDO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageStockInfoDO;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

/**
 * ClassName：LuggageStockServiceImpl <br>
 * Description：箱包库存操作service <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/4 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
@Transactional
public class LuggageStockOpServiceImpl implements LuggageStockOpService {

    @Autowired
    private LuggageStockSaveInfoMapper stockSaveInfoMapper;

    @Autowired
    private LuggageManagementMapper luggageManagementMapper;

    @Autowired
    private LuggageInfoMapper luggageInfoMapper;

    @Autowired
    private LuggageConsumptionMapper luggageConsumptionMapper;

    @Autowired
    private Redisson redisson;

    private static final String REDISSON_PRE_KEY="LuggageStockOp_";

    /**
     * 10秒超时
     */
    private static final Long REPLACE_RULE_SAVE_REDIS_LOCK_TIME=10L;


    /**
     * Title：补偿单添加库存 <br>
     * Description：实物（箱包）补偿单 审核不通过、关闭未领取、逾期未领取 需要补回库存 <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageCompensateStockDTO <br>
     * @return  <br>
     */
    @Override
    public void increaseByCompensation(LuggageCompensateStockDTO luggageCompensateStockDTO) {
        LuggageConsumptionDetailDO detailDO = createConsumptionDetailDTO(luggageCompensateStockDTO,LuggageConsumptionStatusEnum.BACK.getKey());

        int insert = luggageConsumptionMapper.insert(detailDO);

        if(insert<=0){
            throw new BusinessException(LuggageException.OP_STOCK_ERROR,"箱包消耗明细插入异常");
        }

        LuggageStockInfoDTO luggageStockInfoDTO=new LuggageStockInfoDTO();
        luggageStockInfoDTO.setOperatorId(luggageCompensateStockDTO.getOperatorId());
        luggageStockInfoDTO.setLuggageId(luggageCompensateStockDTO.getLuggageId());
        luggageStockInfoDTO.setAmount(luggageCompensateStockDTO.getAmount());
        luggageStockInfoDTO.setStatus(LuggageStockStatusEnum.INCREASE.getKey());
        luggageStockInfoDTO.setLuggageNo(luggageCompensateStockDTO.getLuggageNo());
        luggageStockInfoDTO.setReason("补偿单"+luggageCompensateStockDTO.getCompensationNo()+"退回,原因:"+LuggageConsumptionReasonEnum.build(luggageCompensateStockDTO.getReason()).getValue());
        luggageStockInfoDTO.setTenantId(luggageCompensateStockDTO.getTenantId());
        Long stock = this.updateLuggageStock(luggageStockInfoDTO);
        //补回库存时，同时也减少补偿总数
        log.info("【aps-compensation-impl】修改总补偿数,{}",JSONUtil.toJsonStr(luggageStockInfoDTO));
        luggageInfoMapper.updateCompensate(luggageStockInfoDTO.getLuggageId(),luggageStockInfoDTO.getAmount());

        detailDO.setStock(stock);
        luggageConsumptionMapper.updateById(detailDO);
    }


    /**
     * Title： 补偿单减少库存<br>
     * Description：提交实物（箱包）补偿单时需要扣减库存  <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageCompensateStockDTO <br>
     * @return  <br>
     */
    @Override
    public void decreaseByCompensation(LuggageCompensateStockDTO luggageCompensateStockDTO) {
        LuggageConsumptionDetailDO detailDO = createConsumptionDetailDTO(luggageCompensateStockDTO,LuggageConsumptionStatusEnum.CONSUME.getKey());

        int insert = luggageConsumptionMapper.insert(detailDO);

        if(insert<=0){
            throw new BusinessException(LuggageException.OP_STOCK_ERROR,"箱包消耗明细插入异常");
        }

        LuggageStockInfoDTO luggageStockInfoDTO=new LuggageStockInfoDTO();
        luggageStockInfoDTO.setOperatorId(luggageCompensateStockDTO.getOperatorId());
        luggageStockInfoDTO.setLuggageId(luggageCompensateStockDTO.getLuggageId());
        luggageStockInfoDTO.setAmount(0-luggageCompensateStockDTO.getAmount());
        luggageStockInfoDTO.setStatus(LuggageStockStatusEnum.REDUCE.getKey());
        luggageStockInfoDTO.setLuggageNo(luggageCompensateStockDTO.getLuggageNo());
        luggageStockInfoDTO.setReason("补偿单"+luggageCompensateStockDTO.getCompensationNo()+"消耗,原因:"+ LuggageConsumptionReasonEnum.build(luggageCompensateStockDTO.getReason()).getValue());
        Long stock = this.updateLuggageStock(luggageStockInfoDTO);
        //减少库存的时候，同时增加补偿总数
        log.info("【aps-compensation-impl】修改总补偿数,{}",JSONUtil.toJsonStr(luggageStockInfoDTO));
        luggageInfoMapper.updateCompensate(luggageStockInfoDTO.getLuggageId(),luggageStockInfoDTO.getAmount());

        detailDO.setStock(stock);
        luggageConsumptionMapper.updateById(detailDO);
    }

    /**
     * Title：箱包库存数量修改-增加/减少 <br>
     * Description：箱包库存数量修改-增加/减少 <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param dto   <br>
     * @return  <br>
     */
    @Override
    public Long updateLuggageStock(LuggageStockInfoDTO dto) {
        log.info("【aps-compensation-impl】实物(箱包)库存操作开始，{}", JSONUtil.toJsonStr(dto));
        String lockKey=createLockKey(dto.getLuggageId());
        RLock lock = redisson.getLock(lockKey);
        Long stock=-1L;
        try{
            boolean resLock = lock.tryLock(REPLACE_RULE_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){
                int result=  luggageManagementMapper.updateLuggageStockDetailed(dto);
                if (result>0){
                    String createdBy =luggageInfoMapper.findCreatedBy(dto.getOperatorId());
                    //添加成功调用存管理接口进行明细新增
                    LuggageStockInfoDO stockInfoDO = new LuggageStockInfoDO();
                    stock = luggageInfoMapper.selectLuggageStock(dto.getLuggageNo());
                    stockInfoDO.setStock(stock);
                    stockInfoDO.setExecutor(createdBy);
                    stockInfoDO.setLuggageId(dto.getLuggageId());
                    stockInfoDO.setLuggageNo(dto.getLuggageNo());
                    stockInfoDO.setAmount(dto.getAmount());
                    stockInfoDO.setReason(dto.getReason());
                    stockInfoDO.setStatus(dto.getStatus());
                    stockInfoDO.setCreatedBy(String.valueOf(dto.getOperatorId()));
                    stockInfoDO.setUpdatedBy(String.valueOf(dto.getOperatorId()));
                    stockInfoDO.setTenantId(dto.getTenantId());
                    log.info("【aps-compensation-impl-luggage】新增箱包库存明细：[{}]",dto.toString());

                    stockSaveInfoMapper.insert(stockInfoDO);
                }else {
                    throw new BusinessException(LuggageException.OUT_OF_STOCK);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }
            throw new BusinessException(LuggageException.OP_STOCK_ERROR,e.getMessage());
        }finally {
            lock.unlock();
        }
        return stock;
    }

    /**
     * Title：创建分布式锁KEY <br>
     * Description：创建分布式锁KEY <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageId  <br>
     * @return  分布式锁KEY<br>
     */
    private String createLockKey(Long luggageId) {
        return REDISSON_PRE_KEY+luggageId;
    }

    @NotNull
    private LuggageConsumptionDetailDO createConsumptionDetailDTO(LuggageCompensateStockDTO luggageCompensateStockDTO,String status) {
        LuggageConsumptionDetailDO detailDO=new LuggageConsumptionDetailDO();
        detailDO.setAmount(luggageCompensateStockDTO.getAmount());
        if(LuggageConsumptionStatusEnum.CONSUME.getKey().equals(status)){
            detailDO.setAmount(0-luggageCompensateStockDTO.getAmount());
        }
        detailDO.setCreatedBy(String.valueOf(luggageCompensateStockDTO.getOperatorId()));
        detailDO.setCompensationId(luggageCompensateStockDTO.getCompensationId());
        detailDO.setCompensationNo(luggageCompensateStockDTO.getCompensationNo());
        detailDO.setExecutor(luggageInfoMapper.findCreatedBy(luggageCompensateStockDTO.getOperatorId()));
        detailDO.setLuggageId(luggageCompensateStockDTO.getLuggageId());
        detailDO.setLuggageNo(luggageCompensateStockDTO.getLuggageNo());
        detailDO.setReason(luggageCompensateStockDTO.getReason());
        detailDO.setStock(-1L);
        detailDO.setStatus(status);
        detailDO.setTenantId(luggageCompensateStockDTO.getTenantId());
        return detailDO;
    }
}