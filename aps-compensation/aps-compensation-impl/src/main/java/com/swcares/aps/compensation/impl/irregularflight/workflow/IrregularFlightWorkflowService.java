package com.swcares.aps.compensation.impl.irregularflight.workflow;

import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationSyntheticalSaveDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;

import java.util.Map;
import java.util.Optional;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow <br>
 * Description：TODO(用一句话描述该文件做什么) <br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月28日 9:30 <br>
 * @version v1.0 <br>
 */
public interface IrregularFlightWorkflowService {

    /**
     * Title：startAuditProcess <br>
     * Description： 发起审核流程<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:17 <br>
     * @param
     * @return
     */
    Map<String,Object> startAuditProcess(CompensationSyntheticalSaveDTO dto) throws Exception;

    /**
     * Title： submitterWorkflow<br>
     * Description： submitter节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:18 <br>
     * @param
     * @return
     */
    Map<String,Object> submitterWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception;

    /**
     * Title： auditOperation<br>
     * Description： 执行审批<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:18 <br>
     * @param
     * @return
     */
    Map<String,Object> auditOperation(AuditProcessorDTO dto) throws Exception;

    /**
     * Title： cancelWorkflow<br>
     * Description： 流程取消节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:19 <br>
     * @param
     * @return
     */
    Map<String,Object> cancelWorkflow(NodeNoticeDTO nodeNoticeDTO);

    /**
     * Title： automaticWorkflow <br>
     * Description： 自动过审节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:19 <br>
     * @param
     * @return
     */
    Map<String,Object> automaticWorkflow(NodeNoticeDTO nodeNoticeDTO);

    /**
     * Title：commonWorkflow <br>
     * Description：普通审核节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:19 <br>
     * @param
     * @return
     */
    Map<String,Object> commonWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception;

    /**
     * Title： endWorkflow<br>
     * Description： 流程结束节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:20 <br>
     * @param
     * @return
     */
    Map<String,Object> endWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception;

    /**
     * Title： submitterAuditProcess<br>
     * Description：提交审核<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:43 <br>
     * @param
     * @return
     */
    Map<String,Object> submitterAuditProcess(CompensationSyntheticalSaveDTO dto);

    /**
     * Title：updCompensateStatusByAuditStatus <br>
     * Description： 更新赔偿单状态<br>
     * author：傅欣荣 <br>
     * date：2022/2/16 9:22 <br>
     * @param
     * @return
     */
    void updCompensateStatusByAuditStatus(NodeNoticeDTO nodeNoticeDTO);

    /**
     * Title：getNextNodeTaskVo <br>
     * Description： 查下一节点任务<br>
     * author：傅欣荣 <br>
     * date：2022/2/15 17:44 <br>
     * @param
     * @return
     */
    CurrentTaskActivityVO getNextNodeTaskVo(String busiKey);
}
