package com.swcares.aps.compensation.impl.irregularflight.controller;

import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.model.compensation.dto.OfflineGrantDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxFrozenDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FreezeOrderPaxDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxOrderInfoQueryDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.controller.CompensationPaxInfoController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/compensation/pax/info")
@Api(tags = "旅客接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
public class CompensationPaxInfoController extends BaseController {
    @Autowired
    private CompensationPaxInfoService compensationPaxInfoService;



    @PostMapping("/findChoicePax")
    @ApiOperation(value = "通过ID查询记录")
    public BaseResult<List<CompensationChoicePaxVO>> findChoicePax(@RequestBody CompensationPaxFrozenDTO paxFrozenDTO) {
        return ok(compensationPaxInfoService.findChoicePax(paxFrozenDTO));
    }

    @PostMapping("/findPaxOrderInfo")
    @ApiOperation(value = "通过paxName、idNo、paxId、flightNo、flightDate查询旅客赔偿记录")
    public BaseResult<List<PaxCompensationCountVO>> getPaxOrderInfo(@RequestBody PaxOrderInfoQueryDTO dto) {
        return ok(compensationPaxInfoService.getPaxOrderInfo(dto));
    }

    @PostMapping("/freezeOrderPax")
    @ApiOperation(value = "冻结||激活旅客")
    public BaseResult<Object> freezeOrderPax(@RequestBody FreezeOrderPaxDTO freezeOrderPaxDTO) {
        log.info("【赔偿单业务-冻结||激活旅客】方法freezeOrderPax, 当前用户：【{}】 ，请求参数：【{}】", UserContext.getUserId(), JSON.toJSONString(freezeOrderPaxDTO));
        return ok(compensationPaxInfoService.freezeOrderPax(freezeOrderPaxDTO));
    }


    @GetMapping("/findSelectedPax")
    @ApiOperation(value = "根据赔偿单id查询已选择旅客")
    public BaseResult<Object> findSelectedPax(@RequestParam Long orderId) {
        return ok(compensationPaxInfoService.findSelectedPax(orderId));
    }

    @PostMapping("/updatePaxReceiveInfo")
    @ApiOperation(value = "申领单保存时修改旅客申领信息")
    public BaseResult<Object> updatePaxReceiveInfo(@RequestParam List<Long> paxIds, @RequestParam String receiveChannel, @RequestParam String receiveWay, @RequestParam String receiveStatus) {
        return ok(compensationPaxInfoService.updatePaxReceiveInfo(paxIds,receiveChannel,receiveWay,receiveStatus));
    }

    @GetMapping("/findById")
    @ApiOperation(value = "通过ID查询旅客记录")
    public CompensationPaxInfoDO findById(@RequestParam Long paxId) {
        return compensationPaxInfoService.getById(paxId);
    }

    @PostMapping("offlineGrant")
    @ApiOperation(value = "线下发放")
    public BaseResult<Object> offlineGrant(@RequestBody @Validated OfflineGrantDTO offlineGrantDTO){
        return ok(compensationPaxInfoService.updateReceiveStatus(offlineGrantDTO.getPaxId(),offlineGrantDTO.getCompensationId(),ReceiveStatusEnum.RECEIVED.getKey()));
    }
}
