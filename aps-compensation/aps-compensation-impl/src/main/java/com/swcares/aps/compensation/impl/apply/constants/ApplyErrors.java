package com.swcares.aps.compensation.impl.apply.constants;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.constants.ApplyErrors <br>
 * Description：申领单错误编码 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021/11/25 15:18 <br>
 * @version v1.0 <br>
 */
public final class ApplyErrors {
    //未查询到相关信息，请核实！
    public static int APPLY_AUTH_PAX_ERROR = 30801;
    //您在该航班中的赔偿信息已领取/ 领取中！
    public static int APPLY_AUTH_PAX_ALREADY_RECEIVED = 30802;
    //您在该航班中的赔偿信息存在异常，请联系航司相关人员！
    public static int APPLY_AUTH_PAX_ABNORMAL = 30803;
    //请输入正确的短信验证码
    public static int APPLY_AUTH_PAX_PHONE = 30804;
    //获取openid异常
    public static int GET_OPENID_ERROR = 30805;
    //根据微信openid获取token失败
    public static int GET_TOKEN_ERROR = 308010;
    //补偿信息发生变更，请重新验证！
    public static int APPLY_AUTH_PAX_ORDER = 30806;
    //申领信息提交失败，请重新提交！
    public static int APPLY_SAVE_ERROR = 30807;
    //申领信息查询为空！
    public static int QUERY_RECORD_ERROR = 30808;
    //代领人未在该航班中，请检查信息后再次提交！
    public static int APPLY_AUTH_BEHALF_PAX_ERROR = 30809;
    //处理审核-异常
    public static final int AUDIT_ERROR = 30815;
    //审批任务不存在
    public static final int CPC_AUDIT_ERROR = 30816;
    //审批任务不存在
    public static final int CPC_TASK_ID_ERROR = 30817;
    //处理审核参数错误
    public static final int AUDIT_PARAM_ERROR = 30818;
    //旅客领取：获取的验证码在有效期内
    public static final int SMS_CODE_NOT_EXPIRED = 30820;
    //频繁调用短信接口，请稍后再试！
    public static final int SMS_FREQUENT_CALL_ERROR = 30821;
    //请先进行短信验证操作！
    public static final int SMS_VERIFICATION_ERROR = 30822;
    //参数错误，请检查参数！
    public static final int APPLY_PARAM_ERROR = 30823;
    //请先验证图形验证码！
    public static final int PAX_TELEPHONE_VALIDATE_FAIL = 30824;
    //频繁验证短信验证码失败,短信验证码已失效,请重新获取短信验证码
    public static int SMS_FREQUENTLY_VALIDATE_FAIL = 30825;
    //请先进行验证码验证操作！
    public static int CAPTCH_VERIFICATION_ERROR = 30826;
    //请先进行银联实名认证操作！
    public static int CHINAPAY_AUTH_VERIFICATION_ERROR = 30827;
    //common error
    public static int APPLY_COMMON_ERROR = 30828;

}
