package com.swcares.aps.compensation.impl.compensation.service.handler;

import com.beust.jcommander.internal.Lists;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageConsumptionReasonEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockOpService;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensateStockDTO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName：LuggageStockIncreaseHandlerServiceImpl
 * @Description：箱包库存库存添加status状态;
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 15:21
 * @version： v1.0
 */
@Service
@Transactional
@Slf4j
public class LuggageStockIncreaseHandlerServiceImpl implements CompensationOrderStatusHandlerService {


    private final static String COMPENSATE_TYPE_CASH_DICT_ID="1"; //"211"; //现金
    private final static String COMPENSATE_TYPE_VIRTUAL_DICT_ID="2"; //"212"; //虚拟
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3"; //"213"; //实物

    private final static String MATERIAL_TYPE_LUGGAGE="11";

    private Map<String, List<String>> allowTargetStatusMp=new HashMap<>();

    @Autowired
    private LuggageStockOpService luggageStockOpService;

    @PostConstruct
    private void init(){
        //库存回收的状态变更

        //审核中->审核不通过
        //审核中->驳回
        allowTargetStatusMp.put(CompensateStatusEnum.AUDIT_PROCESS.getKey(), Lists.newArrayList(CompensateStatusEnum.AUDIT_FAILED.getKey(),
                CompensateStatusEnum.REJECT.getKey()));

        //审核通过->逾期
        //审核通过->关闭
        allowTargetStatusMp.put(CompensateStatusEnum.AUDIT_PASS.getKey(), Lists.newArrayList(CompensateStatusEnum.AUDIT_PASS_OVERDUE.getKey(),
                CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey()));

        //生效->关闭
        //生效->逾期
        allowTargetStatusMp.put(CompensateStatusEnum.TAKE_EFFECT.getKey(), Lists.newArrayList(CompensateStatusEnum.CLOSE.getKey(),
                CompensateStatusEnum.OVERDUE.getKey()));

        //驳回->逾期
        allowTargetStatusMp.put(CompensateStatusEnum.REJECT.getKey(),Lists.newArrayList(CompensateStatusEnum.OVERDUE.getKey()));
    }

    @Override
    public void handler(AccidentCompensationDTO compensation, String targetStatus) {
        if(!support(compensation,targetStatus)){
            return;
        }//判断该赔偿单的旅客是否已领取，若已领取不需要退回库存
        else if(ReceiveStatusEnum.RECEIVED.getKey().equals(compensation.getCompensationPaxInfo().get(0).getReceiveStatus())){
            return;
        }
        List<CompensationMaterialInfoDO> luggageMaterials = compensation.getCompensationMaterialInfo().stream()
                .filter(t -> MATERIAL_TYPE_LUGGAGE.equals(t.getMaterialType()))
                .collect(Collectors.toList());

        for(CompensationMaterialInfoDO compensationMaterialInfoDO:luggageMaterials){
            LuggageCompensateStockDTO compensateStockDTO=new LuggageCompensateStockDTO();
            compensateStockDTO.setAmount(compensationMaterialInfoDO.getAmount());
            compensateStockDTO.setCompensationId(compensation.getId());
            compensateStockDTO.setCompensationNo(compensation.getOrderNo());
            compensateStockDTO.setLuggageId(compensationMaterialInfoDO.getMaterialId());
            compensateStockDTO.setLuggageNo(compensationMaterialInfoDO.getMaterialNo());
//            compensateStockDTO.setReason(targetStatus);
            compensateStockDTO.setReason(LuggageConsumptionReasonEnum.buildStatus(targetStatus).getKey());
            compensateStockDTO.setOperatorId(UserContext.getUser().getEmployeeId());
            luggageStockOpService.increaseByCompensation(compensateStockDTO);
        }
    }

    //实物补偿类型为箱包的,且状态满足allowTargetStatusMp
    private boolean support(AccidentCompensationDTO compensation, String targetStatus){

        //不是实物补偿单,肯定没有库存操作
        if(!COMPENSATE_TYPE_MATERIAL_DICT_ID.equals(compensation.getCompensateType())) {
            return false;
        }

        //补偿单状态不满足库存添加操作
        String sourceStatus = compensation.getStatus();
        List<String> targetStatusList = allowTargetStatusMp.get(sourceStatus);
        if(CollectionUtils.isEmpty(targetStatusList) || !targetStatusList.contains(targetStatus)){
            return false;
        }

        //判断是否箱包实物补偿单
        List<CompensationMaterialInfoDO> compensationMaterialInfo = compensation.getCompensationMaterialInfo();
        Optional<CompensationMaterialInfoDO> luggageMaterial = compensationMaterialInfo.stream()
                .filter(t -> MATERIAL_TYPE_LUGGAGE.equals(t.getMaterialType()))
                .findAny();
        if(!luggageMaterial.isPresent()){
            return false;
        }
        return true;
    }

}
