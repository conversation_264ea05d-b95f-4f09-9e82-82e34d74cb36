package com.swcares.aps.compensation.impl.baggage.luggage.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.baggage.luggage.constant.LuggageException;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageStockStatusEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageInfoMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageManagementMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageStockSaveInfoMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageManagementService;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindLuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageStockInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: LuggageManagementServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/2/9 9:41
 */
@Service
@Slf4j
@Primary
public class LuggageManagementServiceImpl extends ServiceImpl<LuggageInfoMapper, LuggageInfoDO> implements LuggageManagementService {
    //箱包编号标识
    private static final String LUGGAGE_NO = "XB";

    @Autowired
    private LuggageStockSaveInfoMapper stockSaveInfoMapper;

    @Autowired
    private LuggageManagementMapper luggageManagementMapper;

    @Autowired
    private LuggageInfoMapper luggageInfoMapper;

    @Override
    public LuggageInfoDO findById(String id) {
        return luggageInfoMapper.findById(id);
    }

    @Override
    public List<String> findSegment(String code) {
        String[] codes = code.split(",");
        return baseMapper.findSegment(codes);
    }

    @Override
    public IPage<LuggageInfoVO> findLuggageList(LuggageInfoPageDTO dto) {
        return baseMapper.findLuggageList(dto,dto.createPage());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLuggage(LuggageInfoDTO dto) {
        log.info("【aps-compensation-impl-luggage】新增箱包信息：[{}]",dto.toString());
        LuggageInfoDO infoDO = ObjectUtils.copyBean(dto, LuggageInfoDO.class);
        //判断是否存在该箱包
        Integer hasExistLuggage = baseMapper.hasExistLuggage(dto);
        if(hasExistLuggage>0){
            throw new BusinessException(LuggageException.ALREADY_EXIST_LUGGAGE);
        }
        //创建人
        String createdBy = baseMapper.findCreatedBy(UserContext.getCurrentUser().getEmployeeId());
//        String createdBy = "zhang";
        infoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        //箱包编号
        infoDO.setLuggageNo(makeLuggageNo(dto.getSegment()));
        //累计补偿
        infoDO.setCompensate(0L);
        infoDO.setSizes(dto.getSize());
        int re = baseMapper.insert(infoDO);
        if(re>0){
            //添加成功调用存管理接口进行明细新增
            LuggageStockInfoDTO stockInfoDTO = new LuggageStockInfoDTO();
            stockInfoDTO.setStock(dto.getStock());
            stockInfoDTO.setExecutor(createdBy);
            stockInfoDTO.setLuggageId(infoDO.getId());
            stockInfoDTO.setLuggageNo(infoDO.getLuggageNo());
            stockInfoDTO.setAmount(dto.getStock());
            stockInfoDTO.setReason(LuggageStockStatusEnum.NEWLY.getValue());
            stockInfoDTO.setStatus(LuggageStockStatusEnum.NEWLY.getKey());
            saveLuggageStockDetailed(stockInfoDTO);
        }
    }

    @Override
    public void removeLuggage(Long id) {
        baseMapper.removeById(id);
    }

    @Override
    public void updateLuggage(LuggageInfoDTO dto) {
        log.info("【aps-compensation-impl-luggage】修改箱包信息：[{}]",dto.toString());
        //判断是否存在该箱包
        Integer hasExistLuggage = baseMapper.hasExistLuggage(dto);
        if(hasExistLuggage>0){
            log.error("【aps-compensation-impl-luggage】该行站已存在同款箱包:[{}]",dto.toString());
            throw new BusinessException(LuggageException.ALREADY_EXIST_LUGGAGE);
        }
        baseMapper.updateLuggage(dto);
    }


    private void saveLuggageStockDetailed(LuggageStockInfoDTO dto) {
        log.info("【aps-compensation-impl-luggage】新增箱包库存明细：[{}]",dto.toString());
        LuggageStockInfoDO stockInfoDO = ObjectUtils.copyBean(dto, LuggageStockInfoDO.class);
//        stockInfoDO.setStock(baseMapper.selectLuggageStock(dto.getLuggageNo()));
        stockSaveInfoMapper.insert(stockInfoDO);
    }


    //唐康
    @Override
    public IPage<FindLuggageStockInfoVO> findLuggageStockDetailed(FindLuggageStockInfoDTO dto) {
        return luggageManagementMapper.findLuggageStockDetailed(dto, dto.createPage());
    }

    @Override
    public IPage<LuggageCompensateDetailVO> getLuggageCompensationReportDetail(LuggageCompensatePageDTO dto) {
        //关闭Mybatis-plus自动优化功能
        return luggageManagementMapper.getLuggageCompensationReportDetail(dto,dto.createPage().setOptimizeCountSql(false));
    }

    @Override
    public List<LuggageInfoVO> getLuggageManagementList(List<String> brand) {
        String[] arrayBrands = {};
        if(ObjectUtils.isNotEmpty(brand)){
            arrayBrands = brand.toArray(new String[brand.size()]);
        }
        List<LuggageInfoVO> luggageInfoVOS = baseMapper.getLuggageManagementList(arrayBrands);
        List<LuggageInfoVO> luggageInfoVOList = new ArrayList<>();
        List<String> list = new ArrayList<>();
        Map<String, List<LuggageInfoVO>> luggageMap = luggageInfoVOS.stream().collect(Collectors.groupingBy(LuggageInfoVO::getBrand));
        luggageInfoVOS.forEach(luggageInfoVO -> {
            List<Long> luggageSizes = luggageMap.get(luggageInfoVO.getBrand()).stream().map(LuggageInfoVO::getSize).distinct().collect(Collectors.toList());
            if(!list.contains(luggageInfoVO.getBrand())){
                list.add(luggageInfoVO.getBrand());
                luggageInfoVO.setLuggageSizes(luggageSizes);
                luggageInfoVOList.add(luggageInfoVO);
            }
        });
        return luggageInfoVOList;
    }

    @Override
    public IPage<LuggageCompensateDetailVO> getLuggageCompensationReport(LuggageCompensatePageDTO dto) {
        return luggageManagementMapper.getLuggageCompensationReport(dto,dto.createPage());
    }


    /**
    * @title makeLuggageNo
    * @description 生成箱包编号
    * @param segment
    * <AUTHOR>
    * @return java.lang.String
    * @date 2022/2/9 15:59
    */
    private String makeLuggageNo(String segment){
        StringBuffer luggageNo = new StringBuffer(LUGGAGE_NO);
        luggageNo.append(segment.substring(segment.length()-3));
        String today = new SimpleDateFormat("yyyyMMdd").format(new Date());
        luggageNo.append(today);
        Integer count = baseMapper.findTodayAmount(luggageNo.toString());
        count++;
        if (count>999){
            log.error("【aps-compensation-impl-luggage】今日箱包新增已到最大限制：999个");
            throw new BusinessException(LuggageException.MAX_NUMBER_REACHED);
        }
        return luggageNo.append(String.format("%03d", count)).toString();
    }
}
