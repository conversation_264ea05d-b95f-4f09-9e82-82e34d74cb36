package com.swcares.aps.compensation.impl.datasync.enums;

import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.baseframe.common.enums.JsonEnumValue;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.enums <br>
 * Description：事故单类型对应的业务code <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月18日 11:22 <br>
 * @version v1.0 <br>
 */
public enum AccidentTypeBusinessCodeEnum {
//    补偿类型-1不正常航班，2异常行李，3超售 4旅客投诉
    IRREGULAR_FLIGHT("1", BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT),
    ABNORMAL_BAGGAGE("2", BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE),
    OVERBOOKING("3", BusinessDataSyncConstant.BUSINESS_OVER_BOOKING),
    COMPLAINT("4", BusinessDataSyncConstant.BUSINESS_COMPLAINT);


    private AccidentTypeBusinessCodeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    @JsonEnumValue
    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static AccidentTypeBusinessCodeEnum build(String key) {
        return build(key, true);
    }

    public static AccidentTypeBusinessCodeEnum build(String key, boolean throwEx) {
        AccidentTypeBusinessCodeEnum typeEnum = null;
        for (AccidentTypeBusinessCodeEnum element : AccidentTypeBusinessCodeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + AccidentTypeBusinessCodeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
