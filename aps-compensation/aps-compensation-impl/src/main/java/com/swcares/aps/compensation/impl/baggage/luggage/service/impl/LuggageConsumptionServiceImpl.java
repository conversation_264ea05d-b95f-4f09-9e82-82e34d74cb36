package com.swcares.aps.compensation.impl.baggage.luggage.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.baggage.luggage.mapper.LuggageConsumptionMapper;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageConsumptionService;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageConsumptionDetailPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageConsumptionDetailDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @title: LuggageConsumptionServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/2/9 9:54
 */

@Service
@Slf4j
public class LuggageConsumptionServiceImpl extends ServiceImpl<LuggageConsumptionMapper,LuggageConsumptionDetailDO> implements LuggageConsumptionService {


	/**
	* @Author: 谭睿
	* @Description: 箱包消耗明细分页查询接口
	* @DateTime: 2022/2/10 14:23
	* @Params: [dto]
	* @Return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.luggage.model.vo.LuggageConsumptionDetailVO>
	*/
	@Override
	public IPage<LuggageConsumptionDetailVO> findDetailed(LuggageConsumptionDetailPageDTO dto) {
		log.info("【aps-compensation-impl-luggage】箱包消耗明细查询参数：[{}]",dto.toString());
		return baseMapper.findDetailed(dto,dto.createPage());
	}
}
