package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * ClassName：CompensateDroolsRuleCodeEnum <br>
 * Description：规则引擎Code枚举类<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月11日 10:28 <br>
 * @version v1.0 <br>
 */
public enum CompensateDroolsRuleCodeEnum {
    IRREGULAR_FLIGHT("IRREGULAR_FLIGHT"),
    ABNORMAL_LUGGAGE("ABNORMAL_LUGGAGE");


    private CompensateDroolsRuleCodeEnum(String value) {
        this.value = value;
    }

    private String value;

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
