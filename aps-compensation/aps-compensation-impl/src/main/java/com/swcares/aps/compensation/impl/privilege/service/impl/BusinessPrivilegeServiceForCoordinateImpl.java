package com.swcares.aps.compensation.impl.privilege.service.impl;

import cn.hutool.http.HttpStatus;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.constant.CompensationErrors;
import com.swcares.aps.compensation.impl.privilege.config.CoordinateConfig;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeChangeHistoryMapper;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapper;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapperForCoordinate;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeChangeHistoryService;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeItemService;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeServiceForCoordinate;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeChangeHistoryDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeItemDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeRequestDTO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeChangeHistory;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;
import com.swcares.aps.compensation.model.privilege.enums.CustomerCategoryEnum;
import com.swcares.aps.component.com.util.EmployeeInfoUtil;
import com.swcares.aps.cpe.coordinate.model.enums.DataCategoryEnum;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadRequestDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.PrivilegeDataUploadDTO;
import com.swcares.aps.cpe.coordinate.util.CoordinateApiUtil;
import com.swcares.aps.usercenter.model.tenant.dto.TenantAdditionalInfoDTO;
import com.swcares.aps.usercenter.remote.api.tenant.TenantInfoApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.idge.IdGenerate;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wang Yi
 * @Classname BusinessPrivilegeServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 09:05
 * @Version 1.0
 */
@Service
@Slf4j
public class BusinessPrivilegeServiceForCoordinateImpl extends ServiceImpl<BusinessPrivilegeMapperForCoordinate, AirlineBusinessPrivilege> implements BusinessPrivilegeServiceForCoordinate {

    @Autowired
    BusinessPrivilegeMapper businessPrivilegeMapper;

    @Autowired
    BusinessPrivilegeChangeHistoryMapper businessPrivilegeChangeHistoryMapper;

    @Autowired
    BusinessPrivilegeChangeHistoryService businessPrivilegeChangeHistoryService;

    @Autowired
    BusinessPrivilegeItemService businessPrivilegeItemService;

    @Autowired
    CoordinateConfig coordinateConfig;

    @Autowired
    TenantInfoApi tenantInfoApi;

    @Transactional
    public boolean create(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO) {
        log.info("【业务授权】创建业务授权{}", JSONUtil.toJsonStr(businessPrivilegeRequestDTO));
        //获取当前用户租户信息
        String tenantCode = UserContext.getCurrentUser().getTenantCode();
        String tenantName = UserContext.getCurrentUser().getTenantName();
        Long tenantId = UserContext.getCurrentUser().getTenantId();

        //检查是否有重复的业务授权数据
        LambdaQueryWrapper<AirlineBusinessPrivilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirlineBusinessPrivilege::getRecipientCode, businessPrivilegeRequestDTO.getRecipientCode());
        queryWrapper.eq(AirlineBusinessPrivilege::getTenantId, tenantId);

        List<AirlineBusinessPrivilege> existingBusinessPrivilege = businessPrivilegeMapper.selectList(queryWrapper);
        if (existingBusinessPrivilege != null && existingBusinessPrivilege.size() > 0) {
            throw new BusinessException(CompensationErrors.DUPLICATE_BUSINESS_PRIVILEGE_EXISTING, "机场","授权数据");
        }

        //插入业务授权数据
        AirlineBusinessPrivilege businessPrivilege = generateBusinessPrivilege(businessPrivilegeRequestDTO, tenantCode, tenantName);
        boolean saveResult = save(businessPrivilege);
        log.info("【业务授权】创建业务授权，结果为{}", JSONUtil.toJsonStr(businessPrivilege));
        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = generateBusinessPrivilegeItems(businessPrivilegeRequestDTO, businessPrivilege.getId());
        businessPrivilegeItemService.saveBatch(newBusinessPrivilegeItems);
        log.info("【业务授权】创建业务授权明细，结果为{}", JSONUtil.toJsonStr(newBusinessPrivilegeItems));

        //插入业务授权历史数据
        AirlineBusinessPrivilegeChangeHistory changeHistory = AirlineBusinessPrivilegeChangeHistory.creatAddBusinessPrivilegeHistory(businessPrivilege, newBusinessPrivilegeItems);
        businessPrivilegeChangeHistoryService.save(changeHistory);
        log.info("【业务授权】创建业务授权历史数据，结果为{}", JSONUtil.toJsonStr(changeHistory));

        //通知协同中心业务授权信息
        BusinessPrivilegeDTO dto = BusinessPrivilegeDTO.fromEntity(businessPrivilege);
        List<BusinessPrivilegeItemDTO> items = BusinessPrivilegeItemDTO.fromEntityList(newBusinessPrivilegeItems);
        dto.setBusinessPrivilegeItemList(items);

        PrivilegeDataUploadDTO uploadDTO = new PrivilegeDataUploadDTO();
        uploadDTO.setOperationType("ADD");
        uploadDTO.setData(JSONUtil.toJsonStr(dto));

        sendCoordinateMessage(JSONUtil.toJsonStr(uploadDTO));

        log.info("【业务授权】创建业务授权完成");
        //返回结果
        return true;
    }



    private static AirlineBusinessPrivilege generateBusinessPrivilege(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO, String tenantCode, String tenantName) {
        AirlineBusinessPrivilege businessPrivilege = new AirlineBusinessPrivilege();

        businessPrivilege.setGrantorCode(tenantCode);
        businessPrivilege.setGrantorCategory(CustomerCategoryEnum.AIRLINE.getCode());
        businessPrivilege.setGrantorName(tenantName);
        businessPrivilege.setRecipientCode(businessPrivilegeRequestDTO.getRecipientCode());
        businessPrivilege.setRecipientCategory(CustomerCategoryEnum.AIRPORT.getCode());
        businessPrivilege.setRecipientName(businessPrivilegeRequestDTO.getRecipientName());
        businessPrivilege.setDisabled(businessPrivilegeRequestDTO.isDisabled());
        businessPrivilege.setRemark(businessPrivilegeRequestDTO.getRemark());
        businessPrivilege.setUpdaterEmployeeName(EmployeeInfoUtil.getCurrentEmployeeName());
        businessPrivilege.setUpdaterJobNumber(EmployeeInfoUtil.getCurrentEmployeeJobNumber());

        return businessPrivilege;
    }

    @NotNull
    private static List<AirlineBusinessPrivilegeItem> generateBusinessPrivilegeItems(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO, Long businessPrivilegeId) {
        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = new ArrayList<>();
        businessPrivilegeRequestDTO.getBusinessTypeCodeList().forEach(businessTypeCode -> {
            AirlineBusinessPrivilegeItem businessPrivilegeItem = new AirlineBusinessPrivilegeItem();
            businessPrivilegeItem.setBusinessPrivilegeId(businessPrivilegeId);
            businessPrivilegeItem.setBusinessTypeCode(businessTypeCode);

            if (businessPrivilegeRequestDTO.getBankrollBusinessTypeCodeList().contains(businessTypeCode)){
                businessPrivilegeItem.setGrantBankroll(true);
            } else {
                businessPrivilegeItem.setGrantBankroll(false);
            }

            newBusinessPrivilegeItems.add(businessPrivilegeItem);
        });
        return newBusinessPrivilegeItems;
    }



    @Transactional
    public boolean update(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO) {
        //从当前用户获取租户相关信息
        String tenantCode = UserContext.getCurrentUser().getTenantCode();
        String tenantName = UserContext.getCurrentUser().getTenantName();


        log.info("【业务授权】更新业务授权{}", JSONUtil.toJsonStr(businessPrivilegeRequestDTO));

        //查找原有的业务授权数据
        LambdaQueryWrapper<AirlineBusinessPrivilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirlineBusinessPrivilege::getRecipientCode, businessPrivilegeRequestDTO.getRecipientCode());
        queryWrapper.eq(AirlineBusinessPrivilege::getGrantorCode, tenantCode);
        AirlineBusinessPrivilege existingBusinessPrivilege = businessPrivilegeMapper.selectOne(queryWrapper);

        if (existingBusinessPrivilege == null){
            //不存在原有的数据，创建新的业务授权
            return create(businessPrivilegeRequestDTO);
        }

        LambdaQueryWrapper<AirlineBusinessPrivilegeItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(AirlineBusinessPrivilegeItem::getBusinessPrivilegeId, existingBusinessPrivilege.getId());
        List<AirlineBusinessPrivilegeItem> existingBusiessPrivilegeItems = businessPrivilegeItemService.list(itemQuery);

        Map<AirlineBusinessPrivilegeItem, AirlineBusinessPrivilegeItem> toBeUpdatedBusinessPrivilegeItems = new HashMap<>();

        AirlineBusinessPrivilege newBusinessPrivilege = generateBusinessPrivilege(businessPrivilegeRequestDTO, tenantCode, tenantName);
        newBusinessPrivilege.setId(existingBusinessPrivilege.getId());
        updateById(newBusinessPrivilege);
        log.info("【业务授权】更新业务授权，结果为{}", JSONUtil.toJsonStr(newBusinessPrivilege));

        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = generateBusinessPrivilegeItems(businessPrivilegeRequestDTO, existingBusinessPrivilege.getId());

        AirlineBusinessPrivilegeChangeHistory changeHistory =
                AirlineBusinessPrivilegeChangeHistory.createUpdateBusinessPrivilegeHistory(existingBusinessPrivilege,
                        newBusinessPrivilege, existingBusiessPrivilegeItems, newBusinessPrivilegeItems);

        //比较原有的和新的数据
        for(AirlineBusinessPrivilegeItem newData : newBusinessPrivilegeItems){
            existingBusiessPrivilegeItems.stream().forEach(existingData ->
                {
                    if (existingData.getBusinessTypeCode().equalsIgnoreCase(newData.getBusinessTypeCode())){
                        newData.setId(existingData.getId());
                        toBeUpdatedBusinessPrivilegeItems.put(existingData, newData);
                    }
                }
            );
        }

        //删除不再有的业务授权
        existingBusiessPrivilegeItems.removeAll(toBeUpdatedBusinessPrivilegeItems.keySet());
        List<Long> deletedIds = existingBusiessPrivilegeItems.stream().map(AirlineBusinessPrivilegeItem::getId).collect(Collectors.toList());
        if (deletedIds != null && !deletedIds.isEmpty()) {
            businessPrivilegeItemService.deleteBatch(deletedIds);
            log.info("【业务授权】更新业务授权-删除不再有的业务授权，被删除的数据{}", JSONUtil.toJsonStr(existingBusinessPrivilege));
        }


        //添加要增加的业务授权
        newBusinessPrivilegeItems.removeAll(toBeUpdatedBusinessPrivilegeItems.values());
        if (!newBusinessPrivilegeItems.isEmpty()) {
            businessPrivilegeItemService.saveBatch(newBusinessPrivilegeItems);
            log.info("【业务授权】更新业务授权-添加要增加的业务授权，新增的数据{}", JSONUtil.toJsonStr(newBusinessPrivilegeItems));
        }

        //更新要修改的业务授权
        if (!toBeUpdatedBusinessPrivilegeItems.isEmpty()) {
            businessPrivilegeItemService.updateBatchById(toBeUpdatedBusinessPrivilegeItems.values());
            log.info("【业务授权】更新业务授权-更新要修改的业务授权，修改的数据{}", JSONUtil.toJsonStr(toBeUpdatedBusinessPrivilegeItems));
        }


        //保存所有的业务授权变更历史
        businessPrivilegeChangeHistoryService.save(changeHistory);
        log.info("【业务授权】更新业务授权-保存所有的业务授权变更历史，变更历史数据{}", JSONUtil.toJsonStr(changeHistory));

        //通知协同中心业务授权信息变更
        AirlineBusinessPrivilege updatedBusinessPrivilege = businessPrivilegeMapper.selectOne(queryWrapper);
        BusinessPrivilegeDTO dto = BusinessPrivilegeDTO.fromEntity(updatedBusinessPrivilege);
        List<AirlineBusinessPrivilegeItem> updatedBusiessPrivilegeItems = businessPrivilegeItemService.list(itemQuery);
        List<BusinessPrivilegeItemDTO> items = BusinessPrivilegeItemDTO.fromEntityList(updatedBusiessPrivilegeItems);
        dto.setBusinessPrivilegeItemList(items);

        PrivilegeDataUploadDTO uploadDTO = new PrivilegeDataUploadDTO();
        uploadDTO.setOperationType("UPDATE");
        uploadDTO.setData(JSONUtil.toJsonStr(dto));

        sendCoordinateMessage(JSONUtil.toJsonStr(uploadDTO));

        log.info("【业务授权】更新业务授权-更新业务授权成功");
        return true;
    }

//    @Override
//    public IPage<BusinessPrivilegeDTO> search(SearchBusinessPrivilegeDTO searchCriteria) {
//        if (searchCriteria == null){
//            searchCriteria = new SearchBusinessPrivilegeDTO();
//        }
//
//        IPage<AirlineBusinessPrivilege> searchResult =getBaseMapper().getBusinessPrivilegePage(searchCriteria, searchCriteria.createPage());
//
//        IPage<BusinessPrivilegeDTO> result = new Page<>(searchResult.getPages(), searchResult.getSize());
//        result.setCurrent(searchResult.getCurrent());
//        result.setPages(searchResult.getPages());
//        result.setTotal(searchResult.getTotal());
//        result.setPages(searchResult.getPages());
//        result.setRecords(BusinessPrivilegeDTO.fromEntityList(searchResult.getRecords()));
//
//        //为每个业务授权添加明细列表
//        if (result.getRecords() != null && !result.getRecords().isEmpty()) {
//            LambdaQueryWrapper detailsQuery = new LambdaQueryWrapper<AirlineBusinessPrivilegeItem>().in(
//                    AirlineBusinessPrivilegeItem::getBusinessPrivilegeId,
//                    searchResult.getRecords().stream().map(AirlineBusinessPrivilege::getId).collect(Collectors.toList())
//            ).orderByAsc(AirlineBusinessPrivilegeItem::getId, AirlineBusinessPrivilegeItem::getBusinessTypeCode);
//            List<AirlineBusinessPrivilegeItem> businessPrivilegeItems = businessPrivilegeItemService.list(detailsQuery);
//
//            Map<Long, List<BusinessPrivilegeItemDTO>> businessPrivilegeItemMap = businessPrivilegeItems.stream()
//                    .map(BusinessPrivilegeItemDTO::fromEntity)
//                    .collect(Collectors.groupingBy(BusinessPrivilegeItemDTO::getBusinessPrivilegeId));
//
//            //获取租户的旅客申领url
//            String passengerApplyUrl = getTenantPassengerApplyUrl();
//
//            //设置业务权限是否包含资金授权
//            result.getRecords().stream().forEach(businessPrivilege -> {
//                businessPrivilege.setBusinessPrivilegeItemList(businessPrivilegeItemMap.get(businessPrivilege.getId()));
//                businessPrivilegeItemMap.get(businessPrivilege.getId())
//                        .forEach(item -> {
//                            if (item.isGrantBankroll()) {
//                                businessPrivilege.setGrantBankroll(true);
//                                businessPrivilege.setPassengerApplyUrl(passengerApplyUrl);
//                                item.setPassengerApplyUrl(passengerApplyUrl);
//                            }
//                            CompensationAccidentTypeEnum typeEnum = CompensationAccidentTypeEnum.build(item.getBusinessTypeCode(), false);
//                            item.setBusinessTypeName(typeEnum != null ? typeEnum.getValue() : item.getBusinessTypeCode());
//                        });
//            });
//        }
//        return result;
//    }

    private String getTenantPassengerApplyUrl(){
        BaseResult<TenantAdditionalInfoDTO> result = tenantInfoApi.getItem("PASSENGER_APPLY_URL");
        if (result == null || result.getCode() != HttpStatus.HTTP_OK){
            return null;
        }

        TenantAdditionalInfoDTO dto = result.getData();
        if (dto == null){
            return null;
        }
        return dto.getAdditionalInfoValue();
    }


    public BusinessPrivilegeDTO getDetail(Long id) {
        AirlineBusinessPrivilege businessPrivilege = getBaseMapper().selectById(id);
        if (businessPrivilege == null){
            return null;
        }

        BusinessPrivilegeDTO result = BusinessPrivilegeDTO.fromEntity(businessPrivilege);

        if (businessPrivilege != null) {
            fillAssociateDetails(businessPrivilege, result);
        }

        return result;
    }

    private void fillAssociateDetails(AirlineBusinessPrivilege businessPrivilege, BusinessPrivilegeDTO result) {
        //为业务授权添加明细列表
        LambdaQueryWrapper detailsQuery = new LambdaQueryWrapper<AirlineBusinessPrivilegeItem>().eq(
                AirlineBusinessPrivilegeItem::getBusinessPrivilegeId, businessPrivilege.getId()
        ).orderByAsc(AirlineBusinessPrivilegeItem::getBusinessTypeCode);
        List<AirlineBusinessPrivilegeItem> businessPrivilegeItems = businessPrivilegeItemService.list(detailsQuery);

        //设置业务权限的变更记录
        LambdaQueryWrapper<AirlineBusinessPrivilegeChangeHistory> changeHistoryLambdaQueryWrapper = new LambdaQueryWrapper<AirlineBusinessPrivilegeChangeHistory>()
                .eq(AirlineBusinessPrivilegeChangeHistory::getBusinessPrivilegeId, businessPrivilege.getId())
                .orderByAsc(AirlineBusinessPrivilegeChangeHistory::getChangeTime);
        List<AirlineBusinessPrivilegeChangeHistory> histories = businessPrivilegeChangeHistoryService.list(changeHistoryLambdaQueryWrapper);
        result.setChangeHistoryList(BusinessPrivilegeChangeHistoryDTO.fromEntityList(histories));



        //获取租户的旅客申领url
        String passengerApplyUrl = getTenantPassengerApplyUrl();

        //设置业务权限是否包含资金授权
        result.setBusinessPrivilegeItemList(businessPrivilegeItems.stream().map(BusinessPrivilegeItemDTO::fromEntity).collect(Collectors.toList()));
        result.getBusinessPrivilegeItemList().stream()
                .forEach(item -> {
                    if (item.isGrantBankroll()) {
                        result.setGrantBankroll(true);
                        result.setPassengerApplyUrl(passengerApplyUrl);
                        item.setPassengerApplyUrl(passengerApplyUrl);
                    }
                    CompensationAccidentTypeEnum typeEnum =CompensationAccidentTypeEnum.build(item.getBusinessTypeCode(), false);
                    item.setBusinessTypeName(typeEnum != null ? typeEnum.getValue() : item.getBusinessTypeCode());
                });


    }


    public BusinessPrivilegeDTO getDetailByAirport(String airportCode) {
        LambdaQueryWrapper<AirlineBusinessPrivilege> queryWrapper = new LambdaQueryWrapper<AirlineBusinessPrivilege>();
        queryWrapper.eq(AirlineBusinessPrivilege::getRecipientCode, airportCode);

        AirlineBusinessPrivilege busienssPrivilege = getBaseMapper().selectOne(queryWrapper);
        BusinessPrivilegeDTO result = BusinessPrivilegeDTO.fromEntity(busienssPrivilege);

        if (busienssPrivilege != null) {
            fillAssociateDetails(busienssPrivilege, result);
        }


        return result;
    }

    @Transactional
    public boolean switchStatus(List<Long> ids, boolean disabled) {
        List<BusinessPrivilegeDTO> businessPrivilegeDTOS    = new ArrayList<>();
        ids.stream().forEach(id ->{
            AirlineBusinessPrivilege businessPrivilege = getBaseMapper().selectById(id);
            if (businessPrivilege != null){
                AirlineBusinessPrivilegeChangeHistory changeHistory = new AirlineBusinessPrivilegeChangeHistory();
                changeHistory.setBusinessPrivilegeId(id);
                changeHistory.setGrantorCode(businessPrivilege.getGrantorCode());
                changeHistory.setRecipientCode(businessPrivilege.getRecipientCode());
                changeHistory.setUpdateType(AirlineBusinessPrivilegeChangeHistory.CHANGE_TYPE_UPDATE);
                changeHistory.setOldValue(JSONUtil.toJsonStr(businessPrivilege));
                changeHistory.setChangeTime(LocalDateTime.now());

                businessPrivilege.setDisabled(disabled);
                businessPrivilege.setUpdaterEmployeeName(EmployeeInfoUtil.getCurrentEmployeeName());
                businessPrivilege.setUpdaterJobNumber(EmployeeInfoUtil.getCurrentEmployeeJobNumber());
                getBaseMapper().updateById(businessPrivilege);

                businessPrivilegeDTOS.add(BusinessPrivilegeDTO.fromEntity(businessPrivilege));

                changeHistory.setUpdaterEmployeeName(businessPrivilege.getUpdaterEmployeeName());
                changeHistory.setUpdaterJobNumber(businessPrivilege.getUpdaterJobNumber());
                changeHistory.setNewValue(JSONUtil.toJsonStr(businessPrivilege));

                businessPrivilegeChangeHistoryService.save(changeHistory);
            }
        });

        PrivilegeDataUploadDTO uploadDTO = new PrivilegeDataUploadDTO();
        if (disabled) {
            uploadDTO.setOperationType("DISABLE");
        }
        else {
            uploadDTO.setOperationType("ENABLE");
        }
        uploadDTO.setData(JSONUtil.toJsonStr(businessPrivilegeDTOS));

        //推送到协同中心
        sendCoordinateMessage(JSONUtil.toJsonStr(uploadDTO));

        return true;
    }

    private void sendCoordinateMessage(String content) {
        String url = coordinateConfig.getPrivilegeDataUploadUrl();
        String requestId = IdGenerate.nextId();

        try {
            CoordinateUploadRequestDTO dto = CoordinateApiUtil.getCoordinateUploadRequestDTO(requestId,
                    DataCategoryEnum.PRIVILEGE_DATA.getCode(), coordinateConfig.getAppClientId(),
                    coordinateConfig.getAppSecretKeyVersion(), coordinateConfig.getAppSecretKey(), content);


            CoordinateApiUtil.uploadCoordinateData(url, dto);
        } catch(BusinessException e){
            log.error("推送数据给协同中心失败，requestId:{},content:{}", requestId,  content, e);
            throw e;
        }
        catch (Exception e){
            log.error("推送数据给协同中心失败，requestId:{},content:{}", requestId,  content, e);
            throw new BusinessException(CompensationErrors.COORDINATE_DATA_FAILED);
        }
    }
}
