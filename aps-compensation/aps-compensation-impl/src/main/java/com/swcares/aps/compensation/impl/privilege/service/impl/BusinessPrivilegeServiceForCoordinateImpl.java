package com.swcares.aps.compensation.impl.privilege.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapperForCoordinate;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeServiceForCoordinate;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 09:05
 * @Version 1.0
 */
@Service
@Slf4j
public class BusinessPrivilegeServiceForCoordinateImpl extends ServiceImpl<BusinessPrivilegeMapperForCoordinate, AirlineBusinessPrivilege> implements BusinessPrivilegeServiceForCoordinate {

}
