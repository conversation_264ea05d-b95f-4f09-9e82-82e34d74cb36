package com.swcares.aps.compensation.impl.baggage.accident.service.handler;

import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import org.springframework.stereotype.Service;

/**
 * ClassName： ChangeToDoToVoidStatusHandlerServiceImpl
 * Description： 待处理状态更改为作废状态处理服务类
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/16 14:33
 * @version v1.0
 */
@Service
public class ChangeToDoToVoidStatusHandlerServiceImpl implements BaggageAccidentStatusHandlerService {
    @Override
    public boolean support(String sourceStatus, String targetStatus) {
        return AccidentStatusEnum.TODO.getValue().equals(sourceStatus)
                && AccidentStatusEnum.TO_VOID.getValue().equals(targetStatus);
    }

    @Override
    public void check(BaggageAccidentInfoDTO accidentInfo, String targetStatus) {

    }
}
