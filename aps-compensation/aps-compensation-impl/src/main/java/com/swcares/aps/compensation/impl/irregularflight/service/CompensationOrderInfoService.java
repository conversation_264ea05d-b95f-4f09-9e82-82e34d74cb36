package com.swcares.aps.compensation.impl.irregularflight.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationSyntheticalSaveDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.service.CompensationOrderInfoService <br>
 * Description：赔偿单信息 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface CompensationOrderInfoService extends IService<CompensationOrderInfoDO> {

    /**
     * Title：logicRemoveById <br>
     * Description：删除 <br>
     * author：TODO <br>
     * date：2021-10-27 <br>
     * @param id <br>
     * @return boolean <br>
     */
    boolean logicRemoveById(Long id);


    /**
     * @title pages
     * @description 条件分页查询赔偿单信息记录
     * <AUTHOR>
     * @date 2022/2/23 16:24
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO>
     */
    IPage<CompensationOrderInfoExamineVO> pages(CompensationOrderInfoPagedDTO dto);


    /**
     * Title：saveOrderInfoAndAudit <br>
     * Description： 保存赔偿单-并发起审核流程<br>
     * author：傅欣荣 <br>
     * date：2021/11/24 10:24 <br>
     * @param dto
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> saveOrderInfoAndAudit(CompensationSyntheticalSaveDTO dto) throws Exception;

    /**
     * Title：saveOrderInfo <br>
     * Description：保存赔偿单信息 <br>
     * author：王磊 <br>
     * date：2021/10/28 13:32 <br>
     * @param dto <br>
     * @return <br>
     */
    boolean saveOrderInfo(CompensationSyntheticalSaveDTO dto);

    /**
     * Title： findById<br>
     * Description： 根据赔付单id查信息<br>
     * author：傅欣荣 <br>
     * date：2021/10/29 15:29 <br>
     * @param id
     * @return com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO
     */
    CompensationOrderDetailsVO findById(@Param("id") Long id);

    /**
    * @title takeEffect
    * @description 补偿发放
    * <AUTHOR>
    * @date 2021/11/9 16:06
    * @param orderId 补偿单id
    * @param userName 操作人信息
    * @return int 成功修改的数据行数
    */
    int takeEffect(Long orderId,String userName);

    /**
    * @title close
    * @description 关闭补偿发放
    * <AUTHOR>
    * @date 2021/11/9 16:07
    * @param orderId
    * @param userName
    * @return int
    */
    int close(Long orderId,String userName);

    /**
    * @title getSameTypeOrders
    * @description 新建赔偿单时提示弹框
    * <AUTHOR>
    * @date 2021/12/13 11:07
    * @param flightDate
    * @param flightNo
    * @param accidentType
    * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderAddTipsVO>
    */
    List<CompensationOrderAddTipsVO> getSameTypeOrders(String flightDate,String flightNo,String accidentType,Long orderId);

    /**
     * Title： getCompensationEditEchoById <br>
     * Description： 获取赔偿单编辑界面回显信息-<br>
     * author：傅欣荣 <br>
     * date：2021/11/11 10:05 <br>
     * @param orderId
     * @return com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderEditEchoVO
     */
    CompensationOrderEditEchoVO getOrderEditEchoById(Long orderId);


    /**
     * Title：updCompensationOrderStatus <br>
     * Description： 更新赔偿单状态<br>
     * author：傅欣荣 <br>
     * date：2021/11/24 14:49 <br>
     * @param orderId
     * @param status
     * @return boolean
     */
    boolean updCompensationOrderStatus(Long orderId,String status);

    /**
     * Title：findPaxReceiveRecordByPaxId <br>
     * Description： 通过旅客PaxID-查领取记录<br>
     * author：傅欣荣 <br>
     * date：2021/11/29 17:15 <br>
     * @param orderId
     * @param paxId
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO>
     */
    List<PaxReceiveRecordVO> findPaxReceiveRecord(Long orderId,String paxId);

    /**
     * Title： deleteAuditInfo<br>
     * Description： 删除待审核记录<br>
     * author：傅欣荣 <br>
     * date：2022/2/14 10:06 <br>
     * @param
     * @return
     */
    void deleteAuditInfo(Long orderId);

    /**
     * @title createCompensationOrder
     * @description @TODO
     * <AUTHOR>
     * @date 2022/3/11 19:08
     * @param compensationOrderInfoDO
     * @return
     */
    CompensationOrderInfoDO createCompensationOrder(CompensationOrderInfoDO compensationOrderInfoDO);


    /***
     * @title findOverdueOrder
     * @description 查找逾期订单
     * <AUTHOR>
     * @date 2022/9/16 10:33

     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO>
     */
    List<CompensationOrderInfoDO> findOverdueOrder();

    /***
     * @title updateOverdueOrder
     * @description 逾期状态修改
     * <AUTHOR>
     * @date 2022/9/16 10:46
     * @param orderIds
     * @param status
     * @return void
     */
    void updateOverdueOrder(List<Long> orderIds,String status);

    /***
     * @title findCloseAccidentOrder
     * @description 查询符合修改为已结案的订单，包含异常行李和不正常航班，事故单下赔偿单全是关闭或逾期
     * <AUTHOR>
     * @date 2022/9/16 11:57

     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO>
     */
    List<CompensationOrderInfoDO> findCloseAccidentOrder();

    /**
     * @title updateCreatedBy
     * @description 补偿单草稿转提交，操作人不一致时，需要更改为当前的操作人
     * <AUTHOR>
     * @date 2022/10/27 16:40
     * @param compensationId
     * @param currentUserId
     * @return void
     */
    void updateCreatedBy(String compensationId, String currentUserId);

    /**
     * @title getTenantIdByCode
     * @description 根据租户code获取id
     * <AUTHOR>
     * @date 2024/7/17 11:35
     * @param tenantCode
     * @return java.lang.Long
     */
    Long getTenantIdByCode(String tenantCode);

    /**
     * @title getTenantCodeById
     * @description
     * <AUTHOR>
     * @date 2024/7/23 13:34
     * @param tenantId
     * @return
     */
    String getTenantCodeById(Long tenantId);

    /**
     * @title getTenantCompanyNameById
     * @description
     * <AUTHOR>
     * @date 2024/7/23 13:34
     * @param tenantId
     * @return
     */
    String getTenantCompanyNameById(Long tenantId);

    /**
     * @title getTenantNameById
     * @description @TODO
     * <AUTHOR>
     * @date 2024/8/8 13:52
     * @param tenantId
     * @return
     */
    String getTenantNameById(Long tenantId);

    /***
     * @title verifyAirportBusinessPrivilege
     * @description 【适用：不正常航班、超售、投诉、异常行李】新建补偿单：验证该补偿航站 是否已给机场业务授权,
     *                  已授权给机场，抛出异常
     * <AUTHOR>
     * @date 2024/7/23 10:50
     * @param airportCode businessTypeCode
     * @return com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO
     */
    void verifyAirportBusinessPrivilege(String airportCode,String businessTypeCode);

    List<CompensationOrderInfoDO> findNeedRefreshWorkflowUserInfoOrder();
}
