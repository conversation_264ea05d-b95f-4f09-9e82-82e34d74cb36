package com.swcares.aps.compensation.impl.privilege.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.privilege.dto.SearchCooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer;
import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname CooperativeCustomerService
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:17
 * @Version 1.0
 */
public interface CooperativeCustomerService extends IService<CooperativeCustomer> {
    public List<CooperativeCustomer> getCooperativeCustomerList(SearchCooperativeCustomerDTO searchCriteria);

    public CooperativeCustomer saveOrUpdate(CustomerDTO customerDTO);
}
