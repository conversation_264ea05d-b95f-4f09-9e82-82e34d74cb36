package com.swcares.aps.compensation.impl.compensation.enums;

/**
 * @ClassName：ReceiveStatusEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 31947
 * @Date： 2022/5/7 10:45
 * @version： v1.0
 */
public enum ReceiveStatusEnum {
    UNCLAIMED("0","待领取"),
    RECEIVED("1","已领取"),
    RECEIVEING("2","领取中"),
    OVERDUE("3","已逾期");


    private String key;

    private String value;

    ReceiveStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }


    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
