package com.swcares.aps.compensation.impl.baggage.luggage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindLuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageInfoPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageManagementService
 * @projectName aps
 * @description:
 * @date 2022/2/8 14:14
 */
public interface LuggageManagementService extends IService<LuggageInfoDO> {

    LuggageInfoDO findById(String id);
    /**
    * @title findSegment
    * @description 通过三字码获取航站信息
    * <AUTHOR>
    * @date 2022/2/14 13:17
    * @param code
    * @return java.util.List<java.lang.String>
    */
    List<String> findSegment(String code);

    /**
    * @title findLuggageList
    * @description 多条件分页查询箱包信息列表
    * <AUTHOR>
    * @date 2022/2/10 13:50
    * @param dto
    * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.luggage.model.vo.LuggageInfoVO>
    */
    IPage<LuggageInfoVO> findLuggageList(LuggageInfoPageDTO dto);

    /**
    * @title saveLuggage
    * @description 新增箱包信息
    * <AUTHOR>
    * @date 2022/2/10 13:50
    * @param dto
    * @return void
    */
    void saveLuggage(LuggageInfoDTO dto);

    /**
    * @title removeLuggage
    * @description 逻辑删除箱包信息
    * <AUTHOR>
    * @date 2022/2/10 13:51
    * @param id
    * @return void
    */
    void removeLuggage(Long id);

    /**
    * @title updateLuggage
    * @description 根据箱包id修改箱包信息
    * <AUTHOR>
    * @date 2022/2/10 13:52
    * @param dto
    * @return void
    */
    void updateLuggage(LuggageInfoDTO dto);


    /**
    * @title findLuggageStockDetailed
    * @description 根据箱包id查询该箱包的库存管理明细列表
    * <AUTHOR>
    * @date 2022/2/11 10:05
    * @param dto
    * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO>
    */
    IPage<FindLuggageStockInfoVO> findLuggageStockDetailed(FindLuggageStockInfoDTO dto);

    /**
     * @title getLuggageCompensationReport
     * @description  箱包补偿报表详情
     * <AUTHOR>
     * @date 2022/7/26 10:05
     * @param dto
     * @return IPage<LuggageCompensateDetailVO>
     */
    IPage<LuggageCompensateDetailVO> getLuggageCompensationReportDetail(LuggageCompensatePageDTO dto);

    /**
     * @title getLuggageManagementList
     * @description @TODO
     * <AUTHOR>
     * @date 2022/8/10 10:34
     * @return List<LuggageInfoVO>
     */
    List<LuggageInfoVO> getLuggageManagementList(List<String> brand);

    IPage<LuggageCompensateDetailVO> getLuggageCompensationReport(LuggageCompensatePageDTO dto);
}
