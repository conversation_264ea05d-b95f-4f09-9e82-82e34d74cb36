package com.swcares.aps.compensation.impl.apply.service.impl;


import cn.hutool.json.JSONUtil;
import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyTypeCodeEnum;
import com.swcares.aps.compensation.impl.apply.enums.PayStatusEnum;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyChinaPayService;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.apply.util.ApplyCodeUtils;
import com.swcares.aps.compensation.impl.apply.util.DistributedUniqueIndexGenerator;
import com.swcares.aps.compensation.impl.constant.CompensationConstants;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.aps.component.pay.pay.bean.PayConstant;
import com.swcares.aps.component.pay.pay.service.chinapay.bean.ChinaPayAuthRequestDTO;
import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderVO;
import com.swcares.aps.compensation.model.apply.vo.AuthPaxVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.aps.component.pay.pay.PayTypeEnum;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaAuthUserInfoService;
import com.swcares.aps.component.pay.pay.service.wx.WxPayProcess;
import com.swcares.aps.component.pay.pay.service.wx.WxPayService;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.MchTransferDetailEntity;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.WxMchTransferReceiveConfirmInfo;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * ClassName：com.swcares.compensation.service.impl.ApplyOrderServiceImpl <br>
 * Description：航延补偿申领单信息表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
//@PropertySource("classpath:application.yml")
@ConfigurationProperties(prefix = "swcares.minio")
public class ApplyOrderServiceImpl extends ServiceImpl<ApplyOrderMapper, ApplyOrderDO> implements ApplyOrderService {

    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    @Autowired
    private ApplyPaxService applyPaxService;

    @Autowired
    private PayRecordService payRecordService;

    @Autowired
    private CompensationPaxInfoService compensationPaxInfoService;

    @Autowired
    private Redisson redisson;

    @Autowired
    private SMSService smsService;

    @Autowired
    private FileAttachmentService fileAttachmentService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    private ChinaAuthUserInfoService chinaAuthUserInfoService;

    @Autowired
    ApplyChinaPayService applyChinaPayService;

    @Resource(name = "wxPayProcess")
    private WxPayProcess wxPayProcess;
    /**
     * @title logicRemoveById
     * @description 逻辑删除
     * <AUTHOR> @date 2021-11-25
     * @param id
     * @return boolean
     */
    @Override
    public boolean logicRemoveById(Long id) {
        ApplyOrderDO entity = new ApplyOrderDO();
        entity.setId(id);
        return updateById(entity);
    }

    /**
     * @title page
     * @description 分页查询
     * <AUTHOR> @date 2021-11-25
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.apply.model.vo.ApplyOrderVO>
     */
    @Override
    public IPage<ApplyOrderVO> page(ApplyOrderPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    /**
     * @title updateAuditStatus
     * @description 修改订单状态
     * <AUTHOR> @date 2022/2/23 9:18
     * @param updateStatusDTO
     * @return void
     */
    @Override
    public void updateAuditStatus(ApplyUpdateStatusDTO updateStatusDTO) {
        ApplyOrderDO applyOrderDO=new ApplyOrderDO();
        applyOrderDO.setApplyStatus(updateStatusDTO.getAuditStatus());
        applyOrderDO.setPaymentWaitingPeriod(updateStatusDTO.getPaymentWaitingPeriod());
        applyOrderDO.setUpdatedTime(LocalDateTime.now());
        applyOrderDO.setUpdatedBy(updateStatusDTO.getAuditorUserId());
        applyOrderDO.setAuditTime(updateStatusDTO.getAuditTime());

        LambdaQueryWrapper<ApplyOrderDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.eq(ApplyOrderDO::getId,Long.valueOf(updateStatusDTO.getApplyId()));
        this.baseMapper.update(applyOrderDO,lambdaQueryWrapper);
    }

    /**
     * @title findPaxBaseInfoByApplyId
     * @description 获取乘机人信息
     * <AUTHOR> @date 2022/2/23 9:19
     * @param applyOrderId
     * @return java.util.List<com.swcares.aps.apply.model.dto.ApplyPaxBaseInfoDTO>
     */
    @Override
    public List<ApplyPaxBaseInfoDTO> findPaxBaseInfoByApplyId(Long applyOrderId) {
        return baseMapper.findPaxBaseInfoByApplyId(applyOrderId);
    }

    /**
     * Title：saveApplyOrder <br>
     * Description：保存申领单 <br>
     * author：王磊 <br>
     * date：2021/11/29 11:00 <br>
     * @param dto 保存申领单的传输对象 <br>
     * @return <br>
     */
    @Override
    public boolean saveApplyOrder(ApplyOrderDTO dto) {
        //整个申领单保存的标记位
        boolean bool = false;
        if(dto.getGetMoneyWay().equals(PayTypeEnum.WECHAT.getKey())){
            log.info("【aps-apply-impl】当前本人领取选择的微信支付，当前用户的openId为【{}】", UserContext.getCurrentUser().getWxOpenid());
            dto.setGetMoneyAccount(UserContext.getCurrentUser().getWxOpenid());
        }
        log.info("【aps-apply-impl开始保存本人领取的申领单信息，当前用户为【{}】请求参数【{}】",
                JSONUtil.toJsonStr(UserContext.getCurrentUser()),JSONUtil.toJsonStr(dto));

        //安测问题优化：小程序本人领取处存在数据篡改，可以篡改领取人信息；建议验证领取人信息。
        CompensationOrderInfoDTO orderInfoVo = dto.getOrderInfoVOS().get(0);
        CompensationPaxInfoDO compensationPaxInfoDO = new LambdaQueryChainWrapper<>(compensationPaxInfoService.getBaseMapper())
                .eq(CompensationPaxInfoDO::getOrderId,orderInfoVo.getOrderId())
                .eq(CompensationPaxInfoDO::getId,orderInfoVo.getPaxId())
                .one();
        if(compensationPaxInfoDO == null){
            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
        }
        if(!dto.getApplyUser().equals(compensationPaxInfoDO.getPaxName())){
            throw new BusinessException(ApplyErrors.QUERY_RECORD_ERROR);
        }

        //【安测问题修复】水平越权--同数据篡改的问题一样【设计如此，知道证件号后可以创建申领单，但是微信打款失败，银联打款是页面输入-需要修改传参方式】
        //账号A可以越权申领账户B的补偿
        if(PayTypeEnum.UNIONPAY.getKey().equals(dto.getCompensateSubType())){
            ChinaPayAuthRequestDTO authRequestDTO = new ChinaPayAuthRequestDTO();
            authRequestDTO.setApplyUser(dto.getApplyUser());
            authRequestDTO.setCardNo(dto.getGetMoneyAccount());
            authRequestDTO.setIdType("身份证");
            authRequestDTO.setIdNo(dto.getIdNo());
            applyChinaPayService.isTokenChinaPayPass(authRequestDTO);
           /* Map map = new HashMap<String, String>();
            map.put("cardNo", dto.getGetMoneyAccount());
            map.put("idType", "身份证");
            map.put("idNo", compensationPaxInfoDO.getIdNo());
            map.put("userName", compensationPaxInfoDO.getPaxName());
            try {
                BaseResult<Object> chinaAuthResult = chinaAuthUserInfoService.authCarUserInfo(JSONObject.toJSONString(map), dto.getAccidentType());
                if(BaseResult.OK_CODE != chinaAuthResult.getCode() || !"true".equals(chinaAuthResult.getData().toString())){
                    throw new BusinessException(PayErrors.RETURN_MSG_FAILED);
                }
            }catch (Exception e) {
                throw new BusinessException(PayErrors.RETURN_MSG_FAILED);
            }*/
        }


        ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
        ApplyOrderDTO irregularFlightOrderDto = new ApplyOrderDTO();
        ObjectUtils.copyBean(applyOrderDO,irregularFlightOrderDto, new String[]{"applyAmount"});
        irregularFlightOrderDto.setOrderInfoVOS(new ArrayList<CompensationOrderInfoDTO>());
        ApplyOrderDTO abnormalLuggageOrderDto = new ApplyOrderDTO();
        ObjectUtils.copyBean(applyOrderDO,abnormalLuggageOrderDto, new String[]{"applyAmount"});
        abnormalLuggageOrderDto.setOrderInfoVOS(new ArrayList<CompensationOrderInfoDTO>());
        ApplyOrderDTO overbookingOrderDto = new ApplyOrderDTO();
        ObjectUtils.copyBean(applyOrderDO,overbookingOrderDto, new String[]{"applyAmount"});
        overbookingOrderDto.setOrderInfoVOS(new ArrayList<CompensationOrderInfoDTO>());

        ApplyOrderDTO complaintOrderDto = new ApplyOrderDTO();
        ObjectUtils.copyBean(applyOrderDO,complaintOrderDto, new String[]{"applyAmount"});
        complaintOrderDto.setOrderInfoVOS(new ArrayList<CompensationOrderInfoDTO>());
        //循环后分类领取单数据
        for (CompensationOrderInfoDTO compensationOrderInfoDTO : dto.getOrderInfoVOS()) {
            AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
            authPaxOrderDTO.setOrderId(compensationOrderInfoDTO.getOrderId());
            authPaxOrderDTO.setPaxId(compensationOrderInfoDTO.getPaxId());
            AuthPaxVO authPaxVO = this.getBaseMapper().findApplyAuthPax(authPaxOrderDTO);
            //判断单子状态如果有问题直接抛错
            if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
            } else {
                if (compensationOrderInfoDTO.getAccidentType().equals(ApplyConstants.IRREGULAR_FLIGHT)) {//不正常航班
                    irregularFlightOrderDto.getOrderInfoVOS().add(compensationOrderInfoDTO);
                    irregularFlightOrderDto.setApplyAmount(
                            ObjectUtils.isEmpty(irregularFlightOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                    : irregularFlightOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                } else if (compensationOrderInfoDTO.getAccidentType().equals(ApplyConstants.ABNORMAL_LUGGAGE)) {//异常行李
                    abnormalLuggageOrderDto.getOrderInfoVOS().add(compensationOrderInfoDTO);
                    abnormalLuggageOrderDto.setApplyAmount(
                            ObjectUtils.isEmpty(abnormalLuggageOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                    : abnormalLuggageOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                } else if (compensationOrderInfoDTO.getAccidentType().equals(ApplyConstants.OVER_BOOKING)) {//超售
                    overbookingOrderDto.getOrderInfoVOS().add(compensationOrderInfoDTO);
                    overbookingOrderDto.setApplyAmount(
                            ObjectUtils.isEmpty(overbookingOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                    : overbookingOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                } else if (compensationOrderInfoDTO.getAccidentType().equals(ApplyConstants.COMPLAINT)) {//投诉
                    complaintOrderDto.getOrderInfoVOS().add(compensationOrderInfoDTO);
                    complaintOrderDto.setApplyAmount(
                            ObjectUtils.isEmpty(complaintOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                    : complaintOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                }
            }
        }
        try {
            //根据事故单类型来保存申领单
            if (ListUtils.isNotEmpty(irregularFlightOrderDto.getOrderInfoVOS())) {
                bool = processApplyOrder(irregularFlightOrderDto);
            }
            if (ListUtils.isNotEmpty(abnormalLuggageOrderDto.getOrderInfoVOS())) {
                bool = processApplyOrder(abnormalLuggageOrderDto);
            }
            if (ListUtils.isNotEmpty(overbookingOrderDto.getOrderInfoVOS())) {
                bool = processApplyOrder(overbookingOrderDto);
            }
            if (ListUtils.isNotEmpty(complaintOrderDto.getOrderInfoVOS())) {
                bool = processApplyOrder(complaintOrderDto);
            }
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("【aps-apply-impl代人领取上传文件异常，整个请求参数为【{}】", JSONUtil.toJsonStr(dto), e);
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }
        return bool;
    }

    /**
     * Title：processApplyOrder <br>
     * Description：申领单不正常航班保存 <br>
     * author：王磊 <br>
     * date：2021/12/8 13:46 <br>
     * @param dto 保存申领单的传输对象 <br>
     * @return <br>
     */
    public boolean processApplyOrder(ApplyOrderDTO dto) throws Exception {
        LoginUserDetails user = UserContext.getUser();
        boolean bool = false;
        //用于更新赔偿单旅客状态的旅客id集合，更新为领取中
        List<Long> paxIds = new ArrayList<Long>();
        List<String> applyCodeList = new ArrayList<String>();

        FileAttachment signFileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(dto.getSignFile()), bucketName,null,null);



        for (CompensationOrderInfoDTO compensationOrderInfoDTO : dto.getOrderInfoVOS()) {
            AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
            authPaxOrderDTO.setOrderId(compensationOrderInfoDTO.getOrderId());
            authPaxOrderDTO.setPaxId(compensationOrderInfoDTO.getPaxId());
            //保存前获取赔偿单的旅客信息用于校验赔偿单用户是否可用
            AuthPaxVO authPaxVO = this.getBaseMapper().findApplyAuthPax(authPaxOrderDTO);



            //判断赔偿单上是否有个旅客 || 该旅客状态是否被冻结 || 旅客是申领单不是未领取状态
            if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                log.error("【aps-apply-impl】申领单保存验证赔偿单状态失败,申领人{},申领人身份证:{}",dto.getApplyUser(), ApsDesensitizedUtil.idCardNum(dto.getIdNo()));
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
            } else {

                ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
                //一定会有赔偿单信息,直接取
                String accidentTypeCode = ApplyTypeCodeEnum.build(dto.getOrderInfoVOS().get(0).getAccidentType()).getValue();
                //生成申领单编号
                String applyCode = ApplyCodeUtils.createApplyCode(ApplyConstants.APPLY_TYPE_S,accidentTypeCode,user.getTenantCode());
                applyOrderDO.setApplyCode(applyCode);
                applyOrderDO.setAccidentType(dto.getOrderInfoVOS().get(0).getAccidentType());
                applyOrderDO.setSignFile(String.valueOf(signFileAttachment.getId()));
                //数据来源  1机场/2航司
                applyOrderDO.setSource(CompensationConstants.BUSINESS_DATA_SOURCE_AIRPORT);
                //申领单归属航司
                applyOrderDO.setBelongAirline(dto.getFlightNo().substring(0, 2));
                //申领金额
                applyOrderDO.setApplyAmount(authPaxVO.getMoney());
                //【保存申领单】
                log.info("【aps-apply-impl】申领单保存,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                //存当前值的目的是保存当前申领人的微信openid，目前用于消息推送
                applyOrderDO.setCreatedBy(UserContext.getCurrentUser().getWxOpenid());
                bool = this.save(applyOrderDO);
                throwSaveError(bool);

                //申领单旅客记录
                ApplyPaxDO applyPaxDO = new ApplyPaxDO();
                PayRecordDO payRecordDO = new PayRecordDO();
                payRecordDO.setId(DistributedUniqueIndexGenerator.generateUniqueIndex());
                applyPaxDO.setApplyCode(applyCode);
                applyPaxDO.setApplyId(applyOrderDO.getId());
                applyPaxDO.setOrderId(new Long(compensationOrderInfoDTO.getOrderId()));
                applyPaxDO.setPaxInfoId(new Long(compensationOrderInfoDTO.getPaxId()));
                applyPaxDO.setApplyPaxStatus(ApplyConstants.APPLY_PAX_STATUS_OPERATION);

                //【保存申领单旅客信息】
                log.info("【aps-apply-impl】申领单保存,保存申领旅客,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                bool = applyPaxService.save(applyPaxDO);
                throwSaveError(bool);


                //支付记录
                payRecordDO.setApplyId(applyOrderDO.getId());
                payRecordDO.setApplyPaxId(new Long(compensationOrderInfoDTO.getPaxId()));
                payRecordDO.setOrderId(new Long(compensationOrderInfoDTO.getOrderId()));
                payRecordDO.setPayStatus(PayStatusEnum.NON_PAYMENT.getKey());
                payRecordDO.setPayType(applyOrderDO.getGetMoneyWay());
                payRecordDO.setTransAmount(authPaxVO.getMoney());
                payRecordDO.setCreatedTime(LocalDateTime.now());

                //【保存支付记录(创建待支付记录)】
                log.info("【aps-apply-impl】申领单保存,保存申领支付记录,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                bool = payRecordService.save(payRecordDO);
                throwSaveError(bool);

                //事故单类型对应的业务code
                String businessCode = AccidentTypeBusinessCodeEnum.build(applyOrderDO.getAccidentType()).getValue();
                businessDataPushHandler.dataStore(applyOrderDO.getId(), businessCode, BusinessDataSyncConstant.DATA_TYPE_APPLY);

                paxIds.add(new Long(compensationOrderInfoDTO.getPaxId()));
                applyCodeList.add(applyCode);
            }
        }




        //【调用赔偿单api更新赔偿单旅客数据】
        log.info("【aps-apply-impl】申领单保存,更新赔偿单,申领人{},申领人身份证:{},申领单号集合:{}",dto.getApplyUser(),ApsDesensitizedUtil.idCardNum(dto.getIdNo()),applyCodeList.toString());
        boolean result = compensationPaxInfoService.updatePaxReceiveInfo(paxIds, dto.getApplyWay(), dto.getGetMoneyWay(), ApplyConstants.GET_RECEIVE_STATUS);
        throwSaveError(result);

        //推送服务消息
        ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
        sendAuditMsg(applyOrderDO, dto);


        return bool;
    }

    private void sendAuditMsg(ApplyOrderDO applyOrderDO, ApplyOrderDTO dto){
        applyOrderDO.setTelephone(dto.getTelephone());
        applyOrderDO.setIdNo(dto.getIdNo());
        //由藏航重构来进行实现
        smsService.sendMiniProgramToPsg(applyOrderDO, true);
        /*String applyUser = applyOrderDO.getApplyUser();
        //通过申领单id查询航班日期; 发送消息需要航班号和日期
        //ApplyInfoDetailsVO applyInfoDetailsVO = this.findFlightById(applyOrderDO.getId() + "");
        boolean res = true;
        String finalTemplate = AuditMsgService.buildMsgTemplate(applyOrderDO, res);
        log.info("【aps-compensation-impl】根据审核结果给申领人推送小程序服务消息：申领人姓名【{}】，申领结果【{}】，推送消息模板【{}】", applyUser, res, finalTemplate);
        boolean msgMark = AuditMsgService.SendMiniProgramMsg(applyOrderDO.getCreatedBy(), applyUser, res? "通过": "不通过", finalTemplate);
        log.info("【aps-compensation-impl】根据审核结果给申领人推送小程序服务消息：申领人姓名【{}】，申领结果【{}】，推送消息模板【{}】; 消息推送结果【{}】", applyUser, res, finalTemplate, msgMark);*/
    }

    /**
     * Title：createApplyCode <br>
     * Description：构建申领单code <br>
     * author：王磊 <br>
     * date：2021/12/1 11:28 <br>
     * <br>
     *
     * @return <br>
     */
    private String createApplyCode(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        StringBuffer stringBuffer = new StringBuffer(localDateTime.format(formatter));
        //add by summer 2022/09/16 根据山航的经验来看，这儿会有id重复的风险，建议5位数
        stringBuffer.append(String.format("%03d", new Random().nextInt(999)));
        return stringBuffer.toString();
    }


    /**
     * Title：throwSaveError <br>
     * Description：保存时抛出错误 <br>
     * author：王磊 <br>
     * date：2021/12/2 11:20 <br>
     *
     * @param bool <br>
     * @return <br>
     */
    private void throwSaveError(boolean bool) {
        if (!bool) {
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }
    }

    public ApplyInfoDetailsVO findFlightByApplyId(String applyId){
        return baseMapper.findFlightByApplyId(applyId);
    }

    public ApplyInfoDetailsVO findFlightById(String id){
        return baseMapper.findFlightById(id);
    }

    @Override
    public WxMchTransferReceiveConfirmInfo getReceiveConfirmInfo(String applyId) {
        PayRecordDO payRecordDO = payRecordService.lambdaQuery().eq(PayRecordDO::getApplyId, applyId).one();
        if(payRecordDO == null){
            log.error("获取微信转账packageInfo失败,{}的支付记录不存在",applyId);
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"申领单不存在");
        }
        if(StringUtils.isEmpty(payRecordDO.getWxReceivePackageInfo())){
            log.error("获取微信转账packageInfo失败,{}的支付记录的packageInfo为空",applyId);
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"微信package信息不存在");
        }
        if(!StringUtils.equals(payRecordDO.getPayTypeApiVersion(),
                PayConstant.PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){
            log.error("获取微信转账packageInfo失败,{}的支付记录的api版本不是V3",applyId);
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"当前支付不属于微信商家转账流程");
        }
        if(PayConstant.PAY_STATUS_PAID.equals(payRecordDO.getPayStatus())){
            log.error("获取微信转账packageInfo失败,{}的支付记录已经是最终状态-支付成功",applyId);
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"您已确认收款，请在微信零钱中查看");
        }
        if(PayConstant.PAY_STATUS_FAIL.equals(payRecordDO.getPayStatus())){
            log.error("获取微信转账packageInfo失败,{}的支付记录已经是最终状态,{}",applyId,payRecordDO.getPayStatus());
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"收款失败，请重新进行申领");
        }
        ApplyOrderDO orderDO = this.getById(applyId);
        WxPayService wxPayService = wxPayProcess.getWxPayService(orderDO.getTenantId(), orderDO.getAccidentType());
        MchTransferDetailEntity transferDetailEntity = wxPayService.getMchTransferDetailByOutBillNo(String.valueOf(payRecordDO.getId()));
        if(transferDetailEntity == null){
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"微信查询异常,请稍后重试");
        }
        if(!PayConstant.MCH_TRANSFER_ALLOW_CONFIRM_SATES.contains(transferDetailEntity.getState())){
            log.error("当前申领单状态不允许确认收款,{},{}", JSONObject.toJSONString(orderDO),transferDetailEntity);
            if(PayConstant.PAX_RECEIVE_STATE_RECEIVED.equals(transferDetailEntity.getState())){
                throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"您已确认收款，请在微信零钱中查看");
            }
            if(PayConstant.PAX_RECEIVE_STATE_FAIL.equals(transferDetailEntity.getState())){
                throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"收款失败，请重新进行申领");
            }
            throw new BusinessException(ApplyErrors.APPLY_COMMON_ERROR,"状态同步中");
        }
        WxMchTransferReceiveConfirmInfo wxMchTransferReceiveConfirmInfo=new WxMchTransferReceiveConfirmInfo();
        wxMchTransferReceiveConfirmInfo.setPackageInfo(payRecordDO.getWxReceivePackageInfo())
                .setMchId(wxPayService.getMchId())
                .setAppId(wxPayService.getAppId());
        return wxMchTransferReceiveConfirmInfo;

    }

}
