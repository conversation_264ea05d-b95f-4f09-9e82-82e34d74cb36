package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.SerialPolicy;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.dataconfig.enums.CompensationConfigEnum;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRuleService;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRuleDTO;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.impl.ReplaceRuleServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ReplaceRuleServiceImpl  implements ReplaceRuleService {

    private static final String CACHE_NAME = "ReplaceRuleCache:";
    private static final String CACHE_KEY = "'enableRule'";
    private static final String REDISSON_KEY="ReplaceRuleSave";
    private static final Long REPLACE_RULE_SAVE_REDIS_LOCK_TIME=100L;

    /**代领人是航班旅客设置项，默认开启，即仅是航班中的旅客才可进入代领渠道*/
    private static final String DEFAULT_RULE_IS_CONTAIN_SELF="1";

    /**本人需一起领取设置，默认开启，本人需要一起进行领取*/
    private static final String DEFAULT_RULE_IS_SAME_FLIGHT="1";

    /**最大可申领乘机人数设置项，默认值为10人*/
    private static final Integer DEFAULT_RULE_MAX_APPLY_PASSENGER=10;
    /**支付等待期默认 5分钟*/
    private static final Integer DEFAULT_PAYMENT_WAITING_PERIOD=5;



    private static final List<String> RULE_SUB_TYPES= Arrays
            .asList(CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getSubType(),
                    CompensationConfigEnum.RULE_IS_SAME_FLIGHT.getSubType(),
                    CompensationConfigEnum.RULE_MAX_APPLY_PASSENGER.getSubType());

    @Autowired
    private Redisson redisson;

    @Autowired
    private ReplaceConfigService replaceConfigService;

    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @CacheInvalidate(name = CACHE_NAME, key = CACHE_KEY)
    @Override
    public boolean saveBaseRule(ReplaceBaseRuleDTO replaceRuleDTO) {

        RLock lock = redisson.getLock(REDISSON_KEY);
        boolean result = false;

        try {
            log.info("saveBaseRule start");
            //添加分布式锁，防止并发修改规则
            boolean resLock = lock.tryLock(REPLACE_RULE_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                log.info("saveBaseRule 获取分布式锁成功");
                //逻辑删除（禁用）当前规则
                List<DataConfigDO> oldEnableRules = replaceConfigService.findByType(CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getType());
                if(CollectionUtil.isNotEmpty(oldEnableRules)){
                    List<DataConfigDO> collect = oldEnableRules.stream().filter(t -> RULE_SUB_TYPES.contains(t.getSubType()))
                            .map(t -> {
                                t.setDeleted(true);
                                t.setUpdatedTime(LocalDateTime.now());
                                t.setUpdatedBy(replaceRuleDTO.getCreatedBy());
                                return t;
                            })
                            .collect(Collectors.toList());
                    if(CollectionUtil.isNotEmpty(collect)){
                        replaceConfigService.saveOrUpdateBatch(collect);
                    }
                }


                DataConfigDO isContainSelfConfig=buildReplaceConfig(CompensationConfigEnum.RULE_IS_CONTAIN_SELF,
                        replaceRuleDTO.getCreatedBy(),
                        replaceRuleDTO.getIsContainSelf());

                DataConfigDO isSameFlightConfig=buildReplaceConfig(CompensationConfigEnum.RULE_IS_SAME_FLIGHT,
                        replaceRuleDTO.getCreatedBy(),
                        replaceRuleDTO.getIsSameFlight());

                DataConfigDO maxApplyPassengerConfig=buildReplaceConfig(CompensationConfigEnum.RULE_MAX_APPLY_PASSENGER,
                        replaceRuleDTO.getCreatedBy(),
                        String.valueOf(replaceRuleDTO.getMaxApplyPassenger()));

                List<DataConfigDO> newEnableRules=Arrays.asList(isContainSelfConfig,isSameFlightConfig,maxApplyPassengerConfig);
                result=replaceConfigService.saveBatch(newEnableRules);

                log.info("saveBaseRule end,result:{}",result);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存代人领取规则出错saveBaseRule",e);
            throw new BusinessException(CompensationException.SAVE_REPLACE_RULE_ERROR);
        }finally {
            lock.unlock();
        }
        return result;
    }

    private DataConfigDO buildReplaceConfig(CompensationConfigEnum replaceConfigType, String createdBy, String value){
        DataConfigDO replaceConfig=new DataConfigDO();
        replaceConfig.setUpdatedBy(createdBy);
        replaceConfig.setCreatedBy(createdBy);
        replaceConfig.setCodeCn(UserContext.getCurrentUser().getTenantName());
        replaceConfig.setAirCode(UserContext.getCurrentUser().getTenantCode());
        replaceConfig.setDeleted(false);
        replaceConfig.setType(replaceConfigType.getType());
        replaceConfig.setSubType(replaceConfigType.getSubType());
        replaceConfig.setDescription(replaceConfigType.getDescription());
        replaceConfig.setValue(value);

        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, createdBy, null);
        if(CollectionUtils.isNotEmpty(reviewer)){
            replaceConfig.setUpdatedBy(reviewer.get(0).getReviewerNameNo());
            replaceConfig.setCreatedBy(reviewer.get(0).getReviewerNameNo());
        }
        return replaceConfig;
    }



    @Cached(name = CACHE_NAME, key = CACHE_KEY,expire = 3600, cacheType = CacheType.REMOTE, serialPolicy = SerialPolicy.KRYO, postCondition = "result!=null")
    @Override
    public Map<String,ReplaceRuleDTO> getRule() {
        Map<String,ReplaceRuleDTO> result = new HashMap<>();
        List<DataConfigDO> replaceRules = replaceConfigService.findByType(CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getType());
        if (replaceRules == null || replaceRules.isEmpty()){
            return null;
        }
        Map<String, List<DataConfigDO>> collect = replaceRules.stream().collect(Collectors.groupingBy(DataConfigDO::getAirCode));
        collect.forEach((k,v)->{
            if(CollectionUtil.isNotEmpty(v)){
                ReplaceRuleDTO replaceRule = createDefaultRule();
                v.forEach(rule->{
                    wrapReplaceRule(replaceRule,rule);
                    result.put(k,replaceRule);
                });
            }
        });
        return result;

    }

    @CacheInvalidate(name = CACHE_NAME, key = CACHE_KEY)
    @Override
    public boolean savePayWaitPeriod(ReplacePayPeriodRuleDTO dto) {

        RLock lock = redisson.getLock(REDISSON_KEY);
        boolean result = false;

        try {
            log.info("savePayWaitPeriod start");
            //添加分布式锁，防止并发修改规则
            boolean resLock = lock.tryLock(REPLACE_RULE_SAVE_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if(resLock){

                log.info("savePayWaitPeriod 获取分布式锁成功");
                //逻辑删除（禁用）当前规则
                DataConfigDO oldPaymentWaitingPeriod = replaceConfigService.findByTypeAndSubType(CompensationConfigEnum.RULE_PAYMENT_WAITING_PERIOD.getType(),
                        CompensationConfigEnum.RULE_PAYMENT_WAITING_PERIOD.getSubType());
                if(oldPaymentWaitingPeriod!=null){
                    oldPaymentWaitingPeriod.setUpdatedBy(dto.getCreatedBy());
                    oldPaymentWaitingPeriod.setUpdatedTime(LocalDateTime.now());
                    oldPaymentWaitingPeriod.setDeleted(true);
                    replaceConfigService.saveOrUpdate(oldPaymentWaitingPeriod);
                }


                DataConfigDO newPaymentWaitingPeriod=buildReplaceConfig(CompensationConfigEnum.RULE_PAYMENT_WAITING_PERIOD,
                        dto.getCreatedBy(),
                        String.valueOf(dto.getPaymentWaitingPeriod()));


                result=replaceConfigService.save(newPaymentWaitingPeriod);

                log.info("savePayWaitPeriod end,result:{}",result);
            }

        } catch (Exception e) {
            e.printStackTrace();
            log.error("保存代人领取支付等待期规则出错savePayWaitPeriod",e);
            throw new BusinessException(CompensationException.SAVE_REPLACE_RULE_ERROR);
        }finally {
            lock.unlock();
        }
        return result;
    }

    private ReplaceRuleDTO createDefaultRule(){
        ReplaceRuleDTO replaceRuleDTO=new ReplaceRuleDTO();
        replaceRuleDTO.setIsContainSelf(DEFAULT_RULE_IS_CONTAIN_SELF);
        replaceRuleDTO.setIsSameFlight(DEFAULT_RULE_IS_SAME_FLIGHT);
        replaceRuleDTO.setMaxApplyPassenger(DEFAULT_RULE_MAX_APPLY_PASSENGER);
        replaceRuleDTO.setPaymentWaitingPeriod(DEFAULT_PAYMENT_WAITING_PERIOD);
        return replaceRuleDTO;
    }

    private void wrapReplaceRule(ReplaceRuleDTO replaceRule, DataConfigDO config) {
        String subType = config.getSubType();
        if(StringUtils.equalsIgnoreCase(CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getSubType(),subType)){
            replaceRule.setIsContainSelf(config.getValue());
        }

        if(StringUtils.equalsIgnoreCase(CompensationConfigEnum.RULE_IS_SAME_FLIGHT.getSubType(),subType)){
            replaceRule.setIsSameFlight(config.getValue());
        }

        if(StringUtils.equalsIgnoreCase(CompensationConfigEnum.RULE_MAX_APPLY_PASSENGER.getSubType(),subType)){
            replaceRule.setMaxApplyPassenger(Integer.parseInt(config.getValue()));
        }

        if(StringUtils.equalsIgnoreCase(CompensationConfigEnum.RULE_PAYMENT_WAITING_PERIOD.getSubType(),subType)){
            replaceRule.setPaymentWaitingPeriod(Integer.parseInt(config.getValue()));
        }
    }
}
