package com.swcares.aps.compensation.impl.datasync.controller;

import com.swcares.aps.compensation.impl.datasync.service.receive.BusinessDataSyncService;
import com.swcares.aps.compensation.impl.datasync.service.receive.ConfigurationDataSyncService;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessConfigureDataCoordinateDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessDataUploadDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：OverBookController
 * @Description：航司端同步机场端-业务数据同步接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 10:52
 * @version： v1.0
 */
@RestController
@RequestMapping("/configureDataSync")
@Api(tags = "航司端同步机场端-接口")
@ApiVersion(value = "航司端同步机场端-配置数据接口 v1.0")
public class ConfigureDataSyncController extends BaseController {

    @Autowired
    ConfigurationDataSyncService configurationDataSyncService;

    @PostMapping("/syncConfigureData")
    @ApiOperation(value = "航司端同步机场端的配置数据")
    public BaseResult<Object> syncAirportBusinessData(@RequestBody BusinessConfigureDataCoordinateDTO businessDataDTO){
        configurationDataSyncService.compensationConfigurationDataSync(businessDataDTO);
        return ok();
    }


}
