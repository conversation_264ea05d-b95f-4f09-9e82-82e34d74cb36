package com.swcares.aps.compensation.impl.irregularflight.service;

import com.swcares.aps.compensation.model.irregularflight.vo.CityCodeVO;

import java.util.List;
import java.util.Set;

/**
 * @ClassName：CompensationImplBasicDataService
 * @Description：赔付模块-使用的公共接口【如：三字码下拉获取】
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/15 14:52
 * @version： v1.0
 */
public interface CompensationImplFltPaxDataService {

    List<CityCodeVO> getTerminal();

    String getAlternateAndStop(String date,String flightNo);

    /**
     * @title getFltAirStation
     * @description 获取航班-航站（始发航站，到达航站，计划备降航站，实际备降航站，经停站）
     * <AUTHOR>
     * @date 2024/6/11 11:07

     * @return void
     */
    Set<String> getFltAirStation(String date, String flightNo, String choiceSegment);
}
