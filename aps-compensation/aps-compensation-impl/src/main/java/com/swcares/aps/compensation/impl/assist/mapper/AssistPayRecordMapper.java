package com.swcares.aps.compensation.impl.assist.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.assist.entity.AssistPayRecordDO;
import com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: AssistPayRecordMapper
 * @projectName aps
 * @description:
 * @date 2022/2/24 13:26
 */
@Mapper
public interface AssistPayRecordMapper extends BaseMapper<AssistPayRecordDO> {
    /**
     * @title isExpire
     * @description 根据手机号修改记录为失效状态
     * @param phone
     * <AUTHOR>
     * @return void
     * @date 2022/2/24 13:30
     */
    @Update("update assists_pay_record set expire ='Y' where phone = #{phone} ")
    void updateExpire(String phone);

    /**
     * @title receivedPaxAmount
     * @description 通过手机号查询半年内协助旅客id
     * @param phone
     * <AUTHOR>
     * @return java.util.Set<java.lang.String>
     * @date 2022/2/24 13:39
     */
    @Select("select CONCAT(cpi.pax_name,cpi.id_no) as paxInfo from assists_pay_record apr LEFT JOIN  " +
            "compensation_pax_info cpi on cpi.id = apr.pax_id " +
            "where phone = #{phone} and expire = 'N'")
    Set<String> receivedPaxAmount(String phone);

    /**
    * @title existPhone
    * @description 查询该手机号是否是第一次使用
    * @param phone
    * <AUTHOR>
    * @return int
    * @date 2022/2/24 14:05
    */
    @Select("select count(*) from assists_pay_record where phone = #{phone} ")
    int existPhone(String phone);

    /**
     * @title findRecord
     * @description 判断手机号是否过期（利用查询是否有超过半年的未失效记录来判断）
     * @param phone
     * <AUTHOR>
     * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO>
     * @date 2022/2/24 13:47
     */
    @Select(value = "select phone,pax_id,created_time from assists_pay_record where phone = #{phone} and expire = 'N' " +
            "and DATE_SUB(CURDATE(), INTERVAL 6 MONTH) > created_time", databaseId = "mysql")
    @Select(value = "select phone,pax_id,created_time from assists_pay_record where phone = #{phone} and expire = 'N' " +
            "and add_months(sysdate, -6) > created_time", databaseId = "oracle")
    List<AssistPayRecordVO> findRecord(String phone);
}
