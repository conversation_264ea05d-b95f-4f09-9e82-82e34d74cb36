package com.swcares.aps.compensation.impl.irregularflight.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxFrozenDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationPaxInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FreezeOrderPaxDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxOrderInfoQueryDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationPaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.service.CompensationPaxInfoService <br>
 * Description： 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface CompensationPaxInfoService extends IService<CompensationPaxInfoDO> {

    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：傅欣荣<br>
     * date：2021-10-27 <br>
     * @param id <br>
     * @return <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：傅欣荣 <br>
     * date：2021-10-27 <br>
     * @param dto <br>
     * @return <br>
     */
    IPage<CompensationPaxInfoVO> page(CompensationPaxInfoPagedDTO dto);

    /**
     * Title：getByOrderId <br>
     * Description： 根据orderid查补偿单旅客信息<br>
     * author：傅欣荣 <br>
     * date：2021/11/1 9:42 <br>
     * @param paxFrozenDTO
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO>
     */
    List<CompensationChoicePaxVO> findChoicePax(CompensationPaxFrozenDTO paxFrozenDTO);

    /**
     * Title： getPaxOrderInfo<br>
     * Description： 旅客赔偿次数-详情List<br>
     * author：傅欣荣 <br>
     * date：2021/11/1 15:37 <br>
     * @param dto
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO>
     */
    List<PaxCompensationCountVO> getPaxOrderInfo(PaxOrderInfoQueryDTO dto);

    /**
     * Title： freezeOrderPax<br>
     * Description： 冻结||解冻 旅客<br>
     * author：傅欣荣 <br>
     * date：2021/11/9 14:46 <br>
     * @param freezeOrderPaxDTO
     * @return boolean
     */
    Integer freezeOrderPax(FreezeOrderPaxDTO freezeOrderPaxDTO);
    /**
     * @title findSelectedPax
     * @description 根据赔偿单id查询已选择旅客
     * <AUTHOR>
     * @date 2022/2/23 16:25
     * @param orderId
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO>
     */
    List<ChoosePaxInfoVO> findSelectedPax(Long orderId);
//    List<ChoosePaxInfoVO> findSelectedPax(ChoosePaxSearchDTO choosePaxSearchDTO);


    /**
     * Title：updatePaxReceiveInfo <br>
     * Description：申领单保存时修改旅客申领信息 <br>
     * author：王磊 <br>
     * date：2021/12/2 15:36 <br>
     * @param paxIds
     * @param receiveChannel
     * @param receiveWay
     * @param receiveStatus <br>
     * @return <br>
     */
    boolean updatePaxReceiveInfo(List<Long> paxIds,String receiveChannel,String receiveWay,String receiveStatus);

    /**
     * @title updateReceiveStatus
     * @description @TODO
     * <AUTHOR>
     * @date 2022/5/7 11:16
     * @param compensationPaxId
     * @param key
     * @return void
     */
    boolean updateReceiveStatus(String compensationPaxId, String key);

    /**
     *
     * @param paxId
     * @param compensationId
     * @param receiveStatus
     * @return
     */
    boolean updateReceiveStatus(String paxId, String compensationId, String receiveStatus);
}

