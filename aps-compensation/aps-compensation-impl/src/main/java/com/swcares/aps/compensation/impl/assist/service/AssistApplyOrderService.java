package com.swcares.aps.compensation.impl.assist.service;

import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.baseframe.common.base.BaseResult;

/**
 * <AUTHOR>
 * @title: AssistApplyOrderService
 * @projectName aps
 * @description:
 * @date 2022/1/20 16:31
 */
public interface AssistApplyOrderService {
    /**
     * @title saveBehalfApply
     * @description 储存申领单，调用王磊接口
     * <AUTHOR>
     * @date 2022/1/20 16:37
     * @param dto
     * @return boolean
     */
    BaseResult<Object> saveAssistApply(ApplyBehalfOrderDTO dto);

    /**
     * @title findPaxAmount
     * @description 通过手机号查询半年内协助领取人数
     * @date 2022/3/2 10:48
     * <AUTHOR>
     * @param phone
     * @param idNo
     * @param paxName
     * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
     */
    BaseResult<Object> findPaxAmount(String phone,String idNo,String paxName);
}