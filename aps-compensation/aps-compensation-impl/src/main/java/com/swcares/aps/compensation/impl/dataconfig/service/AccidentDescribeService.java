package com.swcares.aps.compensation.impl.dataconfig.service;

import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;

import java.util.List;

public interface AccidentDescribeService {
    List<CompensationConfigVO> getAll(String airCode);

    boolean delete(CompensationConfigDeleteDTO deleteDTO);

    boolean update(CompensationConfigDTO dto);

    boolean create(CompensationConfigDTO dto);
}
