package com.swcares.aps.compensation.impl.complaint.service.impl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.FltPassengerDataSourceServiceFactory;
import com.swcares.aps.basic.data.remoteapi.api.PassengerBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationFactory;
import com.swcares.aps.compensation.impl.compensation.service.impl.CompensationAuditServiceImpl;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintCompensationInfoMapper;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.impl.CompensationPaxInfoServiceImpl;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.complaint.dto.*;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.GeneralCompensationOrderInfoDetailVO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.mybatis.base.BaseServiceImpl;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintCompensationService <br>
 * Description：旅客投诉事故单汇总层 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * @version v1.0 <br>
 * @Date 2024/5/27 11:17 <br>
 */
@Slf4j
@Service
public class ComplaintCompensationService extends BaseServiceImpl<ComplaintCompensationInfoMapper, CompensationOrderInfoDO> {

    @Autowired
    ComplaintAccidentService complaintAccidentService;

    @Autowired
    FltPassengerDataSourceServiceFactory fltPassengerDataSourceServiceFactory;

    @Autowired
    ComplaintCompensationInfoMapper complaintCompensationInfoMapper;

    @Autowired
    CompensationPaxInfoServiceImpl compensationPaxInfoService;

    @Autowired
    PassengerAccidentInfoService passengerAccidentInfoService;

    @Autowired
    CompensationOrderInfoMapper compensationOrderInfoMapper;

    @Autowired
    CompensationBasicDataService compensationBasicDataService;

    @Autowired
    CompensationFlightInfoService compensationFlightInfoService;

    @Autowired
    AccidentCompensationDomainService accidentCompensationDomainService;

    @Autowired
    private FieldEncryptor encryptor;

    @Autowired
    private CompensationFactory compensationFactory;


    @Autowired
    private Redisson redisson;
    @Autowired
    ComplaintCompensationService complaintCompensationService;
    private static final String COMPLAINT_COMPENSATION_SAVE_LOCK_PREFIX = "complaint_compensation_save_lock_prefix";
    /**
     * Title: 旅客投诉事故单旅客列表查询
     *
     * @param passengerSelectInfoDto PassengerSelectInfoDto
     * @return List<ComplaintPassengerListVo>
     * <AUTHOR>
     * @date 2024/5/22 11:18
     * @since 2024/5/22
     */
    public List<ComplaintPassengerListVo> select(PassengerSelectInfoDto passengerSelectInfoDto) {
        PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO();
        passengerQueryDTO.setFlightDate(passengerSelectInfoDto.getFlightDate());
        passengerQueryDTO.setFlightNo(passengerSelectInfoDto.getFlightNo());
        passengerQueryDTO.setChoiceSegment(passengerSelectInfoDto.getSegment());
        passengerQueryDTO.setCompensateType(passengerSelectInfoDto.getCompensateType());
        if (StrUtil.isNotBlank(passengerSelectInfoDto.getPredicate())) {
            passengerQueryDTO.setKeySearch("%" + passengerSelectInfoDto.getPredicate() + "%");
            passengerQueryDTO.setEncryptionKeySearch(encryptor.encrypt(passengerSelectInfoDto.getPredicate()));
        }
        PassengerBasicDataService passengerService = fltPassengerDataSourceServiceFactory.getPassengerService();
        List<PassengerBasicInfoVO> passengerInfo = passengerService.getPassengerInfo(passengerQueryDTO);
        List<ComplaintPassengerListVo> result = new ArrayList<>();
        if (passengerInfo != null && passengerInfo.size() > 0) {
            List<CompensationCountByPassengerInfoVo> compensationCountByPassengerInfoVos =
                    complaintCompensationInfoMapper.selectCompensationCountByPassengerInfo(passengerSelectInfoDto);
            Map<String, String> compensationCountMap = compensationCountByPassengerInfoVos.stream()
                    .collect(Collectors.toMap(CompensationCountByPassengerInfoVo::getTicketNumber, CompensationCountByPassengerInfoVo::getFrequency));
            passengerInfo.forEach(passengerBasicInfoVO -> {
                ComplaintPassengerListVo complaintPassengerListVo = new ComplaintPassengerListVo();
                complaintPassengerListVo.setPaxName(passengerBasicInfoVO.getPaxName());
                complaintPassengerListVo.setIdType(passengerBasicInfoVO.getIdType());
                complaintPassengerListVo.setIdNo(passengerBasicInfoVO.getIdNo());
                complaintPassengerListVo.setSegment(passengerBasicInfoVO.getSegment());
                complaintPassengerListVo.setTktNo(passengerBasicInfoVO.getTktNo());
                if (compensationCountMap != null && compensationCountMap.size() > 0) {
                    complaintPassengerListVo.setFrequency(Integer.valueOf(compensationCountMap.get(passengerBasicInfoVO.getTktNo()) != null ? compensationCountMap.get(passengerBasicInfoVO.getTktNo()) : "0"));
                } else {
                    complaintPassengerListVo.setFrequency(0);
                }
                complaintPassengerListVo.setMainClass(passengerBasicInfoVO.getMainClass());
                complaintPassengerListVo.setSubClass(passengerBasicInfoVO.getSubClass());
                complaintPassengerListVo.setCheckIn(passengerBasicInfoVO.getCheckStatus());
                complaintPassengerListVo.setIsCancel(passengerBasicInfoVO.getIsCancel());
                complaintPassengerListVo.setCancelTime(passengerBasicInfoVO.getCancelTime());
                complaintPassengerListVo.setTktIssueTime(passengerBasicInfoVO.getTktDate());
                complaintPassengerListVo.setPnr(passengerBasicInfoVO.getPnr());
                complaintPassengerListVo.setIsChild(passengerBasicInfoVO.getIsChild());
                complaintPassengerListVo.setIsBaby(passengerBasicInfoVO.getWithBaby());
                result.add(complaintPassengerListVo);
            });
        }
        return passengerInfoSort(result);
    }

    public List<CompensationCountByPassengerInfoVo> getFrequency(PassengerQueryDTO dto) {
        PassengerSelectInfoDto selectInfoDto = new PassengerSelectInfoDto();
        selectInfoDto.setFlightDate(dto.getFlightDate());
        selectInfoDto.setFlightNo(dto.getFlightNo());
        selectInfoDto.setSegment(dto.getChoiceSegment());
        selectInfoDto.setCompensateType(dto.getCompensateType());
        return complaintCompensationInfoMapper.selectCompensationCountByPassengerInfo(selectInfoDto);
    }

    /**
     * 对旅客信息进行排序。
     *
     * @param result 待排序的旅客信息列表，列表中的每一项包含旅客的详细信息。
     * @return 排序后的旅客信息列表。排序规则为：
     * 首先按照旅客的证件号码分组，同一证件号码的旅客按照购票时间正序排序；
     * 不同证件号码的旅客，按照补偿次数倒序排序，补偿次数相同则按照购票时间正序排序
     */
    public List<ComplaintPassengerListVo> passengerInfoSort(List<ComplaintPassengerListVo> result) {
        if (result.size() == 0) {
            return result;
        }
        Map<String, List<ComplaintPassengerListVo>> collect = result.stream().collect(Collectors.groupingBy(ComplaintPassengerListVo::getIdNo));
        List<ComplaintPassengerListVo> head = new ArrayList<>(result.size());
        List<ComplaintPassengerListVo> tail = new ArrayList<>();
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                v.forEach(head::add);
            } else {
                tail.add(v.get(0));
            }
        });
        head.sort(Comparator.comparing(ComplaintPassengerListVo::getIdNo).thenComparing(ComplaintPassengerListVo::getTktIssueTime));
        tail.sort(Comparator.comparing(ComplaintPassengerListVo::getFrequency).reversed().thenComparing(ComplaintPassengerListVo::getTktIssueTime));
        head.addAll(tail);
        return head;
    }

    /**
     * Title: 旅客总补偿次数列表
     *
     * @param passengerSelectInfoDto PassengerSelectInfoDto
     * @return CompensationCountListVo
     * <AUTHOR>
     * @date 2024/5/27 15:52
     * @since 2024/5/27
     */
    public CompensationCountListVo countList(PassengerSelectInfoDto passengerSelectInfoDto) {
        CompensationCountListVo compensationCountListVo = new CompensationCountListVo();
        List<CompensationCountVo> compensationCountVos = complaintCompensationInfoMapper.selectCompensationList(passengerSelectInfoDto);
        if (compensationCountListVo != null && compensationCountVos.size() > 0) {
            compensationCountListVo.setCompensationCountVos(compensationCountVos);
            compensationCountListVo.setTotalAmount(compensationCountVos.stream()
                    .map(CompensationCountVo::getCurrentAmount)
                    .map(BigDecimal::new) // 直接使用BigDecimal构造函数，避免Double转换
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
        }
        return compensationCountListVo;
    }


    @Autowired
    CompensationAuditServiceImpl compensationAuditService;

    @Autowired
    CompensationOrderInfoService compensationOrderInfoService;



    public Long create(ComplaintCompensationCreateDto complaintCompensationCreateDto){
        //加锁
        // 取所有票号拼接（用逗号分隔，或其它分隔符）
        String tktNoKey = complaintCompensationCreateDto.getPassengerComplaintDtoList().stream()
                .map(PassengerComplaintDto::getTicketNumber)
                .collect(Collectors.joining(","));
        //锁对象：事故单id、旅客票号
        String lockKey = COMPLAINT_COMPENSATION_SAVE_LOCK_PREFIX+complaintCompensationCreateDto.getId()+tktNoKey;
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            //即不等待，锁自动过期5秒，赔偿单创建大概耗时: 91 ms
            locked = r.tryLock(0, 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【旅客投诉-赔偿单创建】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【旅客投诉-赔偿单创建】加锁对象：事故单id【{}】，旅客票号【{}】，获取锁失败，说明该单子已经被创建了~"
                    ,complaintCompensationCreateDto.getId(),tktNoKey);
            throw new BusinessException(BaggageAccidentException.COMPENSATION_DUPLICATE_SUBMISSION);
        }
        log.info("【旅客投诉-赔偿单创建】加锁对象：事故单id【{}】，旅客票号【{}】，获取锁成功，执行业务逻辑~"
                ,complaintCompensationCreateDto.getId(),tktNoKey);
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        return complaintCompensationService.doCreate(complaintCompensationCreateDto);
    }

    /**
     * Title: create complaint compensation info and return orderNo
     *
     * @param complaintCompensationCreateDto ComplaintCompensationCreateDto
     * @return String orderNo
     * <AUTHOR>
     * @date 2024/5/29 14:30
     * @since 2024/5/29
     */

    @Transactional(rollbackFor = Exception.class)
    public Long doCreate(ComplaintCompensationCreateDto complaintCompensationCreateDto) {
        compensationOrderInfoService.verifyAirportBusinessPrivilege(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationAirport(), BusinessDataSyncConstant.BUSINESS_COMPLAINT);
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = complaintAccidentService.getById(complaintCompensationCreateDto.getId());
        // draft and invalid cant create
        if (AccidentStatusEnum.DRAFT.getValue().equals(complaintAccidentInfoEntity.getAccidentStatus()) || AccidentStatusEnum.TO_VOID.getValue().equals(complaintAccidentInfoEntity.getAccidentStatus())) {
            return null;
        }
        CompensationOrderInfoDO compensationOrderInfoDO = new CompensationOrderInfoDO();
        CompensationOrderInfoDO draftDo = null;
        if (complaintCompensationCreateDto.getOrderId() != null) {
            draftDo = this.getById(complaintCompensationCreateDto.getOrderId());
        }
        if (draftDo!=null){
            compensationOrderInfoDO.setId(draftDo.getId());
            compensationOrderInfoDO.setOrderNo(draftDo.getOrderNo());
            // 删除航班信息和旅客信息
            List<CompensationFlightInfoDO> flightInfoDOS = compensationFlightInfoService.lambdaQuery().eq(CompensationFlightInfoDO::getOrderId, draftDo.getId()).list();
            if (flightInfoDOS != null && flightInfoDOS.size() > 0){
                compensationFlightInfoService.removeByIds(flightInfoDOS.stream().map(CompensationFlightInfoDO::getId).collect(Collectors.toList()));
            }
            List<CompensationPaxInfoDO> paxInfoDOS = compensationPaxInfoService.lambdaQuery().eq(CompensationPaxInfoDO::getOrderId, draftDo.getId()).list();
            if (paxInfoDOS != null && paxInfoDOS.size() > 0){
                compensationPaxInfoService.removeByIds(paxInfoDOS.stream().map(CompensationPaxInfoDO::getId).collect(Collectors.toList()));
            }
        }else{
            compensationOrderInfoDO.setOrderNo(CompensationOrderNoUtils.getOrderNumber(UserContext.getCurrentUser().getTenantCode(), CompensationOrderNoUtils.complaintCompensationCode));
            compensationOrderInfoDO.setStatus(AccidentStatusEnum.DRAFT.getValue());
        }
        compensationOrderInfoDO.setServiceCity(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationAirport());
        compensationOrderInfoDO.setCompensateType(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationWay());
        compensationOrderInfoDO.setCompensateSubType(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationWaySubType());
        compensationOrderInfoDO.setCompensateStandard(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationAmountSubType());
        compensationOrderInfoDO.setAccidentNo(complaintAccidentInfoEntity.getAccidentId());
        compensationOrderInfoDO.setAccidentId(complaintAccidentInfoEntity.getId());
        // flightInfo add used by apply
        // 这里要改申领的逻辑，对于旅客投诉来说存航班信息表没有任何意义
        FlightBaseQueryDTO flightBaseQueryDTO = new FlightBaseQueryDTO();
        flightBaseQueryDTO.setFlightNo(complaintAccidentInfoEntity.getFlightNo());
        flightBaseQueryDTO.setFlightDate(DateUtil.formatDate(complaintAccidentInfoEntity.getFlightDate()));

        compensationOrderInfoDO.setChoiceSegment(complaintAccidentInfoEntity.getSegment());
        compensationOrderInfoDO.setChoiceSegmentCh(complaintAccidentInfoEntity.getSegmentCh());
        compensationOrderInfoDO.setSource(complaintAccidentInfoEntity.getAccidentSource());
        compensationOrderInfoDO.setBelongAirline(complaintAccidentInfoEntity.getBelongAirline());
        compensationOrderInfoDO.setBelongAirlineAbbr(complaintAccidentInfoEntity.getBelongAirlineAbbr());
        compensationOrderInfoDO.setFlightDate(DateUtil.formatDate(complaintAccidentInfoEntity.getFlightDate()));
        compensationOrderInfoDO.setFlightNo(complaintAccidentInfoEntity.getFlightNo());
        compensationOrderInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        compensationOrderInfoDO.setCreatedTime(LocalDateTime.now());
        compensationOrderInfoDO.setRemark(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationReason());
        compensationOrderInfoDO.setSumMoney(calculateTotalAmount(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationPassengerAmountDto()));
        compensationOrderInfoDO.setAccidentType(complaintAccidentInfoEntity.getAccidentType());
        compensationOrderInfoDO.setAccidentSubType(complaintAccidentInfoEntity.getAccidentSubType());
        this.saveOrUpdate(compensationOrderInfoDO);
        //complaintCompensationInfoMapper.insert(compensationOrderInfoDO);

        List<FlightBasicnfoVO> flightBasicInfo = compensationBasicDataService.getFlightBasicInfo(flightBaseQueryDTO);
        if (flightBasicInfo != null && flightBasicInfo.size() > 0){
            compensationOrderInfoDO.setFlightId(flightBasicInfo.get(0).getFlightId());
            CompensationFlightInfoDO flightInfoDO = new CompensationFlightInfoDO();
            flightInfoDO.setOrderId(compensationOrderInfoDO.getId());
            flightInfoDO.setFlightDate(compensationOrderInfoDO.getFlightDate());
            flightInfoDO.setFlightNo(compensationOrderInfoDO.getFlightNo());
            flightInfoDO.setFlightId(compensationOrderInfoDO.getFlightId());
            flightInfoDO.setSegment(compensationOrderInfoDO.getChoiceSegment());
            flightInfoDO.setSegmentCh(compensationOrderInfoDO.getChoiceSegmentCh());
            flightInfoDO.setStd(formatEtdStd(flightBasicInfo.get(0).getStd()));
            flightInfoDO.setEtd(formatEtdStd(flightBasicInfo.get(0).getEtd()));
            flightInfoDO.setAcType(flightBasicInfo.get(0).getAcType());
            flightInfoDO.setPlaneCode(flightBasicInfo.get(0).getAcReg());
            flightInfoDO.setSta(formatEtdStd(flightBasicInfo.get(0).getSta()));
            flightInfoDO.setEta(formatEtdStd(flightBasicInfo.get(0).getEta()));
            flightInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
            flightInfoDO.setCreatedTime(LocalDateTime.now());
            compensationFlightInfoService.save(flightInfoDO);
        }
        // first: use compensationAmount property to check input amount is illegal
        if (!checkStandardAmount(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationAmount())) {
            throw new BusinessException(CommonErrors.INPUT_INVALIDATE_ERROR);
        }
        // before code saved compensation_order_info and return id
        // so after saving compensation_order_info, we can get id and save paxInfo
        List<CompensationPassengerAmountDto> compensationPassengerAmountDto = complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationPassengerAmountDto();
        List<CompensationPaxInfoDO> compensationPaxInfoDOList = new ArrayList<>();
        for (CompensationPassengerAmountDto passengerAmountDto : compensationPassengerAmountDto) {
            CompensationPaxInfoDO compensationPaxInfoDO = new CompensationPaxInfoDO();
            PassengerQueryDTO dto = new PassengerQueryDTO();
            dto.setTktNo(passengerAmountDto.getTicketNo());
            dto.setFlightDate(DateUtil.formatDate(complaintAccidentInfoEntity.getFlightDate()));
            dto.setFlightNo(complaintAccidentInfoEntity.getFlightNo());
            // suggest add new api to query single PassengerInfo
            PassengerBasicDataService passengerService = fltPassengerDataSourceServiceFactory.getPassengerService();
            List<PassengerBasicInfoVO> passengerInfo = passengerService.getPassengerInfo(dto);
            if (passengerInfo != null && passengerInfo.size() > 0) {
                compensationPaxInfoDO.setPaxId(passengerInfo.get(0).getPaxId());
                compensationPaxInfoDO.setPaxName(passengerInfo.get(0).getPaxName());
                compensationPaxInfoDO.setIdNo(passengerInfo.get(0).getIdNo());
                compensationPaxInfoDO.setIdType(passengerInfo.get(0).getIdType());
                compensationPaxInfoDO.setPaxStatus(passengerInfo.get(0).getCheckStatus());
                compensationPaxInfoDO.setSex(passengerInfo.get(0).getSex());
                compensationPaxInfoDO.setTelephone(passengerInfo.get(0).getTelephone());
                compensationPaxInfoDO.setSegment(passengerInfo.get(0).getSegment());
                compensationPaxInfoDO.setSegmentCh(passengerInfo.get(0).getSegmentCh());
                compensationPaxInfoDO.setOrgCityAirp(passengerInfo.get(0).getOrgCityAirp());
                compensationPaxInfoDO.setDstCityAirp(passengerInfo.get(0).getDstCityAirp());
                compensationPaxInfoDO.setIsCancel(passengerInfo.get(0).getIsCancel());
                compensationPaxInfoDO.setMainClass(passengerInfo.get(0).getMainClass());
                compensationPaxInfoDO.setSubClass(passengerInfo.get(0).getSubClass());
                compensationPaxInfoDO.setTktNo(passengerInfo.get(0).getTktNo());
                compensationPaxInfoDO.setWithBaby(passengerInfo.get(0).getWithBaby());
                compensationPaxInfoDO.setBabyPaxName(passengerInfo.get(0).getBabyName());
                compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passengerInfo.get(0).getTktDate(), dateTimeFormatter));
                compensationPaxInfoDO.setCurrentAmount(new BigDecimal(passengerAmountDto.getCurrentAmount()));
                compensationPaxInfoDO.setOrderId(compensationOrderInfoDO.getId());
                compensationPaxInfoDO.setIsChild(passengerInfo.get(0).getIsChild());
                compensationPaxInfoDO.setSwitchOff("0");
                compensationPaxInfoDO.setPnr(passengerInfo.get(0).getPnr());
                compensationPaxInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
                compensationPaxInfoDO.setCreatedTime(LocalDateTime.now());
                compensationPaxInfoDO.setCancelTime(StrUtil.isBlank(passengerInfo.get(0).getCancelTime()) ? null : new DateTime(passengerInfo.get(0).getCancelTime(), DatePattern.NORM_DATETIME_PATTERN));
                compensationPaxInfoDOList.add(compensationPaxInfoDO);
            }
        }
        compensationPaxInfoService.saveBatch(compensationPaxInfoDOList);
        complaintAccidentInfoEntity.setAccidentStatus(AccidentStatusEnum.PROCESS.getValue());
        complaintAccidentService.updateById(complaintAccidentInfoEntity);
        // workflow process
        /*if (complaintCompensationCreateDto.getOrderId() != null) {
            CompensationAuditOperationVO compensationAuditOperationVO = accidentCompensationDomainService.reSubmit(compensationFactory.createByOrderId(String.valueOf(compensationOrderInfoDO.getId())), String.valueOf(compensationOrderInfoDO.getId()));
            // 保存
            CompensationAuditInfoDTO build = CompensationAuditInfoDTO.builder().orderId(Long.valueOf(compensationAuditOperationVO.getOrderId())).taskId(compensationAuditOperationVO.getTaskId()).auditorIds(Arrays.stream(((Long[]) compensationAuditOperationVO.getOrderAuditorList())).toArray(Long[]::new)).build();
            compensationAuditService.saveReviewer(build);
        }*/
        return compensationOrderInfoDO.getId();
    }

    /**
     * Title: checkStandardAmount
     *
     * @param compensationAmount String
     * @return Boolean
     * <AUTHOR>
     * @date 2024/5/30 10:50
     * @since 2024/5/30
     */
    private Boolean checkStandardAmount(String compensationAmount) {
        // TODO:query standard Amount
        return true;
    }

    /**
     * Title: fast check compensation info
     *
     * @param fastCreateComplaintInfoDto FastCreateComplaintInfoDto
     * @return ComplaintCompensationCreateVo
     * <AUTHOR>
     * @date 2024/5/30 10:50
     * @since 2024/5/30
     */
    public ComplaintCompensationCreateVo fastCheck(FastCreateComplaintInfoDto fastCreateComplaintInfoDto) {
        return getComplaintCompensationCreateVo(fastCreateComplaintInfoDto.getComplaintCompensationCreateDto());
    }

    /**
     * Title: check compensation info
     *
     * @param complaintCompensationCreateDto FastCreateComplaintInfoDto
     * @return ComplaintCompensationCreateVo
     * <AUTHOR>
     * @date 2024/5/30 10:50
     * @since 2024/5/30
     */
    public ComplaintCompensationCreateVo check(ComplaintCompensationCreateDto complaintCompensationCreateDto) {
        return getComplaintCompensationCreateVo(complaintCompensationCreateDto);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long fastCreate(FastCreateComplaintInfoDto fastCreateComplaintInfoDto) {
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = complaintAccidentService.insertAccident(fastCreateComplaintInfoDto.getPassengerSelectInfo(), fastCreateComplaintInfoDto.getPassengerCompensationInfoDto(), "1",null);
        passengerAccidentInfoService.saveBatch(complaintAccidentService.fetchPassenger(fastCreateComplaintInfoDto.getPassengerComplaintInfoList(), complaintAccidentInfoEntity));
        ComplaintCompensationCreateDto complaintCompensationCreateDto = fastCreateComplaintInfoDto.getComplaintCompensationCreateDto();
        complaintCompensationCreateDto.setId(complaintAccidentInfoEntity.getId());
        return this.create(fastCreateComplaintInfoDto.getComplaintCompensationCreateDto());
    }

    private @NotNull ComplaintCompensationCreateVo getComplaintCompensationCreateVo(ComplaintCompensationCreateDto complaintCompensationCreateDto) {
        ComplaintCompensationCreateVo complaintAccidentCreateCheckVo = new ComplaintCompensationCreateVo();
        List<ComplaintCompensationCheckInfoVo> complaintCompensationCheckInfoVos = complaintCompensationInfoMapper.checkInfo(complaintCompensationCreateDto.getPassengerComplaintDtoList(),complaintCompensationCreateDto.getOrderId());
        if (complaintCompensationCheckInfoVos == null || complaintCompensationCheckInfoVos.size() == 0) {
            complaintAccidentCreateCheckVo.setFlag(false);
            return complaintAccidentCreateCheckVo;
        }
        complaintAccidentCreateCheckVo.setFlag(true);
        complaintAccidentCreateCheckVo.setComplaintAccidentCheckInfoVos(complaintCompensationCheckInfoVos);
        // ticket number 根据票号去查补偿单的旅客信息，然后查出补偿单。
        List<FrequencyVo> frequency = complaintCompensationInfoMapper.frequency(complaintCompensationCreateDto.getCompleteCompensationInfoDto().getCompensationPassengerAmountDto());
        if (frequency != null && frequency.size() > 0) {
            StringBuilder msg = new StringBuilder("该补偿单下");
            frequency.forEach(result -> msg.append(result.getPaxName() + "旅客已有" + result.getFrequency() + "张补偿单,"));
            complaintAccidentCreateCheckVo.setMsg(msg.append("是否继续新建？").toString());
        }
        return complaintAccidentCreateCheckVo;
    }

    public static BigDecimal calculateTotalAmount(List<CompensationPassengerAmountDto> dtoList) {
        BigDecimal total = dtoList.stream()
                .map(dto -> new BigDecimal(dto.getCurrentAmount()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return total;
    }

    public GeneralCompensationOrderInfoDetailVO getCompensationOrderInfo(Long id) {
        return compensationOrderInfoMapper.getCompensationOrderInfo(id,String.valueOf(UserContext.getUserId()));
    }

    public String formatEtdStd(String td) {
        String date = td;
        if(ObjectUtils.isNotEmpty(td)){
            date = DateUtils.formatDate(DateUtils.parseDate(td), "HH:mm");
        }
        return date;
    }


}
