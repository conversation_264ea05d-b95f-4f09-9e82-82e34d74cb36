package com.swcares.aps.compensation.impl.privilege.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeService;
import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/airline/businessPrivilege")
@Api(tags = "机场端业务授权接口")
@ApiVersion(value = "机场端业务授权 v1.0")
public class AirlineBusinessPrivilegeController extends BaseController {

    @Autowired
    BusinessPrivilegeService businessPrivilegeService;

    @ApiOperation(value = "机场端获取事故单补偿单的创建授权")
    @GetMapping("/verify/{code}")
    public BaseResult<Boolean> verify(@RequestParam("code") String typeCode){
        return ok(businessPrivilegeService.getAirlinePrivilege(typeCode));
    }

    @ApiOperation(value = "机场端获取授权航司二字码")
    @GetMapping("/getBelongAirline/{code}")
    public BaseResult<List<BelongAirlineDTO>> getBelongAirline(@RequestParam("code") String typeCode){
        return ok(businessPrivilegeService.getBelongAirline(typeCode));
    }

    @ApiOperation(value="分页查询业务授权列表")
    @PostMapping("/v1/list")
    public PagedResult<List<BusinessPrivilegeDTO>> list(@RequestBody SearchBusinessPrivilegeDTO searchCriteria)
    {
        IPage<BusinessPrivilegeDTO> result = businessPrivilegeService.search(searchCriteria);

        return ok(result);
    }

    @ApiOperation(value="查询指定的业务授权的详情")
    @GetMapping("/v1/detail")
    public BaseResult<BusinessPrivilegeDTO> getDetail(@RequestParam(value = "id") Long id)
    {
        BusinessPrivilegeDTO detail = businessPrivilegeService.getDetail(id);
        return ok(detail);
    }
}
