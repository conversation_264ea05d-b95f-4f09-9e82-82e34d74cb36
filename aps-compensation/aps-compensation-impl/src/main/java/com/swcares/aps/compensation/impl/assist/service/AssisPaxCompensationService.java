package com.swcares.aps.compensation.impl.assist.service;

import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssisPaxService
 * @projectName aps
 * @description: 查询旅客相关的service类
 * @date 2022/1/20 14:04
 */
public interface AssisPaxCompensationService {
    /**
    * @title getPaxCompensation
    * @description 查询具体某个旅客的未领取赔偿单信息
    * <AUTHOR>
    * @date 2022/1/20 14:06
    * @param dto 校验信息封装类
    * @return com.swcares.aps.apply.model.vo.CompensationInfoVO
    */
    CompensationInfoVO getPaxCompensation(AuthPaxDTO dto);

    /**
    * @title sendSMSCode
    * @description 发送短信验证码
    * <AUTHOR>
    * @date 2022/1/20 14:31
    * @param phoneNum 电话号码
    * @return void
    */
    void sendSMSCode(String phoneNum);

    /**
    * @title verificationSMSCode
    * @description 验证验证码是否正确
    * <AUTHOR>
    * @date 2022/1/20 14:32
    * @param phoneNum 电话号码
    * @param code 短信验证码
    * @return void
    */
    void verificationSMSCode(String phoneNum,String code);

    /**
    * @title getPaxCompensationByFlightInfo
    * @description 航班信息查询满足能申领的旅客列表
    * <AUTHOR>
    * @date 2022/1/20 17:35
    * @param flightNo 航班号
    * @param flightDate 航班日期
    * @param name 旅客姓名
    * @return java.util.List<com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO>
    */
    List<AssistPaxListVO> getPaxCompensationByFlightInfo(String flightNo,String flightDate,String name);

    /**
    * @title getPaxName
    * @description 查询该航班下是否存在某个用户
    * <AUTHOR>
    * @date 2022/2/21 22:08
    * @param flightDate
    * @param flightNo
    * @param idNo
    * @return java.lang.String
    */
    String getPaxName(String flightDate,String flightNo,String idNo);
}