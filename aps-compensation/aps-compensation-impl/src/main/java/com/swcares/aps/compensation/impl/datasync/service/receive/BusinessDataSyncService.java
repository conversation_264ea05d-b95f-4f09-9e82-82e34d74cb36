package com.swcares.aps.compensation.impl.datasync.service.receive;

import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessDataUploadDTO;

/**
 * @ClassName：BusinessDataSyncService
 * @Description：航司端同步机场端
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/7/16 10:47
 * @version： v1.0
 */
public interface BusinessDataSyncService {

    /**
     * @title compensationBusinessDataSync
     * @description 赔付业务数据同步【补偿单、事故单、申领单】
     * <AUTHOR>
     * @date 2024/7/16 10:51
     * @param businessDataDTO
     * @return void
     */
    void compensationBusinessDataSync(BusinessDataUploadDTO businessDataDTO);
}
