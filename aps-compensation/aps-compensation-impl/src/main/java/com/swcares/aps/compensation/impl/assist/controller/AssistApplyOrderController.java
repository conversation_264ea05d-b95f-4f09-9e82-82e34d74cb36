package com.swcares.aps.compensation.impl.assist.controller;

import com.swcares.aps.compensation.impl.apply.service.TransactionLockService;
import com.swcares.aps.compensation.impl.assist.service.AssistApplyOrderService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @title: AssistApplyOrderController
 * @projectName aps
 * @description:
 * @date 2022/1/20 16:25
 */
@RestController
@RequestMapping("/assist/apply")
@Api(tags = "申领单接口")
@ApiVersion(value = "现金协助补偿接口")
public class AssistApplyOrderController extends BaseController {

    @Autowired
    private AssistApplyOrderService assistApplyOrderService;

    @Autowired
    private TransactionLockService transactionLockService;

    @PostMapping("/saveBehalfApply")
    @ApiOperation(value = "代领新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveBehalfApply(@RequestBody @Valid ApplyBehalfOrderDTO dto) {
        return transactionLockService.saveApplyAssistOrder(dto);
    }

    @GetMapping("/findPaxAmount")
    @ApiOperation(value = "通过手机号查询半年内协助领取人数")
    public BaseResult<Object> findPaxAmount(@ApiParam(value = "申领人手机号") String phone,@ApiParam(value = "旅客证件号") String idNo,@ApiParam(value = "旅客姓名") String paxName) {
        return assistApplyOrderService.findPaxAmount(phone, idNo, paxName);
    }


}
