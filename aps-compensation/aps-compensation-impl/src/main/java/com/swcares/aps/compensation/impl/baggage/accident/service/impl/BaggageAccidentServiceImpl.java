package com.swcares.aps.compensation.impl.baggage.accident.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageAccidentTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageInputSourceTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportAccidentRelMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportInfoMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.CompensationExpressMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.accident.utils.AssertBaggageAccidentInfo;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintCompensationInfoMapper;
import com.swcares.aps.compensation.impl.complaint.mapper.PassengerAccidentInfoMapper;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.overbook.constant.OverBookException;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapper;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportAccidentRelDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.*;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.complaint.dto.PassengerSelectInfoDto;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.vo.CompensationCountByPassengerInfoVo;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxMaintainSearchDTO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import com.swcares.baseframe.utils.lang.StringUtils;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvUserApi;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: BaggageAccidentServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/3/4 9:48
 */
@Service
@Transactional(rollbackFor = Exception.class)
// @PropertySource("classpath:application.yml")
// @ConfigurationProperties(prefix = "swcares.minio")
@Slf4j
public class BaggageAccidentServiceImpl extends ServiceImpl<BaggageAccidentMapper, BaggageAccidentInfoDO>
        implements BaggageAccidentService {

    private static final String ACCIDENT_NO_PREFIX = "ABA";

    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    @Autowired
    private FieldEncryptor encryptor;

    @Autowired
    private CompensationExpressMapper compensationExpressMapper;

    @Autowired
    private FileAttachmentService fileAttachmentService;

    @Autowired
    private CompensationBasicDataService compensationBasicDataService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    CompensationOrderInfoService compensationOrderInfoService;

    @Autowired
    BusinessPrivilegeMapper businessPrivilegeMapper;

    @Autowired
    BaggageTransportInfoMapper baggageTransportInfoMapper;

    @Autowired
    BaggageTransportAccidentRelMapper baggageTransportAccidentRelMapper;

    @Autowired
    BaggageAccidentMapper baggageAccidentMapper;

    @Autowired
    ComplaintCompensationInfoMapper complaintCompensationInfoMapper;

    @Autowired
    PassengerAccidentInfoMapper passengerAccidentInfoMapper;

    @Autowired
    WorkflowApi workflowApi;

    @Autowired
    ReaptvUserApi reaptvUserApi;

    @Autowired
    BaggageTransportationWorkflowDomainService baggageTransportationWorkflowDomainService;

    @Autowired
    BaggageAccidentServiceImpl baggageAccidentService;

    @Autowired
    private Redisson redisson;

    private static final String BAGGAGE_ACCIDENT_SAVE_LOCK_PREFIX = "baggage_accident_save_lock_prefix";

    @Override
    public Object saveAccident(BaggageAccidentInfoDTO dto) throws IOException {
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】开始储存异常行李事故单,传入信息[{}]，申请人工号【{}】", dto.toString(),ApsUserUtils.getCreatedBy());

        String lockKey = BAGGAGE_ACCIDENT_SAVE_LOCK_PREFIX + dto.getPaxFlightNo() + dto.getPaxFlightDate() + dto.getTktNo();
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            locked = r.tryLock(0, 10, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【异常行李-事故单创建】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【异常行李-事故单创建】加锁对象：航班号【{}】，航班日期【{}】，旅客票号【{}】，获取锁失败，说明该单子已经被创建了~", dto.getPaxFlightNo(), dto.getPaxFlightDate(), dto.getTktNo());
            throw new BusinessException(BaggageAccidentException.ACCIDENT_DUPLICATE_SUBMISSION);
        }
        log.info("【异常行李-事故单创建】加锁对象：航班号【{}】，航班日期【{}】，旅客票号【{}】，获取锁成功，执行业务逻辑~", dto.getPaxFlightNo(), dto.getPaxFlightDate(), dto.getTktNo());
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        return baggageAccidentService.doSaveAccident(dto);
    }

    @Transactional(rollbackFor = Exception.class)
    public Object doSaveAccident(BaggageAccidentInfoDTO dto) throws IOException {
        //判断新建，编辑是否同一人
        String userId = ApsUserUtils.getCreatedBy();
        if (ObjectUtils.isNotEmpty(dto.getId())) {
            BaggageAccidentInfoVO accidentInfo = baseMapper.findById(dto.getId().toString());
            if (ObjectUtils.isEmpty(accidentInfo)) {
                throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
            }
        }
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】完成判断新建/编辑是否同一人");
        //校验不同类型异常行李事故的必要信息是否完善
        checkAccidentInfo(dto);
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】完成校验不同类型异常行李事故的必要信息");

        FocFlightInfoDTO focFlightInfoDTO=new FocFlightInfoDTO();
        focFlightInfoDTO.setFlightNo(dto.getPaxFlightNo());
        focFlightInfoDTO.setFlightDate(dto.getPaxFlightDate());
        List<FocFlightInfoVO> flightInfoVOS = compensationBasicDataService.getFocFlightInfo(focFlightInfoDTO);
        //机型、飞机号。系统录入和手工录入都从航班表获取，
        if(CollectionUtils.isNotEmpty(flightInfoVOS)){
            String[] splitSegment = dto.getPaxSegment().split("-");
            String org = splitSegment[0].substring(splitSegment[0].length() - 3); // 取最后3个字符
            String dst = splitSegment[1].substring(splitSegment[1].length() - 3);
            String segment=org+"-"+dst;
            Optional<FocFlightInfoVO> optional = flightInfoVOS.stream()
                    .filter(vo -> segment.equals(vo.getSegment()))
                    .findFirst();
            if (optional.isPresent()) {
                FocFlightInfoVO vo = optional.get();
                dto.setPlaneCode(vo.getAcReg());
                dto.setFltCode(vo.getAcType());
            }
        }
        //旅客数据输入的来源=SYSTEM（系统查询），需要进行校验
        if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(dto.getInputSource()) && CollectionUtils.isEmpty(flightInfoVOS)) {
            throw new BusinessException(BaggageAccidentException.NO_PAX_FLIGHT);
        }
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】完成旅客数据来源为SYSTEM的校验");
        //手工录入的事故单，生成旅客id，航班id
        if (BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(dto.getInputSource())) {
            long timestamp = LocalDateTime.now().toEpochSecond(java.time.ZoneOffset.UTC) % 1000000; // 时间戳的后6位
            //航班号数字部分+4位随机数+时间戳的后6位
            String flightId = generateFlightId(dto.getPaxFlightNo(), timestamp);
            //证件号后5位+航班号数字部分+时间戳的后6位
            String paxId = generatePaxId(dto.getIdNo(), dto.getPaxFlightNo(), timestamp);
            dto.setPaxFlightId(flightId);//用于手工录入，确定事故单航班
            dto.setPaxId(paxId);
        }
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】完成手工录入生成旅客ID和航班ID");
        BaggageAccidentInfoDO accidentInfoDO = ObjectUtils.copyBean(dto, BaggageAccidentInfoDO.class);
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】储存异常行李事故单,传入信息[{}]", dto.toString());
        if (ObjectUtils.isNotEmpty(dto.getPaxImgs())) {
            String paxImg = "";
            List<MultipartFile> result = new ArrayList<>();
            dto.getPaxImgs().forEach(e -> {
                MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                result.add(f);
            });
            List<FileAttachment> paxFiles = fileAttachmentService.uploadFile(result, bucketName, null);
            for (int i = 0; i < paxFiles.size(); i++) {
                if (i == 0) {
                    paxImg = paxImg + paxFiles.get(i).getId();
                } else {
                    paxImg = paxImg + "," + paxFiles.get(i).getId();
                }
            }
            accidentInfoDO.setCollectIdentityPaxPhotos(paxImg);
        }
        if (ObjectUtils.isNotEmpty(dto.getVoucherImgs())) {
            String voucherImg = "";
            List<MultipartFile> result = new ArrayList<>();
            dto.getVoucherImgs().forEach(e -> {
                MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                result.add(f);
            });
            List<FileAttachment> voucherFiles = fileAttachmentService.uploadFile(result, bucketName, null);
            for (int i = 0; i < voucherFiles.size(); i++) {
                if (i == 0) {
                    voucherImg = voucherImg + voucherFiles.get(i).getId();
                } else {
                    voucherImg = voucherImg + "," + voucherFiles.get(i).getId();
                }
            }
            accidentInfoDO.setCollectIdentityVoucherPhotos(voucherImg);
        }
        if (ObjectUtils.isNotEmpty(dto.getBaggageImgs())) {
            String baggageImg = "";
            List<MultipartFile> result = new ArrayList<>();
            dto.getBaggageImgs().forEach(e -> {
                MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                result.add(f);
            });
            List<FileAttachment> baggageFiles = fileAttachmentService.uploadFile(result, bucketName, null);
            for (int i = 0; i < baggageFiles.size(); i++) {
                if (i == 0) {
                    baggageImg = baggageImg + baggageFiles.get(i).getId();
                } else {
                    baggageImg = baggageImg + "," + baggageFiles.get(i).getId();
                }
            }
            accidentInfoDO.setCollectIdentityBaggagePhotos(baggageImg);
        }
        if (StringUtils.isNotEmpty(dto.getSignature())) {
            MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(dto.getSignature());
            List<FileAttachment> signatureFiles = fileAttachmentService.uploadFile(Arrays.asList(f), bucketName, null);
            String signatureImg = "";
            if (signatureFiles != null) {
                for (int i = 0; i < signatureFiles.size(); i++) {
                    if (i == 0) {
                        signatureImg = signatureImg + signatureFiles.get(i).getId();
                    } else {
                        signatureImg = signatureImg + "," + signatureFiles.get(i).getId();
                    }
                }
                accidentInfoDO.setSignature(signatureImg);
            }
        }
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】图片上传完毕");

        //a-b-c航段时，获取正确的到达航站
        String[] splitSegment = dto.getPaxSegment().split("-");
        accidentInfoDO.setPoa(dto.getPaxSegment().split("-")[splitSegment.length - 1]);
        accidentInfoDO.setPod(dto.getPaxSegment().split("-")[0]);
        accidentInfoDO.setUpdatedBy(userId);
        accidentInfoDO.setCreatedBy(userId);
        accidentInfoDO.setCreatedTime(LocalDateTime.now());
        accidentInfoDO.setUpdatedTime(LocalDateTime.now());
        LoginUserDetails user = UserContext.getUser();
        accidentInfoDO.setBelongAirline(dto.getPaxFlightNo().substring(0, 2));
        accidentInfoDO.setAccidentSource("1");
        accidentInfoDO.setAccidentNo(CompensationOrderNoUtils.getOrderNumber(user.getTenantCode(), CompensationOrderNoUtils.baggageAccidentCode));
        AirlineBusinessPrivilege businessPrivilege = businessPrivilegeMapper.getBusinessPrivilegeBetweenCustomer(accidentInfoDO.getBelongAirline(), UserContext.getCurrentUser().getTenantCode());
        accidentInfoDO.setBelongAirlineAbbr(businessPrivilege.getGrantorName());
        //判断是否是存为草稿
        if (StringUtils.isNotEmpty(dto.getIsDraft())) {
            accidentInfoDO.setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
        } else {
            accidentInfoDO.setAccidentStatus(AccidentStatusEnum.TODO.getValue());
            accidentInfoDO.setTodoTime(LocalDateTime.now());
            if (accidentInfoDO.getType().equals(BaggageAccidentTypeEnum.LESS_INCOME.getValue()))
                accidentInfoDO.setOverTime(LocalDateTime.now().plusDays(dto.getReminderTime()));
        }
        accidentInfoDO.setId(null);
        if (ObjectUtils.isNotEmpty(dto.getId())) {
            BaggageAccidentInfoVO accidentInfo = baseMapper.findById(dto.getId().toString());
            accidentInfoDO.setAccidentNo(accidentInfo.getAccidentNo());
            accidentInfoDO.setCreatedTime(accidentInfo.getCreatedTime());
            this.removeById(dto.getId());
        }
        // 新的内容进行保存
        boolean result = this.save(accidentInfoDO);
        dto.setId(accidentInfoDO.getId());
        BaggageTransportInfoDO existingTransportInfo = baggageTransportInfoMapper.selectOne(
                new LambdaQueryWrapper<BaggageTransportInfoDO>()
                        .eq(BaggageTransportInfoDO::getAccidentNo, accidentInfoDO.getAccidentNo()));
        if (ObjectUtils.isNotEmpty(dto.getId())) {
            if (existingTransportInfo != null) {
                baggageTransportInfoMapper.deleteByAccidentId(accidentInfoDO.getAccidentNo());
                baggageTransportAccidentRelMapper.delete(new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                        .eq(BaggageTransportAccidentRelDO::getTransportId, existingTransportInfo.getId()));
            }
        }
        if (BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(dto.getInputSource())) {
            PassengerQueryDTO passengerQueryDTO = new PassengerQueryDTO();
            passengerQueryDTO.setPaxId(dto.getPaxId());
            List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(passengerQueryDTO);
            if (ObjectUtils.isEmpty(passengerInfo)) {
                throw new BusinessException(OverBookException.NOT_FOUND_PAX);
            }
            PassengerAccidentInfoEntity passengerAccidentInfoEntity = new PassengerAccidentInfoEntity();
            passengerAccidentInfoEntity.setAccidentPrimaryId(accidentInfoDO.getId());
            passengerAccidentInfoEntity.setPaxName(passengerInfo.get(0).getPaxName());
            passengerAccidentInfoEntity.setPhone(dto.getPhone());
            passengerAccidentInfoEntity.setIdNo(passengerInfo.get(0).getIdNo());
            passengerAccidentInfoEntity.setIdType(passengerInfo.get(0).getIdType());
            passengerAccidentInfoEntity.setTktNo(passengerInfo.get(0).getTktNo());
            passengerAccidentInfoEntity.setSegment(passengerInfo.get(0).getSegment());
            passengerAccidentInfoEntity.setMainClass(passengerInfo.get(0).getMainClass());
            passengerAccidentInfoEntity.setSubClass(passengerInfo.get(0).getSubClass());
            passengerAccidentInfoEntity.setCheckIn(passengerInfo.get(0).getCheckStatus());
            passengerAccidentInfoEntity.setIsBaby(passengerInfo.get(0).getWithBaby());
            passengerAccidentInfoEntity.setIsChild(passengerInfo.get(0).getIsChild());
            passengerAccidentInfoEntity.setPnr(passengerInfo.get(0).getPnr());
            passengerAccidentInfoEntity.setCancelTime(DateUtil.parse(passengerInfo.get(0).getCancelTime()));
            passengerAccidentInfoEntity.setTktIssueTime(DateUtil.parse(passengerInfo.get(0).getTktDate()));
            passengerAccidentInfoEntity.setIsCancel(passengerInfo.get(0).getIsCancel());
            // 通过旅客id查询
            passengerAccidentInfoMapper.insert(passengerAccidentInfoEntity);
        }
        if (BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(dto.getInputSource())) {
            PassengerAccidentInfoEntity passengerAccidentInfoEntity = new PassengerAccidentInfoEntity();
            passengerAccidentInfoEntity.setAccidentPrimaryId(accidentInfoDO.getId());
            passengerAccidentInfoEntity.setPhone(dto.getPhone());
            passengerAccidentInfoEntity.setPaxName(dto.getPaxName());
            passengerAccidentInfoEntity.setIdNo(dto.getIdNo());
            passengerAccidentInfoEntity.setIdType(dto.getIdType());
            passengerAccidentInfoEntity.setTktNo(dto.getTktNo());
            passengerAccidentInfoEntity.setSegment(dto.getPaxSegment());
            // 从传入的获取
            passengerAccidentInfoMapper.insert(passengerAccidentInfoEntity);
        }
        BaggageTransportInfoDO baggageTransportInfoDO = saveTransport(dto, accidentInfoDO);
        log.info("【aps-compensation-impl-baggage】储存异常行李事故单,储存信息[{}]", accidentInfoDO.toString());

        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】储存异常行李事故单,储存信息[{}]", accidentInfoDO.toString());
        //推送业务数据到协同中心
        if (!AccidentStatusEnum.DRAFT.getValue().equals(accidentInfoDO.getAccidentStatus())) {
            businessDataPushHandler.dataStore(Long.valueOf(accidentInfoDO.getId()), BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }
        log.info("【新建异常行李事故单】【aps-compensation-impl-baggage】推送业务数据到协同中心完毕");
        return baggageTransportInfoDO;
    }

    public BaggageTransportInfoDO saveTransport(BaggageAccidentInfoDTO dto, BaggageAccidentInfoDO accidentInfoDO) {
        if (dto.getTransportInfoDTO() != null) {
            BaggageTransportInfoDTO transportInfoDTO = dto.getTransportInfoDTO();
            BaggageTransportInfoDO baggageTransportInfoDO = new BaggageTransportInfoDO();
            baggageTransportInfoDO.setTransportAirport(UserContext.getCurrentUser().getTenantCode());
            baggageTransportInfoDO.setBelongAirline(accidentInfoDO.getPaxFlightNo().substring(0,2));
            transportInfoDTO.setAccidentId(accidentInfoDO.getId());
            transportInfoDTO.setAccidentNo(accidentInfoDO.getAccidentNo());
            BeanUtils.copyProperties(transportInfoDTO, baggageTransportInfoDO);
            baggageTransportInfoDO.setMergedTransport(
                    transportInfoDTO.getMergedId() != null && !transportInfoDTO.getMergedId().isEmpty());
            baggageTransportInfoDO.setUpdatedBy(getCurrentOperator());
            baggageTransportInfoDO.setUpdatedTime(LocalDateTime.now());
            boolean insertResult = baggageTransportInfoMapper.insert(baggageTransportInfoDO) > 0;
            // 新增运输单时，添加数据推送记录
            if (insertResult) {
                // 提交审核
                businessDataPushHandler.dataStore(baggageTransportInfoDO.getId(),
                    BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, 
                    BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);
            }
            if (transportInfoDTO.getMergedId() != null && !transportInfoDTO.getMergedId().isEmpty()) {
                for (String mergedAccidentNo : transportInfoDTO.getMergedId()) {
                    if (baggageAccidentMapper.existTransport(mergedAccidentNo) == 0) {
                        BaggageTransportAccidentRelDO relDO = new BaggageTransportAccidentRelDO();
                        relDO.setTransportId(baggageTransportInfoDO.getId());
                        relDO.setAccidentNo(mergedAccidentNo);
                        baggageTransportAccidentRelMapper.insert(relDO);
                    }
                }
            }
            return baggageTransportInfoDO;
        }
        return null;
    }

    public String getCurrentOperator() {
        LoginUserDetails user = UserContext.getUser();
        if (user == null) {
            return "system";
        }
        if (StringUtils.isNotEmpty(user.getEmployeeName())) {
            if (StringUtils.isNotEmpty(user.getJobNumber())) {
                return user.getEmployeeName() + user.getJobNumber();
            }
            return user.getEmployeeName() + user.getUsername();
        }
        return user.getUsername();
    }

    @Override
    public BaggageAccidentInfoVO editAccident(String accidentId) {
        BaggageAccidentInfoVO accidentInfo = baseMapper.findById(accidentId);
        // 异常行李事故单编辑回显，若查询出来不是草稿单不允许回显
        if (accidentInfo == null) {
            return null;
        } else if (accidentInfo.getAccidentStatus().equals(AccidentStatusEnum.DRAFT.getValue())) {
            // 校验机场端 是否有某个航司的事故单及补偿单的创建授权
            compensationOrderInfoService.verifyAirlineBusinessPrivile(
                    BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, accidentInfo.getPaxFlightNo().substring(0, 2));
            BaggageTransportInfoDO baggageTransportInfoDO = baggageTransportInfoMapper.selectOne(
                    new LambdaQueryWrapper<BaggageTransportInfoDO>().eq(BaggageTransportInfoDO::getAccidentNo,
                            accidentInfo.getAccidentNo()));
            if (baggageTransportInfoDO != null) {
                List<BaggageTransportAccidentRelDO> relList = baggageTransportAccidentRelMapper.selectList(
                        new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                .eq(BaggageTransportAccidentRelDO::getTransportId, baggageTransportInfoDO.getId()));
                List<String> mergedAccidentNos = Optional.ofNullable(relList).map(listStream -> listStream.stream()
                        .filter(rel -> rel != null && StringUtils.isNotEmpty(rel.getAccidentNo()))
                        .map(BaggageTransportAccidentRelDO::getAccidentNo)
                        .collect(Collectors.toList()))
                        .orElse(new ArrayList<>());
                accidentInfo.setTransportInfoDTO(
                        BeanUtil.copyProperties(baggageTransportInfoDO, BaggageTransportInfoDTO.class));
                if (accidentInfo.getTransportInfoDTO() != null) {
                    accidentInfo.getTransportInfoDTO().setMergedId(mergedAccidentNos);
                    accidentInfo.getTransportInfoDTO().setEditFlag(canEditTransport(accidentInfo.getAccidentNo()));
                }
            } else {
                BaggageTransportAccidentRelDO relDO = baggageTransportAccidentRelMapper.selectOne(
                        new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                .eq(BaggageTransportAccidentRelDO::getAccidentNo, accidentInfo.getAccidentNo()));
                if (relDO != null) {
                    BaggageTransportInfoDO infoDO = baggageTransportInfoMapper.selectOne(
                            new LambdaQueryWrapper<BaggageTransportInfoDO>().eq(BaggageTransportInfoDO::getId,
                                    relDO.getTransportId()));
                    accidentInfo.setTransportInfoDTO(BeanUtil.copyProperties(infoDO, BaggageTransportInfoDTO.class));
                    List<BaggageTransportAccidentRelDO> relList = baggageTransportAccidentRelMapper.selectList(
                            new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                    .eq(BaggageTransportAccidentRelDO::getTransportId, relDO.getTransportId()));
                    List<String> mergedAccidentNos = Optional.ofNullable(relList).map(listStream -> listStream.stream()
                            .filter(rel -> rel != null && StringUtils.isNotEmpty(rel.getAccidentNo()))
                            .map(BaggageTransportAccidentRelDO::getAccidentNo)
                            .collect(Collectors.toList()))
                            .orElse(new ArrayList<>());
                    if (accidentInfo.getTransportInfoDTO() != null) {
                        accidentInfo.getTransportInfoDTO().setMergedId(mergedAccidentNos);
                        accidentInfo.getTransportInfoDTO().setEditFlag(canEditTransport(accidentInfo.getAccidentNo()));
                    }
                }
            }
            return accidentInfo;
        }
        throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_UNSUPPORTED_OPERATION);

    }

    @Override
    public boolean toLost(String accidentId) {
        BaggageAccidentInfoVO accidentInfo = baseMapper.findById(accidentId);
        // 校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                accidentInfo.getPaxFlightNo().substring(0, 2));

        String s = UserContext.getUserId().toString();
        boolean retBool = SqlHelper.retBool(baseMapper.toLost(accidentId, new Date()));

        // 推送业务数据到协同中心
        businessDataPushHandler.dataStore(Long.valueOf(accidentId), BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        return retBool;

    }

    @Override
    public List<MatchResultVO> toMatch(String accidentId) {
        if (StringUtils.isEmpty(accidentId)) {
            return null;
        }
        BaggageAccidentInfoVO accidentInfoVO = baseMapper.findById(accidentId);
        if (ObjectUtils.isEmpty(accidentInfoVO)) {
            return null;
        }
        MatchIncomeDTO matchIncomeDTO = new MatchIncomeDTO();
        if (accidentInfoVO.getType().equals(BaggageAccidentTypeEnum.LESS_INCOME.getValue())) {
            matchIncomeDTO.setType(BaggageAccidentTypeEnum.OVERCHARGE.getValue());
        } else if (accidentInfoVO.getType().equals(BaggageAccidentTypeEnum.OVERCHARGE.getValue())) {
            matchIncomeDTO.setType(BaggageAccidentTypeEnum.LESS_INCOME.getValue());
        } else {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_UNSUPPORTED_OPERATION);
        }

        matchIncomeDTO.setBaggageNo(accidentInfoVO.getBaggageNo());
        matchIncomeDTO.setFlightDate(accidentInfoVO.getPaxFlightDate());
        matchIncomeDTO.setFlightNo(accidentInfoVO.getPaxFlightNo());
        // matchIncomeDTO.setServeSegment(accidentInfoVO.getServeSegment());
        return baseMapper.toMatch(matchIncomeDTO);
    }

    @Override
    public IPage<FindBaggageVO> findBaggageAccidentList(FindBaggageDTO dto) {
        if ("1".equals(dto.getIdType())) {
            dto.setIdNo(encryptor.encrypt(dto.getIdNo()));
        }
        if (!(StringUtil.isEmpty(dto.getAccidentStatus()))) {
            String accidentStatus = dto.getAccidentStatus();
            String[] accidentStatusSplits = accidentStatus.split(",");
            dto.setAccidentStatusSplits(accidentStatusSplits);
        }
        if (!(StringUtil.isEmpty(dto.getTypes()))) {
            String types = dto.getTypes();
            String[] typesSplits = types.split(",");
            dto.setTypesSplits(typesSplits);
        }
        IPage<FindBaggageVO> baggageAccidentList = baseMapper.findBaggageAccidentList(dto, dto.createPage());
        baggageAccidentList.getRecords().stream().forEach(t -> {
            t.setEditFlag(canEditTransport(t.getAccidentNo()));
            t.setAddFlag(canAddTransport(t.getAccidentNo()));
        });
        baggageAccidentList.getRecords().stream().filter(d -> d.getPaxSegment().split("-").length > 2).forEach(d -> {
            String[] split = d.getPaxSegment().split("-");
            d.setPaxSegment(split[0] + "-" + split[2]);
        });

        return baggageAccidentList;
    }

    /**
     * @param dto
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVo
     * @title findBaggageDetail
     * @description 异常行李事故单详细信息
     * <AUTHOR>
     * @date 2022/3/7 16:07
     */
    @Override
    public BaggageDetailFinalVO findBaggageDetail(FindBaggageDetailDTO dto) {
        if (ObjectUtils.isEmpty(dto.getAccidentId())) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        String no = baseMapper.findNo(dto);
        FindBaggageDetailVO findBaggageDetailVO = baseMapper.findBaggageDetail(dto);
        if (ObjectUtils.isEmpty(findBaggageDetailVO)) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }
        if (StringUtils.isNotEmpty(no)) {
            String[] accidentNos = no.split(",");
            dto.setAccidentNos(accidentNos);
            List<MatchResultVO> matchResult = baseMapper.findMatchResult(dto);
            findBaggageDetailVO.setMatchResultList(matchResult);
        }
        // 查询事故单下的快递信息
        List<BaggageExpressVO> list = compensationExpressMapper.selectExpressListByAccidentId(dto.getAccidentId());
        // 以下操作把详情信息分断展示给前端
        BaggageDetailFinalVO baggageDetailFinalVO = new BaggageDetailFinalVO();

        if (findBaggageDetailVO.getBaggageCompensationList() != null) {
            findBaggageDetailVO.getBaggageCompensationList().forEach(baggageCompensationVO -> {
                if ("4".equals(baggageCompensationVO.getStatus()) || "5".equals(baggageCompensationVO.getStatus())) {
                    HistoryTaskAuditActivityVO historyTaskAuditActivityVO = null;
                    try {
                        historyTaskAuditActivityVO = workflowApi
                                .historyTaskAuditActivity(BaseQueryParamDTO.builder()
                                        .businessKey(baggageCompensationVO.getOrderId()).build())
                                .getData();
                        List<HistoryTaskAuditActivityDTO> auditActivityDTOList = historyTaskAuditActivityVO
                                .getHistoryTaskAuditActivityDTOS();
                        if (!ListUtils.isEmpty(auditActivityDTOList)) {
                            auditActivityDTOList.sort(
                                    Comparator.comparing(HistoryTaskAuditActivityDTO::getTaskCreateTime).reversed());
                            log.info("[aps-workflow-impl】查询审核记录，获取接口结果:【{}】", JSONUtil.toJsonStr(auditActivityDTOList));
                            for (HistoryTaskAuditActivityDTO historyTaskAuditActivityDTO : auditActivityDTOList) {
                                if (StringUtils.isNotEmpty(historyTaskAuditActivityDTO.getAssignee())) {
                                    String[] split = historyTaskAuditActivityDTO.getAssignee().split(":");
                                    ReaptvUserDetailVO reaptvUserDetailVO = reaptvUserApi
                                            .getById(Long.valueOf(split[1])).getData();
                                    log.info("[aps-workflow-impl】查询审核记录，遍历:【{}】",
                                            JSONUtil.toJsonStr(historyTaskAuditActivityDTO));
                                    if (reaptvUserDetailVO != null
                                            && StringUtils.isNotEmpty(reaptvUserDetailVO.getEmployeeName())) {
                                        log.info("[aps-workflow-impl】查询审核记录，结果:【{}】",
                                                JSONUtil.toJsonStr(historyTaskAuditActivityDTO));
                                        baggageCompensationVO.setAssignee(reaptvUserDetailVO.getEmployeeName()
                                                + (StringUtils.isNotEmpty(reaptvUserDetailVO.getJobNumber())
                                                        ? reaptvUserDetailVO.getJobNumber()
                                                        : ""));
                                        break;
                                    }
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("【aps-compensation】查询审核记录，赔偿单id:【{" + baggageCompensationVO.getOrderId() + "}】", e);
                    }
                }
            });
            baggageDetailFinalVO.setCompensations(findBaggageDetailVO.getBaggageCompensationList());// 塞赔偿单信息
        }
        baggageDetailFinalVO.setExpresses(list);// 塞快递信息
        baggageDetailFinalVO.setMatches(findBaggageDetailVO.getMatchResultList());// 塞多收少收信息

        BaggageDetailAccident accidentMessage = new BaggageDetailAccident();// 以下塞事故信息
        accidentMessage.setAccidentId(findBaggageDetailVO.getAccidentId());
        accidentMessage.setSignature(findBaggageDetailVO.getSignature());
        accidentMessage.setInputSource(findBaggageDetailVO.getInputSource());
        accidentMessage.setAccidentNo(findBaggageDetailVO.getAccidentNo());
        accidentMessage.setAccidentStatus(findBaggageDetailVO.getAccidentStatus());
        accidentMessage.setType(findBaggageDetailVO.getType());
        accidentMessage.setAccidentSource(findBaggageDetailVO.getAccidentSource());
        accidentMessage.setReasonType(findBaggageDetailVO.getReasonType());
        accidentMessage.setBelongAirline(findBaggageDetailVO.getBelongAirline());
        accidentMessage.setBelongAirlineAbbr(findBaggageDetailVO.getBelongAirlineAbbr());
        accidentMessage.setServeSegment(findBaggageDetailVO.getServeSegment());
        accidentMessage.setDamageType(findBaggageDetailVO.getDamageType());
        accidentMessage.setDamagePart(findBaggageDetailVO.getDamagePart());
        accidentMessage.setDamageDegree(findBaggageDetailVO.getDamageDegree());
        accidentMessage.setBaggageBrand(findBaggageDetailVO.getBaggageBrand());
        accidentMessage.setLostWeight(findBaggageDetailVO.getLostWeight());
        accidentMessage.setLostType(findBaggageDetailVO.getLostType());
        accidentMessage.setReminderTime(findBaggageDetailVO.getReminderTime());
        accidentMessage.setOverTime(findBaggageDetailVO.getOverTime());
        accidentMessage.setLostTime(findBaggageDetailVO.getLostTime());
        accidentMessage.setConfirmLostTime(findBaggageDetailVO.getConfirmLostTime());
        accidentMessage.setLostAmount(findBaggageDetailVO.getLostAmount());
        accidentMessage.setDeclarationType(findBaggageDetailVO.getDeclarationType());
        accidentMessage.setCreatedBy(findBaggageDetailVO.getCreatedBy());
        accidentMessage.setCreatedTime(findBaggageDetailVO.getCreatedTime());
        accidentMessage.setAccidentReason(findBaggageDetailVO.getAccidentReason());
        accidentMessage.setCollectIdentityPaxPhotos(findBaggageDetailVO.getCollectIdentityPaxPhotos());
        accidentMessage.setCollectIdentityVoucherPhotos(findBaggageDetailVO.getCollectIdentityVoucherPhotos());
        accidentMessage.setCollectIdentityBaggagePhotos(findBaggageDetailVO.getCollectIdentityBaggagePhotos());
        String s = "2";
        accidentMessage.setAccidentType(s);
        accidentMessage.setTovoidBy(findBaggageDetailVO.getTovoidBy());
        accidentMessage.setTovoidTime(findBaggageDetailVO.getTovoidTime());
        accidentMessage.setCloseUser(findBaggageDetailVO.getCloseUser());
        accidentMessage.setCloseTime(findBaggageDetailVO.getCloseTime());

        BaggageDetailFightVO fightMessage = new BaggageDetailFightVO();//以下塞航班信息
        fightMessage.setPaxFlightNo(findBaggageDetailVO.getPaxFlightNo());
        fightMessage.setPaxFlightDate(findBaggageDetailVO.getPaxFlightDate());
        fightMessage.setPlaneCode(findBaggageDetailVO.getPlaneCode());
        fightMessage.setFltCode(findBaggageDetailVO.getFltCode());

        String acSegment = findBaggageDetailVO.getPaxSegment();
        if (findBaggageDetailVO.getPaxSegment().split("-").length > 2) {
            String[] split = findBaggageDetailVO.getPaxSegment().split("-");
            acSegment = split[0] + "-" + split[2];
        }
        fightMessage.setPaxSegment(acSegment);
        fightMessage.setStd(findBaggageDetailVO.getStd());
        fightMessage.setEtd(findBaggageDetailVO.getEtd());

        BaggageDetailPaxVO paxMessage = new BaggageDetailPaxVO();//以下塞旅客信息
        // 改造：从AccidentInfo旅客表中获取数据，使用事故单id
        LambdaQueryWrapper<PassengerAccidentInfoEntity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PassengerAccidentInfoEntity::getAccidentPrimaryId, findBaggageDetailVO.getAccidentId());
        // 只会有一条数据
        PassengerAccidentInfoEntity passengerAccidentInfoEntity = passengerAccidentInfoMapper.selectOne(queryWrapper);
        if (passengerAccidentInfoEntity != null) {
            paxMessage.setPhone(passengerAccidentInfoEntity.getPhone());
            paxMessage.setPaxName(passengerAccidentInfoEntity.getPaxName());
            paxMessage.setPaxEnName(PinyinUtil.getPinyin(passengerAccidentInfoEntity.getPaxName(), "").toUpperCase());
            paxMessage.setIdType(passengerAccidentInfoEntity.getIdType());
            paxMessage.setIdNo(passengerAccidentInfoEntity.getIdNo());
            paxMessage.setTktNo(passengerAccidentInfoEntity.getTktNo());
            paxMessage.setSegment(passengerAccidentInfoEntity.getSegment());
            paxMessage.setMainClass(passengerAccidentInfoEntity.getMainClass());
            paxMessage.setSubClass(passengerAccidentInfoEntity.getSubClass());
            paxMessage.setCheckIn(passengerAccidentInfoEntity.getCheckIn());
            paxMessage.setIsCancel(passengerAccidentInfoEntity.getIsCancel());
            paxMessage.setCancelTime(passengerAccidentInfoEntity.getCancelTime() != null ? DateUtil.format(passengerAccidentInfoEntity.getCancelTime(), DatePattern.NORM_DATETIME_PATTERN) : null);
            paxMessage.setPnr(passengerAccidentInfoEntity.getPnr());
            paxMessage.setTktIssueTime(passengerAccidentInfoEntity.getTktIssueTime() != null ? DateUtil.format(passengerAccidentInfoEntity.getTktIssueTime(), DatePattern.NORM_DATETIME_PATTERN) : null);
            paxMessage.setIsBaby(passengerAccidentInfoEntity.getIsBaby());
            paxMessage.setIsChild(passengerAccidentInfoEntity.getIsChild());
            // 构建查询补偿次数的参数
            PassengerSelectInfoDto passengerSelectInfoDto = new PassengerSelectInfoDto();
            passengerSelectInfoDto.setFlightDate(findBaggageDetailVO.getBaggageFlightDate());
            passengerSelectInfoDto.setFlightNo(findBaggageDetailVO.getPaxFlightNo());
            // 查询补偿次数
            List<CompensationCountByPassengerInfoVo> compensationCountList =
                    complaintCompensationInfoMapper.selectCompensationCountByPassengerInfo(passengerSelectInfoDto);
            // 设置补偿次数
            if (CollectionUtil.isNotEmpty(compensationCountList)) {
                Map<String, String> compensationCountMap = compensationCountList.stream()
                        .collect(Collectors.toMap(
                                CompensationCountByPassengerInfoVo::getTicketNumber,
                                CompensationCountByPassengerInfoVo::getFrequency
                        ));
                paxMessage.setFrequency(Integer.valueOf(
                        compensationCountMap.getOrDefault(paxMessage.getTktNo(), "0")
                ));
            } else {
                paxMessage.setFrequency(0);
            }
        }

        BaggageDetailBaggageVO baggageMessage = new BaggageDetailBaggageVO();//以下塞行李信息
//        //获取经停和备降航站
//        String alternateTerminal = flightPassengerApi.getAlternateAndStop(findBaggageDetailVO.getPaxFlightDate(),findBaggageDetailVO.getPaxFlightNo()).getData();
        baggageMessage.setBaggageNo(findBaggageDetailVO.getBaggageNo());
        baggageMessage.setBaggageFlightDate(findBaggageDetailVO.getBaggageFlightDate());
        baggageMessage.setBaggageFlightNo(findBaggageDetailVO.getBaggageFlightNo());
        baggageMessage.setBaggageSegment(findBaggageDetailVO.getBaggageSegment());

        baggageMessage.setStopoverSegment(findBaggageDetailVO.getStopoverSegment());
        baggageMessage.setAlternateTerminal(findBaggageDetailVO.getAlternateTerminal());

        baggageMessage.setBaggageType(findBaggageDetailVO.getBaggageType());
        baggageMessage.setOverweightNo(findBaggageDetailVO.getOverweightNo());
        baggageDetailFinalVO.setAccidentDetail(accidentMessage);
        baggageDetailFinalVO.setFightDetail(fightMessage);
        baggageDetailFinalVO.setPaxDetail(paxMessage);
        baggageDetailFinalVO.setBaggageDetail(baggageMessage);
        BaggageAccidentInfoDO baggageAccidentInfoDO = baseMapper.selectById(dto.getAccidentId());
        if (baggageAccidentInfoDO != null) {
            BaggageTransportInfoDO baggageTransportInfoDO = baggageTransportInfoMapper.selectOne(
                    new LambdaQueryWrapper<BaggageTransportInfoDO>().eq(BaggageTransportInfoDO::getAccidentNo, baggageAccidentInfoDO.getAccidentNo()));
            if (baggageTransportInfoDO != null) {
                List<BaggageTransportAccidentRelDO> relList = baggageTransportAccidentRelMapper.selectList(
                        new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                .eq(BaggageTransportAccidentRelDO::getTransportId, baggageTransportInfoDO.getId()));
                List<String> mergedAccidentNos = Optional.ofNullable(relList).map(listStream -> listStream.stream()
                                .filter(rel -> rel != null && StringUtils.isNotEmpty(rel.getAccidentNo()))
                                .map(BaggageTransportAccidentRelDO::getAccidentNo)
                                .collect(Collectors.toList()))
                        .orElse(new ArrayList<>());
                if (CollectionUtil.isNotEmpty(mergedAccidentNos)){
                    baggageDetailFinalVO.setBandingBaggageAccidentInfoDOS(baggageAccidentMapper.selectList(new LambdaQueryWrapper<BaggageAccidentInfoDO>().in(BaggageAccidentInfoDO::getAccidentNo, mergedAccidentNos)));
                }
                baggageDetailFinalVO.setTransportInfoDTO(BeanUtil.copyProperties(baggageTransportInfoDO, BaggageTransportInfoDTO.class));
                if (baggageDetailFinalVO.getTransportInfoDTO() != null) {
                    baggageDetailFinalVO.getTransportInfoDTO().setMergedId(mergedAccidentNos);
                    baggageDetailFinalVO.getTransportInfoDTO().setEditFlag(canEditTransport(baggageAccidentInfoDO.getAccidentNo()));

                    // 计算行李总件数
                    List<String> allAccidentNos = new ArrayList<>();
                    allAccidentNos.add(baggageAccidentInfoDO.getAccidentNo()); // 添加主事故单号
                    allAccidentNos.addAll(mergedAccidentNos); // 添加合并的事故单号

                    List<BaggageAccidentInfoDO> allAccidentInfos = baseMapper.selectList(
                            new LambdaQueryWrapper<BaggageAccidentInfoDO>()
                                    .in(BaggageAccidentInfoDO::getAccidentNo, allAccidentNos));

                    int totalBaggageCount = 0;
                    for (BaggageAccidentInfoDO accidentInfo : allAccidentInfos) {
                        String baggageNo = accidentInfo.getBaggageNo();
                        if (StringUtils.isNotEmpty(baggageNo)) {
                            // 行李号可能是逗号分隔的多个行李号
                            String[] baggageNos = baggageNo.split(",");
                            totalBaggageCount += baggageNos.length;
                        }
                    }
                    baggageDetailFinalVO.getTransportInfoDTO().setTotalBaggageCount(totalBaggageCount);
                }
            } else {
                BaggageTransportAccidentRelDO relDO = baggageTransportAccidentRelMapper.selectOne(
                        new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                .eq(BaggageTransportAccidentRelDO::getAccidentNo, baggageAccidentInfoDO.getAccidentNo()));
                if (relDO != null) {
                    BaggageTransportInfoDO infoDO = baggageTransportInfoMapper.selectById(relDO.getTransportId());
                    baggageDetailFinalVO.setTransportInfoDTO(BeanUtil.copyProperties(infoDO, BaggageTransportInfoDTO.class));
                    List<BaggageTransportAccidentRelDO> relList = baggageTransportAccidentRelMapper.selectList(
                            new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                                    .eq(BaggageTransportAccidentRelDO::getTransportId, relDO.getTransportId()));
                    List<String> mergedAccidentNos = Optional.ofNullable(relList).map(listStream -> listStream.stream()
                                    .filter(rel -> rel != null && StringUtils.isNotEmpty(rel.getAccidentNo()))
                                    .map(BaggageTransportAccidentRelDO::getAccidentNo)
                                    .collect(Collectors.toList()))
                            .orElse(new ArrayList<>());
                    if (baggageDetailFinalVO.getTransportInfoDTO() != null) {
                        baggageDetailFinalVO.getTransportInfoDTO().setMergedId(mergedAccidentNos);
                        baggageDetailFinalVO.getTransportInfoDTO().setEditFlag(canEditTransport(baggageAccidentInfoDO.getAccidentNo()));

                        // 计算行李总件数
                        List<String> allAccidentNos = new ArrayList<>();
                        allAccidentNos.add(baggageAccidentInfoDO.getAccidentNo()); // 添加主事故单号
                        allAccidentNos.addAll(mergedAccidentNos); // 添加合并的事故单号

                        List<BaggageAccidentInfoDO> allAccidentInfos = baseMapper.selectList(
                                new LambdaQueryWrapper<BaggageAccidentInfoDO>()
                                        .in(BaggageAccidentInfoDO::getAccidentNo, allAccidentNos));

                        int totalBaggageCount = 0;
                        for (BaggageAccidentInfoDO accidentInfo : allAccidentInfos) {
                            String baggageNo = accidentInfo.getBaggageNo();
                            if (StringUtils.isNotEmpty(baggageNo)) {
                                // 行李号可能是逗号分隔的多个行李号
                                String[] baggageNos = baggageNo.split(",");
                                totalBaggageCount += baggageNos.length;
                            }
                        }
                        baggageDetailFinalVO.getTransportInfoDTO().setTotalBaggageCount(totalBaggageCount);
                    }
                }
            }
        }
        return baggageDetailFinalVO;
    }

    @Override
    public boolean saveMatch(String accidentId, String accidentNo) {
        log.info("【aps-compensation-impl-baggageAccident】需要进行事故单{}绑定到当前详情的事故单id{}", accidentNo, accidentId);
        if (StringUtils.isEmpty(accidentId) || StringUtils.isEmpty(accidentNo)) {
            return false;
        }
        String isBoundAccidentNo = baseMapper.isBoundBaggageAccident(accidentNo);
        if (StringUtils.isNotEmpty(isBoundAccidentNo)) {
            log.error("【aps-compensation-impl-baggageAccident】事故单{}已被此单{}进行绑定", accidentNo, isBoundAccidentNo);
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_HAS_BAND);
        }
        BaggageAccidentInfoVO accidentInfoVO = baseMapper.findById(accidentId);
        //进行反向绑定
        baseMapper.toBoundBaggageAccident(accidentInfoVO.getAccidentNo(), accidentNo.split(","));

        if (accidentInfoVO != null && StringUtils.isNotEmpty(accidentInfoVO.getMatchAccidentNo())) {
            accidentNo = accidentInfoVO.getMatchAccidentNo() + "," + accidentNo;
        }
        boolean bool = SqlHelper.retBool(baseMapper.saveMatch(accidentId, accidentNo));

        //推送业务数据到协同中心 [草稿和作废不能绑定]
        if (!AccidentStatusEnum.DRAFT.getValue().equals(accidentInfoVO.getAccidentStatus())) {
            businessDataPushHandler.dataStore(Long.valueOf(accidentId), BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }

        return bool;
    }

    @Override
    public boolean relieveMatch(String accidentId, String accidentNo) {
        String oldAccidentNo = accidentNo;
        log.info("【aps-compensation-impl-baggageAccident】需要进行事故单{}与当前详情展示的事故单id{}解绑", accidentNo, accidentId);
        //正向解绑 a->b
        if (StringUtils.isEmpty(accidentId) || StringUtils.isEmpty(accidentNo)) {
            return false;
        }
        BaggageAccidentInfoVO accidentInfoVO = baseMapper.findById(accidentId);
        log.info("【aps-compensation-impl-baggageAccident】解绑匹配的多/少收事故单，当前事故单【{}】", JSONUtil.toJsonStr(accidentInfoVO));
        if (StringUtils.isNotEmpty(accidentInfoVO.getMatchAccidentNo())) {
            String[] accidentNos = accidentInfoVO.getMatchAccidentNo().split(",");
            ArrayList<String> list = new ArrayList<String>(Arrays.asList(accidentNos));
            if (list.size() == 1 && list.get(0).equals(accidentNo)) {
                accidentNo = "";
            } else {
                list.remove(accidentNo);
                accidentNo = "";
                for (int i = 0; i < list.size(); i++) {
                    if (i < list.size() - 1) {
                        accidentNo = accidentNo + list.get(i) + ",";
                    } else {
                        accidentNo = accidentNo + list.get(i);
                    }
                }
            }
        } else {
            return false;
        }
        boolean re1 = SqlHelper.retBool(baseMapper.saveMatch(accidentId, accidentNo));
        //反向解绑 b->a
        BaggageAccidentInfoVO accidentInfoVO2 = baseMapper.findByAccidentNo(oldAccidentNo);
        log.info("【aps-compensation-impl-baggageAccident】解绑匹配的多/少收事故单，被绑定的事故单【{}】", JSONUtil.toJsonStr(accidentInfoVO2));
        accidentNo = accidentInfoVO.getAccidentNo();
        if (StringUtils.isNotEmpty(accidentInfoVO2.getMatchAccidentNo())) {
            String[] accidentNos = accidentInfoVO2.getMatchAccidentNo().split(",");
            ArrayList<String> list = new ArrayList<String>(Arrays.asList(accidentNos));
            if (list.size() == 1 && list.get(0).equals(accidentNo)) {
                accidentNo = "";
            } else {
                list.remove(accidentNo);
                accidentNo = "";
                for (int i = 0; i < list.size(); i++) {
                    if (i < list.size() - 1) {
                        accidentNo = accidentNo + list.get(i) + ",";
                    } else {
                        accidentNo = accidentNo + list.get(i);
                    }
                }
            }
        } else {
            return false;
        }
        boolean re2 = SqlHelper.retBool(baseMapper.saveMatch(accidentInfoVO2.getId(), accidentNo));

        //推送业务数据到协同中心 [草稿和作废不能绑定]
        if (!AccidentStatusEnum.DRAFT.getValue().equals(accidentInfoVO.getAccidentStatus())) {
            businessDataPushHandler.dataStore(Long.valueOf(accidentId), BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }
        return (re1 && re2);
    }

    /**
     * @param id
     * @return java.lang.Boolean
     * @title deleteById
     * @description 通过id删除异常行李事故单
     * <AUTHOR>
     * @date 2022/3/15 20:13
     */
    @Override
    public Boolean deleteById(Long id) {
        BaggageAccidentInfoDO baggageAccidentInfoDO = new BaggageAccidentInfoDO();
        baggageAccidentInfoDO.setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
        baggageAccidentInfoDO.setId(id);
        LambdaQueryWrapper<BaggageAccidentInfoDO> wrapper = Wrappers.lambdaQuery(baggageAccidentInfoDO);
        return SqlHelper.retBool(getBaseMapper().delete(wrapper));
    }

    @Override
    public Integer getBaggageAccidentNumber(PaxMaintainSearchDTO dto) {
        return baseMapper.getBaggageAccidentNumber(dto);
    }


    /**
     * @return java.lang.String
     * @title getAccidentNo
     * @description 生成异常行李事故单号
     * <AUTHOR>
     * @date 2022/3/4 10:32
     */
    private static String getAccidentNo() {
        StringBuffer accidentNo = new StringBuffer(ACCIDENT_NO_PREFIX);
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdfMonth = new SimpleDateFormat("MM");
        SimpleDateFormat sdfDate = new SimpleDateFormat("dd");
        accidentNo.append(cal.get(Calendar.YEAR));
        accidentNo.append(sdfMonth.format(cal.getTime()));
        accidentNo.append(sdfDate.format(cal.getTime()));
        accidentNo.append(String.format("%04d", new Random().nextInt(9999)));
        return accidentNo.toString();
    }

    /**
     * @param dto
     * @return com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO
     * @title checkAccidentInfo
     * @description 保存异常行李事故单时进行信息校验（不同类型的异常行李事故单有不同字段需要进行校验）
     * <AUTHOR>
     * @date 2022/3/18 14:16
     */
    private static void checkAccidentInfo(BaggageAccidentInfoDTO dto) {
        int code = BaggageAccidentException.BAGGAGE_ACCIDENT_INCOMPLETE_INFORMATION;
        if (StringUtils.isNotEmpty(dto.getIsDraft())) {//草稿不校验字段
            return;
        }
        if (BaggageAccidentTypeEnum.LESS_INCOME.getValue().equals(dto.getType())) {
            AssertBaggageAccidentInfo.isNotEmpty(dto.getLostWeight(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getLostType(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getReminderTime(), code);
        } else if (BaggageAccidentTypeEnum.DAMAGE.getValue().equals(dto.getType())) {
            AssertBaggageAccidentInfo.isNotEmpty(dto.getDamageDegree(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getDamagePart(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getDamageType(), code);
        } else if (BaggageAccidentTypeEnum.INSIDE_LOST.getValue().equals(dto.getType())) {
            AssertBaggageAccidentInfo.isNotEmpty(dto.getLostAmount(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getDeclarationType(), code);
            AssertBaggageAccidentInfo.isNotEmpty(dto.getLostWeight(), code);
        }
    }

    @Override
    public boolean updBaggageAccidentStatusBatch(List<String> accidentNo, String accidentStatus) {
        return this.getBaseMapper().updBaggageAccidentStatusBatch(accidentNo, accidentStatus);
    }

    @Override
    public Integer getExistAccidentNumber(VerifyAlikePaxOrderDTO dto) {
        //需判断该事故单下旅客是否已经存在相同日期相同航班相同事故类型的事故单
        return this.getBaseMapper().getExistAccidentNumber(dto);
    }

    @Override
    public Integer getExistCompensationNumber(VerifyAlikePaxOrderDTO dto) {
        //当前航班下相同补偿单类型下是否存在该补偿单中存在旅客的其他补偿单（包含全部状态）
        return this.getBaseMapper().getExistCompensationNumber(dto);
    }

    @Override
    public List<BaggageAccidentInfoDO> getOvertimeAccident() {
        return this.getBaseMapper().getOvertimeAccident();
    }


    private String generateFlightId(String flightNumber, long timestamp) {
        // 提取航班号中的数字部分
        String flightNumPart = flightNumber.replaceAll("[^\\d]", "");
        SecureRandom secureRandom = new SecureRandom();
        // 生成1000到9999之间的随机数，确保是4位数
        int randomNumber = secureRandom.nextInt(9000) + 1000;
        // 组合各部分，生成唯一的编号
        return String.format("%s%s%06d", flightNumPart, randomNumber, timestamp);
    }

    private String generatePaxId(String idNumber, String flightNumber, long timestamp) {
        // 提取证件号或护照的后4位
        String idPart = idNumber.substring(idNumber.length() - 5);
        // 提取航班号中的数字部分
        String flightNumPart = flightNumber.replaceAll("[^\\d]", "");
        // 组合各部分，生成唯一的编号
        return String.format("%s%s%06d", idPart, flightNumPart, timestamp);
    }

    @Override
    public List<BaggageAccidentDropdownVO> getUndeliveredAccidentDropdown(UndeliveredAccidentDTO dto) {
        return baggageAccidentMapper.selectUndeliveredAccidentDropdown(dto);
    }

    @Override
    public Boolean canEditTransport(String accidentNo) {
        BaggageTransportInfoDO existingTransportInfo = baggageTransportInfoMapper.selectOne(
                new LambdaQueryWrapper<BaggageTransportInfoDO>()
                        .eq(BaggageTransportInfoDO::getAccidentNo, accidentNo));
        if (existingTransportInfo != null
                && (StrUtil.isBlank(existingTransportInfo.getAuditStatus())
                        || CompensateStatusEnum.AUDIT_FAILED.getKey().equals(existingTransportInfo.getAuditStatus()))) {
            return true;
        }
        return false;
    }

    public Boolean canAddTransport(String accidentNo) {
        BaggageTransportAccidentRelDO existingTransportRefInfo = baggageTransportAccidentRelMapper.selectOne(
                new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                        .eq(BaggageTransportAccidentRelDO::getAccidentNo, accidentNo));
        if (!canEditTransport(accidentNo) && existingTransportRefInfo == null) {
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaggageTransportInfoDO saveTransportInfo(BaggageTransportInfoDTO dto) {
        if (dto == null) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        // 检查事故单是否存在
        BaggageAccidentInfoVO accidentInfo = baseMapper.findById(dto.getAccidentId().toString());
        if (accidentInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }
        // 转换DTO为DO
        BaggageTransportInfoDO transportInfoDO = new BaggageTransportInfoDO();
        transportInfoDO.setTransportAirport(UserContext.getCurrentUser().getTenantCode());
        transportInfoDO.setBelongAirline(accidentInfo.getPaxFlightNo().substring(0,2));
        BeanUtils.copyProperties(dto, transportInfoDO);
        transportInfoDO.setMergedTransport(dto.getMergedId() != null && !dto.getMergedId().isEmpty());
        transportInfoDO.setUpdatedBy(getCurrentOperator());
        transportInfoDO.setUpdatedTime(LocalDateTime.now());

        // 生成或保留code码
        if (dto.getId() == null) {
            // 新增时生成code码
            transportInfoDO.setBaggageCode(generateBaggageCode(accidentInfo.getPaxFlightNo().substring(0, 2)));
        } else {
            // 编辑时保留原有code码
            BaggageTransportInfoDO existingTransport = baggageTransportInfoMapper.selectById(dto.getId());
            if (existingTransport != null) {
                transportInfoDO.setBaggageCode(existingTransport.getBaggageCode());
            }
        }

        boolean result;
        // 如果是编辑
        if (dto.getId() != null) {
            // 检查是否有权限编辑
            if (!canEditTransport(accidentInfo.getAccidentNo())) {
                throw new BusinessException(BaggageAccidentException.NO_AUTHORITY_EDIT);
            }
            // 更新运输信息
            LambdaQueryWrapper<BaggageTransportInfoDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
            lambdaQueryWrapper.eq(BaggageTransportInfoDO::getId, transportInfoDO.getId());
            result = baggageTransportInfoMapper.update(transportInfoDO, lambdaQueryWrapper) > 0;
            // 更新运输单时，添加数据推送记录
            if (result) {
                businessDataPushHandler.dataStore(transportInfoDO.getId(),
                    BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                    BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);
            }

            // 如果更新失败，抛出异常
            if (!result) {
                throw new BusinessException(CommonErrors.UPDATE_ERROR);
            }

            // 更新关联关系
            // 先删除原有关联
            baggageTransportAccidentRelMapper.delete(new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                    .eq(BaggageTransportAccidentRelDO::getTransportId, dto.getId()));
            // 重新建立关联关系
            if (dto.getMergedId() != null && !dto.getMergedId().isEmpty()) {
                for (String mergedAccidentNo : dto.getMergedId()) {
                    if (baggageAccidentMapper.existTransport(mergedAccidentNo) == 0) {
                        BaggageTransportAccidentRelDO relDO = new BaggageTransportAccidentRelDO();
                        relDO.setTransportId(transportInfoDO.getId());
                        relDO.setAccidentNo(mergedAccidentNo);
                        baggageTransportAccidentRelMapper.insert(relDO);
                    }
                }
            }

            // 返回更新后的对象
            return baggageTransportInfoMapper.selectById(transportInfoDO.getId());
        } else {
            // 新增运输信息
            result = baggageTransportInfoMapper.insert(transportInfoDO) > 0;
            // 新增运输单时，添加数据推送记录
            if (result) {
                businessDataPushHandler.dataStore(transportInfoDO.getId(),
                    BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                    BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);
            }

            // 保存关联的事故单号
            if (result && dto.getMergedId() != null && !dto.getMergedId().isEmpty()) {
                for (String mergedAccidentNo : dto.getMergedId()) {
                    if (baggageAccidentMapper.existTransport(mergedAccidentNo) == 0) {
                        BaggageTransportAccidentRelDO relDO = new BaggageTransportAccidentRelDO();
                        relDO.setTransportId(transportInfoDO.getId());
                        relDO.setAccidentNo(mergedAccidentNo);
                        baggageTransportAccidentRelMapper.insert(relDO);
                    }
                }
            }

            // 如果新增失败，抛出异常
            if (!result) {
                throw new BusinessException(CommonErrors.CREATE_ERROR);
            }

            // 返回新增后的对象
            return transportInfoDO;
        }
    }

    /**
     * 生成异常行李code码
     * 规则：航司二字码+CTD+年+月+日+五位随机数
     * 例如：CACTD202002021111
     *
     * @param airlineCode 航司二字码
     * @return 生成的code码
     */
    private String generateBaggageCode(String airlineCode) {
        StringBuilder code = new StringBuilder();
        // 添加航司二字码
        code.append(airlineCode);
        // 添加CTD标识
        code.append("CTD");
        // 添加年月日
        LocalDateTime now = LocalDateTime.now();
        code.append(now.getYear());
        code.append(String.format("%02d", now.getMonthValue()));
        code.append(String.format("%02d", now.getDayOfMonth()));
        // 添加五位随机数
        SecureRandom random = new SecureRandom();
        code.append(String.format("%05d", random.nextInt(100000)));
        return code.toString();
    }

    @Override
    public IPage<BaggageTransportListVO> queryBaggageTransportList(BaggageTransportQueryDTO dto) {
        // 设置当前用户的工号用于审核权限判断
        if (UserContext.getCurrentUser() != null
                && StringUtils.isNotEmpty(UserContext.getCurrentUser().getJobNumber())) {
            dto.setJobNumber(UserContext.getCurrentUser().getJobNumber());
        }
        return baggageTransportInfoMapper.queryBaggageTransportList(dto.createPage(), dto);
    }

    @Override
    public BaggageTransportDetailVO getTransportDetail(Long transportId) {
        // 1. 查询运输单基本信息
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectById(transportId);
        BaggageAccidentInfoDO baggageAccidentInfoDO = baseMapper.selectById(transportInfo.getAccidentId());
        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }
        // 2. 查询关联的事故单信息
        List<BaggageTransportAccidentRelDO> relList = baggageTransportAccidentRelMapper.selectList(
                new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                        .eq(BaggageTransportAccidentRelDO::getTransportId, transportId));
        // 3. 获取所有关联的事故单号
        List<String> accidentNos = new ArrayList<>();
        accidentNos.add(transportInfo.getAccidentNo()); // 添加主事故单号
        if (relList != null && !relList.isEmpty()) {
            accidentNos.addAll(relList.stream()
                    .map(BaggageTransportAccidentRelDO::getAccidentNo)
                    .collect(Collectors.toList()));
        }
        // 4. 查询所有关联的事故单详细信息
        List<BaggageAccidentInfoDO> accidentInfos = baseMapper.selectList(
                new LambdaQueryWrapper<BaggageAccidentInfoDO>()
                        .in(BaggageAccidentInfoDO::getAccidentNo, accidentNos));
        // 5. 组装返回数据
        BaggageTransportDetailVO detailVO = new BaggageTransportDetailVO();
        // 复制运输单基本信息
        BeanUtils.copyProperties(transportInfo, detailVO);
        detailVO.setInputAirport(baggageAccidentInfoDO.getServeSegment());
        // 设置关联的事故单信息
        detailVO.setAccidentInfos(accidentInfos);
        // 设置是否为合并运输
        detailVO.setMergedTransport(transportInfo.getMergedTransport());
        // 统计行李总件数
        int totalBaggageCount = 0;
        for (BaggageAccidentInfoDO accidentInfo : accidentInfos) {
            String baggageNo = accidentInfo.getBaggageNo();
            if (StringUtils.isNotEmpty(baggageNo)) {
                // 行李号可能是逗号分隔的多个行李号
                String[] baggageNos = baggageNo.split(",");
                totalBaggageCount += baggageNos.length;
            }
        }
        detailVO.setTotalBaggageCount(totalBaggageCount);

        Boolean auditFlag = false;
        // 设置审核权限标识
        if (UserContext.getCurrentUser() != null
                && StringUtils.isNotEmpty(UserContext.getCurrentUser().getJobNumber())) {
            String currentUserJobNumber = UserContext.getCurrentUser().getJobNumber();
            WorkflowAuditorIdInfoDO workflowAuditorIdInfoDO = baggageTransportInfoMapper.checkTransportAuditPermission(transportId.toString(), currentUserJobNumber);
            if (workflowAuditorIdInfoDO != null) {
                auditFlag = true;
                detailVO.setTaskId(workflowAuditorIdInfoDO.getTaskId());
            }
        }
        detailVO.setAuditFlag(auditFlag);
        return detailVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateTransportAddress(BaggageTransportAddressDTO dto) {
        // 参数校验
        if (dto == null || StringUtils.isEmpty(dto.getAccidentNo())) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询是否存在运输信息
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectOne(
                new LambdaQueryWrapper<BaggageTransportInfoDO>()
                        .eq(BaggageTransportInfoDO::getAccidentNo, dto.getAccidentNo()));

        // 如果不存在记录，抛出异常
        if (transportInfo == null || !canEditTransport(transportInfo.getAccidentNo())) {
            throw new BusinessException(BaggageAccidentException.NO_AUTHORITY_EDIT);
        }
        // 更新地址信息
        transportInfo.setProvince(dto.getProvince());
        transportInfo.setCity(dto.getCity());
        transportInfo.setDistrict(dto.getDistrict());
        transportInfo.setAddressDetail(dto.getAddressDetail());
        transportInfo.setUpdatedBy(getCurrentOperator());
        transportInfo.setUpdatedTime(LocalDateTime.now());

        // 执行更新操作
        boolean result = baggageTransportInfoMapper.updateById(transportInfo) > 0;
        // 更新运输地址时，添加数据推送记录
        if (result) {
            businessDataPushHandler.dataStore(transportInfo.getId(), 
                BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, 
                BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object submitTransport(Long transportId) {
        log.info("【aps-compensation-impl】-行李运输工作流-submitTransport【开始】提交运输单, 运输单ID[{}]", transportId);

        // 参数校验
        if (transportId == null) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询运输单信息
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectById(transportId);
        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_NOT_FOUND, "运输单不存在");
        }

        // 检查是否可以提交（状态为空或驳回状态）
        if (StrUtil.isNotBlank(transportInfo.getAuditStatus())
                && !CompensateStatusEnum.AUDIT_FAILED.getKey().equals(transportInfo.getAuditStatus())) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_CANNOT_SUBMIT, "运输单状态无法提交审核");
        }

        try {
            Object result;

            // 判断是首次提交还是重新提交
            if (StrUtil.isBlank(transportInfo.getAuditStatus())) {
                // 首次提交：启动工作流
                log.info("【aps-compensation-impl】-行李运输工作流-submitTransport 首次提交，启动工作流");
                result = baggageTransportationWorkflowDomainService.startProcess(transportInfo);
            } else {
                // 重新提交：调用processSubmit
                log.info("【aps-compensation-impl】-行李运输工作流-submitTransport 重新提交，调用processSubmit");
                result = baggageTransportationWorkflowDomainService.processSubmit(transportInfo);
            }

            // 提交成功后，更新运输单状态为审核中
            updateTransportStatusToAuditing(transportInfo);
            
            // 运输单提交审核时，添加数据推送记录
            businessDataPushHandler.dataStore(transportId, 
                BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, 
                BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);

            return result;
        } catch (Exception e) {
            log.error("【aps-compensation-impl】-行李运输工作流-submitTransport 提交失败, 运输单ID[{}], 错误信息: {}",
                    transportId, e.getMessage(), e);
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_CANNOT_SUBMIT, "提交运输单失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object auditTransport(BaggageTransportAuditDTO dto) {
        log.info("【aps-compensation-impl】-行李运输工作流-auditTransport【开始】审核运输单, 运输单ID[{}], 审核状态[{}]",
                dto.getTransportId(), dto.getAuditStatus());

        // 参数校验
        if (dto == null || dto.getTransportId() == null) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询运输单信息
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectById(dto.getTransportId());
        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_NOT_FOUND, "运输单不存在");
        }

        // 检查运输单状态是否可以审核（应该是审核中状态）
        if (!CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(transportInfo.getAuditStatus())) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_CANNOT_SUBMIT, "运输单状态无法审核");
        }

        try {
            // 调用工作流审核
            BaggageWorkflowAuditResultVO result = baggageTransportationWorkflowDomainService.processAudit(
                    transportInfo, dto.getTaskId(), dto.getAuditStatus(), dto.getComment());

            // 只有当工作流正常结束时才更新运输单状态
            updateTransportStatusByWorkflowResult(transportInfo, result, dto.getAuditStatus());

            // 运输单审核完成时，添加数据推送记录
            businessDataPushHandler.dataStore(dto.getTransportId(), 
                BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, 
                BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);

            log.info("【aps-compensation-impl】-行李运输工作流-auditTransport【结束】审核运输单成功, 运输单ID[{}]",
                    dto.getTransportId());
            return result;

        } catch (Exception e) {
            log.error("【aps-compensation-impl】-行李运输工作流-auditTransport 审核失败, 运输单ID[{}], 错误信息: {}",
                    dto.getTransportId(), e.getMessage(), e);
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_CANNOT_SUBMIT,
                    "审核运输单失败: " + e.getMessage());
        }
    }

    /**
     * 更新运输单状态为审核中
     *
     * @param transportInfo 运输单信息
     */
    private void updateTransportStatusToAuditing(BaggageTransportInfoDO transportInfo) {
        log.info("【aps-compensation-impl】-行李运输工作流-updateTransportStatusToAuditing 更新运输单状态为审核中, 运输单ID[{}]",
                transportInfo.getId());

        transportInfo.setAuditStatus(CompensateStatusEnum.AUDIT_PROCESS.getKey());
        transportInfo.setUpdatedBy(getCurrentOperator());
        transportInfo.setUpdatedTime(LocalDateTime.now());

        int updateResult = baggageTransportInfoMapper.updateById(transportInfo);
        if (updateResult > 0) {
            log.info("【aps-compensation-impl】-行李运输工作流-updateTransportStatusToAuditing 更新运输单状态为审核中成功, 运输单ID[{}]",
                    transportInfo.getId());
        } else {
            log.error("【aps-compensation-impl】-行李运输工作流-updateTransportStatusToAuditing 更新运输单状态失败, 运输单ID[{}]",
                    transportInfo.getId());
            throw new BusinessException(CommonErrors.UPDATE_ERROR, "更新运输单状态失败");
        }
    }

    /**
     * 根据工作流结果更新运输单状态
     * <p>
     * 更新逻辑：
     * 1. AGREE + 流程结束 → 更新为"审核通过"（状态2）
     * 2. AGREE + 流程未结束 → 保持"审核中"状态（状态1），转到下一个审核节点
     * 3. REJECT + 流程结束 → 更新为"审核不通过"（状态3），最终拒绝，不可重新提交
     * 4. REJECT + 流程未结束 → 保持"审核中"状态（状态1），转到下一个审核节点
     * 5. BACK → 更新为"驳回"状态（状态4），可以重新提交
     * </p>
     *
     * @param transportInfo  运输单信息
     * @param workflowResult 工作流审核结果
     * @param auditStatus    审核状态（AGREE/REJECT/BACK）
     */
    private void updateTransportStatusByWorkflowResult(BaggageTransportInfoDO transportInfo,
            BaggageWorkflowAuditResultVO workflowResult,
            String auditStatus) {
        log.info(
                "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 检查是否需要更新运输单状态, 运输单ID[{}], 审核状态[{}], 工作流是否结束[{}]",
                transportInfo.getId(), auditStatus, workflowResult.isWorkflowEnded());

        String newStatus = null;

        // 根据工作流审核状态和流程状态决定运输单状态
        switch (auditStatus) {
            case "AGREE":
                // 同意：只有流程结束时才算审核通过
                if (workflowResult.isWorkflowEnded()) {
                    newStatus = CompensateStatusEnum.AUDIT_PASS.getKey();
                    // 更新机场审核人字段：记录结束节点前一个节点的执行人信息
                    updateAuditorInfo(transportInfo, workflowResult, auditStatus);
                    log.info(
                            "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 工作流正常结束且审核通过, 运输单ID[{}]",
                            transportInfo.getId());
                } else {
                    log.info(
                            "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 审核同意但流程未结束，转到下一个审核节点，保持审核中状态, 运输单ID[{}]",
                            transportInfo.getId());
                    return; // 保持审核中状态，不更新
                }
                break;
            case "REJECT":
                // 不同意：只有流程结束时才算最终拒绝
                newStatus = CompensateStatusEnum.AUDIT_FAILED.getKey();
                // 更新机场审核人字段：记录结束节点前一个节点的执行人信息
                updateAuditorInfo(transportInfo, workflowResult, auditStatus);
                log.info(
                        "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 工作流正常结束且审核不通过, 运输单ID[{}]",
                        transportInfo.getId());

                break;
            case "BACK":
                // 驳回：更新为驳回状态，表示可以重新提交
                newStatus = CompensateStatusEnum.REJECT.getKey();
                log.info(
                        "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 驳回到提交人，更新为驳回状态（可重新提交）, 运输单ID[{}]",
                        transportInfo.getId());
                break;
            default:
                log.warn("【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 未知的审核状态: {}",
                        auditStatus);
                return;
        }

        // 更新运输单状态和审核人信息
        if (newStatus != null) {
            transportInfo.setAuditStatus(newStatus);
            transportInfo.setUpdatedBy(getCurrentOperator());
            transportInfo.setUpdatedTime(LocalDateTime.now());

            int updateResult = baggageTransportInfoMapper.updateById(transportInfo);
            if (updateResult > 0) {
                log.info(
                        "【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 更新运输单状态成功, 运输单ID[{}], 新状态[{}]",
                        transportInfo.getId(), newStatus);
            } else {
                log.error("【aps-compensation-impl】-行李运输工作流-updateTransportStatusByWorkflowResult 更新运输单状态失败, 运输单ID[{}]",
                        transportInfo.getId());
                throw new BusinessException(CommonErrors.UPDATE_ERROR, "更新运输单状态失败");
            }
        }
    }

    /**
     * 更新审核人信息
     * <p>
     * 根据工作流结果更新运输单的审核人字段。主要逻辑：
     * 1. 当工作流结束时，记录结束节点前一个节点的执行人作为机场审核人
     * 2. 当驳回到提交人时，记录当前审核人信息
     * 3. 优先使用当前审核人（UserContext），如果没有则使用工作流中的执行人信息
     * </p>
     *
     * @param transportInfo  运输单信息
     * @param workflowResult 工作流审核结果
     * @param auditStatus    审核状态
     */
    private void updateAuditorInfo(BaggageTransportInfoDO transportInfo,
                                   BaggageWorkflowAuditResultVO workflowResult,
                                   String auditStatus) {
        log.info("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 开始更新审核人信息, 运输单ID[{}], 审核状态[{}]",
                transportInfo.getId(), auditStatus);

        String auditorUserId = null;
        String auditorName = null;

        try {
            // 优先使用当前登录用户作为审核人
            Long currentUserId = UserContext.getUserId();
            if (currentUserId != null) {
                auditorUserId = currentUserId.toString();

                // 获取当前用户详细信息
                ReaptvUserDetailVO currentUserDetail = reaptvUserApi.getById(currentUserId).getData();
                if (currentUserDetail != null) {
                    auditorName = currentUserDetail.getEmployeeName();
                    if (StringUtils.isBlank(auditorName)) {
                        auditorName = currentUserDetail.getName();
                    }
                    log.info("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 使用当前登录用户作为审核人, 用户ID[{}], 姓名[{}]",
                            auditorUserId, auditorName);
                }
            }

            // 如果当前用户信息获取失败，则使用工作流中的执行人信息
            if (StringUtils.isBlank(auditorName)) {
                String lastExecutor = workflowResult.getLastExecutor();
                if (StringUtils.isNotBlank(lastExecutor)) {
                    // 解析执行人ID（格式通常为 "userId:123"）
                    auditorUserId = lastExecutor;
                    if (lastExecutor.contains(":")) {
                        String[] parts = lastExecutor.split(":");
                        if (parts.length > 1) {
                            auditorUserId = parts[1];
                        }
                    }

                    // 获取用户详细信息
                    ReaptvUserDetailVO userDetail = reaptvUserApi.getById(Long.valueOf(auditorUserId)).getData();
                    if (userDetail != null) {
                        auditorName = userDetail.getEmployeeName();
                        if (StringUtils.isBlank(auditorName)) {
                            auditorName = userDetail.getName();
                        }
                        log.info("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 使用工作流执行人作为审核人, 用户ID[{}], 姓名[{}]",
                                auditorUserId, auditorName);
                    }
                }
            }

            // 更新机场审核人字段
            if (StringUtils.isNotBlank(auditorName)) {
                transportInfo.setAirportAuditor(auditorName);
                log.info("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 更新机场审核人成功, 运输单ID[{}], 审核人[{}]",
                        transportInfo.getId(), auditorName);
            } else {
                log.warn("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 未能获取审核人信息, 运输单ID[{}]",
                        transportInfo.getId());
            }

        } catch (Exception e) {
            log.error("【aps-compensation-impl】-行李运输工作流-updateAuditorInfo 更新审核人信息失败, 运输单ID[{}], 错误: {}",
                    transportInfo.getId(), e.getMessage(), e);
            // 不抛出异常，避免影响主流程
        }
    }

    @Override
    public Object getTransportReviewers(Long transportId, String taskId) {
        log.info("【aps-compensation-impl】-行李运输工作流-getTransportReviewers【开始】查询可选审核人, 运输单ID[{}], 任务ID[{}]",
                transportId, taskId);

        // 参数校验
        if (transportId == null) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询运输单信息，验证运输单是否存在
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectById(transportId);
        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_NOT_FOUND, "运输单不存在");
        }

        try {
            // 调用工作流服务查询审核人
            List<AuditorInfoDTO> reviewers = baggageTransportationWorkflowDomainService.findReviewer(transportId,
                    taskId);

            log.info("【aps-compensation-impl】-行李运输工作流-getTransportReviewers【结束】查询到{}个可选审核人, 运输单ID[{}]",
                    reviewers.size(), transportId);

            return reviewers;

        } catch (Exception e) {
            log.error("【aps-compensation-impl】-行李运输工作流-getTransportReviewers 查询失败, 运输单ID[{}], 错误信息: {}",
                    transportId, e.getMessage(), e);
            throw new BusinessException(BaggageTransportationWorkflowDomainService.COMMON_ERROR, "查询审核人失败");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Object saveTransportReviewers(BaggageTransportReviewerSaveDTO dto) {
        log.info("【aps-compensation-impl】-行李运输工作流-saveTransportReviewers【开始】保存审核人, 运输单ID[{}], 任务ID[{}], 审核人数量[{}]",
                dto.getTransportId(), dto.getTaskId(), dto.getAuditorIds().length);

        // 参数校验
        if (dto == null || dto.getTransportId() == null || StringUtils.isBlank(dto.getTaskId())
                || dto.getAuditorIds() == null || dto.getAuditorIds().length == 0) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询运输单信息，验证运输单是否存在
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectById(dto.getTransportId());
        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_NOT_FOUND, "运输单不存在");
        }

        // 检查运输单状态是否可以设置审核人（应该是审核中状态）
        if (!CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(transportInfo.getAuditStatus())) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_CANNOT_SUBMIT, "运输单状态无法设置审核人");
        }

        try {
            // 调用工作流服务保存审核人
            // 注意：这里传入运输单ID作为业务键，因为我们的 findReviewer 方法是基于运输单ID查询的
            List<AuditorInfoDTO> savedReviewers = baggageTransportationWorkflowDomainService.saveReviewer(
                    dto.getTransportId(), dto.getTaskId(), dto.getAuditorIds());

            log.info("【aps-compensation-impl】-行李运输工作流-saveTransportReviewers【结束】保存{}个审核人成功, 运输单ID[{}]",
                    savedReviewers.size(), dto.getTransportId());

            return savedReviewers;

        } catch (BusinessException e) {
            // 重新抛出业务异常
            throw e;
        } catch (Exception e) {
            log.error("【aps-compensation-impl】-行李运输工作流-saveTransportReviewers 保存失败, 运输单ID[{}], 错误信息: {}",
                    dto.getTransportId(), e.getMessage(), e);
            throw new BusinessException(BaggageTransportationWorkflowDomainService.COMMON_ERROR,
                    "保存审核人失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean reviewTransport(Long accidentId) {
        log.info("【aps-compensation-impl】-复核运输单【开始】事故单ID[{}]", accidentId);

        // 参数校验
        if (accidentId == null) {
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }

        // 查询事故单信息
        BaggageAccidentInfoVO accidentInfo = baseMapper.findById(accidentId.toString());
        if (accidentInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }

        // 查询运输单信息
        BaggageTransportInfoDO transportInfo = baggageTransportInfoMapper.selectOne(
                new LambdaQueryWrapper<BaggageTransportInfoDO>()
                        .eq(BaggageTransportInfoDO::getAccidentId, accidentId));

        if (transportInfo == null) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_NOT_FOUND, "运输单不存在");
        }

        // 检查是否已经复核过
        if (Boolean.TRUE.equals(transportInfo.getReviewed())) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_TRANSPORT_ALREADY_REVIEWED, "运输单已复核");
        }

        try {
            // 更新复核状态
            transportInfo.setReviewed(true);
            transportInfo.setReviewedBy(UserContext.getCurrentUser().getJobNumber());
            transportInfo.setReviewedTime(LocalDateTime.now());

            // 更新数据库
            boolean result = baggageTransportInfoMapper.updateById(transportInfo) > 0;

            if (result) {
                // 复核成功后，添加数据推送记录
                businessDataPushHandler.dataStore(transportInfo.getId(),
                        BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                        BusinessDataSyncConstant.DATA_TYPE_TRANSPORT);

                log.info("【aps-compensation-impl】-复核运输单【成功】事故单ID[{}], 运输单ID[{}]", accidentId, transportInfo.getId());
            } else {
                log.error("【aps-compensation-impl】-复核运输单【失败】事故单ID[{}], 运输单ID[{}]", accidentId, transportInfo.getId());
                throw new BusinessException(CommonErrors.UPDATE_ERROR, "复核失败");
            }

            return result;

        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("【aps-compensation-impl】-复核运输单【异常】事故单ID[{}], 错误信息: {}", accidentId, e.getMessage(), e);
            throw new BusinessException(CommonErrors.UPDATE_ERROR, "复核失败: " + e.getMessage());
        }
    }

}
