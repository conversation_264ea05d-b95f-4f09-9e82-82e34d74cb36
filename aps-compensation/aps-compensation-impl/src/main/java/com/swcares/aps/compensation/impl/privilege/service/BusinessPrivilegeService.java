package com.swcares.aps.compensation.impl.privilege.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeRequestDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeService
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 09:05
 * @Version 1.0
 */
public interface BusinessPrivilegeService extends IService<AirlineBusinessPrivilege> {
    /* @title create
     * @description 创建新的对航司的业务授权
     * <AUTHOR> Yi
     * @date 2024/6/12 09:22
     * @param businessPrivilegeRequestDTO: 新的业务授权数据请求
     * @return BusinessPrivilegeDTO 创建后的业务授权数据
     */
    public boolean create(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO);

    public boolean update(BusinessPrivilegeRequestDTO businessPrivilegeRequestDTO);

    IPage<BusinessPrivilegeDTO> search(SearchBusinessPrivilegeDTO searchCriteria);

    BusinessPrivilegeDTO getDetail(Long id);

    BusinessPrivilegeDTO getDetailByAirport(String airportCode);

    boolean switchStatus(List<Long> ids, boolean disabled);

    Boolean getAirportPrivilege(String code,String airCode);
}
