package com.swcares.aps.compensation.impl.privilege.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeMapper
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 09:04
 * @Version 1.0
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface BusinessPrivilegeMapperForCoordinate extends BaseMapper<AirlineBusinessPrivilege> {

    AirlineBusinessPrivilege getBusinessPrivilegeBetweenCustomer(@Param("grantorCode")String grantorCode, @Param("recipientCode") String recipientCode);

    Long getTeanantIdByTenantCode(@Param("tenantCode") String upperCase);
}
