package com.swcares.aps.compensation.impl.assist.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.assist.mapper.AssistPayRecordMapper;
import com.swcares.aps.compensation.impl.assist.service.AssistPayRecordService;
import com.swcares.aps.compensation.model.assist.entity.AssistPayRecordDO;
import com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: AssistPayRecordServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/2/24 13:25
 */
@Service
public class AssistPayRecordServiceImpl extends ServiceImpl<AssistPayRecordMapper, AssistPayRecordDO> implements AssistPayRecordService {

    @Override
    public void updateExpire(String phone) {
        baseMapper.updateExpire(phone);
    }

    @Override
    public Set<String> receivedPaxAmount(String phone) {
        return baseMapper.receivedPaxAmount(phone);
    }

    @Override
    public int existPhone(String phone) {
        return baseMapper.existPhone(phone);
    }

    @Override
    public List<AssistPayRecordVO> findRecord(String phone) {
        return baseMapper.findRecord(phone);
    }
}
