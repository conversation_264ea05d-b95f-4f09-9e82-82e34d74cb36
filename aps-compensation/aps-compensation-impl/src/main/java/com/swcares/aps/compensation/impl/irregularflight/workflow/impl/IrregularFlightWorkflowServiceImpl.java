package com.swcares.aps.compensation.impl.irregularflight.workflow.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.AuditStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.aps.compensation.model.complaint.dto.ComplaintWorkflowSubmitDo;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationAuditInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderDetailsVO;
import com.swcares.aps.compensation.model.overbook.dto.OverBookWorkflowSubmitDTO;
import com.swcares.aps.compensation.model.privilege.enums.CustomerCategoryEnum;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.aps.component.workflow.NodeNoticeProcessProxy;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeProcessResult;
import com.swcares.aps.component.workflow.entity.WorkflowModelCodeInfoDO;
import com.swcares.aps.component.workflow.enums.ApsProjectEnum;
import com.swcares.aps.component.workflow.service.WorkflowAuditorIdInfoService;
import com.swcares.aps.component.workflow.service.WorkflowModelCodeInfoService;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.workflow.impl <br>
 * Description：赔偿单工作流处理类<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月28日 9:29 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class IrregularFlightWorkflowServiceImpl implements IrregularFlightWorkflowService {
    @Autowired
    private NodeNoticeProcessProxy nodeNoticeProcessProxy;
    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;
    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    private WorkflowApi workflowApi;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private WorkflowAuditorIdInfoService workflowAuditorIdInfoService;
    @Autowired
    private Redisson redisson;
    private static final String REDISSON_PRE_KEY = "APPLY_AUDIT_";

    private static final Long REDISSON_KEY_REDIS_LOCK_TIME = 100L;
    @Autowired
    private WorkflowModelCodeInfoService workflowModelCodeInfoService;

    @Autowired(required = false)
    private SMSService smsService;


    /**
     * 异常航班事故单
     */
    private final String ACCIDENT_TYPE_FLIGHT="1";
    /**
     * 异常行李故单
     */
    private final String ACCIDENT_TYPE="2";
    //超售
    private final String OVERBOOKING="3";
    private final String COMPLAINT="4";
    /**
     * 现金补偿单
     */
    private final String COMPENSATE_TYPE_CASH="1";
    /**
     * 实物补偿单
     */
    private final String COMPENSATE_TYPE_MATERIAL="3";

    @Override
    public Map<String, Object> startAuditProcess(CompensationSyntheticalSaveDTO dto) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + dto.getOrderInfoDTO().getId());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {
                CompensationOrderInfoDTO orderInfoDTO = dto.getOrderInfoDTO();
                log.info("【aps-compensation-impl】-赔偿单工作流-startProcess【开始】启动流程实例,，赔偿单id[{}]", orderInfoDTO.getOrderNo());
                String userId = String.valueOf(UserContext.getUserId());
                Long tenantId = TenantHolder.getTenant();
                String tenantCode = compensationOrderInfoService.getTenantCodeById(tenantId);
                String tenantName = compensationOrderInfoService.getTenantNameById(tenantId);
                WorkflowModelCodeInfoDO workflowModelCodeInfoDO = new WorkflowModelCodeInfoDO();
                if(dto.getOrderInfoDTO().getAccidentType().equals(ACCIDENT_TYPE_FLIGHT)) {
                     workflowModelCodeInfoDO = workflowModelCodeInfoService.findByProjectAndBusiness(ApsProjectEnum.COMPENSATION_IMPL.getProjectType(), CompensationConstant.IRREGULARFLIGHT_WORKFLOW_BUSINESS);
                    workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
                }else if(dto.getOrderInfoDTO().getAccidentType().equals(ACCIDENT_TYPE)){
                     workflowModelCodeInfoDO = workflowModelCodeInfoService.findByProjectAndBusiness(ApsProjectEnum.BAGGAGE_IMPL.getProjectType(), CompensationConstant.BAGGAGE_WORKFLOW_BUSINESS);
                    workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
                }else if(dto.getOrderInfoDTO().getAccidentType().equals(COMPLAINT)){
                    workflowModelCodeInfoDO = workflowModelCodeInfoService.findByProjectAndBusiness(ApsProjectEnum.COMPENSATION_IMPL.getProjectType(), CompensationConstant.COMPLAINT_WORKFLOW_BUSINESS);
                    workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
                }else if(dto.getOrderInfoDTO().getAccidentType().equals(OVERBOOKING)){
                    workflowModelCodeInfoDO = workflowModelCodeInfoService.findByProjectAndBusiness(ApsProjectEnum.COMPENSATION_IMPL.getProjectType(), CompensationConstant.OVERBOOK_WORKFLOW_BUSINESS);
                    workflowModelCodeInfoDO.setModelCode(workflowModelCodeInfoDO.getModelCode()+"_"+tenantCode);
                }
                log.info("【aps-compensation-impl】在workflow_model_code_info报表中查找异常行李或者不正常航班工作流模型配置表");
                /**发起审核流程参数*/
                StartProcessParamsDTO startProcessParamsDTO = new StartProcessParamsDTO();
                startProcessParamsDTO.setAssignee("userId:" + userId);
                startProcessParamsDTO.setBusinessKey(String.valueOf(orderInfoDTO.getId()));
                startProcessParamsDTO.setProcDefKey(workflowModelCodeInfoDO.getModelCode());
                String tenantCompanyName = UserContext.getCurrentUser().getTenantName();
                CustomerDTO customerDTO=new CustomerDTO();
                customerDTO.setCustomer(tenantCode);
                customerDTO.setCustomerCategory(CustomerCategoryEnum.AIRPORT.getCode());
                customerDTO.setBusinessName(tenantCompanyName);
                startProcessParamsDTO.setCustomerDTO(customerDTO);
                startProcessParamsDTO.setStartSyncWorkflowInfoDTO(dto.getStartSyncWorkflowInfoDTO());
                if(dto.getStartSyncWorkflowInfoDTO()!= null){
                    startProcessParamsDTO.setProcessFlag(StartProcessParamsDTO.REQUEST);
                }else {
                    startProcessParamsDTO.setProcessFlag(StartProcessParamsDTO.COMMON);
                }

                /**设置系统标记*/
                startProcessParamsDTO.setExtVars(JSONUtil.parseObj(this.createNodeExtVarsDTO(dto.getOrderInfoDTO().getAccidentType())));
                long startTime = System.currentTimeMillis();
                BaseResult<CurrentTaskActivityVO> startResult = workflowApi.startProcess(startProcessParamsDTO);
                log.info("【aps-compensation-impl】耗时统计——startProcess启动流程实例【结束】，当前步骤（非累计）耗时：【{}】-------",  (System.currentTimeMillis() - startTime));
                log.info("【aps-compensation-impl】-赔偿单工作流-startProcess启动流程实例【结束】，赔偿单id[{}],启动产数【{}】----启动流程实例返回信息[{}]", orderInfoDTO.getId(),JSONUtil.toJsonStr(startProcessParamsDTO), startResult);

                /**执行提交申请节点*/
                return this.submitterAuditProcess(dto);
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-start发起审核流程操作，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        } finally {
            lock.unlock();
            log.info("【aps-compensation-impl】耗时统计——submitterAuditProcess开始提交第一个节点【结束】");
        }
    }

    @Override
    public Map<String, Object> submitterWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + nodeNoticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {
                log.info("【aps-compensation-impl】-赔偿单工作流-submitter提交审核操作,begin：【{}】", JSONUtil.toJsonStr(nodeNoticeDTO));
                /**获取业务参数*/
                Object extVars = nodeNoticeDTO.getExtVars().getExtVars();
                CompensationSyntheticalSaveDTO syntheticalSaveDTO = (CompensationSyntheticalSaveDTO) extVars;
                CompensationOrderInfoDTO orderInfoDTO = syntheticalSaveDTO.getOrderInfoDTO();
                String userId = String.valueOf(UserContext.getUserId());
                CompleteProcessParamsDTO completeDto = this.getSubmitterCompleteDTO(nodeNoticeDTO.getTaskId(), userId, syntheticalSaveDTO);
                /**设置系统标记*/
                CompensationSyntheticalSaveDTO compensationSyntheticalSaveDTO =(CompensationSyntheticalSaveDTO) nodeNoticeDTO.getExtVars().getExtVars();
                log.info("【aps-compensation-impl】-审核业务端传给流程引擎的额外参数【{}】",JSONUtil.toJsonStr(compensationSyntheticalSaveDTO));
                completeDto.setExtVars(JSONUtil.parseObj(createNodeExtVarsDTO(compensationSyntheticalSaveDTO.getOrderInfoDTO().getAccidentType())));
                /**发起人执行审批*/
                BaseResult<CurrentTaskActivityVO> completeResult =  workflowApi.completeTask(completeDto);
                compensationOrderInfoService.deleteAuditInfo(orderInfoDTO.getId());
                /**触发策略流程处理，得到下一节点流程审批人等信息*/
                Map<String, Object> resultMap = this.triggerNextNodeNotice(nodeNoticeDTO.getBusiKey());
                log.info("【aps-compensation-impl】-赔偿单工作流-submitter提交审核操作---返回结果[{}]", JSONUtil.toJsonStr(resultMap));
                //发起后进入自动审批流程，执行审批返回end节点,返回下一个节点不可能为空的
                //若在业务自动完成自动审批，就不用此判断封装
                if (CollectionUtil.isEmpty(resultMap) && "end".equals(completeResult.getData().getCurrentTaskActivityDTOS().get(0).getNodeKey())) {
                    resultMap = new HashMap<>();
                    resultMap.put("orderId", nodeNoticeDTO.getBusiKey());
                    resultMap.put("isCancel", false);
                    resultMap.put("taskId", null);
                    resultMap.put("orderAuditorList", Collections.EMPTY_LIST);
                    resultMap.put("isPrompt", false);
                }
                return resultMap;
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-submitter提交审核操作，获取分布式锁失败：{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Map<String, Object> auditOperation(AuditProcessorDTO dto) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + dto.getOrderId());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {
                String userId = dto.getUserId();
                log.info("【aps-compensation-impl】-赔偿单工作流-执行审核方法,审核请求参数[{}],当前审核人[{}]", JSONUtil.toJsonStr(dto), userId);
                if (StringUtils.isEmpty(userId)) {
                    userId = String.valueOf(UserContext.getUserId());
                }
                CompensationAuditInfoDO auditInfoDO = new CompensationAuditInfoDO();
                auditInfoDO.setTaskId(dto.getTaskId());
                auditInfoDO.setOrderId(dto.getOrderId());
                auditInfoDO.setAuditorId(Long.valueOf(userId));
                CompensationAuditInfoDO auditInfo = compensationAuditInfoService.findCompensationAuditInfo(auditInfoDO);
                //判断是否有权限审批
                log.info("【aps-compensation-impl】-赔偿单工作流-执行审核方法,审核请求参数[{}],当前审核人[{}],根据业务id查询当前待办任务查询结果：【{}】", JSONUtil.toJsonStr(dto), userId, JSON.toJSONString(auditInfo));
                if(ObjectUtils.isEmpty(auditInfo)
                        && !(CompensationConstant.ADMIN_END_REMARKS.equals(dto.getRemarks())
                        && CompensationConstant.ADMIN_END_ID.equals(dto.getUserId()))
                ){
                    log.info("【aps-compensation-impl】-赔偿单工作流-执行审核方法,当前审核人[{}],不是此任务[{}],的审批人，没有权限进行审核!!",  userId,JSONUtil.toJsonStr(dto));
                    throw new BusinessException(CompensationException.AUDIT_ERROR);
                }

                CompleteProcessParamsDTO completeDto = this.getAuditCompleteParam(dto, userId);
                StopWatch stopWatch = new StopWatch();
                stopWatch.start();
                // first step complete task
                BaseResult<CurrentTaskActivityVO> completeTask = workflowApi.completeTask(completeDto);
                CurrentTaskActivityVO nextTaskActivityDTO = completeTask.getData();
                // second step return next taskId
                Map<String, Object> resultMap = this.triggerNextNodeNotice(nextTaskActivityDTO);
                stopWatch.stop();
                log.info("【aps-compensation-impl】赔偿单审核流程-处理节点耗时：【{}】",stopWatch.prettyPrint());
                /**删除业务待审核记录*/
                Map<String, Object> columnMap = new HashMap<>();
                columnMap.put("order_id", dto.getOrderId());
                columnMap.put("task_id", dto.getTaskId());
                compensationAuditInfoService.removeByMap(columnMap);

                if (CollectionUtil.isEmpty(resultMap) && "end".equals(completeTask.getData().getCurrentTaskActivityDTOS().get(0).getNodeKey())) {
                    resultMap = new HashMap<>();
                    resultMap.put("orderId", dto.getOrderId());
                    resultMap.put("isCancel", false);
                    resultMap.put("taskId", null);
                    resultMap.put("orderAuditorList", null);
                    resultMap.put("isPrompt", false);
                }else if(CollectionUtil.isEmpty(resultMap)){
                    //如果为空，且流程没有结束，可能是同步审核流程，前端不显示下一节点选择人员
                    resultMap = new HashMap<>();
                    resultMap.put("orderId", dto.getOrderId());
                    resultMap.put("isCancel", false);
                    resultMap.put("taskId", completeTask.getData().getCurrentTaskActivityDTOS().get(0).getTaskId());
                    resultMap.put("orderAuditorList", null);
                    resultMap.put("isPrompt", false);
                }
                return resultMap;
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-执行审核操作，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        } catch (DecoderHandlerException de) {
            if (WorkflowUtils.isExecutedTask(de.getCode())) {
                throw new BusinessException(CompensationException.CPC_TASKID_ERROR);
            } else {
                throw new BusinessException(CompensationException.AUDIT_ERROR);
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            lock.unlock();
        }
    }

    @Override
    public Map<String, Object> cancelWorkflow(NodeNoticeDTO nodeNoticeDTO) {
        compensationOrderInfoService.updCompensationOrderStatus(Long.valueOf(nodeNoticeDTO.getBusiKey()), CompensateStatusEnum.AUDIT_FAILED.getKey());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("orderId", nodeNoticeDTO.getBusiKey());
        resultMap.put("isCancel", true);//是否提示【规则判断不通过，请核实后重新提交。】
        resultMap.put("taskId", null);
        resultMap.put("orderAuditorList", Collections.EMPTY_LIST);
        resultMap.put("isPrompt", false);//是否提示【未匹配到下一节点审核人员，系统已自动为您结束审核流程】
        return resultMap;
    }

    @Override
    public Map<String, Object> automaticWorkflow(NodeNoticeDTO nodeNoticeDTO) {
        /*String userId = nodeNoticeDTO.getReviewer();
        String busiKey = nodeNoticeDTO.getBusiKey();
        *//**由发起人执行自动审批*//*
        CompleteProcessParamsDTO completeDto = this.getAuditCompleteParam(AuditProcessorDTO.builder().taskId(nodeNoticeDTO.getTaskId())
                .remarks("自动过审")
                .auditStatus(AuditStatusEnum.AGREE.getKey())
                .orderId(Long.valueOf(busiKey))
                .build()
                ,userId);
        BaseResult<CurrentTaskActivityVO> completeResult = workflowApi.completeTask(completeDto);
        this.isResultSuccess(completeResult);
        *//**end节点修改业务状态*//*
        this.triggerNextNodeNotice(busiKey,userId);
        Map<String,Object> resultMap = new HashMap<>();
        resultMap.put("orderId", busiKey);
        resultMap.put("isCancel", false);//是否提示【规则判断不通过，请核实后重新提交。】
        resultMap.put("taskId", null);
        resultMap.put("orderAuditorList",null);
        resultMap.put("isPrompt", false);//是否提示【未匹配到下一节点审核人员，系统已自动为您结束审核流程】*/
        Map<String, Object> resultMap = new HashMap<>();
        return resultMap;
    }


    @Override
    public Map<String, Object> commonWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + nodeNoticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {
                log.info("【aps-compensation-impl】-赔偿单工作流-common节点审核，begin:{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                CurrentTaskActivityVO nextNodeTaskVo = getNextNodeTaskVo(nodeNoticeDTO.getBusiKey());
                Map<String, Object> resultMap = new HashMap<>();
                Long busiKey = Long.valueOf(nodeNoticeDTO.getBusiKey());
                /**删除业务待审核记录*/
                compensationOrderInfoService.deleteAuditInfo(busiKey);
                AuditProcessorDTO auditProcessorDTO = AuditProcessorDTO.builder().orderId(busiKey).build();
                /**审批流程异常标识，前端弹框提示*/
                boolean isPrompt = false;
                /**查询节点审批人信息*/
                List<AuditorInfoDTO> orderAuditorList = this.findAuditorList(nextNodeTaskVo, nodeNoticeDTO, auditProcessorDTO);
                /**当前节点无审批人，admin执行流程结束*/
                if (CollectionUtils.isEmpty(orderAuditorList)) {
                    isPrompt = true;
                    this.adminAuditProcess(nodeNoticeDTO.getTaskId(), auditProcessorDTO);
                }
                /**节点处理人有值 且 size = 1; 自行保存待审核数据*/
                if (orderAuditorList.size() == 1) {
                    compensationAuditInfoService.saveReviewer(CompensationAuditInfoDTO.builder().orderId(busiKey)
                            .taskId(nodeNoticeDTO.getTaskId())
                            .auditorIds(new Long[]{orderAuditorList.get(0).getReviewerId()})
                            .build());
                    orderAuditorList.clear();
                }
                log.info("【aps-compensation-impl】-赔偿单工作流-common节点审核，begin:{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                //上一节点是驳回或提交节点的情况，需要发送站内信
                if(AuditStatusEnum.REJECT.getKey().equals(nodeNoticeDTO.getOptionCode())){
                    if(StringUtils.isEmpty(nodeNoticeDTO.getReviewer())){
                        nodeNoticeDTO.setReviewer("system");
                    }
                    smsService.sendInnerSMS(nodeNoticeDTO, null);
                }
                resultMap.put("orderId", busiKey);
                resultMap.put("isCancel", false);
                resultMap.put("taskId", isPrompt ? null : nodeNoticeDTO.getTaskId());
                resultMap.put("orderAuditorList", orderAuditorList);
                resultMap.put("isPrompt", isPrompt);
                return resultMap;
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-common节点审核，获取分布式锁失败：{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            lock.unlock();
        }
    }


    @Override
    public Map<String, Object> endWorkflow(NodeNoticeDTO nodeNoticeDTO) throws Exception {
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + nodeNoticeDTO.getBusiKey());
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {
                log.info("【aps-compensation-impl】-赔偿单工作流-end节点，begin:{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                compensationOrderInfoService.deleteAuditInfo(Long.valueOf(nodeNoticeDTO.getBusiKey()));
                this.updCompensateStatusByAuditStatus(nodeNoticeDTO);
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-end节点，获取分布式锁失败：{}", JSONUtil.toJsonStr(nodeNoticeDTO));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        } finally {
            lock.unlock();
        }
        return Collections.EMPTY_MAP;
    }


    /**
     * 修改赔偿单审核状态，删除待审核数据
     * @param nodeNoticeDTO
     */
    @Override
    public void updCompensateStatusByAuditStatus(NodeNoticeDTO nodeNoticeDTO) {
        boolean isUpd = false;//更新标识
        String status = "";
        switch (nodeNoticeDTO.getOptionCode()) {
            //同意
            case CompensationConstant.AUDIT_AGREE:
                if (CompensationConstant.NODE_END.equals(nodeNoticeDTO.getNodeKey())) {
                    //判断审核节点是否为最后一个节点
                    isUpd = true;
                    status = CompensateStatusEnum.AUDIT_PASS.getKey();
                }
                log.info("updCompensateStatusByAuditStatus:执行审核通过站内信,nodeNoticeDTO【{}】， status【{}】", nodeNoticeDTO, status);
                smsService.sendInnerSMS(nodeNoticeDTO, status);
                break;
            //不同意-结束审核、更新赔偿单-不通过
            case CompensationConstant.AUDIT_DISAGREE:
                isUpd = true;
                status = CompensateStatusEnum.AUDIT_FAILED.getKey();
                log.info("updCompensateStatusByAuditStatus:执行审核不通过站内信,nodeNoticeDTO【{}】， status【{}】", nodeNoticeDTO, status);
                smsService.sendInnerSMS(nodeNoticeDTO, status);
                break;
            //驳回
            case CompensationConstant.AUDIT_REJECT:
                //判断审核节点是否驳回到发起人
                if (CompensationConstant.NODE_KEY_SUBMITTER.equals(nodeNoticeDTO.getNodeKey())) {
                    isUpd = true;
                    status = CompensateStatusEnum.REJECT.getKey();
                }
                log.info("updCompensateStatusByAuditStatus:执行审核驳回站内信,nodeNoticeDTO【{}】， status【{}】", nodeNoticeDTO, status);
                smsService.sendInnerSMS(nodeNoticeDTO, status);
                break;
        }
        Long orderId = Long.valueOf(nodeNoticeDTO.getBusiKey());
        if (isUpd && StringUtils.isNotEmpty(status)) {
            compensationOrderInfoService.updCompensationOrderStatus(orderId, status);
        }
    }



    @Override
    public CurrentTaskActivityVO getNextNodeTaskVo(String busiKey) {
        BaseResult<CurrentTaskActivityVO> nextTaskResult = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(busiKey).build());
        this.isQueryResultSuccess(busiKey, nextTaskResult);
        return nextTaskResult.getData();
    }

    /**
     * Title：triggerNextNodeNotice <br>
     * Description：触发同步下一个节点信息<br>
     * author：傅欣荣 <br>
     * date：2022/2/10 13:48 <br>
     * @param
     * @return
     */
    private Map<String, Object> triggerNextNodeNotice(String busiKey) {
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotice(this.getNextNodeTaskVo(busiKey), null);
        return process.isPresent()?(Map<String, Object>) process.get().getData():Collections.EMPTY_MAP;
    }

    public Map<String, Object> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO) {
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotice(currentTaskActivityVO, null);
        return process.isPresent()?(Map<String, Object>) process.get().getData():Collections.EMPTY_MAP;
    }


    private Optional<NodeNoticeProcessResult<Object>> triggerNextNodeNotice(CurrentTaskActivityVO currentTaskActivityVO, Object obj) {
        JSONObject jsonObject = currentTaskActivityVO.getPreTaskExtVars();
        CurrentTaskActivityDTO taskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);

        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        if(extVarsDTO == null){
            extVarsDTO = new NodeExtVarsDTO();
        }
        /**设置业务数据*/
        if (ObjectUtils.isNotEmpty(obj)) {
            extVarsDTO.setExtVars(obj);
            if(obj instanceof CompensationSyntheticalSaveDTO){
                CompensationSyntheticalSaveDTO dto = (CompensationSyntheticalSaveDTO)obj;
                dto.setPaxInfoDTO(null);
                extVarsDTO.setExtVars(dto);
            }
        }
        NodeNoticeDTO nodeNoticeDTO = ObjectUtils.copyBean(taskActivityDTO, NodeNoticeDTO.class);
        nodeNoticeDTO.setExtVars(extVarsDTO);
        nodeNoticeDTO.setBusiKey(currentTaskActivityVO.getBusiKey());
        /**当前审批状态传入下一个节点*/
        if (StringUtils.isNotEmpty(currentTaskActivityVO.getPreOptionCode())) {
            nodeNoticeDTO.setOptionCode(currentTaskActivityVO.getPreOptionCode());
        }
        return nodeNoticeProcessProxy.process(nodeNoticeDTO);
    }


    @Override
    public Map<String, Object> submitterAuditProcess(CompensationSyntheticalSaveDTO dto) {
        CompensationOrderInfoDTO orderInfoDTO = dto.getOrderInfoDTO();
        CurrentTaskActivityVO nextNodeTaskVo = this.getNextNodeTaskVo(String.valueOf(orderInfoDTO.getId()));
        if(nextNodeTaskVo.getPreTaskExtVars() == null){
            nextNodeTaskVo.setPreTaskExtVars(JSONUtil.parseObj(this.createNodeExtVarsDTO(orderInfoDTO.getAccidentType())));
        }
        Optional<NodeNoticeProcessResult<Object>> process = this.triggerNextNodeNotice(nextNodeTaskVo, dto);
        return (Map<String, Object>) process.get().getData();
    }


    public NodeExtVarsDTO createNodeExtVarsDTO(String busiKey) {
        /**设置系统标记*/
        NodeExtVarsDTO nodeExtVars = new NodeExtVarsDTO();
        if(busiKey.equals(ACCIDENT_TYPE_FLIGHT)){
            nodeExtVars.setProject(ApsProjectEnum.COMPENSATION_IMPL.getProjectType());
            nodeExtVars.setBusiness(CompensationConstant.IRREGULARFLIGHT_WORKFLOW_BUSINESS);
        }else if(busiKey.equals(ACCIDENT_TYPE)){
            nodeExtVars.setProject(ApsProjectEnum.BAGGAGE_IMPL.getProjectType());
            nodeExtVars.setBusiness(CompensationConstant.BAGGAGE_WORKFLOW_BUSINESS);
        }else if (busiKey.equals(COMPLAINT)) {
            nodeExtVars.setProject(ApsProjectEnum.COMPENSATION_IMPL.getProjectType());
            nodeExtVars.setBusiness(CompensationConstant.COMPLAINT_WORKFLOW_BUSINESS);
        }else if (busiKey.equals(OVERBOOKING)) {
            nodeExtVars.setProject(ApsProjectEnum.COMPENSATION_IMPL.getProjectType());
            nodeExtVars.setBusiness(CompensationConstant.OVERBOOK_WORKFLOW_BUSINESS);
        }
        return nodeExtVars;
    }

    /**
     * Title： isResultSuccess<br>
     * Description： 判断返回结果是否成功<br>
     * author：傅欣荣 <br>
     * date：2021/12/2 14:36 <br>
     * @param
     * @return
     */

    public <T extends Object> void isQueryResultSuccess(String busiKey, BaseResult<T> baseResult) {
        if (baseResult.getData() == null) {
            log.info("【aps-compensation】调用flowAble接口返回异常，没有查询到下一个节点----请求参数【{}】，返回结果[{}]", busiKey, JSONUtil.toJsonStr(baseResult));
            log.info("【aps-compensation】调用flowAble接口返回异常,没有查询到下一个节点----请求参数【{}】，返回结果[{}]", busiKey, JSONUtil.toJsonStr(baseResult));
            throw new BusinessException(CompensationException.AUDIT_ERROR);
        }
    }


    /**
     * Title： getCompleteDTO<br>
     * Description： 发起人自动审批-审批参数<br>
     * author：傅欣荣 <br>
     * date：2021/12/2 15:00 <br>
     * @param
     * @return
     */
    public CompleteProcessParamsDTO getSubmitterCompleteDTO(String taskId, String userId, CompensationSyntheticalSaveDTO dto) {

//        String workTerminal = UserContext.getCurrentUser().getWorkTerminal();
        String workTerminal = dto.getOrderInfoDTO().getServiceCity();
        String[] workTerminals = StringUtils.isEmpty(workTerminal) ? new String[]{"0"} : workTerminal.split(",");

        String agreeKey = AuditStatusEnum.AGREE.getKey();
        CompleteProcessParamsDTO completeDto = new CompleteProcessParamsDTO();
        completeDto.setBusinessKey(String.valueOf(dto.getOrderInfoDTO().getId()));
        completeDto.setTaskId(taskId);
        completeDto.setUserId("userId:" + userId);
        completeDto.setOptionCode(AuditStatusEnum.SUBMIT.getKey());


        if(StringUtils.equals(dto.getOrderInfoDTO().getAccidentType(),ACCIDENT_TYPE_FLIGHT) &&
                StringUtils.equals(dto.getOrderInfoDTO().getCompensateType(),COMPENSATE_TYPE_CASH)){
            CompensationOrderInfoDTO orderInfoDTO = dto.getOrderInfoDTO();
            /*List<CompensationPaxInfoDTO> paxInfoDTO = dto.getPaxInfoDTO();
            String adultAmount = "0";
            //取任意一个成人的赔偿金额进行流程判断
            for(CompensationPaxInfoDTO paxInfoDTO1 :paxInfoDTO){
                if("0".equals(paxInfoDTO1.getIsChild())){
                    adultAmount= paxInfoDTO1.getCurrentAmount().stripTrailingZeros().toPlainString();
                    break;
                }
            }*/

            String[] timeDiff = StringUtils.isEmpty(dto.getAccidentInfoDTO().getDelayInterval())? new String[]{"0"}
                    :dto.getAccidentInfoDTO().getDelayInterval().split(":");
            JSONObject busiData = JSONUtil.parseObj(AuditStartParamDTO.builder()
                    .fcType(dto.getAccidentInfoDTO().getFcType())
                    .accidentType(dto.getAccidentInfoDTO().getAccidentType())
                    .compensateType(dto.getOrderInfoDTO().getCompensateType())
                    .serviceCity(workTerminals[0])
                    .isCustom(dto.getRuleRecordDTO().get(0).getIsCustom())
                    .sumMoney(dto.getOrderInfoDTO().getSumMoney().stripTrailingZeros().toPlainString())
                    .adultAmount(orderInfoDTO.getCurrentAmount().stripTrailingZeros().toPlainString())
                    .delayInterval(Integer.valueOf(timeDiff[0]).toString())
                    .optionCode(agreeKey)
                    .accidentSource(dto.getOrderInfoDTO().getSource())
                    .compensationAccidentType(dto.getOrderInfoDTO().getAccidentType())
                    .fcTypeOwner(dto.getAccidentInfoDTO().getFcTypeOwner())
                    .build());
            completeDto.setBusiData(busiData);
        }else if(StringUtils.equals(dto.getOrderInfoDTO().getAccidentType(),ACCIDENT_TYPE) &&(
                StringUtils.equals(dto.getOrderInfoDTO().getCompensateType(),COMPENSATE_TYPE_CASH)||
                        StringUtils.equals(dto.getOrderInfoDTO().getCompensateType(),COMPENSATE_TYPE_MATERIAL))){
            JSONObject busiData = JSONUtil.parseObj(AuditStartParamDTO.builder()
                    .compensateType(dto.getOrderInfoDTO().getCompensateType())
                    .compensationAccidentType(dto.getOrderInfoDTO().getAccidentType())
                    .serviceCity(workTerminals[0])
                    .compensationAmount(dto.getOrderInfoDTO().getSumMoney().toString())
                    .source(dto.getOrderInfoDTO().getSource())
                    .belongAirline(dto.getOrderInfoDTO().getBelongAirline())
                    .accidentSourceAirportCode(dto.getOrderInfoDTO().getSourceTenantCode())
                    .build());


            completeDto.setBusiData(busiData);
        } else if (StringUtils.equals(dto.getOrderInfoDTO().getAccidentType(), COMPLAINT) && (
                StringUtils.equals(dto.getOrderInfoDTO().getCompensateType(), COMPENSATE_TYPE_CASH))) {
            JSONObject busiData = JSONUtil.parseObj(ComplaintWorkflowSubmitDo.builder()
                    .compensationAirport(dto.getOrderInfoDTO().getServiceCity())
                    .compensationAmount(dto.getOrderInfoDTO().getSumMoney().toString())
                    .compensationSubType(dto.getOrderInfoDTO().getAccidentSubType())
                    // TODO:current submit user company
                    .belongUnit("")
                    .reasonType(dto.getComplaintAccidentInfoEntity().getReasonType())
                    .source(dto.getOrderInfoDTO().getSource())
                    .build());
            completeDto.setBusiData(busiData);
        }else if (StringUtils.equals(dto.getOrderInfoDTO().getAccidentType(), OVERBOOKING) && (
                StringUtils.equals(dto.getOrderInfoDTO().getCompensateType(), COMPENSATE_TYPE_CASH))) {
            JSONObject busiData = JSONUtil.parseObj(OverBookWorkflowSubmitDTO.builder()
                    .compensationAirport(dto.getOrderInfoDTO().getServiceCity())
                    .compensationAmount(dto.getOrderInfoDTO().getSumMoney().toString())
                    .compensationSubType(dto.getOrderInfoDTO().getAccidentSubType())
                    .accidentSource(dto.getOrderInfoDTO().getSource())
                    .build());
            completeDto.setBusiData(busiData);
        }


        //以下测试模拟数据
//        String agreeKey = AuditStatusEnum.AGREE.getKey();
//        CompleteProcessParamsDTO completeDto = new CompleteProcessParamsDTO();
//        completeDto.setBusinessKey(String.valueOf(dto.getOrderInfoDTO().getId()));
//        completeDto.setTaskId(taskId);
//        completeDto.setUserId("userId:"+userId);
//        completeDto.setOptionCode(AuditStatusEnum.SUBMIT.getKey());
//        completeDto.setBusiData(JSONUtil.parseObj(AuditStartParamDTO.builder().fcType("206").accidentType("222").compensateType("211")
//                .serviceCity("CTU").isCustom("0").sumMoney("200").delayInterval("4").optionCode(agreeKey).build()));

        return completeDto;
    }

    private CompleteProcessParamsDTO getAuditCompleteParam(AuditProcessorDTO auditProcessorDTO, String userId) {
        CompleteProcessParamsDTO completeDto = new CompleteProcessParamsDTO();
        completeDto.setBusinessKey(String.valueOf(auditProcessorDTO.getOrderId()));
        completeDto.setTaskId(auditProcessorDTO.getTaskId());
        completeDto.setUserId("userId:" + userId);
        completeDto.setOptionCode(auditProcessorDTO.getAuditStatus());
        completeDto.setComment(auditProcessorDTO.getRemarks());
        CompensationOrderDetailsVO orderVo = compensationOrderInfoService.findById(auditProcessorDTO.getOrderId());
        completeDto.setExtVars(JSONUtil.parseObj(this.createNodeExtVarsDTO(orderVo.getAccidentType())));
        return completeDto;
    }


    /**
     * Title：findAuditorList <br>
     * Description： 查下一节点审核用户列表<br>
     * author：傅欣荣 <br>
     * date：2021/12/3 16:46 <br>
     * @param
     * @return
     */
    private List<AuditorInfoDTO> findAuditorList(CurrentTaskActivityVO nextNodeTaskVo, NodeNoticeDTO nodeNoticeDTO, AuditProcessorDTO orderInfoDTO) {
        List<String> assignees = new ArrayList<>();
        if (nodeNoticeDTO.getOptionCode().equals(AuditStatusEnum.REJECT.getKey()) && nextNodeTaskVo.getCurrentTaskActivityDTOS() != null && nextNodeTaskVo.getCurrentTaskActivityDTOS().get(0).getLastAssignee() != null) {
            assignees.add(nextNodeTaskVo.getCurrentTaskActivityDTOS().get(0).getLastAssignee());
        } else {
            assignees.addAll(nodeNoticeDTO.getAssignees());
        }
        List<AuditorInfoDTO> auditorList = workflowAuditorIdInfoService.findAuditorList(assignees);
        log.info("【aps-compensation】审核赔偿单，赔偿单id[{}]----查询下一审核节点用户列表-参数[{}]，返回结果[{}]", orderInfoDTO.getOrderId(), assignees, auditorList);
        if (CollectionUtils.isNotEmpty(auditorList)) {
            redisUtil.set(StrUtil.format(CompensationConstant.COMPENSATION_AUDITOR_KEY, orderInfoDTO.getOrderId()), this.getListToMap(assignees));
        }
        return auditorList;
    }


    private Map<String, String> getListToMap(List<String> assignees) {
        Map<String, String> m = new HashMap<String, String>();
        if (CollectionUtils.isEmpty(assignees)) {
            return Collections.EMPTY_MAP;
        }
        Map<String, String> auditorMap = new HashMap<>();
        assignees.forEach(t -> {
            String[] split = t.split(":");
            auditorMap.put(split[0], split[1]);
        });
        return auditorMap;
    }

    public void adminAuditProcess(String taskId, AuditProcessorDTO orderInfoDTO) throws Exception {
        log.info("【aps-compensation】审核赔偿单，赔偿单id[{}]----admin结束审核流程，taskId[{}]", orderInfoDTO.getOrderId(), taskId);
        this.auditOperation(AuditProcessorDTO.builder().orderId(orderInfoDTO.getOrderId())
                .auditStatus(AuditStatusEnum.DISAGREE.getKey())
                //.orderNo(orderInfoDTO.getOrderNo())
                .remarks(CompensationConstant.ADMIN_END_REMARKS)
                .taskId(taskId)
                .userId(CompensationConstant.ADMIN_END_ID)
                .build()
        );
    }
}
