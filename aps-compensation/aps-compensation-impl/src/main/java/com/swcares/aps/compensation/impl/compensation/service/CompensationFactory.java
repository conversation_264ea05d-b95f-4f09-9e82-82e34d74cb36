package com.swcares.aps.compensation.impl.compensation.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintAccidentInfoMapper;
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationFlightInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationPaxInfoMapper;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @ClassName：CompensationFactory
 * @Description：补偿单工厂类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 9:48
 * @version： v1.0
 */
@Component
public class CompensationFactory {

    private final static String COMPENSATE_TYPE_CASH_DICT_ID="1";//"211"; //现金
    private final static String COMPENSATE_TYPE_VIRTUAL_DICT_ID="2";//"212"; //虚拟
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3";//"213"; //实物
    private CompensationOrderInfoMapper compensationOrderInfoMapper;

    private CompensationPaxInfoMapper compensationPaxInfoMapper;

    private CompensationFlightInfoMapper compensationFlightInfoMapper;

    private CompensationMaterialInfoMapper compensationMaterialInfoMapper;

    private ComplaintAccidentInfoMapper complaintAccidentInfoMapper;
    @Autowired
    public CompensationFactory(CompensationOrderInfoMapper compensationOrderInfoMapper,
                               CompensationPaxInfoMapper compensationPaxInfoMapper,
                               CompensationFlightInfoMapper compensationFlightInfoMapper,
                               CompensationMaterialInfoMapper compensationMaterialInfoMapper,
                               ComplaintAccidentInfoMapper complaintAccidentInfoMapper) {
        this.compensationOrderInfoMapper = compensationOrderInfoMapper;
        this.compensationPaxInfoMapper = compensationPaxInfoMapper;
        this.compensationFlightInfoMapper = compensationFlightInfoMapper;
        this.compensationMaterialInfoMapper = compensationMaterialInfoMapper;
        this.complaintAccidentInfoMapper = complaintAccidentInfoMapper;
}

    public AccidentCompensationDTO createByOrderId(String orderId){
        LambdaQueryWrapper<CompensationOrderInfoDO> compensationOrderInfoWrapper=Wrappers.lambdaQuery();
        compensationOrderInfoWrapper.eq(CompensationOrderInfoDO::getId,orderId);

        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectOneById(orderId,null);
        return createAccidentCompensationDTO(compensationOrderInfoDO);

    }
    public AccidentCompensationDTO createByOrderIdOld(String orderId){
        LambdaQueryWrapper<CompensationOrderInfoDO> compensationOrderInfoWrapper=Wrappers.lambdaQuery();
        compensationOrderInfoWrapper.eq(CompensationOrderInfoDO::getId,orderId);

        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectOne(compensationOrderInfoWrapper);
        return createAccidentCompensationDTO(compensationOrderInfoDO);

    }


    public AccidentCompensationDTO createByOrderNo(String orderNo) {

        LambdaQueryWrapper<CompensationOrderInfoDO> compensationOrderInfoWrapper=Wrappers.lambdaQuery();
        compensationOrderInfoWrapper.eq(CompensationOrderInfoDO::getOrderNo,orderNo);

        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectOneById(null,orderNo);

        return createAccidentCompensationDTO(compensationOrderInfoDO);
    }

    private AccidentCompensationDTO createAccidentCompensationDTO(CompensationOrderInfoDO compensationOrderInfoDO) {
        if(compensationOrderInfoDO==null){
            return null;
        }
        Long compensationId=compensationOrderInfoDO.getId();
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = getPaxInfoDOS(compensationId);
        LambdaQueryWrapper<CompensationFlightInfoDO> fltWrapper= Wrappers.lambdaQuery();
        fltWrapper.eq(CompensationFlightInfoDO::getOrderId,compensationId);
        CompensationFlightInfoDO compensationFlightInfoDO = compensationFlightInfoMapper.selectOne(fltWrapper);

        ComplaintAccidentInfoEntity complaintAccidentInfoEntity=null;
        if (compensationOrderInfoDO.getAccidentType().equals("4")){
            // 不正常航班的需要提取一下，新增旅客投诉的
            LambdaQueryWrapper<ComplaintAccidentInfoEntity> wrapper= Wrappers.lambdaQuery();
            wrapper.eq(ComplaintAccidentInfoEntity::getId,compensationOrderInfoDO.getAccidentId());
            complaintAccidentInfoEntity = complaintAccidentInfoMapper.selectOne(wrapper);
        }

        List<CompensationMaterialInfoDO> compensationMaterialInfoDO =null;
        if(COMPENSATE_TYPE_MATERIAL_DICT_ID.equals(compensationOrderInfoDO.getCompensateType())){
            LambdaQueryWrapper<CompensationMaterialInfoDO> luggageWrapper=Wrappers.lambdaQuery();
            luggageWrapper.eq(CompensationMaterialInfoDO::getOrderId,compensationId);
            compensationMaterialInfoDO = compensationMaterialInfoMapper.selectList(luggageWrapper);

        }
        AccidentCompensationDTO of = AccidentCompensationDTO.of(compensationOrderInfoDO,
                                                                compensationPaxInfoDOS,
                                                                compensationFlightInfoDO,
                                                                compensationMaterialInfoDO,
                                                                complaintAccidentInfoEntity);
        return of;
    }

    private List<CompensationPaxInfoDO> getPaxInfoDOS(Long compensationId) {
//        LambdaQueryWrapper<CompensationPaxInfoDO> wrapper = Wrappers.lambdaQuery();
//        wrapper.eq(CompensationPaxInfoDO::getOrderId,compensationId);
//        return compensationPaxInfoMapper.selectList(wrapper);
        return compensationPaxInfoMapper.findListByCompensationId(compensationId);
    }

}
