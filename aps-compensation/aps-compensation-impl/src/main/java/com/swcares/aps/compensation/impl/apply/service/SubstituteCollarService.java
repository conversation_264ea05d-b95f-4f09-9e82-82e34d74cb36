package com.swcares.aps.compensation.impl.apply.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.apply.dto.SubstituteCollarPaxPageDTO;
import com.swcares.aps.compensation.model.apply.vo.ApplyBehalfOrderVO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxDetailsVO;
import com.swcares.aps.compensation.model.apply.vo.SubstituteCollarPaxPageVO;

import java.util.List;

/**
 * ClassName：com.swcares.aps.apply.impl.service <br>
 * Description ： web代领旅客审核模块<br>
 * Copyright  2022 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月17日 11:22 <br>
 * @version v1.0 <br>
 */
public interface SubstituteCollarService {

    /**
     * @title webPage
     * @description web代领审核列表
     * <AUTHOR>
     * @date 2022/1/17 14:30
     * @param dto
     * @param auditorId
     * @return IPage<SubstituteCollarPaxPageVO>
     */
    IPage<SubstituteCollarPaxPageVO> webPage(SubstituteCollarPaxPageDTO dto, String auditorId);


    /**
     * @title findDetails
     * @description 代领审核详情
     * <AUTHOR>
     * @date 2022/1/17 16:54
     * @param id
     * @return SubstituteCollarPaxDetailsVO
     */
    SubstituteCollarPaxDetailsVO findDetails(Long id);


    /**
     * @title updQuickPay
     * @description 修改为快速支付
     * <AUTHOR>
     * @date 2022/1/18 14:52
     * @param id
     * @return void
     */
    void updQuickPay(Long id);


    /**
     * @title updQuickPay
     * @description 修改为等待期满-快速支付
     * <AUTHOR>
     * @date 2022/1/18 14:52
     * @param id
     * @return void
     */
    void updQuickPayByExpires(Long id, long tenantId);

    /**
     * @title getOverTimeActOrder
     * @description 获取代领审核订单是否超时
     * <AUTHOR>
     * @date 2022/10/25 15:35

     * @return com.swcares.aps.compensation.model.apply.vo.ApplyBehalfOrderVO
     */
    List<ApplyBehalfOrderVO> getOverTimeActOrder();

}
