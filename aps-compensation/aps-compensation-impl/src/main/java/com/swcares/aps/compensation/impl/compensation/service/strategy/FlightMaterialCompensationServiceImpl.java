package com.swcares.aps.compensation.impl.compensation.service.strategy;

import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.baseframe.common.exception.BusinessException;
import org.springframework.stereotype.Service;

/**
 * @ClassName：FlightMaterialCompensationServiceImpl
 * @Description：异常航班实物补偿单创建编辑服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 17:01
 * @version： v1.0
 */
@Service("flightMaterialCompensationService")
public class FlightMaterialCompensationServiceImpl implements CompensationAddAndEditService<CompensationMaterialAddCommandDTO, CompensationMaterialEditCommandDTO> {

    @Override
    public String add(CompensationMaterialAddCommandDTO addCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"异常航班实物补偿单暂未实现");
    }

    @Override
    public String edit(CompensationMaterialEditCommandDTO editCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,"异常航班实物补偿单暂未实现");
    }

    @Override
    public CompensationAuditOperationVO reSubmit(CompensationMaterialEditCommandDTO editCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,"异常航班实物补偿单暂未实现");
    }
}
