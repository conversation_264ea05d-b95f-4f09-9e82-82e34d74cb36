package com.swcares.aps.compensation.impl.compensation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationBaseInfoPageDTO;
import com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditInfoVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO;

import java.util.List;
import java.util.Map;


/**
 * @ClassName：CompensationOrderQueriesService
 * @Description：补偿单查询服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 13:42
 * @version： v1.0
 */
public interface CompensationOrderQueriesService {
    /**
     * @title compensationBaseInfoPage
     * @description 基本信息分页查询
     * <AUTHOR>
     * @date 2022/3/9 14:04
     * @param compensationBaseInfoPage
     * @return 
     */
    IPage<CompensationBaseInfoVO> compensationBaseInfoPage(CompensationBaseInfoPageDTO compensationBaseInfoPage);

    /**
     * @title compensationDetailSearch
     * @description 补偿单详情查询
     * <AUTHOR>
     * @date 2022/3/10 9:24
     * @param orderId 补偿单ID
     * @return
     */
    AccidentCompensationDTO compensationDetailSearch(String orderId);

    /**
     * @title getLoginUserCanAuditOrderIdToTaskIdMp
     * @description
     * <AUTHOR>
     * @date 2022/5/12 15:00
     * @param orderIds
     * @return
     */
    Map<String,String> getLoginUserCanAuditOrderIdToTaskIdMp(List<String> orderIds);

    /**
     * @title compensationAuditInfoSearch
     * @description 查询审核人信息
     * <AUTHOR>
     * @date 2022/3/9 16:15
     * @param compensationAuditInfoDTO
     * @return
     */
    List<CompensationAuditInfoVO> compensationAuditInfoSearch(CompensationAuditInfoDTO compensationAuditInfoDTO);

    /**
     * @title findCashCompensationList
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/18 13:49
     * @param compensationBaseInfoPageDTO
     * @return IPage<Object>
     */
    IPage<Object> findCashCompensationList(CompensationBaseInfoPageDTO compensationBaseInfoPageDTO);

    /**
     * @title getCashBusinessCostsDetail
     * @description 查询现金业务明细详情
     * <AUTHOR>
     * @date 2022/7/25 12:35
     * @param dto
     * @return IPage<CashBusinessCostsDetailVO>
     */
    IPage<CashBusinessCostsDetailVO> getCashBusinessCostsDetail(CompensationCashReportDTO dto);

    /**
     * @title getCashPayDetail
     * @description 查询现金支付明细
     * <AUTHOR>
     * @date 2022/7/25 20:49
     * @param dto
     * @return IPage<CashBusinessCostsDetailVO>
     */
    IPage<CashPayDetailVO> getCashPayDetail(CompensationCashReportDTO dto);

    /**
     * @title getCashBusinessCostsDetailReport
     * @description 现金业务成本明细报表重载（用于导出）
     * <AUTHOR>
     * @date 2022/10/17 9:29
     * @param dto
     * @return IPage<CashBusinessCostsDetailVO>
     */
    List<CashBusinessCostsDetailVO> getCashBusinessCostsDetailReport(CompensationCashReportDTO dto);

    /**
     * @title getCashPayDetailReport
     * @description 查询现金支付明细报表(用于报表导出)
     * <AUTHOR>
     * @date 2022/10/17 11:11
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO>
     */
    IPage<CashPayDetailVO> getCashPayDetailReport(CompensationCashReportDTO dto);
}
