package com.swcares.aps.compensation.impl.baggage.luggage.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageConsumptionService;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageConsumptionDetailPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @title: LuggageConsumptionController
 * @projectName aps
 * @description: 箱包消耗明细接口
 * @date 2022/2/8 13:59
 */
@RestController
@RequestMapping("/luggage/consumption")
@Api(tags = "箱包消耗明细")
@ApiVersion(value = "箱包管理v1.0")
public class LuggageConsumptionController extends BaseController {

    @Autowired
    private LuggageConsumptionService luggageConsumptionService;

    @PostMapping("/page")
    @ApiOperation(value = "箱包消耗明细")
    public PagedResult<List<LuggageConsumptionDetailVO>> findDetailed(@RequestBody LuggageConsumptionDetailPageDTO dto){
        IPage<LuggageConsumptionDetailVO> page = luggageConsumptionService.findDetailed(dto);
        return ok(page);
    }


}
