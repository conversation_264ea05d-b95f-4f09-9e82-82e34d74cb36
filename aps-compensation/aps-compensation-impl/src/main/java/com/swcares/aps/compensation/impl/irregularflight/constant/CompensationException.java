package com.swcares.aps.compensation.impl.irregularflight.constant;

/**
 * @title: CompensationException
 * @projectName aps
 * @description: aps针对compensation模块的异常处理类
 * <AUTHOR> @date 2021/11/11 17:32
 */
public class CompensationException {
    //发起审核流程错误
    public static final int START_CPC_PROCESS_ERROR = 30100;

    //发起审核流程-参数有问题，导致流程结束，需要重新提交保存并发起流程
    public static final int START_CPC_PROCESS_ABNORMAL_END = 30101;

    //处理审核-异常
    public static final int CPC_AUDIT_ERROR = 30102;

    //处理审核-异常
    public static final int AUDIT_ERROR = 30102;

    //保存代人领取规则出错
    public static final int SAVE_REPLACE_RULE_ERROR = 30103;
    //审批任务不存在
    public static final int CPC_TASKID_ERROR = 30104;
    //该审批流程-节点审批人已存在，请勿重复提交！
    public static final int AUDIT_ORDER_USER_ERROR = 30105;
    //补偿单保存出错
    public static final int COMPENSATION_SAVE_ERROR = 30170;
    //补偿单编辑出错
    public static final int COMPENSATION_EDIT_ERROR = 30171;
    //补偿单提交出错
    public static final int COMPENSATION_SUBMIT_ERROR = 30172;
    //补偿单删除出错
    public static final int COMPENSATION_DELETE_ERROR = 30173;
    //补偿单状态扭转出错
    public static final int COMPENSATION_CHANGE_ERROR = 30174;


    //异常补偿单保存出错，库存不足
    public static final int LUGGAGE_STOCK_NOT_ENOUGH = 30183;

    //该用户无权限提交，仅创建人可提交！
    public static final int COMPENSATION_SUBMIT_AUTHORITY_ERROR = 30184;


    //该业务补偿工作已经授权给机场，航司不可以创建补偿单
    public static final int COMPENSATION_SAVE_BUSINESS_PRIVILEGE_ERROR = 30185;

}
