package com.swcares.aps.compensation.impl.compensation.service.handler;

import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName：BaggageAccidentStatusHandlerServiceImpl
 * @Description：异常行李事故单更改状态为处理中
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 16:52
 * @version： v1.0
 */
@Service
public class BaggageAccidentProcessStatusHandlerServiceImpl implements CompensationOrderStatusHandlerService {

    /**
     * 异常行李事故单
     */
    private final String ACCIDENT_TYPE_BAGGAGE_DICT_ID="2";//"223";


    private BaggageAccidentStatusService baggageAccidentStatusService;

    private BaggageAccidentService baggageAccidentService;


    @Autowired
    public BaggageAccidentProcessStatusHandlerServiceImpl(BaggageAccidentStatusService baggageAccidentStatusService,
                                                          BaggageAccidentService baggageAccidentService){
        this.baggageAccidentStatusService=baggageAccidentStatusService;
        this.baggageAccidentService=baggageAccidentService;
    }

    @Override
    public void handler(AccidentCompensationDTO compensation, String targetStatus) {
        if(!support(compensation,targetStatus)){
            return;
        }
        baggageAccidentStatusService.changeStatus(String.valueOf(compensation.getAccidentId()), AccidentStatusEnum.PROCESS.getValue());
    }

    private boolean support(AccidentCompensationDTO compensation, String targetStatus){

        boolean result=false;
        if(CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(targetStatus)
            && ACCIDENT_TYPE_BAGGAGE_DICT_ID.equals(compensation.getAccidentType())
        && CompensateStatusEnum.DRAFT.getKey().equals(compensation.getStatus())){
            BaggageAccidentInfoDO baggageAccident = baggageAccidentService.getById(compensation.getAccidentId());
            if(baggageAccident!=null && (!AccidentStatusEnum.PROCESS.getValue().equals(baggageAccident.getAccidentStatus()))){
                return true;
            }
        }
        return result;
    }

}
