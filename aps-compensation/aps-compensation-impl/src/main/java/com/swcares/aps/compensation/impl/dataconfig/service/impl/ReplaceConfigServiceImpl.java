package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.dataconfig.enums.CompensationConfigEnum;
import com.swcares.aps.compensation.impl.dataconfig.mapper.ReplaceConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.impl.ReplaceConfigServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-11 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ReplaceConfigServiceImpl extends ServiceImpl<ReplaceConfigMapper, DataConfigDO> implements ReplaceConfigService {
    @Override
    public List<DataConfigDO> findByType(String type) {
        DataConfigDO queryReplaceConfig=new DataConfigDO();
        queryReplaceConfig.setType(type);
        queryReplaceConfig.setDeleted(false);
        queryReplaceConfig.setAirCode(UserContext.getCurrentUser().getTenantCode());
        List<DataConfigDO> replaceConfigs = this.baseMapper.selectList(Wrappers.lambdaQuery(queryReplaceConfig));
        return replaceConfigs;
    }

    @Override
    public DataConfigDO findByTypeAndSubTypeAndAirCode(String type, String subType,String airCode) {
        DataConfigDO queryReplaceConfig=new DataConfigDO();
        queryReplaceConfig.setAirCode(airCode);
        queryReplaceConfig.setType(type);
        queryReplaceConfig.setSubType(subType);
        queryReplaceConfig.setDeleted(false);
        DataConfigDO replaceConfig = this.baseMapper.selectOne(Wrappers.lambdaQuery(queryReplaceConfig));
        return replaceConfig;

    }

    @Override
    public List<ReplaceRejectReasonDTO> rejectReasonPages(ReplaceRejectReasonPageDTO rejectReasonPageDTO) {
        return this.baseMapper.rejectReasonPages(rejectReasonPageDTO);
    }

    @Override
    public List<ReplaceRejectReasonDTO> getAllRejectReason() {
        DataConfigDO queryReplaceConfig=new DataConfigDO();
        queryReplaceConfig.setType(CompensationConfigEnum.AUDIT_REJECT_REASON.getType());
        queryReplaceConfig.setSubType(CompensationConfigEnum.AUDIT_REJECT_REASON.getSubType());
        queryReplaceConfig.setAirCode(UserContext.getCurrentUser().getTenantCode());
        return this.baseMapper.getAllRejectReason(queryReplaceConfig);
    }
}
