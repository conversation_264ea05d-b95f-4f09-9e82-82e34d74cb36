package com.swcares.aps.compensation.impl.baggage.accident.enums;

/**
 * @ClassName：BaggageAccidentTypeEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/8 14:04
 * @version： v1.0
 */
public enum BaggageAccidentTypeEnum {

    DAMAGE("270", "21","破损"),
    LESS_INCOME("271", "22","少收"),
    OVERCHARGE("272", "23","多收"),
    INSIDE_LOST("273", "24","内件缺失"),
    LOST("274", "25","丢失"),
    BACTH_LESS_INCOME("", "26","批量少收"),;

    private BaggageAccidentTypeEnum(String key, String value,String lable) {
        this.key = key;
        this.value = value;
        this.lable=lable;
    }

    private String key;

    private String value;

    private String lable;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaggageAccidentTypeEnum build(String key) {
        return build(key, true);
    }

    public static BaggageAccidentTypeEnum buildValue(String value) {
        return buildValue(value, true);
    }

    public static BaggageAccidentTypeEnum build(String key, boolean throwEx) {
        BaggageAccidentTypeEnum typeEnum = null;
        for (BaggageAccidentTypeEnum element : BaggageAccidentTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + BaggageAccidentTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }

    public static BaggageAccidentTypeEnum buildValue(String value, boolean throwEx) {
        BaggageAccidentTypeEnum typeEnum = null;
        for (BaggageAccidentTypeEnum element : BaggageAccidentTypeEnum.values()) {
            if (element.getValue().equals(value)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + value + ",请核对" + BaggageAccidentTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }

}
