package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.dataconfig.enums.CompensationConfigEnum;
import com.swcares.aps.compensation.impl.dataconfig.mapper.CompensationDataConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.AccidentDescribeService;
import com.swcares.aps.compensation.impl.dataconfig.service.CompensationConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.CompensationDataConfigService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeService;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class AccidentDescribeServiceImpl implements AccidentDescribeService {
    @Autowired
    CompensationDataConfigService compensationDataConfigService;
    @Autowired
    CompensationDataConfigMapper compensationDataConfigMapper;
    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @Autowired
    private CompensationConfigService compensationConfigService;

    @Autowired
    BusinessPrivilegeService businessPrivilegeService;

    private String getAccidentDescribeTypeByTenantCode(){
        String tenantCode = UserContext.getCurrentUser().getTenantCode();
        //当前租户的事故说明配置key
        return tenantCode+"_"+CompensationConfigEnum.ACCIDENT_DESCRIBE_SELECT.getType();
    }
    @Override
    public List<CompensationConfigVO> getAll(String airCode) {
        String type = getAccidentDescribeTypeByTenantCode();
        List<CompensationConfigVO> result = compensationConfigService.getCompensationConfigByTypes(Arrays.asList(type));
        if (result!= null && result.size() > 0) {
            return result.stream().filter(dto -> dto.getAirCode().equals(airCode)).collect(Collectors.toList());
        }
        return Collections.emptyList();
    }

    @Override
    public boolean delete(CompensationConfigDeleteDTO deleteDTO) {
        if (StringUtils.isEmpty(deleteDTO.getId())) {
            return false;
        }
        //是否同步到当前租户的其他授权航司，Y是 N否
        if("Y".equals(deleteDTO.getToSync())){
            if (StringUtils.isEmpty(deleteDTO.getType()) || StringUtils.isEmpty(deleteDTO.getSubType())) {
                throw new BusinessException(CommonErrors.PARAM_VALIDATE_ERROR);
            }
            DataConfigDO dataConfigDO = new DataConfigDO();
            dataConfigDO.setDeleted(true);
            dataConfigDO.setUpdatedBy(deleteDTO.getUpdatedBy());
            dataConfigDO.setUpdatedTime(LocalDateTime.now());
            LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(DataConfigDO::getType,deleteDTO.getType());
            lambdaQuery.eq(DataConfigDO::getSubType,deleteDTO.getSubType());
            return compensationDataConfigService.update(dataConfigDO, lambdaQuery);
        }else {
            DataConfigDO dataConfigDO = new DataConfigDO();
            dataConfigDO.setDeleted(true);
            dataConfigDO.setUpdatedBy(deleteDTO.getUpdatedBy());
            dataConfigDO.setUpdatedTime(LocalDateTime.now());
            LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
            return compensationDataConfigService.update(dataConfigDO, lambdaQuery.eq(DataConfigDO::getId, deleteDTO.getId()));
        }
    }

    @Override
    public boolean update(CompensationConfigDTO dto) {
        DataConfigDO updateReplaceConfig = new DataConfigDO();
        updateReplaceConfig.setValue(dto.getValue());
        updateReplaceConfig.setUpdatedBy(dto.getUpdatedBy());
        updateReplaceConfig.setUpdatedTime(LocalDateTime.now());

        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, dto.getUpdatedBy(), null);
        if (CollectionUtils.isNotEmpty(reviewer)) {
            updateReplaceConfig.setUpdatedBy(reviewer.get(0).getReviewerNameNo());
        }

        //是否同步到当前租户的其他授权航司，Y是 N否
        if("Y".equals(dto.getToSync())){
            DataConfigDO byId = compensationDataConfigService.getById(dto.getId());
            //查询修改的事故说明，此类型下的所有数据，（此类型每个授权航司的配置）
            LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(DataConfigDO::getType, StringUtils.isEmpty(dto.getType())?byId.getType():dto.getType())
                    .eq(DataConfigDO::getDeleted, false)
                    .eq(DataConfigDO::getSubType, StringUtils.isEmpty(dto.getSubType())?byId.getSubType():dto.getSubType());
            List<DataConfigDO> airlineList = compensationDataConfigService.list(lambdaQuery);
            Map<String, List<DataConfigDO>> groupedByAirCode = airlineList.stream()
                    .collect(Collectors.groupingBy(DataConfigDO::getAirCode));

            List<DataConfigDO> configDOList = new ArrayList<>();
            //获取已授权的航司。 ABNORMAL_LUGGAGE
            List<BelongAirlineDTO> belongAirline = businessPrivilegeService.getBelongAirline(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE);
            for(BelongAirlineDTO airlineDTO:belongAirline){
                if(groupedByAirCode.containsKey(airlineDTO.getCode())){
                    //需要更新的,一个子类型配置，每个授权航司只有一个
                    DataConfigDO configDO = groupedByAirCode.get(airlineDTO.getCode()).get(0);
                    configDO.setValue(dto.getValue());
                    configDO.setUpdatedTime(LocalDateTime.now());
                    configDO.setUpdatedBy(updateReplaceConfig.getUpdatedBy());
                    configDOList.add(configDO);
                }else {
                    //需要新增的
                    DataConfigDO configDO = new DataConfigDO();
                    BeanUtil.copyProperties(byId,configDO);
                    configDO.setId(null);
                    configDO.setValue(dto.getValue());
                    configDO.setUpdatedBy(updateReplaceConfig.getUpdatedBy());
                    configDO.setUpdatedTime(LocalDateTime.now());
                    configDO.setAirCode(airlineDTO.getCode());
                    configDO.setCodeCn(airlineDTO.getName());
                    configDOList.add(configDO);
                }
            }
            return compensationDataConfigService.saveOrUpdateBatch(configDOList);
        }else {
            //当前租户的事故说明配置key
            String type = getAccidentDescribeTypeByTenantCode();
            //根据id，类型、航司二字码
            LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(DataConfigDO::getId, dto.getId())
                    .eq(DataConfigDO::getType,type)
                    .eq(DataConfigDO::getAirCode,  dto.getAirCode());
            return compensationDataConfigService.update(updateReplaceConfig, lambdaQuery);
        }

    }

    @Override
    public boolean create(CompensationConfigDTO dto) {
        if(StringUtils.isEmpty(dto.getAirCode()) || StringUtils.isEmpty(dto.getCodeCn()) ){
            return false;
        }
        //系统生成子类型，用于同步到租户的其他授权航司配置
        String subTypeCode = generateSubTypeCode();
        do {
            subTypeCode = generateSubTypeCode();
        } while (compensationDataConfigMapper.existsBySubTypeCode(subTypeCode)>0);


        DataConfigDO updateReplaceConfig = new DataConfigDO();
        updateReplaceConfig.setType(getAccidentDescribeTypeByTenantCode());
        updateReplaceConfig.setSubType(subTypeCode);
        updateReplaceConfig.setValue(dto.getValue());
        updateReplaceConfig.setUpdatedBy(dto.getUpdatedBy());
        updateReplaceConfig.setCreatedBy(dto.getUpdatedBy());
        updateReplaceConfig.setDescription(CompensationConfigEnum.ACCIDENT_DESCRIBE_SELECT.getDescription());
        updateReplaceConfig.setAirCode(dto.getAirCode());
        updateReplaceConfig.setCodeCn(dto.getCodeCn());
        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, dto.getUpdatedBy(), null);
        if (CollectionUtils.isNotEmpty(reviewer)) {
            updateReplaceConfig.setUpdatedBy(reviewer.get(0).getReviewerNameNo());
            updateReplaceConfig.setCreatedBy(reviewer.get(0).getReviewerNameNo());
        }
        //是否同步到当前租户的其他授权航司，Y是 N否
        if("Y".equals(dto.getToSync())){
            List<DataConfigDO> configDOList = new ArrayList<>();
            //获取已授权的航司。 ABNORMAL_LUGGAGE
            List<BelongAirlineDTO> belongAirline = businessPrivilegeService.getBelongAirline(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE);
            for(BelongAirlineDTO airlineDTO:belongAirline){
                DataConfigDO configDO = new DataConfigDO();
                BeanUtil.copyProperties(updateReplaceConfig,configDO);
                configDO.setAirCode(airlineDTO.getCode());
                configDO.setCodeCn(airlineDTO.getName());
                configDOList.add(configDO);
            }
            return compensationDataConfigService.saveBatch(configDOList);
        }else {
            return compensationDataConfigService.save(updateReplaceConfig);
        }
    }



    private static final String UPPER = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String LOWER = UPPER.toLowerCase();
    private static final String ALPHABET = UPPER + LOWER;
    private static final int CODE_LENGTH = 12; // 与SELECT_DESCRIBE相同长度
    private static final Random RANDOM = new SecureRandom();

    /**
     * 生成随机的子类型Code
     * @return 12位大小写字母组成的随机字符串
     */
    public static String generateSubTypeCode() {
        StringBuilder sb = new StringBuilder(CODE_LENGTH);
        for (int i = 0; i < CODE_LENGTH; i++) {
            sb.append(ALPHABET.charAt(RANDOM.nextInt(ALPHABET.length())));
        }
        return CompensationConfigEnum.ACCIDENT_DESCRIBE_SELECT.getSubType()+sb.toString();
    }

    // 使用示例
    public static void main(String[] args) {
        System.out.println(generateSubTypeCode()); // 输出如: "aBcDeFgHiJkL"
    }
}
