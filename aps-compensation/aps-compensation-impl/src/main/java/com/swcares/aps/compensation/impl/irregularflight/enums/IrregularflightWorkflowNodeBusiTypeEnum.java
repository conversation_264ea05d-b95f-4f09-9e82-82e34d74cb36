package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * ClassName：ApplyWorkflowNodeBusiTypeEnum <br>
 * Description：申领单审核工作流节点业务类型 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/20 <br>
 * @version v1.0 <br>
 */
public enum IrregularflightWorkflowNodeBusiTypeEnum {
    SUBMITTER("submitter","流程提交节点—流程的第一个节点;提交业务数据给工作流引擎"),
    COMMON("common","其他普通节点-记录当前审核人信息，等待页面审核人发起审核流程"),
    AUTOMATIC("automatic","自动审核节点—直接修改节点为complete状态"),
    END("end","流程结束节点—修改申领单状态为通过或者不通过"),
    CANCEL("cancel","流程取消节点—修改申领单状态不通过");

    private String type;
    private String name;

    IrregularflightWorkflowNodeBusiTypeEnum(String type, String name) {
        this.type = type;
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public String getName() {
        return name;
    }
}
