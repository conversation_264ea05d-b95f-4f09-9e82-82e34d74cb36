package com.swcares.aps.compensation.impl.irregularflight.mapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationFlightInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationFlightInfoVO;
import org.apache.ibatis.annotations.Param;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.mapper.CompensationFlightInfoMapper <br>
 * Description：赔偿航班信息 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface CompensationFlightInfoMapper extends BaseMapper<CompensationFlightInfoDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-10-27 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<CompensationFlightInfoVO> page(@Param("dto") CompensationFlightInfoPagedDTO dto, Page<CompensationFlightInfoVO> page);
}
