package com.swcares.aps.compensation.impl.baggage.accident.mapper;


import com.swcares.aps.component.workflow.entity.WorkflowAuditorIdInfoDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageTransportQueryDTO;

public interface BaggageTransportInfoMapper extends BaseMapper<BaggageTransportInfoDO>{

    @Delete("delete from BAGGAGE_TRANSPORT_INFO where accident_no = #{accidentNo}")
    void deleteByAccidentId(@Param("accidentNo") String accidentNo);
    /**
     * 根据事故单ID查询运输信息
     */


    IPage<BaggageTransportListVO> queryBaggageTransportList(IPage<?> page, @Param("query") BaggageTransportQueryDTO query);

    /**
     * 检查用户是否有运输单审核权限
     * @param transportId 运输单ID
     * @param jobNumber 用户工号
     * @return 是否有审核权限
     */
    WorkflowAuditorIdInfoDO checkTransportAuditPermission(@Param("transportId") String transportId, @Param("jobNumber") String jobNumber);
}
