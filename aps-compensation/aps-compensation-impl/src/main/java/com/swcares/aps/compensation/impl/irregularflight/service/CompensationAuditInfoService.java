package com.swcares.aps.compensation.impl.irregularflight.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationAuditInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.compensation.service.AuditInfoService <br>
 * Description：赔偿单-待审核记录 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
public interface CompensationAuditInfoService extends IService<CompensationAuditInfoDO> {
    /***
     * @title findReviewerList
     * @description 查询审批人集合，根据单号taskId\
     * <AUTHOR>
     * @date 2022/12/9 17:05

     * @return java.util.List<java.lang.String>
     */
    CompensationAuditInfoDO findCompensationAuditInfo(CompensationAuditInfoDO auditInfoDO);

    /**
    * @title findReviewerIds
    * @description 条件查询审核人id集合，cpc调用
    * <AUTHOR>
    * @date 2021/12/21 11:29
    * @param positions
    * @return java.util.List<java.lang.String>
    */
    List<String> findReviewerIds(List<String> positions);

    /**
     * @title findReviewer
     * @description 前端，根据条件筛选查询审核人
     * <AUTHOR>
     * @date 2021/12/17 15:27
     * @param orgId
     * @param userInfo
     * @param taskId
     * @param orderId
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> findReviewer(Long orgId, String userInfo, String taskId,Long orderId);

    /**
     * @title getReviewer
     * @description cpc，根据条件查询审核人
     * <AUTHOR>
     * @date 2021/12/17 15:27
     * @param deptIds   部门id
     * @param userIds   用户id
     * @param roleIds   角色id
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO>
     */
    List<CompensationReviewerInfoVO> getReviewer(String deptIds, String userIds,String roleIds);

    /**
     * @title saveReviewer
     * @description 新增待审核信息（选择审核人）
     * <AUTHOR>
     * @date 2021/12/17 15:28
     * @param dto   审核人查询条件封装对象
     * @return void
     */
    void saveReviewer(CompensationAuditInfoDTO dto);

    /**
     * @title findAuditRecord
     * @description 查询审核记录
     * <AUTHOR>
     * @date 2021/12/17 15:28
     * @param orderId   赔偿单id
     * @param orderNo   赔偿单号
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO>
     */
    List<CompensationAuditRecordVO> findAuditRecord(Long orderId,String orderNo);

    /**
     * Title：auditOperation <br>
     * Description： 执行审核<br>
     * author：傅欣荣 <br>
     * date：2021/11/24 14:40 <br>
     * @param dto
     * @return java.util.Map<java.lang.String,java.lang.Object>
     */
    Map<String,Object> auditOperation(AuditProcessorDTO dto) throws Exception;

    /**
     * Title：getStringToMap <br>
     * Description：string转map<br>
     * author：傅欣荣 <br>
     * date：2021/12/6 16:32 <br>
     * @param str
     * @return java.util.Map<java.lang.String,java.lang.String>
     */
    Map<String, String> getStringToMap(String str);

}
