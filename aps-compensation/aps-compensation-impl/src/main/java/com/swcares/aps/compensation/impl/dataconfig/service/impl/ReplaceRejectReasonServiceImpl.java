package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.alicp.jetcache.anno.SerialPolicy;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.dataconfig.enums.CompensationConfigEnum;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRejectReasonService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.ReplaceBaseRuleVO;
import com.swcares.aps.compensation.model.dataconfig.vo.ReplacePayPeriodRuleVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.impl.ReplaceRejectReasonServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022-01-12 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ReplaceRejectReasonServiceImpl implements ReplaceRejectReasonService {

    private static final String CACHE_NAME = "ReplaceAuditReasonCache:";
    private static final String CACHE_KEY = "'RejectReason'";

    @Autowired
    private ReplaceConfigService replaceConfigService;

    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @Autowired
    private BusinessDataPushHandler businessDataPushHandler;
    @Override
    public Map<String, RuleManageDTO> pages() {
        ReplaceRejectReasonPageDTO rejectReasonPageDTO = new ReplaceRejectReasonPageDTO();
        rejectReasonPageDTO.setType(CompensationConfigEnum.AUDIT_REJECT_REASON.getType());
        rejectReasonPageDTO.setSubType(CompensationConfigEnum.AUDIT_REJECT_REASON.getSubType());
        rejectReasonPageDTO.setAirCode(UserContext.getCurrentUser().getTenantCode());
        List<ReplaceRejectReasonDTO> rejectReasonDTOS = replaceConfigService.rejectReasonPages(rejectReasonPageDTO);
        ReplaceRejectReasonPageDTO others = new ReplaceRejectReasonPageDTO();
        others.setType(CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getType());
        others.setAirCode(UserContext.getCurrentUser().getTenantCode());
        List<ReplaceRejectReasonDTO> rules = replaceConfigService.rejectReasonPages(others);
        if (rules != null && !rules.isEmpty()) {
            rejectReasonDTOS.addAll(rules);
        }
        Map<String, RuleManageDTO> ruleManageMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(rejectReasonDTOS)) {
            Map<String, List<ReplaceRejectReasonDTO>> collect = rejectReasonDTOS.stream().collect(Collectors.groupingBy(key -> key.getAirCode()));
            collect.forEach((k, v) -> {
                RuleManageDTO ruleManageDTO = new RuleManageDTO();
                ruleManageDTO.setReasonDTOList(v.stream().filter(t -> CompensationConfigEnum.AUDIT_REJECT_REASON.getSubType().equalsIgnoreCase(t.getSubType())).collect(Collectors.toList()));
                ReplacePayPeriodRuleVO replacePayPeriodRuleVO = new ReplacePayPeriodRuleVO();
                Optional<ReplaceRejectReasonDTO> replaceRejectReasonDTOOptional = v.stream().filter(t -> CompensationConfigEnum.RULE_PAYMENT_WAITING_PERIOD.getSubType().equalsIgnoreCase(t.getSubType())).findFirst();
                replacePayPeriodRuleVO.setPaymentWaitingPeriod(replaceRejectReasonDTOOptional.isPresent() ? StrUtil.isNotBlank(replaceRejectReasonDTOOptional.get().getValue())? Integer.valueOf(replaceRejectReasonDTOOptional.get().getValue()):null : null);
                ruleManageDTO.setReplacePayPeriodRuleVO(replacePayPeriodRuleVO);
                ReplaceBaseRuleVO replaceBaseRuleVO = new ReplaceBaseRuleVO();
                Optional<ReplaceRejectReasonDTO> present = v.stream().filter(t -> CompensationConfigEnum.RULE_IS_CONTAIN_SELF.getSubType().equalsIgnoreCase(t.getSubType())).findFirst();
                replaceBaseRuleVO.setIsContainSelf(present.isPresent() ? present.get().getValue() : null);
                Optional<ReplaceRejectReasonDTO> first = v.stream().filter(t -> CompensationConfigEnum.RULE_IS_SAME_FLIGHT.getSubType().equalsIgnoreCase(t.getSubType())).findFirst();
                replaceBaseRuleVO.setIsSameFlight(first.isPresent()? first.get().getValue():null);
                Optional<ReplaceRejectReasonDTO> rejectPresent = v.stream().filter(t -> CompensationConfigEnum.RULE_MAX_APPLY_PASSENGER.getSubType().equalsIgnoreCase(t.getSubType())).findFirst();
                replaceBaseRuleVO.setMaxApplyPassenger(rejectPresent.isPresent()? Integer.valueOf(rejectPresent.get().getValue()) : null);
                ruleManageDTO.setReplaceBaseRuleVO(replaceBaseRuleVO);
                ruleManageMap.put(k, ruleManageDTO);
            });
        }
        return ruleManageMap;
    }

    private void wrapUserNameAndNo(List<ReplaceRejectReasonDTO> rejectReasonDTOS) {
        Set<String> userIds = rejectReasonDTOS.stream().map(t -> Arrays.asList(t.getCreatedBy(), t.getUpdatedBy()))
                .flatMap(t -> t.stream())
                .collect(Collectors.toSet());

        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, StringUtils.join(userIds, ","), null);
        if (CollectionUtils.isNotEmpty(reviewer)) {
            Map<Long, List<CompensationReviewerInfoVO>> reviewerIdMap = reviewer.stream()
                    .collect(Collectors.groupingBy(CompensationReviewerInfoVO::getReviewerId));

            rejectReasonDTOS.forEach(t -> {
                if (reviewerIdMap.containsKey(Long.valueOf(t.getCreatedBy()))) {
                    t.setCreatedBy(reviewerIdMap.get(Long.valueOf(t.getCreatedBy())).get(0).getReviewerNameNo());
                }
                if (reviewerIdMap.containsKey(Long.valueOf(t.getUpdatedBy()))) {
                    t.setUpdatedBy(reviewerIdMap.get(Long.valueOf(t.getUpdatedBy())).get(0).getReviewerNameNo());
                }
            });
        }
    }

    @Cached(name = CACHE_NAME, key = CACHE_KEY, expire = 3600, cacheType = CacheType.REMOTE, serialPolicy = SerialPolicy.KRYO, postCondition = "result!=null")
    @Override
    public List<ReplaceRejectReasonDTO> getAllRejectReason() {
        List<ReplaceRejectReasonDTO> list = replaceConfigService.getAllRejectReason();
        return list;
    }

    @CacheInvalidate(name = CACHE_NAME, key = CACHE_KEY)
    @Override
    public boolean delete(ReplaceConfigDeleteDTO deleteDTO) {
        if (CollectionUtils.isEmpty(deleteDTO.getIds())) {
            return true;
        }
        DataConfigDO updateReplaceConfig = new DataConfigDO();
        updateReplaceConfig.setDeleted(true);
        updateReplaceConfig.setUpdatedBy(deleteDTO.getUpdatedBy());
        updateReplaceConfig.setUpdatedTime(LocalDateTime.now());
        LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        boolean result = replaceConfigService.update(updateReplaceConfig, lambdaQuery.in(DataConfigDO::getId, deleteDTO.getIds()));

        return result;
    }

    @CacheInvalidate(name = CACHE_NAME, key = CACHE_KEY)
    @Override
    public boolean update(ReplaceRejectReasonDTO rejectReasonDTO) {
        DataConfigDO updateReplaceConfig = new DataConfigDO();
        updateReplaceConfig.setValue(rejectReasonDTO.getValue());
        updateReplaceConfig.setUpdatedBy(rejectReasonDTO.getUpdatedBy());
        updateReplaceConfig.setUpdatedTime(LocalDateTime.now());

        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, rejectReasonDTO.getUpdatedBy(), null);
        if (CollectionUtils.isNotEmpty(reviewer)) {
            updateReplaceConfig.setUpdatedBy(reviewer.get(0).getReviewerNameNo());
        }

        LambdaQueryWrapper<DataConfigDO> lambdaQuery = Wrappers.lambdaQuery();
        lambdaQuery.eq(DataConfigDO::getId, rejectReasonDTO.getId())
                .eq(DataConfigDO::getType, CompensationConfigEnum.AUDIT_REJECT_REASON.getType())
                .eq(DataConfigDO::getSubType, CompensationConfigEnum.AUDIT_REJECT_REASON.getSubType());
        boolean updated = replaceConfigService.update(updateReplaceConfig, lambdaQuery);
        businessDataPushHandler.dataStore(rejectReasonDTO.getId(), BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_REPLACE);
        return updated;
    }

    @CacheInvalidate(name = CACHE_NAME, key = CACHE_KEY)
    @Override
    public boolean create(ReplaceRejectReasonDTO rejectReasonDTO) {
        DataConfigDO updateReplaceConfig = new DataConfigDO();
        updateReplaceConfig.setType(CompensationConfigEnum.AUDIT_REJECT_REASON.getType());
        updateReplaceConfig.setSubType(CompensationConfigEnum.AUDIT_REJECT_REASON.getSubType());
        updateReplaceConfig.setValue(rejectReasonDTO.getValue());
        updateReplaceConfig.setUpdatedBy(rejectReasonDTO.getUpdatedBy());
        updateReplaceConfig.setCreatedBy(rejectReasonDTO.getUpdatedBy());
        updateReplaceConfig.setDescription(CompensationConfigEnum.AUDIT_REJECT_REASON.getDescription());
        updateReplaceConfig.setAirCode(UserContext.getCurrentUser().getTenantCode());
        updateReplaceConfig.setCodeCn(UserContext.getCurrentUser().getTenantName());
        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoService.getReviewer(null, rejectReasonDTO.getUpdatedBy(), null);
        if (CollectionUtils.isNotEmpty(reviewer)) {
            updateReplaceConfig.setUpdatedBy(reviewer.get(0).getReviewerNameNo());
            updateReplaceConfig.setCreatedBy(reviewer.get(0).getReviewerNameNo());
        }

        boolean created = replaceConfigService.save(updateReplaceConfig);
        businessDataPushHandler.dataStore(updateReplaceConfig.getId(), BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_REPLACE);
        return created;
    }
}
