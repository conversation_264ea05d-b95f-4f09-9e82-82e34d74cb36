package com.swcares.aps.compensation.impl.irregularflight.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.irregularflight.entity.AccidentRecordDO;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.service.AccidentRecordService <br>
 * Description：事故单将操作记录表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface AccidentRecordService extends IService<AccidentRecordDO> {
}
