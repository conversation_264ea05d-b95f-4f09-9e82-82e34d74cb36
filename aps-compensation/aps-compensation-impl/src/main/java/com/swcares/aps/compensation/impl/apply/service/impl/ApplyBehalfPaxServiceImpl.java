package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyPaxMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfPaxService;
import com.swcares.aps.compensation.impl.apply.service.ApplyCaptchaService;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRuleService;
import com.swcares.aps.compensation.model.apply.dto.AuthBehalfPaxDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.vo.AuthCompensationOrderVO;
import com.swcares.aps.compensation.model.apply.vo.AuthPaxVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationFlightInfoVO;
import com.swcares.aps.compensation.model.apply.vo.CompensationOrderInfoVO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRuleDTO;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.compensation.service.impl.ApplyPaxServiceImpl <br>
 * Description：航延补偿申领旅客信息表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ApplyBehalfPaxServiceImpl extends ServiceImpl<ApplyPaxMapper, ApplyPaxDO> implements ApplyBehalfPaxService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ReplaceRuleService replaceRuleService;

    @Autowired
    private CompensationBasicDataService compensationBasicDataService;

    @Autowired
    private FieldEncryptor encryptor;


    @Autowired
    private ApplyCaptchaService applyCaptchaService;

    /**
     * Title：authBehalfPax <br>
     * Description：验证代领规则和代领人是否匹配 <br>
     * author：王磊 <br>
     * date：2022/1/6 13:51 <br>
     * @param dto <br>
     * @return <br>
     */
    @Override
    public void authBehalfPax(AuthBehalfPaxDTO dto) {
        String idCardNumDesensitization = ApsDesensitizedUtil.idCardNum(dto.getIdNo());
        log.info("【申领单代领业务-authBehalfPax方法】请求参数:航班号:{},航班日期:{},证件号:{},代领人姓名:{}"
                , dto.getFlightNo(), dto.getFlightDate(), idCardNumDesensitization,
                dto.getPaxName());
        PassengerQueryDTO searchDTO = ObjectUtils.copyBean(dto, PassengerQueryDTO.class);
        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(searchDTO);
        int count = passengerInfo.size();
        Map<String, ReplaceRuleDTO> replaceRule = getReplaceRule();
        if (replaceRule == null){
            log.error("【申领单代领业务-authBehalfPax方法】请求参数:航班号:{},航班日期:{},证件号:{},代领人姓名:{}, 获取代领规则为空,抛异常：APPLY_AUTH_BEHALF_PAX_ERROR(申领人未在该航班中，请检查信息后再次提交！)"
                    ,dto.getFlightNo(),dto.getFlightDate(),idCardNumDesensitization,dto.getPaxName());
            throw new BusinessException(ApplyErrors.APPLY_AUTH_BEHALF_PAX_ERROR);
        }
        log.info("【申领单代领业务-authBehalfPax方法】请求参数:航班号:{},航班日期:{},证件号:{},代领人姓名:{}, 查询旅客信息size:{},获取代领规则:{}"
                ,dto.getFlightNo(),dto.getFlightDate(),idCardNumDesensitization,dto.getPaxName(),count,JSON.toJSONString(replaceRule));
        replaceRule.forEach((k, v) -> {
            if (k.equalsIgnoreCase(dto.getFlightNo().substring(0,2))){
                //当WEB端设置了代领人在本航班的开关开启时，检验航班号、航班日期、代领人姓名、身份证号是否在该航班中。
                if (ApplyConstants.SAME_FLIGHT.equals(v.getIsSameFlight())) {
                    if (count == 0) {
                        log.info("【申领单代领业务-authBehalfPax方法】请求参数:航班号:{},航班日期:{},证件号:{},代领人姓名:{}, 是否同航班旅客(1:是),抛异常：APPLY_AUTH_BEHALF_PAX_ERROR(代领人未在该航班中，请检查信息后再次提交！)"
                                ,dto.getFlightNo(),dto.getFlightDate(),idCardNumDesensitization,dto.getPaxName());
                        throw new BusinessException(ApplyErrors.APPLY_AUTH_BEHALF_PAX_ERROR);
                    }
                }
            }
        });

    }

    /**
     * Title：authCompensationOrderBehalf <br>
     * Description：通过参数校验旅客数据是否存在异常,如存在异常则返回对应错误的身份证号 <br>
     * author：王磊 <br>
     * date：2022/1/18 9:41 <br>
     * @param dtos <br>
     * @return <br>
     */
    @Override
    public AuthCompensationOrderVO authCompensationOrderBehalf(List<AuthBehalfPaxDTO> dtos) {
        AuthCompensationOrderVO vo = new AuthCompensationOrderVO();
        for (AuthBehalfPaxDTO dto : dtos) {
            //校验提交的乘机人是否在相同的航班上
//            int c = this.getBaseMapper().findPaxFlight(dto);
            String idNo = dto.getIdNo();
            Map<String, ReplaceRuleDTO> replaceRule = getReplaceRule();
            if (replaceRule == null || !replaceRule.containsKey(dto.getFlightNo().substring(0,2))){
                throw new BusinessException(ApplyErrors.APPLY_AUTH_BEHALF_PAX_ERROR);
            }
            replaceRule.forEach((k, v) -> {
                if (k.equalsIgnoreCase(dto.getFlightNo().substring(0,2))){
                    //当WEB端设置了代领人在本航班的开关开启时，检验航班号、航班日期、代领人姓名、身份证号是否在该航班中。
                    if (ApplyConstants.SAME_FLIGHT.equals(v.getIsSameFlight())) {
                        PassengerQueryDTO searchDTO = ObjectUtils.copyBean(dto, PassengerQueryDTO.class);
                        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(searchDTO);
                        int c = passengerInfo.size();
                        if (c <= 0) {
                            vo.getFlightErrorMap().put(idNo, dto.getPaxName());
                        }
                    }
                }
            });

            //获取旅客是否存在有效赔偿单
            dto.setIdNo(encryptor.encrypt(dto.getIdNo()));
            List<AuthPaxVO> authPaxVOS = this.getBaseMapper().authPaxFindBehalf(dto);
            if (ListUtils.isEmpty(authPaxVOS)) {
                if(StringUtils.isEmpty(vo.getFlightErrorMap().get(idNo))){
                    vo.getCompensationErrorMap().put(idNo, dto.getPaxName());
                }
            }
            boolean authPaxBool = false;
            for (AuthPaxVO authPaxVO : authPaxVOS) {
                if (!ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) && ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                    authPaxBool = true;
                }
            }
            if (!authPaxBool) {
                int countSwitch = 0;
                int countReceive = 0;
                //状态判断
                for (AuthPaxVO authPaxVO : authPaxVOS) {
                    if (ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff())) {
                        countSwitch++;
                    }
                    if (!ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                        countReceive++;
                    }
                }
                if (countSwitch > 0 && authPaxVOS.size() == countSwitch + countReceive) {
                    if(StringUtils.isEmpty(vo.getFlightErrorMap().get(idNo)) && StringUtils.isEmpty(vo.getCompensationErrorMap().get(idNo))){
                        vo.getFreezeErrorMap().put(idNo, dto.getPaxName());
                    }
                }
                if (countReceive > 0 && authPaxVOS.size() == countReceive + countSwitch) {
                    if(StringUtils.isEmpty(vo.getFlightErrorMap().get(idNo)) && StringUtils.isEmpty(vo.getCompensationErrorMap().get(idNo))
                            && StringUtils.isEmpty(vo.getFreezeErrorMap().get(idNo))){
                        vo.getAlreadyErrorMap().put(idNo, dto.getPaxName());
                    }
                }
            }
        }
        return vo;
    }

    /**
     * Title：findCompensationOrderBehalf <br>
     * Description：代领通过条件获取旅客数据 <br>
     * author：王磊 <br>
     * date：2022/1/10 13:47 <br>
     * @param dtos <br>
     * @return <br>
     */
    @Override
    public List<List<CompensationOrderInfoVO>> findCompensationOrderBehalf(List<AuthBehalfPaxDTO> dtos) {
        //安全验证：避免越过短信验证步骤，直接调用信息接口
        applyCaptchaService.verifyTokenCaptchaPassByTenant();
        log.info("【申领单代领业务-获取旅客赔偿单详情】findCompensationOrderBehalf方法,请求参数dtos:{}" , JSONUtil.toJsonStr(dtos));
        List<List<CompensationOrderInfoVO>> list = new ArrayList<List<CompensationOrderInfoVO>>();
        //如数据无异常则获取旅客赔偿单信息
        for (AuthBehalfPaxDTO dto : dtos) {
            //加密证件号
            dto.setIdNo(encryptor.encrypt(dto.getIdNo()));
            //获取旅客赔偿单数据
            List<CompensationOrderInfoVO> orderInfoVOS = this.getBaseMapper().findCompensationOrderBehalf(dto);
            //获取配置的商务舱参数
            String[] bMainClasssign = ConfigUtil.get("B_MAIN_CLASSSIGN").
                    getValues().get(0).getConfigValue().split(",");
            String[] bClasssign = ConfigUtil.get("B_CLASSSIGN").
                    getValues().get(0).getConfigValue().split(",");
            for (CompensationOrderInfoVO orderInfoVO : orderInfoVOS) {
                if (Arrays.asList(bMainClasssign).contains(orderInfoVO.getMainClass()) ||
                        Arrays.asList(bClasssign).contains(orderInfoVO.getSubClass())) {
                    orderInfoVO.setIsBClass(ApplyConstants.BUSINESS_CLASS);
                }
            }
            list.add(orderInfoVOS);
        }
        log.info("【申领单代领业务-获取旅客赔偿单详情】findCompensationOrderBehalf方法,请求参数dtos:{},返回数据：{}"
                ,JSONUtil.toJsonStr(dtos),JSONUtil.toJsonStr(list));
        return list;
    }

    /**
     * Title：findFlightInfo <br>
     * Description：通过航班号和航班日期获取航班信息 <br>
     * author：王磊 <br>
     * date：2022/1/10 14:08 <br>
     * @param flightNo
     * @param flightDate <br>
     * @return <br>
     */
    @Override
    public CompensationFlightInfoVO findFlightInfo(String flightNo, String flightDate) {
        //先去业务表查询
        AuthPaxDTO dto = new AuthPaxDTO();
        dto.setFlightDate(flightDate);
        dto.setFlightNo(flightNo);
        CompensationFlightInfoVO flightInfoVO = this.getBaseMapper().findCompensationOrderFlightBehalf(dto);
        if (ObjectUtils.isNotEmpty(flightInfoVO)) {
            return flightInfoVO;
        }
        //业务表查询没有数据，再去航班表查询
        FlightBaseQueryDTO flightBaseQueryDTO = new FlightBaseQueryDTO();
        flightBaseQueryDTO.setFlightNo(flightNo);
        flightBaseQueryDTO.setFlightDate(flightDate);
        List<FlightBasicnfoVO> flightBasicInfo = compensationBasicDataService.getFlightBasicInfo(flightBaseQueryDTO);
        log.info("【compensation-apply-service】旅客申领查询的航班信息【{}】", JSONUtil.toJsonStr(flightBasicInfo));
        if (ListUtils.isEmpty(flightBasicInfo)) {
            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ERROR);
        }

        CompensationFlightInfoVO flight = new CompensationFlightInfoVO();
        flight.setFlightDate(flightDate);
        flight.setFlightNo(flightNo);

        if(flightBasicInfo != null && flightBasicInfo.size()==1){
            FlightBasicnfoVO basicnfoVO1 = flightBasicInfo.get(0);
            flight.setFlightId(basicnfoVO1.getFlightId());
            flight.setSegment(basicnfoVO1.getSegment());
            flight.setSegmentCH(basicnfoVO1.getSegmentCh());
            flight.setSegmentCHFlight(basicnfoVO1.getSegmentCh());
            flight.setStd(formatEtdStd(basicnfoVO1.getStd()));
            flight.setSta(formatEtdStd(basicnfoVO1.getSta()));

        }else if(flightBasicInfo != null && flightBasicInfo.size()>1){
            //多航段航班 返回顺序可能是：CTU-WHA,WHA-TAO 或者 WHA-TAO,CTU-WHA
            FlightBasicnfoVO basicnfoVO1 = flightBasicInfo.get(0);
            FlightBasicnfoVO basicnfoVO2 = flightBasicInfo.get(1);
            String segment ="";
            String segmentCh ="";
            String flightId ="";

            //判断：前序和后续航班，规则前序航班的到达站，是后续航班的起始站
            if(basicnfoVO1.getArrivalPort().equals(basicnfoVO2.getDepartPort())){
                //get(0)到达站是get(1)的起始站，那get(0)数据就是前序航班航段，get(1)数据为后续航班航段
                segment = basicnfoVO1.getSegment()+"-"+basicnfoVO2.getPoa();
                segmentCh = basicnfoVO1.getSegmentCh()+"-"+basicnfoVO2.getArrivalPort()+basicnfoVO2.getPoa();
                flightId = basicnfoVO1.getFlightId();
                flight.setStd(formatEtdStd(basicnfoVO1.getStd()));
                flight.setSta(formatEtdStd(basicnfoVO2.getSta()));

            }else if(basicnfoVO2.getArrivalPort().equals(basicnfoVO1.getDepartPort())){
                //get(1)到达站是get(0)的起始站，那get(1)数据就是前序航班航段，get(0)数据为后续航班航段
                segment = basicnfoVO2.getSegment()+"-"+basicnfoVO1.getPoa();
                segmentCh = basicnfoVO2.getSegmentCh()+"-"+basicnfoVO1.getArrivalPort()+basicnfoVO1.getPoa();
                flightId = basicnfoVO2.getFlightId();
                flight.setStd(formatEtdStd(basicnfoVO2.getStd()));
                flight.setSta(formatEtdStd(basicnfoVO1.getSta()));
            }
            flight.setFlightId(flightId);
            flight.setSegment(segment);
            flight.setSegmentCH(segmentCh);
            flight.setSegmentCHFlight(segmentCh);
        }

        return flight;
    }

    /**
     * @title getReplaceRule
     * @description 获取配置的代领规则
     * <AUTHOR>
     * @date 2022/1/13 9:30

     * @return com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO
     */
    @Override
    public Map<String,ReplaceRuleDTO> getReplaceRule() {
        return replaceRuleService.getRule();
    }

    public String formatEtdStd(String td) {
        String date = td;
        if(org.apache.commons.lang3.ObjectUtils.isNotEmpty(td)){
            date = DateUtils.formatDate(DateUtils.parseDate(td), "HH:mm");
        }
        return date;
    }


}
