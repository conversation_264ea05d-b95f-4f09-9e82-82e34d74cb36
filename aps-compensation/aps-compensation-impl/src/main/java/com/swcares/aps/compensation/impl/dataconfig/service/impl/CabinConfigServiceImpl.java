package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.swcares.aps.compensation.impl.dataconfig.mapper.CabinConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.CabinConfigService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.ConfigurationDataPushHandler;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;
import com.swcares.aps.compensation.model.datasync.dto.BusinessDataSyncDTO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @ClassName：CabinConfigServiceImpl
 * @Description：舱位设置的实现类
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 16:23
 * @version： v1.0
 */
@Slf4j
@Service
public class CabinConfigServiceImpl implements CabinConfigService {

    @Autowired
    private CabinConfigMapper cabinConfigMapper;

    @Autowired
    private ConfigurationDataPushHandler configurationDataPushHandler;

    @Override
    public CabinConfigVO loadCabinByAirline(String airlineCode) {
        QueryWrapper<CabinConfigDO> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("AIRLINE_CODE",airlineCode);
        CabinConfigDO cabinConfigDO = cabinConfigMapper.selectOne(queryWrapper);

        CabinConfigVO cabinConfigVO = new CabinConfigVO();
        if(cabinConfigDO != null){
            BeanUtils.copyProperties(cabinConfigDO, cabinConfigVO);
        }
        return cabinConfigVO;
    }

    @Override
    public void createCabinConfig(String businessCabin, String economyCabin) {
        if(StringUtils.isEmpty(economyCabin)){
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL, "经济舱");
        }
        QueryWrapper<CabinConfigDO> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("AIRLINE_CODE", UserContext.getCurrentUser().getTenantCode());
        CabinConfigDO cabinConfigDO = cabinConfigMapper.selectOne(queryWrapper);
        int mark = 0;
        if(ObjectUtils.isNotEmpty(cabinConfigDO)){
            cabinConfigDO.setBusinessCabin(businessCabin);
            cabinConfigDO.setEconomyCabin(economyCabin);
            cabinConfigDO.setUpdatedTime(null);
            cabinConfigDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
            cabinConfigDO.setAirlineName(UserContext.getCurrentUser().getTenantName());
            mark = cabinConfigMapper.updateById(cabinConfigDO);
        }else{
            cabinConfigDO = new CabinConfigDO();
            cabinConfigDO.setBusinessCabin(businessCabin);
            cabinConfigDO.setEconomyCabin(economyCabin);
            cabinConfigDO.setAirlineCode(UserContext.getCurrentUser().getTenantCode());
            cabinConfigDO.setAirlineName(UserContext.getCurrentUser().getTenantName());
            mark = cabinConfigMapper.insert(cabinConfigDO);
        }

        if(mark > 0){
            BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder().senderCustomer(UserContext.getCurrentUser().getTenantCode())
                    .dataType(BusinessDataSyncConstant.CABIN_TYPE)
                    .cabinConfigDO(cabinConfigDO).build();
            try {
                configurationDataPushHandler.processConfigurationDataPush(dataSyncDTO);
            } catch (Exception e) {
                log.error("航司端推送舱位配置数据到协同中心失败，推送内容【{}】异常：", JSONUtil.toJsonStr(cabinConfigDO), e);
                throw new BusinessException(CommonErrors.CREATE_ERROR);
            }
        }

    }
}
