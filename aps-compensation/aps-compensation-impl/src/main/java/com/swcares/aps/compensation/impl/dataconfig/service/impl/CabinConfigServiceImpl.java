package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.impl.dataconfig.mapper.CabinConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.CabinConfigService;
import com.swcares.aps.compensation.model.dataconfig.dto.CabinConfigPageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.workflow.service.WorkflowModelCodeInfoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * @ClassName：CabinConfigServiceImpl
 * @Description：舱位设置的实现类
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 16:23
 * @version： v1.0
 */
@Service
public class CabinConfigServiceImpl implements CabinConfigService {

    @Autowired
    private CabinConfigMapper cabinConfigMapper;

    @Override
    public List<CabinConfigVO> loadCabinByAirline(String airlineCode) {
        QueryWrapper<CabinConfigDO> queryWrapper=new QueryWrapper<>();
        if(StringUtils.isNotEmpty(airlineCode)){
            queryWrapper.eq("AIRLINE_CODE",airlineCode);
        }
        List<CabinConfigDO> cabinConfigDO = cabinConfigMapper.selectList(queryWrapper);
        List<CabinConfigVO> result = new ArrayList<>();
        if (cabinConfigDO != null && cabinConfigDO.size() > 0) {
            for (CabinConfigDO configDO : cabinConfigDO) {
                CabinConfigVO cabinConfigVO = new CabinConfigVO();
                BeanUtils.copyProperties(configDO, cabinConfigVO);
                result.add(cabinConfigVO);
            }
        }
        return result;
    }

    @Override
    public IPage<CabinConfigVO> loadCabinByAirlinePageList(CabinConfigPageDTO dto) {
        QueryWrapper<CabinConfigDO> queryWrapper=new QueryWrapper<>();
        if(StringUtils.isNotEmpty(dto.getAirlineCode())){
            queryWrapper.eq("AIRLINE_CODE",dto.getAirlineCode());
        }
        if(StringUtils.isNotEmpty(dto.getAirlineName())){
            queryWrapper.eq("AIRLINE_NAME",dto.getAirlineName());
        }
        IPage<CabinConfigDO> cabinConfigDOPage = cabinConfigMapper.selectPage(dto.createPage(), queryWrapper);
        IPage<CabinConfigVO> result = dto.createPage();
        List<CabinConfigVO> configVOList = new ArrayList<>();
        if (cabinConfigDOPage != null && cabinConfigDOPage.getRecords().size() > 0) {
            for (CabinConfigDO configDO : cabinConfigDOPage.getRecords()) {
                CabinConfigVO cabinConfigVO = new CabinConfigVO();
                BeanUtils.copyProperties(configDO, cabinConfigVO);
                configVOList.add(cabinConfigVO);
            }
        }
        result.setSize(cabinConfigDOPage.getSize());
        result.setTotal(cabinConfigDOPage.getTotal());
        result.setPages(cabinConfigDOPage.getPages());
        result.setCurrent(cabinConfigDOPage.getCurrent());
        result.setRecords(configVOList);
        return result;
    }
    @Autowired
    WorkflowModelCodeInfoService workflowModelCodeInfoService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createCabinConfig(CabinConfigDO cabinConfigDO) {
        QueryWrapper<CabinConfigDO> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("AIRLINE_CODE", cabinConfigDO.getAirlineCode());
        Optional<CabinConfigDO> optionalCabinConfigDO = Optional.ofNullable(cabinConfigMapper.selectOne(queryWrapper));
        if (!optionalCabinConfigDO.isPresent()){
            cabinConfigMapper.insert(cabinConfigDO);
        }else{
            CabinConfigDO existingConfig = optionalCabinConfigDO.get();
            existingConfig.setEconomyCabin(cabinConfigDO.getEconomyCabin());
            existingConfig.setBusinessCabin(cabinConfigDO.getBusinessCabin());
            existingConfig.setRemark(cabinConfigDO.getRemark());
            existingConfig.setUpdatedTime(cabinConfigDO.getUpdatedTime());
            existingConfig.setUpdatedBy(ApsUserUtils.getCreatedBy());
            existingConfig.setAirlineName(cabinConfigDO.getAirlineName());
            cabinConfigMapper.updateById(existingConfig);
        }
    }
}
