package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * ClassName：irregularflight.enums <br>
 * Description：事故单状态枚举类<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月14日 14:28 <br>
 * @version v1.0 <br>
 */
public enum AccidentStatusEnum {

    DRAFT("201", "0","草稿"),
    TODO("202", "1","待处理"),
    PROCESS("203", "2","处理中"),
    CASE_CLOSED("204", "3","已结案"),
    TO_VOID("205", "4","作废");

    /**
     *
     * @param key 字典ID
     * @param value 业务状态值
     * @param label 描述
     */
    private AccidentStatusEnum(String key, String value,String label) {
        this.key = key;
        this.value = value;
        this.label=label;
    }

    private String key;

    private String value;

    private String label;

    public String getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public String getLabel(){
        return label;
    }

    public static AccidentStatusEnum build(String key) {
        return build(key, true);
    }

    public static AccidentStatusEnum build(String key, boolean throwEx) {
        AccidentStatusEnum typeEnum = null;
        for (AccidentStatusEnum element : AccidentStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + AccidentStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }

    public static AccidentStatusEnum buildValue(String value){
        return buildValue(value,true);
    }
    public static AccidentStatusEnum buildValue(String value, boolean throwEx) {
        AccidentStatusEnum typeEnum = null;
        for (AccidentStatusEnum element : AccidentStatusEnum.values()) {
            if (element.getValue().equals(value)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + value + ",请核对" + AccidentStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }

}
