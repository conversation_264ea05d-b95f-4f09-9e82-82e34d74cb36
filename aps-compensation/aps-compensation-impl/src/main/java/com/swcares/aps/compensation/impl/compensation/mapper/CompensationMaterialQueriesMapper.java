package com.swcares.aps.compensation.impl.compensation.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialDetailDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialListDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO;
import org.apache.ibatis.annotations.Param;

/**
 * @ClassName：CommpensationRepresentation
 * @Description：箱包赔偿单查询Mapper
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 16:44
 * @version： v1.0
 */
public interface CompensationMaterialQueriesMapper extends BaseMapper<CompensationMaterialInfoDO> {

    IPage<CompensationMaterialQueriesListVO> findCompensationLuggageList(@Param("dto") CompensationMaterialListDTO dto, Page<Object> page);

    CompensationMaterialDetailVO findCompensationLuggageDetailInfo( @Param("dto")  CompensationMaterialDetailDTO dto);

}
