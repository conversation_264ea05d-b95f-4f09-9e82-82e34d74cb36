package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * <AUTHOR>
 * @title: RowPermissionTableNameEnum
 * @projectName aps
 * @description: 用于从redis中获取行权限时进行传参，
 * 本类中table常量值需要和data_permission_model表中属于类型是3的menu_name能够有相同字样（保证能够唯一的模糊对比即可）
 * rowType常量值则是行权限用到的值
 * @date 2021/12/29 15:56
 */
public class RowPermissionTableName {
    //table
    public static String ACCIDENT = "事故单";

    public static String COMPENSATION = "补偿单";

    //rowType
    public static String ACCIDENTTYPES = "accidentTypes";

    public static String WORKSTATIONS = "workStations";
}
