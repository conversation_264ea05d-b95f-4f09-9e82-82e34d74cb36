package com.swcares.aps.compensation.impl.assist.constant;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistApplyTips
 * @projectName aps
 * @description:
 * @date 2022/2/16 10:53
 */
public final class AssistApplyTips {
    //航司名
//    public static String AVIATION_DEPARTMENT = "[xx航司]";
    //配置人数
    public static String ASSISTS_MAX_APPLY_PASSENGER = "ASSISTS_MAX_APPLY_PASSENGER";
    //配置管理号码
    public static String ASSISTS_MANAGER_ROLE = "ASSISTS_MANAGER_ROLE";
    //配置主类名
    public static String ASSISTS_CONFIG_TYPE = "ASSISTS_RULE";
    //业务系统编码
    public static String SYSTEM_CODE = "10000000";
    //业务类型
    public static String BUSINESS_TYPE = "apply";
    //业务id
    public static Long MASTER_ID = 1L;
    //工作人员端的提示信息
    public static String STAFF_MSG = "该手机号 %s 在半年内已超过6个不同乘机人通过协助领取使用";
    //管理层的提示信息
    public static String MANAGEMENT_MSG = "尊敬的管理员，旅客手机号 %s 半年内使用协助领取业务领取服务补偿金已超6人，请及时核实是否存在风险！";
    //号主本人的提示信息
    public static String AGENT_MSG = "尊敬的旅客您好，您的手机号码 %s 在我司半年内协助他人领取赔偿金的人数已超6人，存在手机号被盗用风险，请及时核实，谢谢！";
}
