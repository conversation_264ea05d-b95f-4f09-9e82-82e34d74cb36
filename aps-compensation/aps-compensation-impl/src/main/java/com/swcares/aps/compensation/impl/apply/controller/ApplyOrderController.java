package com.swcares.aps.compensation.impl.apply.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.apply.service.TransactionLockService;
import com.swcares.aps.compensation.model.apply.dto.ApplyJsapiSignDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderPagedDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderVO;
import com.swcares.aps.component.com.msg.WechatMsgUtil;
import com.swcares.aps.component.pay.pay.service.wx.WxPayProcess;
import com.swcares.aps.component.pay.pay.service.wx.WxPayService;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.WxMchTransferReceiveConfirmInfo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.util.List;
import java.util.Map;


/**
 * ClassName：com.swcares.compensation.controller.ApplyOrderController <br>
 * Description：航延补偿申领单信息表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/order")
@Api(tags = "航延补偿申领单信息表接口")
@Slf4j
@ApiVersion(value = "申领单接口 v1.0")
public class ApplyOrderController extends BaseController {

    @Autowired
    private ApplyOrderService applyOrderService;

    @Autowired
    private TransactionLockService transactionLockService;

    @Resource(name = "wxPayProcess")
    private WxPayProcess wxPayProcess;

    @PostMapping("/saveApply")
    @ApiOperation(value = "新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveApply(@RequestBody @Valid ApplyOrderDTO dto, HttpServletRequest req) {
        log.info("【aps-apply-impl】本人领取，当前用户的token信息为【{}】，当前请求参数【{}】",req.getHeader("Authorization") ,dto);
        transactionLockService.saveApplyNormalOrder(dto);
        String wxPayTypeApiVersion = wxPayProcess.getWxPayTypeApiVersion(TenantHolder.getTenant());
        return ok(wxPayTypeApiVersion);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "通过ID删除航延补偿申领单信息表记录")
    public BaseResult<Object> delete(@ApiParam(value = "主键id", required = true) Long id) {
        boolean deleted = applyOrderService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改航延补偿申领单信息表记录")
    public BaseResult<Object> update(@RequestBody @Valid ApplyOrderDTO dto) {
        ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
        boolean updated = applyOrderService.updateById(applyOrderDO);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/get")
    @ApiOperation(value = "通过ID查询航延补偿申领单信息表记录")
    public BaseResult<ApplyOrderVO> get(@ApiParam(value = "主键id", required = true) Long id) {
        ApplyOrderDO applyOrderDO = applyOrderService.getById(id);
        ApplyOrderVO applyOrderVO = ObjectUtils.copyBean(applyOrderDO, ApplyOrderVO.class);
        return ok(applyOrderVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询航延补偿申领单信息表记录")
    public PagedResult<List<ApplyOrderVO>> page(@RequestBody @Valid ApplyOrderPagedDTO dto) {
        IPage<ApplyOrderVO> result = applyOrderService.page(dto);
        return ok(result);
    }

    @PostMapping("/getJsapiSign")
    @ApiOperation(value = "获取JsapiSign")
    public BaseResult<Map<String, String>> getJsapiSign(@RequestBody @Valid ApplyJsapiSignDTO dto) {
        ApplyOrderDO orderDO = applyOrderService.getById(dto.getApplyId());
        WxPayService wxPayService = wxPayProcess.getWxPayService(orderDO.getTenantId(), orderDO.getAccidentType());
        return ok(WechatMsgUtil.takeJsapiSign(wxPayService.getAppId(),wxPayService.getAppSecret(),dto.getUrl()));
    }

    @GetMapping("/getReceiveConfirmInfo")
    @ApiOperation(value = "通过申领单ID获取用户确认收款package")
    public BaseResult<WxMchTransferReceiveConfirmInfo> getReceiveConfirmInfo(@ApiParam(value = "申领单ID", required = true)@RequestParam String applyId) {
        log.info("通过申领单ID获取用户确认收款package,applyId:{} " , applyId);
        return ok(applyOrderService.getReceiveConfirmInfo(applyId));
    }


}
