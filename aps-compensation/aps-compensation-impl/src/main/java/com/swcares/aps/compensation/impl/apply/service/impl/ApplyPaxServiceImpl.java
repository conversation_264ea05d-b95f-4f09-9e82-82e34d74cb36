package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyPaxMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfPaxService;
import com.swcares.aps.compensation.impl.apply.service.ApplyCaptchaService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageInputSourceTypeEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.model.apply.dto.ApplyPaxPagedDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.dto.UpdateApplyPaxStatusDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.vo.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.feign.FeignTokenContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.msg.CustomerMessageRemoteService;
import com.swcares.components.msg.dto.CustomerMessageDepositoryDTO;
import com.swcares.components.msg.enums.ReceiveModeEnum;
import com.swcares.components.oauth2.SecurityUtils;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * ClassName：com.swcares.compensation.service.impl.ApplyPaxServiceImpl <br>
 * Description：航延补偿申领旅客信息表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ApplyPaxServiceImpl extends ServiceImpl<ApplyPaxMapper, ApplyPaxDO> implements ApplyPaxService {

    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private FieldEncryptor encryptor;

    @Autowired
    private CustomerMessageRemoteService customerMessageRemoteService;
    @Autowired
    private CompensationBasicDataService compensationBasicDataService;

    @Autowired
    private ApplyBehalfPaxService applyBehalfPaxService;

    @Autowired
    private CompensationFlightInfoService compensationFlightInfoService;

    @Autowired
    private ApplyCaptchaService applyCaptchaService;
    /**
     * Title：logicRemoveById <br>
     * Description：逻辑删除 <br>
     * author：TODO <br>
     * date：2021-11-25 <br>
     * @param id <br>
     * @return <br>
     */
    @Override
    public boolean logicRemoveById(Long id) {
        ApplyPaxDO entity = new ApplyPaxDO();
        entity.setId(id);
        return updateById(entity);
    }

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-11-25 <br>
     * @param dto <br>
     * @return <br>
     */
    @Override
    public IPage<ApplyPaxDOVO> page(ApplyPaxPagedDTO dto) {
        return baseMapper.page(dto, dto.createPage());
    }

    /**
     * Title：authPax <br>
     * Description：验证本人领取信息 <br>
     * author：王磊 <br>
     * date：2021/11/25 14:43 <br>
     * @param dto <br>
     * @return <br>
     */
    @Override
    public void authPax(AuthPaxDTO dto) {
        dto.setIdNo(encryptor.encrypt(dto.getIdNo()));
        log.info("【申领单业务-验证本人领取信息】-开始-原始请求参数:{}" , JSON.toJSONString(dto));
        List<AuthPaxVO> list = this.getBaseMapper().authPaxFind(dto);
        log.info("【申领单业务-验证本人领取信息】验证用户:{},查询authPaxFind结果size:{}" , dto.getIdNo(), (list == null ? 0 : list.size()));

        if (ListUtils.isEmpty(list)) {
            log.info("【申领单业务-验证本人领取信息-验证用户失败】验证用户:{},查询authPaxFind结果为空,抛异常信息！" , dto.getIdNo());
            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ERROR);
        }
        boolean authPaxBool = false;
        for (AuthPaxVO vo : list) {
            if (!ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(vo.getSwitchOff()) && ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(vo.getReceiveStatus())
                && ApplyConstants.TAKE_EFFECT.equals(vo.getCompensationStatus())) {
                authPaxBool = true;
            }
        }
        if (!authPaxBool) {
            int countSwitch = 0;
            int countReceive = 0;
            int takeEffect = 0;
            for (AuthPaxVO vo : list) {
                if (ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(vo.getSwitchOff())) {
                    countSwitch++;
                }
                if (!ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(vo.getReceiveStatus())) {
                    countReceive++;
                }
                if(ApplyConstants.TAKE_EFFECT.equals(vo.getCompensationStatus())){
                    takeEffect++;
                }
            }
            if ((countSwitch > 0 && list.size() == countSwitch + countReceive) || takeEffect==0) {
                log.info("【申领单业务-验证本人领取信息-验证用户失败】验证用户:{},您在该航班中的赔偿信息存在异常，请联系航司相关人员！" , dto.getIdNo());
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ABNORMAL);
            }
            if (countReceive > 0 && list.size() == countReceive + countSwitch) {
                log.info("【申领单业务-验证本人领取信息-验证用户失败】验证用户:{},您在该航班中的赔偿信息已领取/ 领取中！" , dto.getIdNo());
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ALREADY_RECEIVED);
            }
        }
        log.info("【申领单业务-验证本人领取信息-验证用户成功】-结束-验证用户:{}" , dto.getIdNo());

    }

    /**
     * Title：sendSMS <br>
     * Description：发送短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum <br>
     * @return <br>
     */
    @Override
    public void sendSMS(String phoneNum) {
        //不登录生成token
        FeignTokenContext.setToken(SecurityUtils.getTokenByClientModel());
        log.info("【aps-apply-impl】发送短信:" + phoneNum);
        StringBuffer key = new StringBuffer(ApplyConstants.AUTH_PHONE_PREFIX);
        key.append(phoneNum);
        String oldCode = redisUtil.get(key.toString());
        String authCode = null;
        //如果之前验证码未过期则使用之前的
        if (StringUtils.isNotEmpty(oldCode)) {
            authCode = oldCode;
        } else {
            authCode = String.format("%04d", new Random().nextInt(999999));
            redisUtil.set(key.toString(), authCode, ApplyConstants.AUTH_PHONE_PREFIX_PAST_DUE);
        }
        StringBuffer smsContent = new StringBuffer("尊敬的旅客，您的验证码为：");
        smsContent.append(authCode);
        smsContent.append("，请在5分钟内输入，感谢您对XX航空的支持！");
        //发送短信
        List<CustomerMessageDepositoryDTO> dtos = new ArrayList<CustomerMessageDepositoryDTO>();
        CustomerMessageDepositoryDTO dto = new CustomerMessageDepositoryDTO();
        dto.setMsgContent(smsContent.toString());
        dto.setReceiveNum(phoneNum);
        dto.setReceiveMode(ReceiveModeEnum.SMS);
        dto.setSystemCode("10000000");
        dto.setBusinessType("apply");
        dto.setMasterId(1L);
        dtos.add(dto);
        customerMessageRemoteService.sendNow(dtos);
    }

    /**
     * Title：verificationSMS <br>
     * Description：验证短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum
     * @param authCode <br>
     * @return <br>
     */
    @Override
    public void verificationSMS(String phoneNum, String authCode) {
        log.info("【aps-apply-impl】短信验证,手机号{},验证码{}:", phoneNum, authCode);
        /*StringBuffer key = new StringBuffer(ApplyConstants.AUTH_PHONE_PREFIX);
        key.append(phoneNum);
        String authCodeLocal = redisUtil.get(key.toString());
        if (!authCode.equals(authCodeLocal)) {
            log.info("【aps-apply-impl】短信验证失败,手机号{},验证码{}:", phoneNum, authCode);
            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_PHONE);
        }*/
        log.info("【aps-apply-impl】短信验证测试");
    }
    @Override
    public CompensationInfoVO findCompensationOrderSmsVerify(AuthPaxDTO dto) {
        log.info("【申领单业务-获取旅客赔偿单详情】请求参数:{}" ,JSONUtil.toJsonStr(dto));
        //安全验证：避免越过短信验证步骤，直接调用信息接口
        applyCaptchaService.verifyTokenCaptchaPassByTenant();
        log.info("【申领单业务-获取旅客赔偿单详情】验证码安全验证后，开始执行findCompensationOrder方法:{}" ,JSONUtil.toJsonStr(dto));
        return this.findCompensationOrder(dto);
    }
    /**
     * Title：findCompensationOrder <br>
     * Description：通过旅客信息查询出赔偿单信息 <br>
     * author：王磊 <br>
     * date：2021/11/26 14:30 <br>
     * @param dto <br>
     * @return <br>
     */
    @Override
    public CompensationInfoVO findCompensationOrder(AuthPaxDTO dto) {
        dto.setIdNo(encryptor.encrypt(dto.getIdNo()));
        log.info("【申领单业务-获取旅客赔偿单详情】-开始-findCompensationOrder方法,请求参数:{}" ,JSONUtil.toJsonStr(dto));
        CompensationInfoVO vo = new CompensationInfoVO();

        List<CompensationOrderInfoVO> orderInfoVOS = this.getBaseMapper().findCompensationOrder(dto);
        log.info("【申领单业务-获取旅客赔偿单详情】findCompensationOrder方法,请求参数:{},sql查询结果orderInfoVOS:{}"
                ,JSONUtil.toJsonStr(dto),JSONUtil.toJsonStr(orderInfoVOS));
        //获取配置的商务舱参数
        String[] bMainClasssign = ConfigUtil.get("B_MAIN_CLASSSIGN").
                getValues().get(0).getConfigValue().split(",");
        String[] bClasssign = ConfigUtil.get("B_CLASSSIGN").
                getValues().get(0).getConfigValue().split(",");
        for (CompensationOrderInfoVO orderInfoVO : orderInfoVOS) {
            if (Arrays.asList(bMainClasssign).contains(orderInfoVO.getMainClass()) ||
                    Arrays.asList(bClasssign).contains(orderInfoVO.getSubClass())) {
                orderInfoVO.setIsBClass(ApplyConstants.BUSINESS_CLASS);
            }
        }
        vo.setOrderInfoVOS(orderInfoVOS);
        //判断补偿单信息集合，是否包含异常行李手工录入类型的数据，如果有直接取快照表
        Optional<CompensationOrderInfoVO> result = orderInfoVOS.stream()
         .filter(orderInfoVO -> BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(orderInfoVO.getInputSource()))
                .findFirst(); // 返回第一个匹配的元素
        if (result.isPresent()) {
            CompensationFlightInfoDO compensationFlightInfoDO= compensationFlightInfoService.lambdaQuery()
                    .eq(CompensationFlightInfoDO::getFlightNo, dto.getFlightNo())
                    .eq(CompensationFlightInfoDO::getFlightDate, dto.getFlightDate())
                    .eq(CompensationFlightInfoDO::getOrderId, result.get().getOrderId()).one();
            CompensationFlightInfoVO manualInputFlightVo = new CompensationFlightInfoVO();
            BeanUtils.copyProperties(compensationFlightInfoDO,manualInputFlightVo);
            manualInputFlightVo.setSegmentCHFlight(compensationFlightInfoDO.getSegmentCh());
            manualInputFlightVo.setSegmentCH(compensationFlightInfoDO.getSegmentCh());
            vo.setCompensationFlightInfoVO(manualInputFlightVo);
        }else {
            vo.setCompensationFlightInfoVO(applyBehalfPaxService.findFlightInfo(dto.getFlightNo(),dto.getFlightDate()));
        }
        log.info("【申领单业务-获取旅客赔偿单详情】-结束-findCompensationOrder方法,请求参数:{},返回对象:{}"
                ,JSONUtil.toJsonStr(dto),JSONUtil.toJsonStr(vo));
        return vo;
    }


    /**
     * Title：updateApplyPaxStatus <br>
     * Description：通过id修改当前旅客申请单的状态 <br>
     * author：王磊 <br>
     * date：2022/1/20 17:33 <br>
     * @param dto<br>
     * @return <br>
     */
    @Override
    public int updateApplyPaxStatus(UpdateApplyPaxStatusDTO dto) {
        return this.getBaseMapper().updateApplyPaxStatus(dto);
    }

}
