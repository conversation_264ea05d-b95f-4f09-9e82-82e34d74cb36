package com.swcares.aps.compensation.impl.irregularflight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationPaxInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.mapper.CompensationPaxInfoMapper <br>
 * Description： Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
public interface CompensationPaxInfoMapper extends BaseMapper<CompensationPaxInfoDO> {

    List<CompensationPaxInfoDO> findListByCompensationId(@Param("compensationId") Long compensationId);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-10-27 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<CompensationPaxInfoVO> page(@Param("dto") CompensationPaxInfoPagedDTO dto, Page<CompensationPaxInfoVO> page);

    /**
     * Title： getByOrderId<br>
     * Description： 根据赔偿单号查询旅客数据（赔偿次数、 申领次数）<br>
     * author：傅欣荣 <br>
     * date：2021/11/1 9:56 <br>
     * @param paxFrozenDTO
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO>
     */
    List<CompensationChoicePaxVO> findChoicePax(@Param("dto") CompensationPaxFrozenDTO paxFrozenDTO);

    /**
     * Title： getPaxOrderInfo<br>
     * Description： 旅客赔偿次数-详情List<br>
     * author：傅欣荣 <br>
     * date：2021/11/1 15:37 <br>
     * @param dto
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO>
     */
    List<PaxCompensationCountVO> getPaxOrderInfo(@Param("dto") PaxOrderInfoQueryDTO dto);


    /**
     * Title：freezeOrderPax <br>
     * Description：  冻结||解冻 旅客<br>
     * author：傅欣荣 <br>
     * date：2021/11/9 14:49 <br>
     * @param freezeOrderPaxDTO
     * @return boolean
     */
    Integer freezeOrderPax(@Param("dto")FreezeOrderPaxDTO freezeOrderPaxDTO);

//    List<ChoosePaxInfoVO> findSelectedPax(@Param("dto")ChoosePaxSearchDTO dto);
    /**
     * @title findSelectedPax
     * @description 根据赔偿单id查询已选择旅客
     * <AUTHOR>
     * @date 2022/2/23 16:15
     * @param orderId
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.ChoosePaxInfoVO>
     */
    List<ChoosePaxInfoVO> findSelectedPax(Long orderId);

    /**
     * @title updateReceiveStatus
     * @description @TODO
     * <AUTHOR>
     * @date 2022/5/7 11:17
     * @param compensationPaxId
     * @param key
     * @return void
     */
    Integer updateReceiveStatus(@Param("compensationPaxId") String compensationPaxId,@Param("key") String key);

    Integer updateReceiveStatusByOrderNo(@Param("dto")CompensationPaxInfoDTO dto);
}
