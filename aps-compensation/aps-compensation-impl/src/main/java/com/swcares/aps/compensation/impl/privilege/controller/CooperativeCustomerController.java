package com.swcares.aps.compensation.impl.privilege.controller;

import com.swcares.aps.compensation.impl.privilege.service.CooperativeCustomerService;
import com.swcares.aps.compensation.model.privilege.dto.CooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchCooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname CustomerController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 08:46
 * @Version 1.0
 */
@RestController
@RequestMapping("/cooperativeCustomer")
@Api(tags = "客户查询接口")
@ApiVersion(value = "可合作的客户查询接口 v1.0")
public class CooperativeCustomerController extends BaseController {
    @Autowired
    private CooperativeCustomerService cooperativeCustomerService;

    @ApiOperation(value="查询所有符合条件的客户数据")
    @PostMapping("/v1/list")
    public BaseResult<List<CooperativeCustomerDTO>> list(@RequestBody SearchCooperativeCustomerDTO searchCriteria){
        List<CooperativeCustomer> customers = cooperativeCustomerService.getCooperativeCustomerList(searchCriteria);
        List<CooperativeCustomerDTO> result = CooperativeCustomerDTO.fromEntityList(customers);
        return ok(result);
    }


}
