package com.swcares.aps.compensation.impl.baggage.accident.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.entity.CompensationExpressInfoDO;

/**
 * @ClassName：CompensationExpressService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/10 11:26
 * @version： v1.0
 */
public interface CompensationExpressService extends IService<CompensationExpressInfoDO> {
    /**
     * @title saveExpress
     * @description 新建快递信息
     * <AUTHOR>
     * @date 2022/3/10 11:23
     * @param dto
     * @return void
     */
    boolean saveExpress(CompensationExpressInfoDTO dto);

    /**
    * @title removeExpress
    * @description 根据快递id删除一条快递信息
    * <AUTHOR>
    * @date 2022/3/10 13:06
    * @param expressId 快递id
    * @return void
    */
    boolean removeExpress(String expressId);
}
