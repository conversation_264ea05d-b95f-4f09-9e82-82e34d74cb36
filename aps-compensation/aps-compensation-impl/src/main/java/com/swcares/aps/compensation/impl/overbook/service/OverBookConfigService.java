package com.swcares.aps.compensation.impl.overbook.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.overbook.entity.OverBookConfigDO;
import com.swcares.aps.compensation.model.overbook.vo.OverBookConfigVO;

import java.util.List;

/**
 * @ClassName：OverBookAccidentInfoService
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 11:27
 * @version： v1.0
 */
public interface OverBookConfigService extends IService<OverBookConfigDO> {

    List<OverBookConfigVO> getConfigInfo();

    List<OverBookConfigVO> getConfigInfo(String time,String type);

    void saveConfigInfo(List<OverBookConfigDO> doList);

}
