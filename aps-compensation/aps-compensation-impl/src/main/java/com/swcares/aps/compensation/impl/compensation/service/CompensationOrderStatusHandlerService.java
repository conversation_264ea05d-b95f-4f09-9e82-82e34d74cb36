package com.swcares.aps.compensation.impl.compensation.service;

import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;

/**
 * @ClassName：CompensationOrderStatusHandlerService
 * @Description：状态handler
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 14:30
 * @version： v1.0
 */
public interface CompensationOrderStatusHandlerService {
    void handler(AccidentCompensationDTO compensation, String targetStatus);
}
