package com.swcares.aps.compensation.impl.irregularflight.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationAuditInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.compensation.mapper.AuditInfoMapper <br>
 * Description：赔偿单-待审核记录 Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
public interface CompensationAuditInfoMapper extends BaseMapper<CompensationAuditInfoDO> {
    /**
    * @title findReviewer
    * @description 获取审核人信息
    * <AUTHOR>
    * @date 2021/11/26 9:15
    * @param deptIds cpc传递的部门id
    * @param userIds cpc传递的审核人id数组
    * @param roleIds cpc传递的审核人id数组
    * @param orgId 前端传递条件查询部门id
    * @param userInfoW 前端传递条件查询审核人信息（姓名/工号）
    * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO>
    */
    List<CompensationReviewerInfoVO> findReviewer(@Param("deptIds") String[] deptIds, @Param("userIds")String[] userIds,@Param("roleIds") String[] roleIds,@Param("orgId")Long orgId, @Param("userInfo")String userInfoW);

    /**
    * @title findOrganization
    * @description 根据userID查询部门名字
    * <AUTHOR>
    * @date 2022/1/5 14:34
    * @param userId
    * @param OrgId
    * @return java.lang.String
    */
    String findOrganization(@Param("userId")String userId,@Param("orgId")String OrgId);

    /**
    * @title findReviewerByTaskId
    * @description 根据taskId查询当前节点的待审核人
    * <AUTHOR>
    * @date 2022/1/5 14:54
    * @param taskId
    * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO>
    */
    List<CompensationReviewerInfoVO> findReviewerByTaskId(@Param("taskId") String taskId);

    /***
     * @title getUserId
     * @description 根据createdBy
     * <AUTHOR>
     * @date 2024/7/8 11:17
     * @param createdBy
     * @return java.lang.String
     */
    @Select("SELECT uc.ID  FROM UC_USER uc LEFT JOIN UC_EMPLOYEE ue ON ue.ID = uc.EMPLOYEE_ID  WHERE INSTR(  #{createdBy} , ue.JOB_NUMBER )>1 ")
    String getUserId(@Param("createdBy") String createdBy);


    @InterceptorIgnore(tenantLine = "true")
    Map<String,Object> getUserInfo(@Param("createdBy") String createdBy);
}
