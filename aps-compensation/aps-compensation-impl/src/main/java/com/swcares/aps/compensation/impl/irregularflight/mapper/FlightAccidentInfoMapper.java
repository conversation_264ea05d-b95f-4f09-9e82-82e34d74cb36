package com.swcares.aps.compensation.impl.irregularflight.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.irregularflight.mapper.DpFlightAccidentInfoMapper <br>
 * Description： Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
public interface FlightAccidentInfoMapper extends BaseMapper<FlightAccidentInfoDO> {

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：TODO <br>
     * date：2021-10-14 <br>
     * @param dto
     * @param page <br>
     * @return <br>
     */
    IPage<FlightAccidentInfoVO> page(@Param("dto") FlightAccidentInfoPagedDTO dto, Page<FlightAccidentInfoVO> page);

    /**
     * Title：findFilghtExistsAccident <br>
     * Description：通过航班号和日期来获取是否存在事故单 <br>
     * author：王磊 <br>
     * date：2021/10/27 20:43 <br>
     * @param date
     * @param flightNo <br>
     * @return <br>
     */
    List<FlightExistsAccidentVO> findFilghtExistsAccident(@Param(value = "flightNo") String flightNo,@Param(value = "date") String date,@Param(value = "id") Long id);


    /**
     * Title：getCompensationOrderById <br>
     * Description： 根据事故单id 查询所有赔偿单信息<br>
     * author：傅欣荣 <br>
     * date：2021/10/27 19:37 <br>
     * @param id
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO>
     */
    List<AccidentCompensationOrderVO> getCompensationOrderById(@Param("id") Long id);

    /**
     * Title：findById <br>
     * Description： 根据id查事故单<br>
     * author：傅欣荣 <br>
     * date：2021/10/28 14:23 <br>
     * @param id
     * @return com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO
     */
    FlightAccidentInfoDetailsVO findById(@Param("id") Long id);


    /**
     * Title： findFlightCompensationInfo<br>
     * Description： 根据id查已选航段<br>
     * author：傅欣荣 <br>
     * date：2021/11/8 17:19 <br>
     * @param id
     * @return CompensationFlightInfoVO
     */
    CompensationFlightInfoVO findFlightCompensationInfo(@Param("id") Long id);

    /**
     * Title：updAccidentStatusToDo <br>
     * Description： 更新事故单状态为【待处理】- 无赔偿单<br>
     * author：傅欣荣 <br>
     * date：2021/11/15 15:35 <br>
     * @param accidentId
     * @param accidentStatus
     * @return boolean
     */
    boolean updAccidentStatusToDo(@Param("id") Long accidentId,@Param("accidentStatus") String accidentStatus);

    /***
     * @title updAccidentStatusToDo
     * @description 更新事故单状态
     * <AUTHOR>
     * @date 2022/9/16 12:17
     * @param accidentNo
     * @param accidentStatus
     * @return boolean
     */
    @InterceptorIgnore(tenantLine = "true")
    boolean updAccidentStatusBatch(@Param("accidentNo") List<String> accidentNo,@Param("accidentStatus") String accidentStatus);


}
