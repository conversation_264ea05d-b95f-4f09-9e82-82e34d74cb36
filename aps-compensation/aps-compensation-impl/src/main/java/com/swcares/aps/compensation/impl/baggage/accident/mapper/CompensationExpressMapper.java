package com.swcares.aps.compensation.impl.baggage.accident.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.baggage.accident.entity.CompensationExpressInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageExpressVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName：CompensationExpressMapper
 * @Description： 赔偿单快递信息mapper
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/10 11:27
 * @version： v1.0
 */
@Mapper
public interface CompensationExpressMapper extends BaseMapper<CompensationExpressInfoDO> {
    List<BaggageExpressVO> selectExpressListByAccidentId(@Param("accidentId") String accidentId);
}