package com.swcares.aps.compensation.impl.dataconfig.service;

import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceBaseRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplacePayPeriodRuleDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRuleDTO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.ReplaceRuleService <br>
 * Description： 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
public interface ReplaceRuleService {

    /**
     * Title：getRule <br>
     * Description：获取当前可用代人领取基本规则 <br>
     * author：陈明东 <br>
     * date：2022-01-10 <br>
     * @return com.swcares.aps.compensation.model.replace.dto.ReplaceRuleDTO <br>
     */
    Map<String,ReplaceRuleDTO> getRule();

    /**
     * Title：saveBaseRule <br>
     * Description：创建新可用代人领取规则 <br>
     * author：陈明东 <br>
     * date：2022-01-10 <br>
     * @param replaceRuleDTO <br>
     * @return boolean  <br>
     */
    boolean saveBaseRule(ReplaceBaseRuleDTO replaceRuleDTO);


    /**
     * Title：savePayWaitPeriod <br>
     * Description：创建新可用代人支付等待期领取规则 <br>
     * author：陈明东 <br>
     * date：2022-01-10 <br>
     * @return  boolean  <br>
     */
    boolean savePayWaitPeriod(ReplacePayPeriodRuleDTO dto);
}
