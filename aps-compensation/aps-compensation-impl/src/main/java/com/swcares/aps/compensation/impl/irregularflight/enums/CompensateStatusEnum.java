package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * ClassName：irregularflight.enums <br>
 * Description：赔偿单状态枚举类<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月14日 14:28 <br>
 * @version v1.0 <br>
 */
public enum CompensateStatusEnum {
//0草稿 1驳回 2审核中 3审核通过 4生效 5关闭 6逾期 7审核不通过

    DRAFT("0", "草稿"),
    REJECT("1", "驳回"),
    AUDIT_PROCESS("2", "审核中"),
    AUDIT_PASS("3", "审核通过"),
    TAKE_EFFECT("4", "生效"),
    CLOSE("5", "生效->关闭"),
    OVERDUE("6", "生效->逾期"),
    AUDIT_FAILED("7", "审核不通过"),

    AUDIT_PASS_CLOSE("8","审核通过->关闭"),

    AUDIT_PASS_OVERDUE("9","审核通过->逾期");


    private CompensateStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static CompensateStatusEnum build(String key) {
        return build(key, true);
    }

    public static CompensateStatusEnum build(String key, boolean throwEx) {
        CompensateStatusEnum typeEnum = null;
        for (CompensateStatusEnum element : CompensateStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + CompensateStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }

}
