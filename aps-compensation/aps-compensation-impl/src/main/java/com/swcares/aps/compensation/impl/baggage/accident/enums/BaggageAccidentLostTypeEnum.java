package com.swcares.aps.compensation.impl.baggage.accident.enums;

/**
 * @ClassName：BaggageAccidentLostTypeEnum
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/22 18:52
 * @version： v1.0
 */
public enum BaggageAccidentLostTypeEnum {
    ROUTINE("1", "常规"),
    CUSTOM("2", "自定义");

    private BaggageAccidentLostTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static BaggageAccidentLostTypeEnum build(String key) {
        return build(key, true);
    }

    public static BaggageAccidentLostTypeEnum build(String key, boolean throwEx) {
        BaggageAccidentLostTypeEnum typeEnum = null;
        for (BaggageAccidentLostTypeEnum element : BaggageAccidentLostTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + BaggageAccidentLostTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
