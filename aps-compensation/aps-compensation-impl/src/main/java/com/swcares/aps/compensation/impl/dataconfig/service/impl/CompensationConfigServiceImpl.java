package com.swcares.aps.compensation.impl.dataconfig.service.impl;

import com.swcares.aps.compensation.impl.dataconfig.mapper.CompensationConfigMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.CompensationConfigService;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName： <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/8 <br>
 * @version v1.0 <br>
 */
@Service
public class CompensationConfigServiceImpl implements CompensationConfigService {
	@Autowired
	private CompensationConfigMapper compensationConfigMapper;


	/**
	 * @title getCompensationConfig
	 * @description 异常行李配置类
	 * <AUTHOR>
	 * @date 2022/3/8 15:24

	 * @return Map<String,List<DamageTypeVO>>
	 */
	@Override
	public Map<String, List<CompensationConfigVO>> getCompensationConfigByType(String type) {

		List<CompensationConfigVO> compensationConfigVOList = compensationConfigMapper.getCompensationConfigByType(type);
		Map<String,List<CompensationConfigVO>> map = new HashMap<>();
		compensationConfigVOList.forEach(e->{
             if(map.containsKey(e.getSubType())){
             	map.get(e.getSubType()).add(e);
             }
             else{
	             map.put(e.getSubType(),new ArrayList<CompensationConfigVO>());
	             map.get(e.getSubType()).add(e);
             }
		});
		return map;
	}

	@Override
	public List<CompensationConfigVO> getCompensationConfigByTypes(List<String> types) {
		List<CompensationConfigVO> compensationConfigVOS=new ArrayList<>();
		for(String type:types){
			List<CompensationConfigVO> vos = compensationConfigMapper.getCompensationConfigByType(type);
			if(CollectionUtils.isNotEmpty(vos)){
				compensationConfigVOS.addAll(vos);
			}
		}
		return compensationConfigVOS;
	}
}
