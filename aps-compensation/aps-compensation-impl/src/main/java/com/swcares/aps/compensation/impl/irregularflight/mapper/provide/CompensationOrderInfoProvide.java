package com.swcares.aps.compensation.impl.irregularflight.mapper.provide;

import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationOrderInfoDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.jdbc.SQL;

/**
 * <AUTHOR>
 * @title: CompensationOrderInfoProvide
 * @projectName aps
 * @description:
 * @date 2021/11/9 9:18
 */
public class CompensationOrderInfoProvide {
    public String update(@Param("dto") CompensationOrderInfoDTO dto) {
        return new SQL() {
            {
                UPDATE("compensation_order_info");
                if (dto.getStatus() != null && dto.getStatus().equals(CompensateStatusEnum.TAKE_EFFECT.getKey())) {
                    SET("status = '" + dto.getStatus() + "',iss_user = '" + dto.getIssUser() + "',expiry_date=(select sysdate from dual)" + ",RELEASE = '" + dto.getRelease() + "',RELEASE_TIME =(select sysdate from dual)" );
                    WHERE(" status = '" + CompensateStatusEnum.AUDIT_PASS.getKey() + "' and id = " + dto.getId());
                }
                if (dto.getStatus() !=null && dto.getStatus().equals(CompensateStatusEnum.CLOSE.getKey())){
                    SET("status = '"+dto.getStatus()+"',close_user = '"+dto.getCloseUser()+"',close_time=(select sysdate from dual)");
                    WHERE(" status = '" +CompensateStatusEnum.TAKE_EFFECT.getKey()+"' and id = "+dto.getId());
                }
                if (dto.getStatus() !=null && dto.getStatus().equals(CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey())){
                    SET("status = '"+dto.getStatus()+"',close_user = '"+dto.getCloseUser()+"',close_time=(select sysdate from dual)");
                    WHERE(" status = '"+CompensateStatusEnum.AUDIT_PASS.getKey()+"' and id = "+dto.getId());
                }
            }
        }.toString();
    }
}
