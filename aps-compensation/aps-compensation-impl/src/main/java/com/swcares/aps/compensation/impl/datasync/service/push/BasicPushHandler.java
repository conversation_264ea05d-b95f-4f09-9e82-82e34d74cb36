package com.swcares.aps.compensation.impl.datasync.service.push;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.privilege.config.CoordinateConfig;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadRequestDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadResponseDTO;
import com.swcares.aps.cpe.coordinate.util.CoordinateApiUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.UUID;

/**
 * @ClassName：BasicPushHandler
 * @Description：数据推送基类
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/10/8 11:00
 * @version： v1.0
 */
@Slf4j
public class BasicPushHandler {

    public static final String AIRLINE = "Airline";
    public static final String AIRPORT = "Airport";

    /**
     * @title BasicPushHandler.java
     * @description 推送给协同中心的具体实现
     * <AUTHOR>
     * @date 2024/10/8 11:09
     * @param coordinateConfig  协同中心配置类，主要是基础配置信息，密钥之类的
     * @param obj 数据传输类，具体要推送的东西
     * @param dataCategoryEnum 推送数据类型，业务配置数据、业务数据、授权数据、基础数据等，参见DataCategoryEnum.java
     * @return boolean
     */
    public boolean doDataPush(CoordinateConfig coordinateConfig, Object obj, String dataCategoryEnum) throws Exception {
        //协同中心分配的appClientId，实际应用中应该从配置文件或者数据库配置中获取
        String appClientId = coordinateConfig.getAppClientId();// "AIRPORT_SAAS";
        //协同中心分配的密钥，实际应用中应该从配置文件或者数据库配置中获取
        String appPublicKey = coordinateConfig.getAppSecretKey();//"MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmbdm/tgv5mGMtBML2Yy8iCtcmgIUCLO3JKGbjrgaP2Pg59dEH+VqrW5USpF8rBvzo6JNPyJpKUJyBGk5YbNmZd5Q/FjQy6FvDJUG6R6I0O4c51T/QrvGdmYpEzk8TsAOp0kmtfCo89K6mg+8nLLOncUwwJGM7y6xashRLDE/sBzASwDzvCrjrDOX3dI7wTv7SeHdhpkopMIPz8pC0FUCIOAxHOV9gH093PDtCULA+/Da1NN0nHBF6Vy2n67+IUxoULC++KYossRZaYC+CnNIMoEy/zfp0ufWGSyhFin6iC3CmzfIimXCq6ycRzpUOsa7cZ7uywAmfgofUMqeqjFn7QIDAQAB";
        //协同中心分配的密钥的版本，实际应用中应该从配置文件或者数据库配置中获取
        String appSecretKeyVersion = coordinateConfig.getAppSecretKeyVersion();// "1.0";
        //协同中心提供的接口的地址，实际应用中应该从配置文件或者数据库配置中获取
        String configureDataUploadUrl = coordinateConfig.getCoordinateCenterUrl();// "http://**************:9803/api/coordinate-center-thirdapi-bff/coordinate/send";
        String requestID = UUID.randomUUID().toString(); //每次发送需要使用不同的requestID， requestID在每一个应用端中应该保证唯一性

        log.info("【航司端-给机场端推送业务数据】 推送数据类型【{}】， 推送业务数据内容：【{}】", dataCategoryEnum, JSON.toJSONString(obj));

        CoordinateUploadRequestDTO requestDTO = CoordinateApiUtil.getCoordinateUploadRequestDTO(requestID, dataCategoryEnum,
                appClientId,appSecretKeyVersion,appPublicKey, JSONUtil.toJsonStr(obj));
        CoordinateUploadResponseDTO responseDTO = CoordinateApiUtil.uploadCoordinateData(configureDataUploadUrl, requestDTO);

        log.info("【航司端-给机场端推送业务数据】 推送数据类型【{}】，推送协同中心返回结果:【{}】, 推送业务数据：【{}】", dataCategoryEnum, JSON.toJSONString(responseDTO),JSON.toJSONString(obj));
        String decryptedData = CoordinateApiUtil.getDecryptedData(responseDTO, appPublicKey);
        log.info("【航司端-给机场端推送业务数据】 推送数据类型【{}】，推送协同中心返回结果:【{}】， 解密data：【{}】", dataCategoryEnum, JSON.toJSONString(responseDTO), decryptedData);
        return  "SUCCESS".equals(decryptedData);
    }
}
