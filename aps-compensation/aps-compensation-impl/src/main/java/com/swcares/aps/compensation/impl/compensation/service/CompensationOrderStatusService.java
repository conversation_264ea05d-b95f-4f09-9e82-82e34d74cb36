package com.swcares.aps.compensation.impl.compensation.service;

import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;

/**
 * @ClassName：CompensationChangeStatusService
 * @Description：补偿单状态更改服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 12:53
 * @version： v1.0
 */
public interface CompensationOrderStatusService {
    boolean changeStatus(AccidentCompensationDTO compensation,String targetStatus);
}
