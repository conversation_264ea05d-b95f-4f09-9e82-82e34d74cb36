package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfOrderService;
import com.swcares.aps.compensation.impl.assist.constant.AssistApplyErrors;
import com.swcares.aps.compensation.impl.assist.constant.AssistApplyTips;
import com.swcares.aps.compensation.impl.assist.mapper.AssistPaxListMapper;
import com.swcares.aps.compensation.impl.assist.service.*;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.assist.entity.AssistPayRecordDO;
import com.swcares.aps.compensation.model.assist.vo.AssistPayRecordVO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.component.com.decoder.DecoderHandlerException;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.components.encrypt.FieldEncryptor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @title: AssistApplyOrderServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/1/20 16:33
 */
@Service
@Slf4j
public class AssistApplyOrderServiceImpl implements AssistApplyOrderService {
    @Autowired
    private ApplyBehalfOrderService applyBehalfOrderService;

    @Autowired
    private AssistPaxListMapper assistPaxListMapper;

    @Autowired
    private AssistAgentTipsService agentTipsService;

    @Autowired
    private AssistStaffTipsService staffTipsService;

    @Autowired
    private AssistManagementTipsService managementTipsService;

    @Autowired
    private ReplaceConfigService configService;

    @Autowired
    private AssistPayRecordService assistPayRecordService;
    @Autowired
    private FieldEncryptor fieldEncryptor;

    @Override
    @Transactional
    public BaseResult<Object> saveAssistApply(ApplyBehalfOrderDTO dto) {
        try{
            dto.setReceieveTime(LocalDateTime.now());
            applyBehalfOrderService.saveAssistApply(dto);
            // 保存协助领取记录
            saveRecordDO(dto.getTelephone(),dto.getOrderInfoVOS().get(0).getPaxId());
        }catch (DecoderHandlerException e){
            throw new BusinessException(AssistApplyErrors.APPLY_SAVE_ERROR);
        }
        return BaseResult.ok();
    }

    @Override
    public BaseResult<Object> findPaxAmount(String phone,String idNo,String paxName) {
        int exist = assistPayRecordService.existPhone(phone);
        //判断该手机号是否第一次使用
        if(exist==0){
            //首次使用直接记录无需提示
            return BaseResult.ok();
        }
        //判断是否超过半年周期
        List<AssistPayRecordVO> record = assistPayRecordService.findRecord(phone);
        if(record != null && record.size()!=0){
            //超过半年周期，需重置第一次使用时间并且重置领取人数
            assistPayRecordService.updateExpire(phone);
            return BaseResult.ok();
        }
        //获取配置的协助限制人数
        DataConfigDO byTypeAndSubType = configService.findByTypeAndSubTypeAndAirCode(AssistApplyTips.ASSISTS_CONFIG_TYPE, AssistApplyTips.ASSISTS_MAX_APPLY_PASSENGER, UserContext.getCurrentUser().getTenantCode());
        //判断该手机号半年内代领的人数是否超过6人
        Set<String> result = assistPayRecordService.receivedPaxAmount(phone);
        String paxInfo = paxName + fieldEncryptor.encrypt(idNo);
        //刚好达到设置的临界值时，判断当前被协助旅客是否已经存在于之前被协助领取的人中（存在是不需要提醒的）
        if(ObjectUtils.isNotEmpty(result) && result.size()==Integer.parseInt(byTypeAndSubType.getValue())){
            if(result.contains(paxInfo)){
                return BaseResult.ok();
            }
        }
        //若是超过临界值后，不管当前被协助旅客是否存在于之前被协助领取的人中都应该提醒
        if(result != null && result.size()>(Integer.parseInt(byTypeAndSubType.getValue())-1)){
            log.info("【aps-compensation-impl-assist】协助领取限制人数：[{}]",byTypeAndSubType.getValue());
            //通知号主
            agentTipsService.sendSMSToAgent(phone);
            //通知管理层
            managementTipsService.sendSMSToManagement(phone);
            //通知工作人员端
            return staffTipsService.sendSMSToStaff(phone);
        }
        return BaseResult.ok();
    }
    /**
     * @title saveRecordDO
     * @description 用于储存协助领取记录
     * @param phone
     * @param paxId
     * <AUTHOR>
     * @return void
     * @date 2022/2/24 14:30
     */
    public void saveRecordDO(String phone,String paxId){
        AssistPayRecordDO recordDO = new AssistPayRecordDO();
        recordDO.setExpire("N");
        recordDO.setPaxId(paxId);
        recordDO.setPhone(phone);
        assistPayRecordService.save(recordDO);
    }
}
