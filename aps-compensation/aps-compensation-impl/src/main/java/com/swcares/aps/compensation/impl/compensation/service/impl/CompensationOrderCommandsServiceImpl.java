package com.swcares.aps.compensation.impl.compensation.service.impl;

import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageAccidentTypeEnum;
import com.alibaba.nacos.common.utils.StringUtils;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderCommandsService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.*;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;

/**
 * @ClassName：CompensationOrderCommandsServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:20
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationOrderCommandsServiceImpl implements CompensationOrderCommandsService {

    private Map<String, Function<CompensationAddCommand,String>> addActionMappings = new HashMap<>();
    private Map<String, Function<CompensationEditCommand,String>> editActionMappings = new HashMap<>();
    private Map<String, Function<CompensationEditCommand,CompensationAuditOperationVO>> reSubmitActionMappings = new HashMap<>();

    /**
     * 异常航班事故单
     */
    private final String ACCIDENT_TYPE_FLIGHT="1";
    /**
     * 异常行李事故单
     */
    private final String ACCIDENT_TYPE_BAGGAGE="2";
    /**
     * 现金补偿单
     */
    private final String COMPENSATION_TYPE_CASH="CASH";
    /**
     * 实物补偿单
     */
    private final String COMPENSATION_TYPE_MATERIAL="MATERIAL";

    @Resource(name="baggageCashCompensationService")
    private CompensationAddAndEditService baggageCashCompensationService;


    @Resource(name="baggageMaterialCompensationService")
    private CompensationAddAndEditService baggageMaterialCompensationService;

    @Resource(name="flightCashCompensationService")
    private CompensationAddAndEditService flightCashCompensationService;


    @Resource(name="flightMaterialCompensationService")
    private CompensationAddAndEditService flightMaterialCompensationService;

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    private BaggageAccidentService baggageAccidentService;
    @Autowired
    private Redisson redisson;

    private static final String BAGGAGE_CASH_COMPENSATION_SAVE_LOCK_PREFIX = "baggage_cash_compensation_save_lock_prefix";
    private static final String BAGGAGE_MATERIAL_COMPENSATION_SAVE_LOCK_PREFIX = "baggage_material_compensation_save_lock_prefix";

    @PostConstruct
    private void init(){
        //异常行李现金补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::reSubmit);

        //异常行李实物补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::reSubmit);

        //异常航班现金补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::reSubmit);

        //异常航班实物补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::reSubmit);
    }

    private String addApply(String accidentType,String compensationType, CompensationAddCommand command){
        String key=accidentType+":"+compensationType;
        return addActionMappings.get(key).apply(command);
    }

    private String editApply(String accidentType,String compensationType, CompensationEditCommand command){
        String key=accidentType+":"+compensationType;
        return editActionMappings.get(key).apply(command);
    }

    private CompensationAuditOperationVO reSubmitApply(String accidentType,String compensationType, CompensationEditCommand command){
        String key=accidentType+":"+compensationType;
        return reSubmitActionMappings.get(key).apply(command);
    }


    @Override
    public String addMaterialCompensation(CompensationMaterialAddCommandDTO request) {
        //加锁
        AddAccidentCompensationDTO accident = request.getAccident();
        String paxIds = StringUtils.join(request.getPaxIds(), ",");
        //锁对象：事故单id、旅客id
        String lockKey = BAGGAGE_MATERIAL_COMPENSATION_SAVE_LOCK_PREFIX+accident.getAccidentId()+paxIds;
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            //即不等待，锁自动过期5秒，赔偿单创建大概耗时: 91 ms
            locked = r.tryLock(0, 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【异常行李-创建实物补偿单】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【异常行李-创建实物补偿单】加锁对象：事故单id【{}】，旅客ids【{}】，获取锁失败，说明该单子已经被创建了~"
                    ,accident.getAccidentId(),paxIds);
            throw new BusinessException(BaggageAccidentException.COMPENSATION_DUPLICATE_SUBMISSION);
        }
        log.info("【异常行李-创建实物补偿单】加锁对象：事故单id【{}】，旅客ids【{}】，获取锁成功，执行业务逻辑~"
                ,accident.getAccidentId(),paxIds);
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        //批量少收类型,抛错
        verifyBatchLessIncomeCreate(request.getAccident().getAccidentId());
        return this.addApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public String editMaterialCompensation(CompensationMaterialEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.editApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public CompensationAuditOperationVO reSubmitMaterialCompensation(CompensationMaterialEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.reSubmitApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public String addCashCompensation(CompensationCashAddCommandDTO request) {
        //加锁
        AddAccidentCompensationDTO accident = request.getAccident();
        String paxIds = StringUtils.join(request.getPaxIds(), ",");
        //锁对象：事故单id、旅客id
        String lockKey = BAGGAGE_CASH_COMPENSATION_SAVE_LOCK_PREFIX+accident.getAccidentId()+paxIds;
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            //即不等待，锁自动过期5秒，赔偿单创建大概耗时: 91 ms
            locked = r.tryLock(0, 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【异常行李-现金赔偿单创建】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【异常行李-现金赔偿单创建】加锁对象：事故单id【{}】，旅客ids【{}】，获取锁失败，说明该单子已经被创建了~"
                    ,accident.getAccidentId(),paxIds);
            throw new BusinessException(BaggageAccidentException.COMPENSATION_DUPLICATE_SUBMISSION);
        }
        log.info("【异常行李-现金赔偿单创建】加锁对象：事故单id【{}】，旅客ids【{}】，获取锁成功，执行业务逻辑~"
                ,accident.getAccidentId(),paxIds);
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        //批量少收类型,抛错
        verifyBatchLessIncomeCreate(request.getAccident().getAccidentId());
        return this.addApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);

    }

    @Override
    public String editCashCompensation(CompensationCashEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.editApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);
    }

    @Override
    public CompensationAuditOperationVO reSubmitCashCompensation(CompensationCashEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.reSubmitApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);
    }


    /***
     * @title verifyAirlineBusinessPrivileBaggage
     * @description 异常行李补偿单创建-校验是否有航司业务授权
     * <AUTHOR>
     * @date 2024/8/6 15:22
     * @param accidentId
     * @return void
     */
    private void verifyAirlineBusinessPrivileBaggage(String accidentId){
        BaggageAccidentInfoDO accident = baggageAccidentService.getById(accidentId);
        if(accident==null){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accidentId+"事故单不存在");
        }
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,accident.getPaxFlightNo().substring(0, 2));

    }

    /***
     * 判断批量少收类型，创建补偿单，抛异常
     * @param accidentId
     */
    public void verifyBatchLessIncomeCreate(String accidentId){
        BaggageAccidentInfoDO accident = baggageAccidentService.getById(accidentId);
        if(accident==null){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accidentId+"事故单不存在");
        }
        //批量少收类型,抛错
        if(BaggageAccidentTypeEnum.BACTH_LESS_INCOME.getValue().equals(accident.getType())){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_UNSUPPORTED_OPERATION);
        }
    }
}