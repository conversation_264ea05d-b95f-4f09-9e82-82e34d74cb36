package com.swcares.aps.compensation.impl.compensation.service.impl;

import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderCommandsService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationAddCommand;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationEditCommand;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * @ClassName：CompensationOrderCommandsServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 16:20
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationOrderCommandsServiceImpl implements CompensationOrderCommandsService {

    private Map<String, Function<CompensationAddCommand,String>> addActionMappings = new HashMap<>();
    private Map<String, Function<CompensationEditCommand,String>> editActionMappings = new HashMap<>();
    private Map<String, Function<CompensationEditCommand,CompensationAuditOperationVO>> reSubmitActionMappings = new HashMap<>();

    /**
     * 异常航班事故单
     */
    private final String ACCIDENT_TYPE_FLIGHT="1";
    /**
     * 异常行李事故单
     */
    private final String ACCIDENT_TYPE_BAGGAGE="2";
    /**
     * 现金补偿单
     */
    private final String COMPENSATION_TYPE_CASH="CASH";
    /**
     * 实物补偿单
     */
    private final String COMPENSATION_TYPE_MATERIAL="MATERIAL";

    @Resource(name="baggageCashCompensationService")
    private CompensationAddAndEditService baggageCashCompensationService;


    @Resource(name="baggageMaterialCompensationService")
    private CompensationAddAndEditService baggageMaterialCompensationService;

    @Resource(name="flightCashCompensationService")
    private CompensationAddAndEditService flightCashCompensationService;


    @Resource(name="flightMaterialCompensationService")
    private CompensationAddAndEditService flightMaterialCompensationService;

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    private BaggageAccidentService baggageAccidentService;
    @PostConstruct
    private void init(){
        //异常行李现金补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_CASH,baggageCashCompensationService::reSubmit);

        //异常行李实物补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_BAGGAGE+":"+COMPENSATION_TYPE_MATERIAL,baggageMaterialCompensationService::reSubmit);

        //异常航班现金补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_CASH,flightCashCompensationService::reSubmit);

        //异常航班实物补偿单创建编辑功能
        addActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::add);
        editActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::edit);
        reSubmitActionMappings.put(ACCIDENT_TYPE_FLIGHT+":"+COMPENSATION_TYPE_MATERIAL,flightMaterialCompensationService::reSubmit);
    }

    private String addApply(String accidentType,String compensationType, CompensationAddCommand command){
        String key=accidentType+":"+compensationType;
        return addActionMappings.get(key).apply(command);
    }

    private String editApply(String accidentType,String compensationType, CompensationEditCommand command){
        String key=accidentType+":"+compensationType;
        return editActionMappings.get(key).apply(command);
    }

    private CompensationAuditOperationVO reSubmitApply(String accidentType,String compensationType, CompensationEditCommand command){
        String key=accidentType+":"+compensationType;
        return reSubmitActionMappings.get(key).apply(command);
    }


    @Override
    public String addMaterialCompensation(CompensationMaterialAddCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.addApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public String editMaterialCompensation(CompensationMaterialEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.editApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public CompensationAuditOperationVO reSubmitMaterialCompensation(CompensationMaterialEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.reSubmitApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_MATERIAL,request);
    }

    @Override
    public String addCashCompensation(CompensationCashAddCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.addApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);

    }

    @Override
    public String editCashCompensation(CompensationCashEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.editApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);
    }

    @Override
    public CompensationAuditOperationVO reSubmitCashCompensation(CompensationCashEditCommandDTO request) {
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        verifyAirlineBusinessPrivileBaggage(request.getAccident().getAccidentId());
        return this.reSubmitApply(request.getAccident().getAccidentType(),COMPENSATION_TYPE_CASH,request);
    }


    /***
     * @title verifyAirlineBusinessPrivileBaggage
     * @description 异常行李补偿单创建-校验是否有航司业务授权
     * <AUTHOR>
     * @date 2024/8/6 15:22
     * @param accidentId
     * @return void
     */
    private void verifyAirlineBusinessPrivileBaggage(String accidentId){
        BaggageAccidentInfoDO accident = baggageAccidentService.getById(accidentId);
        if(accident==null){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accidentId+"事故单不存在");
        }
        //校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,accident.getPaxFlightNo().substring(0, 2));

    }
}