package com.swcares.aps.compensation.impl.compensation.service.impl;

import com.beust.jcommander.internal.Lists;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusHandlerService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：CompensationChangeStatusServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 12:54
 * @version： v1.0
 */
@Service
@Slf4j
@Transactional
public class CompensationOrderStatusServiceImpl implements CompensationOrderStatusService {

    private Map<String, List<String>> allowTargetStatusMp=new HashMap<>();

    @Autowired
    private CompensationOrderInfoMapper compensationOrderInfoMapper;

    @Autowired
    private BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    private List<CompensationOrderStatusHandlerService> compensationOrderStatusHandlerServices;

    @PostConstruct
    private void init(){
        //草稿->审核中
        allowTargetStatusMp.put(CompensateStatusEnum.DRAFT.getKey(), Lists.newArrayList(CompensateStatusEnum.AUDIT_PROCESS.getKey()));

        //审核中->审核不通过;
        //审核中->驳回
        //审核中->逾期
        allowTargetStatusMp.put(CompensateStatusEnum.AUDIT_PROCESS.getKey(), Lists.newArrayList(CompensateStatusEnum.AUDIT_FAILED.getKey(),
                CompensateStatusEnum.REJECT.getKey(),
                CompensateStatusEnum.AUDIT_PASS.getKey(),
                CompensateStatusEnum.OVERDUE.getKey()));

        //审核通过->生效
        //审核通过->关闭
        //审核通过->逾期
        allowTargetStatusMp.put(CompensateStatusEnum.AUDIT_PASS.getKey(), Lists.newArrayList(CompensateStatusEnum.TAKE_EFFECT.getKey(),
                CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey(),
                CompensateStatusEnum.AUDIT_PASS_OVERDUE.getKey()));

        //驳回->逾期
        //驳回->审核中
        allowTargetStatusMp.put(CompensateStatusEnum.REJECT.getKey(),Lists.newArrayList(CompensateStatusEnum.OVERDUE.getKey(),
                CompensateStatusEnum.AUDIT_PROCESS.getKey()));

        //生效->逾期
        //生效->关闭
        allowTargetStatusMp.put(CompensateStatusEnum.TAKE_EFFECT.getKey(),Lists.newArrayList(CompensateStatusEnum.OVERDUE.getKey(),
                CompensateStatusEnum.CLOSE.getKey()));

    }

    @Override
    public boolean changeStatus(AccidentCompensationDTO compensation, String targetStatus) {
        //目标状态校验
        verifyTargetStatus(compensation, targetStatus);
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectById(compensation.getId());
        if(CompensateStatusEnum.CLOSE.getKey().equals(targetStatus) || CompensateStatusEnum.AUDIT_PASS_CLOSE.getKey().equals(targetStatus)){
            // close user and close time
            compensationOrderInfoDO.setCloseTime(LocalDateTime.now());
            compensationOrderInfoDO.setCloseUser(ApsUserUtils.getCreatedBy());
            //compensationOrderInfoMapper.updateById(compensationOrderInfoDO);
        }
        if (CompensateStatusEnum.TAKE_EFFECT.getKey().equals(targetStatus)) {
            // release user and release time
            compensationOrderInfoDO.setReleaseTime(new Date());
            compensationOrderInfoDO.setRelease(ApsUserUtils.getCreatedBy());
            //ompensationOrderInfoMapper.updateById(compensationOrderInfoDO);
        }
        //修改补偿单状态
        //compensationOrderInfoMapper.updCompensationOrderStatus(compensation.getId(),targetStatus);
        compensationOrderInfoDO.setStatus(targetStatus);
        compensationOrderInfoDO.setUpdatedTime(LocalDateTime.now());
        compensationOrderInfoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        compensationOrderInfoMapper.updateById(compensationOrderInfoDO);

        if(!CompensateStatusEnum.REJECT.getKey().equals(targetStatus)){
            //推送业务数据到协同中心
            businessDataPushHandler.dataStore(Long.valueOf(compensationOrderInfoDO.getId()),  AccidentTypeBusinessCodeEnum.build(compensationOrderInfoDO.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);
        }

        //修改事故单状态
        //如果有实物还需要进行实物扣减CompensationInfoApi
        for(CompensationOrderStatusHandlerService statusHandlerService:compensationOrderStatusHandlerServices){
            statusHandlerService.handler(compensation,targetStatus);
        }
        return true;
    }

    /**
     * @title verifyTargetStatus
     * @description 校验状态
     * <AUTHOR>
     * @date 2022/3/14 15:07
     * @param compensation
     * @param targetStatus
     * @return
     */
    private void verifyTargetStatus(AccidentCompensationDTO compensation, String targetStatus) {
        String sourceStatus = compensation.getStatus();
        List<String> targetStatusList = allowTargetStatusMp.get(sourceStatus);
        if(CollectionUtils.isEmpty(targetStatusList) || !targetStatusList.contains(targetStatus)){
            throw new BusinessException(CompensationException.COMPENSATION_CHANGE_ERROR,"当前状态不能修改为"+targetStatus+"状态");
        }
    }
}