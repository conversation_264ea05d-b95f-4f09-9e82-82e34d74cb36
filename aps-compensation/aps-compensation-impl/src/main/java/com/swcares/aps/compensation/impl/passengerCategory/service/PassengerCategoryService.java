package com.swcares.aps.compensation.impl.passengerCategory.service;



import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryStateDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;

import javax.validation.ValidationException;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.passengerCategory.service.impl.PassengerCategoryService <br>;
 * Description：旅客类别服务 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 16:32 <br>;
 * @version v1.0 <br>;
 */
public interface PassengerCategoryService extends IService<PassengerCategoryConfigureDepository> {

    /**
     * Title：sentPage <br>;
     * Description：分页查询旅客类别配置数据信息 <br>;
     * @param: PassengerCategorySentPageDto <br>;
     * @return: IPage<PassengerCategorySentPageVo> <br>;
     * <AUTHOR> <br>;
     * date 2022/5/24 16:48 <br>;
     * @throws null <br>;
     */
    IPage<PassengerCategorySentPageVo> sentPage(PassengerCategorySentPageDto dto);

    /**
     * Title：savePassengerCategoryConfigure <br>;
     * Description：保存旅客类别配置文件对象 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/5/24 17:09 <br>;
     * @throws  <br>;
     */
    void savePassengerCategoryConfigure(PassengerCategoryChangeDto dto);

    /**
     * Title：updatePassengerCategoryConfigure <br>;
     * Description：更新旅客类别配置文件对象 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/5/25 9:10 <br>;
     * @throws  <br>;
     */
    void updatePassengerCategoryConfigure(PassengerCategoryChangeDto dto);

    /**
     * Title：updatePassengerCategoryConfigureState <br>;
     * Description：批量更新旅客类别配置文件对象状态 <br>;
     *
     * @throws <br>;
     * @param: <br>;
     * @return: <br>;
     * <AUTHOR> <br>;
     * date 2022/5/31 17:17 <br>;
     */
    ValidationException updatePassengerCategoryConfigureState(PassengerCategoryStateDto dto);

    /**
     * Title：passengerCategory <br>;
     * Description：获取树状旅客类型 <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/6/15 15:58 <br>;
     * @throws  <br>;
     */
    List<PassengerCategoryVo> passengerCategory(PassengerCategoryChangeDto dto);

    /**
     * @title listPassengerCategoryConfigure
     * @description 根据CODE获取旅客类别信息
     * <AUTHOR>
     * @date 2022/8/22 17:16
     * @param codes
     * @return
     */
    List<PassengerCategoryConfigureDepository> listPassengerCategoryConfigure(List<String> codes);
}
