package com.swcares.aps.compensation.impl.baggage.accident.controller;

import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusService;
import com.swcares.aps.compensation.impl.baggage.accident.service.CompensationExpressService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportDetailVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxMaintainSearchDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * ClassName：BaggageAccidentController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 *         Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/baggage/accident")
@Api(tags = "异常行李事故单相关接口")
@ApiVersion(value = "异常行李事故单 v1.0")
public class BaggageAccidentController extends BaseController {
    @Autowired
    private BaggageAccidentService baggageAccidentService;

    @Autowired
    private BaggageAccidentStatusService statusService;

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;

    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "单一投诉事故单详情")
    public BaseResult<BaggageAccidentInfoDO> getBaggageAccidentInfo(@PathVariable(value = "id") Long id) {
        return ok(baggageAccidentService.getById(id));
    }

    @PostMapping("/findBaggageAccidentList")
    @ApiOperation(value = "异常行李事故单信息列表查询")
    public PagedResult<List<FindBaggageVO>> findBaggageAccidentList(@RequestBody FindBaggageDTO dto) {
        return ok(baggageAccidentService.findBaggageAccidentList(dto));
    }

    @PostMapping("/baggageAccidentDetailInfo")
    @ApiOperation(value = "异常行李事故单详情信息")
    public BaseResult<BaggageDetailFinalVO> baggageAccidentDetailInfo(@RequestBody @Valid FindBaggageDetailDTO dto) {
        return ok(baggageAccidentService.findBaggageDetail(dto));
    }

    @Autowired
    private CompensationExpressService compensationExpressService;

    // -----------------------快递相关----------------
    @PostMapping("/express/save")
    @ApiOperation(value = "新建异常行李快递信息")
    public BaseResult<Object> saveExpress(@Validated @RequestBody CompensationExpressInfoDTO dto) {
        boolean result = compensationExpressService.saveExpress(dto);
        if (!result) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @DeleteMapping("/express/remove")
    @ApiOperation(value = "根据id删除一条快递信息")
    public BaseResult<Object> removeExpress(String expressId) {
        boolean result = compensationExpressService.removeExpress(expressId);
        if (!result) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    //-----------------------事故单相关----------------
    @PostMapping("/save")
    @ApiOperation(value = "新建异常行李事故单")
    public BaseResult<Object> saveBaggageAccident(@Validated @RequestBody BaggageAccidentInfoDTO dto)
            throws IOException {
        // 校验机场端 是否有某个航司的事故单及补偿单的创建授权
        compensationOrderInfoService.verifyAirlineBusinessPrivile(BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE,
                dto.getPaxFlightNo().substring(0, 2));
        Object o = baggageAccidentService.saveAccident(dto);
        if (Objects.nonNull(o)) {
            BaggageTransportInfoDTO baggageTransportInfoDTO = new BaggageTransportInfoDTO();
            BeanUtils.copyProperties(o,baggageTransportInfoDTO);
            dto.setTransportInfoDTO(baggageTransportInfoDTO);
        }
        return ok(dto);
    }

    @GetMapping("/edit")
    @ApiOperation(value = "编辑异常行李事故单回显")
    public BaseResult<Object> editBaggageAccident(@ApiParam(value = "异常行李事故单id", required = true) String accidentId) {
        return ok(baggageAccidentService.editAccident(accidentId));
    }

    @GetMapping("/toLost")
    @ApiOperation(value = "少收转丢失")
    public BaseResult<Object> toLostBaggageAccident(@ApiParam(value = "异常行李事故单id", required = true) String accidentId) {
        if (!baggageAccidentService.toLost(accidentId)) {
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_UNSUPPORTED_OPERATION);
        }
        return ok();
    }

    @GetMapping("/toMatch")
    @ApiOperation(value = "匹配查询多/少收")
    public BaseResult<Object> toMatchBaggageAccident(
            @ApiParam(value = "异常行李事故单id", required = true) String accidentId) {
        return ok(baggageAccidentService.toMatch(accidentId));
    }

    @GetMapping("/saveMatch")
    @ApiOperation(value = "匹配绑定多/少收")
    public BaseResult<Object> saveMatchBaggageAccident(
            @ApiParam(value = "当前行李事故单id", required = true) String accidentId,
            @ApiParam(value = "绑定匹配的行李事故单号,多个时以英文逗号隔开", required = true) String accidentNo) {
        boolean result = baggageAccidentService.saveMatch(accidentId, accidentNo);
        if (!result) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/relieveMatch")
    @ApiOperation(value = "解除匹配绑定多/少收")
    public BaseResult<Object> relieveMatchBaggageAccident(
            @ApiParam(value = "当前行李事故单id", required = true) String accidentId,
            @ApiParam(value = "解除匹配的行李事故单号", required = true) String accidentNo) {
        boolean result = baggageAccidentService.relieveMatch(accidentId, accidentNo);
        if (!result) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @GetMapping("/changeStatus")
    @ApiOperation(value = "事故单状态扭转")
    public BaseResult<Object> changeBaggageAccidentStatus(
            @ApiParam(value = "当前行李事故单id", required = true) String accidentId,
            @ApiParam(value = "被修改的状态值", required = true) String targetStatus) {
        return ok(statusService.changeStatus(accidentId, targetStatus));
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "通过id删除异常行李事故单")
    public BaseResult<Object> deleteBaggageAccidentById(
            @PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        Boolean delete = baggageAccidentService.deleteById(id);
        if (!delete)
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        return ok();
    }

    @PostMapping("/getBaggageAccidentNumber")
    @ApiOperation(value = "根据条件获取已生成的补偿单数量")
    public BaseResult<Integer> getBaggageAccidentNumber(@RequestBody PaxMaintainSearchDTO dto) {
        return ok(baggageAccidentService.getBaggageAccidentNumber(dto));
    }

    @PostMapping("/existAccidentNumber")
    @ApiOperation(value = "已存在的事故单数量")
    public BaseResult<Integer> getExistAccidentNumber(@Validated @RequestBody VerifyAlikePaxOrderDTO dto) {
        // 需判断该事故单下旅客是否已经存在相同日期相同航班相同事故类型的事故单
        return ok(baggageAccidentService.getExistAccidentNumber(dto));
    }

    @PostMapping("/existCompensationNumber")
    @ApiOperation(value = "已存在的补偿单数量")
    public BaseResult<Integer> getExistCompensationNumber(@Validated @RequestBody VerifyAlikePaxOrderDTO dto) {
        // 当前航班下相同补偿单类型下是否存在该补偿单中存在旅客的其他补偿单（包含全部状态）
        return ok(baggageAccidentService.getExistCompensationNumber(dto));
    }

    @PostMapping("/undeliveredDropdown")
    @ApiOperation(value = "获取未交付的异常行李事故单下拉列表")
    public BaseResult<List<BaggageAccidentDropdownVO>> getUndeliveredAccidentDropdown(
            @RequestBody UndeliveredAccidentDTO dto) {
        return ok(baggageAccidentService.getUndeliveredAccidentDropdown(dto));
    }

    @PostMapping("/canEditTransport/{accidentNo}")
    @ApiOperation(value = "判断是否可以编辑运输单")
    public BaseResult<Boolean> canEditTransport(@PathVariable(value = "accidentNo") String accidentNo) {
        return ok(baggageAccidentService.canEditTransport(accidentNo));
    }

    @PostMapping("/transport/save")
    @ApiOperation(value = "保存或修改运输单信息")
    public BaseResult<BaggageTransportInfoDO> saveTransportInfo(@Validated @RequestBody BaggageTransportInfoDTO dto) {
        BaggageTransportInfoDO result = baggageAccidentService.saveTransportInfo(dto);
        return ok(result);
    }

    @PostMapping("/transport/list")
    @ApiOperation(value = "查询异常行李运输单列表")
    public PagedResult<List<BaggageTransportListVO>> queryBaggageTransportList(
            @RequestBody @Valid BaggageTransportQueryDTO dto) {
        return ok(baggageAccidentService.queryBaggageTransportList(dto));
    }

    @GetMapping("/transport/detail/{transportId}")
    @ApiOperation(value = "查询运输单详情")
    public BaseResult<BaggageTransportDetailVO> getTransportDetail(
            @PathVariable(value = "transportId") Long transportId) {
        return ok(baggageAccidentService.getTransportDetail(transportId));
    }

    @PostMapping("/transport/address")
    @ApiOperation(value = "修改运输单地址")
    public BaseResult<Boolean> updateTransportAddress(@Valid @RequestBody BaggageTransportAddressDTO dto) {
        return ok(baggageAccidentService.updateTransportAddress(dto));
    }

    @PostMapping("/transport/submit/{transportId}")
    @ApiOperation(value = "提交运输单到工作流")
    public BaseResult<Object> submitTransport(@PathVariable(value = "transportId") Long transportId) {
        return ok(baggageAccidentService.submitTransport(transportId));
    }

    @PostMapping("/transport/audit")
    @ApiOperation(value = "审核运输单")
    public BaseResult<Object> auditTransport(@Valid @RequestBody BaggageTransportAuditDTO dto) {
        return ok(baggageAccidentService.auditTransport(dto));
    }

    @GetMapping("/transport/reviewers/{transportId}")
    @ApiOperation(value = "查询运输单可选审核人,这个接口可能用不到")
    public BaseResult<Object> getTransportReviewers(@PathVariable(value = "transportId") Long transportId,
            @ApiParam(value = "任务ID，可选") @RequestParam(required = false) String taskId) {
        return ok(baggageAccidentService.getTransportReviewers(transportId, taskId));
    }

    @PostMapping("/transport/reviewers/save")
    @ApiOperation(value = "保存运输单审核人")
    public BaseResult<Object> saveTransportReviewers(@Valid @RequestBody BaggageTransportReviewerSaveDTO dto) {
        return ok(baggageAccidentService.saveTransportReviewers(dto));
    }

    @PostMapping("/transport/review")
    @ApiOperation(value = "复核运输单")
    public BaseResult<Boolean> reviewTransport(@Valid @RequestBody BaggageTransportReviewDTO dto) {
        return ok(baggageAccidentService.reviewTransport(dto.getAccidentId()));
    }

}
