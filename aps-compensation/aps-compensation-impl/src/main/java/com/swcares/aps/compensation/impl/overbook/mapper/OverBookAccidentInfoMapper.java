package com.swcares.aps.compensation.impl.overbook.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.overbook.vo.*;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @ClassName：OverBookAccidentInfoMapper
 * @Description：超售事故单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 11:24
 * @version： v1.0
 */
public interface OverBookAccidentInfoMapper extends BaseMapper<OverBookAccidentInfoDO> {

    IPage<OverBookPcListVO> getPcPages(@Param("dto") OverBookPcSearchDTO dto, Page<OverBookPcListVO> page);

    List<CompensationNumListVO> getCompensateNumList(@Param("dto") OverBookPaxSearchDTO dto);

    IPage<OverBookH5ListVO> getH5List(@Param("dto") OverBookH5SearchDTO dto, Page<OverBookH5ListVO> page);

    int verifyAlikeAccidentOrder(@Param("dto") VerifyAlikeOrderDTO dto);

    int verifyAlikeOrder(@Param("dto") VerifyAlikeOrderDTO dto);

    OverBookBasicPaxVO getBookBasicPaxVO(@Param("accidentId")Long accidentId ,@Param("paxId") String paxId);

    List<OverBookCompensationDetailsVO> getObCompensationDetails(@Param("accidentId")Long accidentId);

    @InterceptorIgnore(tenantLine = "true")
    boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus);


}
