package com.swcares.aps.compensation.impl.irregularflight.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightExistsAccidentVO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.irregularflight.service.DpFlightAccidentInfoService <br>
 * Description： 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
public interface FlightAccidentInfoService extends IService<FlightAccidentInfoDO> {
    /**
     * Title：getById <br>
     * Description： 根据id获取信息<br>
     * author：傅欣荣 <br>
     * date：2021/10/27 11:07 <br>
     * @param id
     * @return com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO
     */
    FlightAccidentInfoDetailsVO findById(Long id);

    /**
     * Title：logicRemoveById <br>
     * Description：删除 - 草稿状态下 <br>
     * author：傅欣荣 <br>
     * date：2021-10-14 <br>
     * @param id <br>
     * @return boolean <br>
     */
    boolean logicRemoveById(Long id);

    /**
     * Title：toVoidById <br>
     * Description： 作废-待处理状态下<br>
     * author：傅欣荣 <br>
     * date：2021/10/27 11:07 <br>
     * @param id <br>
     * @return boolean <br>
     */
    boolean toVoidById(Long id);

    /**
     * Title： updateById <br>
     * Description： 修改- 草稿状态下 ；记录修改记录<br>
     * author：傅欣荣 <br>
     * date：2021/10/27 14:16 <br>
     * @param flightAccidentInfoDO <br>
     * @return boolean <br>
     */
    boolean update(FlightAccidentInfoDO flightAccidentInfoDO);

    /**
     * Title：page <br>
     * Description：分页查询 <br>
     * author：傅欣荣 <br>
     * date：2021-10-14 <br>
     * @param dto <br>
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO> <br>
     */
    IPage<FlightAccidentInfoVO> page(FlightAccidentInfoPagedDTO dto);

    /**
     * Title：saveAccident <br>
     * Description：新增事故单 <br>
     * author：王磊 <br>
     * date：2021/10/27 19:52 <br>
     * @param dto <br>
     * @return <br>
     */
    boolean saveAccident(FlightAccidentInfoDTO dto);

    /**
     * Title：findFilghtExistsAccident <br>
     * Description：通过航班号和日期来获取是否存在事故单 <br>
     * author：王磊 <br>
     * date：2021/10/27 20:37 <br>
     * @param date
     * @param flightNo <br>
     * @return <br>
     */
    List<FlightExistsAccidentVO> findFilghtExistsAccident( String flightNo,String date,Long id);

    /**
     * Title：findCompensationOrderById <br>
     * Description： 事故单详情-根据事故单查询赔偿单列表数据 <br>
     * author：傅欣荣 <br>
     * date：2021/10/28 10:05 <br>
     * @param id <br>
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO> <br>
     */
    List<AccidentCompensationOrderVO> findCompensationOrderById(Long id);


    /**
     * Title：findChoiceSegment <br>
     * Description： 通过事故单号查已选航段数据<br>
     * author：傅欣荣 <br>
     * date：2021/11/8 17:17 <br>
     * @param id <br>
     * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>> <br>
     */
    List<Map<String,Object>> findChoiceSegment(Long id);


    /**
     * Title：updAccidentStatusToDo <br>
     * Description： 更新事故单状态为【待处理】- 无赔偿单<br>
     * author：傅欣荣 <br>
     * date：2021/11/15 15:35 <br>
     * @param accidentId
     * @param accidentStatus <br>
     * @return boolean <br>
     */
    boolean updAccidentStatusToDo(Long accidentId, String accidentStatus);

    /***
     * @title updAccidentStatusToDo
     * @description 批量更新事故单状态
     * <AUTHOR>
     * @date 2022/9/16 12:16
     * @param accidentNo
     * @param accidentStatus
     * @return boolean
     */
    boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus);
}
