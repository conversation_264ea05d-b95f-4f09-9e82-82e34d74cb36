package com.swcares.aps.compensation.impl.dataconfig.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRejectReasonService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.controller.ReplaceRejectReasonController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-12 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/replace/rejectReason")
@Api(tags = "代领人领取审核拒绝原由接口")
@ApiVersion(value = "代领设置 v1.0")
public class ReplaceRejectReasonController extends BaseController {

    @Autowired
    private ReplaceRejectReasonService replaceRejectReasonService;
    @Autowired
    private BusinessDataPushHandler businessDataPushHandler;
    @PostMapping("/pages")
    @ApiOperation(value = "条件分页查询审核拒绝原由记录")
    public BaseResult<Map<String, RuleManageDTO>> pages() {
        return ok(replaceRejectReasonService.pages());
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取所有审核拒绝原由记录")
    public BaseResult<List<ReplaceRejectReasonDTO>> list() {
        List<ReplaceRejectReasonDTO> result = replaceRejectReasonService.getAllRejectReason();
        return ok(result);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "刪除拒绝原由记录")
    public BaseResult<Object> delete(@RequestBody ReplaceConfigDeleteDTO dto) {
        boolean deleted=replaceRejectReasonService.delete(dto);
        if(!deleted){
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        businessDataPushHandler.dataStore(0L, BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_REPLACE);
        return ok();
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改拒绝原由记录")
    public BaseResult<Object>  update(@RequestBody ReplaceRejectReasonDTO dto) {
        if(dto.getId()==null){
            throw new BusinessException(CommonErrors.PARAM_VALIDATE_ERROR);
        }
        boolean updated = replaceRejectReasonService.update(dto);
        if(!updated){
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @PostMapping("/create")
    @ApiOperation(value = "添加拒绝原由记录")
    public BaseResult<Object>  create(@RequestBody ReplaceRejectReasonDTO dto) {
        boolean created = replaceRejectReasonService.create(dto);
        if(!created){
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }
}
