package com.swcares.aps.compensation.impl.dataconfig.service;

import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;

/**
 * @ClassName：CabinConfigService
 * @Description：舱位配置
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 16:12
 * @version： v1.0
 */
public interface CabinConfigService {

    /**
     * @title CabinConfigService.java
     * @description 通过航司二字码获取舱位配置信息
     * <AUTHOR>
     * @date 2024/5/14 16:17
     * @return com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO
     */
    CabinConfigVO loadCabinByAirline(String airlineCode);

    /**
     * @title CabinConfigService.java
     * @description 创建舱位配置信息
     * <AUTHOR>
     * @date 2024/5/14 16:19
     * @param businessCabin 商务舱
     * @param economyCabin 经济舱
     * @return void
     */
    void createCabinConfig(String businessCabin, String economyCabin);

}
