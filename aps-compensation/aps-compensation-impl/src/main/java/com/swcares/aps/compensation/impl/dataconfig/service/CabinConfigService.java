package com.swcares.aps.compensation.impl.dataconfig.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.dataconfig.dto.CabinConfigPageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.CabinConfigDO;
import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;

import java.util.List;

/**
 * @ClassName：CabinConfigService
 * @Description：舱位配置
 * @Copyright：© 2023 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2024/5/14 16:12
 * @version： v1.0
 */
public interface CabinConfigService {

    /**
     * @title CabinConfigService.java
     * @description 通过航司二字码获取舱位配置信息
     * <AUTHOR>
     * @date 2024/5/14 16:17
     * @return com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO
     */
    List<CabinConfigVO> loadCabinByAirline(String airlineCode);
    /***
     * @title loadCabinByAirline
     * @description 分页查询舱位列表
     * <AUTHOR>
     * @date 2025/1/8 14:31
     * @return java.util.List<com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO>
     */
    IPage<CabinConfigVO> loadCabinByAirlinePageList(CabinConfigPageDTO dto);
    /**
     * @title CabinConfigService.java
     * @description 创建舱位配置信息
     * <AUTHOR>
     * @date 2024/5/14 16:19
     * @param cabinConfigDO
     * @return void
     */
    void createCabinConfig(CabinConfigDO cabinConfigDO);

}
