package com.swcares.aps.compensation.impl.assist.controller;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.assist.service.AssisPaxCompensationService;
import com.swcares.aps.compensation.impl.assist.service.AssistCheckService;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.assist.dto.CheckInfoDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;


/**
 * <AUTHOR>
 * @title: AssistFlightController
 * @projectName aps
 * @description: 根据航班查询领取
 * @date 2022/1/20 10:28
 */

@RestController
@RequestMapping("/assist/pax/compensation")
@Slf4j
@Api(tags = "旅客未领取赔偿查询领取接口")
@ApiVersion(value = "现金协助补偿接口")
public class AssistPaxCompensationController extends BaseController {

    @Autowired
    private AssisPaxCompensationService assisPaxCompensationService;

    @Autowired
    private AssistCheckService assistCheckService;

    /**
    * @title findPaxCompensation
    * @description 查询具体某个旅客的可申领信息-复用王磊代码
    * @param dto 王磊的校验信息封装类
    * <AUTHOR>
    * @return com.swcares.baseframe.common.base.BaseResult<java.lang.Object>
    * @date 2022/1/21 9:09
    */
    @PostMapping("/findPaxCompensation")
    @ApiOperation(value = "查询具体某个旅客的可申领信息")
    public BaseResult<CompensationInfoVO> findPaxCompensation(@RequestBody @ApiParam(value = "校验信息DTO") AuthPaxDTO dto){
        return ok(assisPaxCompensationService.getPaxCompensation(dto));
    }

    @PostMapping("/check")
    @ApiOperation(value = "信息校验")
    public BaseResult<Object> checked(@ApiParam(value = "校验信息DTO")@RequestBody CheckInfoDTO dto, HttpServletRequest req){
        log.info("【aps-apply-impl】工作人员协助领取，信息校验,当前用户的token信息为【{}】，当前请求的旅客姓名【{}】", req.getHeader("Authorization") , JSONUtil.toJsonStr(dto));
        return ok(assistCheckService.checked(dto));
    }

    @GetMapping("/findPaxList")
    @ApiOperation(value = "通过航班号和航班日期查出可进行协助领取的旅客列表")
    public BaseResult<Object> findPaxList(@ApiParam(value = "航班号")String flightNo,@ApiParam(value = "航班日期")String flightDate,@ApiParam(value = "旅客姓名")String paxName, HttpServletRequest req){
        log.info("【aps-apply-impl】工作人员协助领取，通过航班号和航班日期查出可进行协助领取的旅客列表,当前用户的token信息为【{}】，当前请求的旅客姓名【{}】", req.getHeader("Authorization") , paxName);
        return ok(assisPaxCompensationService.getPaxCompensationByFlightInfo(flightNo, flightDate, paxName));
    }
}
