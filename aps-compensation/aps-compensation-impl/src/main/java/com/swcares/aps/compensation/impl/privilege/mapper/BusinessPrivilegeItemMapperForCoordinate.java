package com.swcares.aps.compensation.impl.privilege.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeItemMapper
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/13 16:28
 * @Version 1.0
 */
@Mapper
@InterceptorIgnore(tenantLine = "true")
public interface BusinessPrivilegeItemMapperForCoordinate extends BaseMapper<AirlineBusinessPrivilegeItem> {
}
