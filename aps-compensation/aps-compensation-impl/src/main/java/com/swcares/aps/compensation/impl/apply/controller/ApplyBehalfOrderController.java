package com.swcares.aps.compensation.impl.apply.controller;

import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfOrderService;
import com.swcares.aps.compensation.impl.apply.service.TransactionLockService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.component.pay.pay.service.wx.WxPayProcess;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;


/**
 * ClassName：com.swcares.compensation.controller.ApplyOrderController <br>
 * Description：航延补偿申领单信息表 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/applyBehalf/behalfOrder")
@Api(tags = "航延补偿申领单信息表接口")
@ApiVersion(value = "申领单接口 v1.0")
@Slf4j
public class ApplyBehalfOrderController extends BaseController {

    @Autowired
    private ApplyBehalfOrderService applyBehalfOrderService;

    @Autowired
    private TransactionLockService transactionLockService;

    @Resource(name = "wxPayProcess")
    private WxPayProcess wxPayProcess;

    @PostMapping("/saveBehalfApply")
    @ApiOperation(value = "代领新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveBehalfApply(@RequestBody @Valid ApplyBehalfOrderDTO dto, HttpServletRequest req) {
        log.info("【aps-apply-impl】本人领取，当前用户的token信息为【{}】，当前请求参数【{}】", req.getHeader("Authorization") ,dto);
        transactionLockService.saveApplyBehalfOrder(dto);
        String wxPayTypeApiVersion = wxPayProcess.getWxPayTypeApiVersion(TenantHolder.getTenant());
        return ok(wxPayTypeApiVersion);
    }

    @PostMapping("/saveAssistApply")
    @ApiOperation(value = "协助领取新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveAssistApply(@RequestBody @Valid ApplyBehalfOrderDTO dto) {
        boolean created = applyBehalfOrderService.saveAssistApply(dto);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok();
    }

}
