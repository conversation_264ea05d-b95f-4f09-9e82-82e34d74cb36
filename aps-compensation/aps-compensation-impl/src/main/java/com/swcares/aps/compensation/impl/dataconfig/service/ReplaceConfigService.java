package com.swcares.aps.compensation.impl.dataconfig.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.ReplaceConfigService <br>
 * Description： 代领配置服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
public interface ReplaceConfigService extends IService<DataConfigDO> {

    /**
     * Title：findByType <br>
     * Description：根据配置主类型获取配置列表 <br>
     * author：陈明东 <br>
     * date：2022-01-11 <br>
     * @param type <br>
     * @return java.util.List<com.swcares.aps.compensation.model.replace.entity.ReplaceConfig> <br>
     */
    List<DataConfigDO> findByType(String type);

    /**
     * Title：findByType <br>
     * Description：根据配置主类型获,子配置类型取配置列表 <br>
     * author：陈明东 <br>
     * date：2022-01-11 <br>
     * @param type    <br>
     * @param subType <br>
     * @return com.swcares.aps.compensation.model.replace.entity.ReplaceConfig <br>
     */
    DataConfigDO findByTypeAndSubType(String type, String subType);

    /**
     * Title：pages <br>
     * Description：根据配置主类型获,子配置类型取配置列表 <br>
     * author：陈明东<br>
     * date：2022-01-11 <br>
     * @param rejectReasonPageDTO    <br>
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO> <br>
     */
    List<ReplaceRejectReasonDTO> rejectReasonPages(ReplaceRejectReasonPageDTO rejectReasonPageDTO);



    /**
     * @title getAllRejectReason
     * @description 获取所有的审核拒绝原由
     * <AUTHOR>
     * @date 2022/2/23 16:29
     * @return java.util.List<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO>
     */
    List<ReplaceRejectReasonDTO> getAllRejectReason();
}
