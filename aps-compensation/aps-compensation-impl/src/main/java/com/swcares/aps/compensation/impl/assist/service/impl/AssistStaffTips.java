package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.assist.constant.AssistApplyErrors;
import com.swcares.aps.compensation.impl.assist.constant.AssistApplyTips;
import com.swcares.aps.compensation.impl.assist.service.AssistStaffTipsService;
import com.swcares.aps.compensation.model.assist.vo.ReceivedPaxVO;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistStaffTips
 * @projectName aps
 * @description: 工作人员端提示具体实现类
 * @date 2022/2/16 10:34
 */
@Service
public class AssistStaffTips implements AssistStaffTipsService {
    @Override
    public BaseResult<Object> sendSMSToStaff(String phone) {
        return new BaseResult<Object>(AssistApplyErrors.BUSINESS_CODE,String.format(AssistApplyTips.STAFF_MSG,phone));
    }
}
