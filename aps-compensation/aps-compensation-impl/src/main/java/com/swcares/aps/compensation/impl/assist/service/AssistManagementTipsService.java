package com.swcares.aps.compensation.impl.assist.service;

/**
 * <AUTHOR>
 * @title: AssistManagementTips
 * @projectName aps
 * @description:    管理层提示
 * @date 2022/2/16 10:22
 */
public interface AssistManagementTipsService {
    /**
    * @title sendSMSToManagement
    * @description 发送信息给管理层
    * <AUTHOR>
    * @date 2022/2/16 13:06
    * @param phone 代领人手机号
    * @return void
    */
    void sendSMSToManagement(String phone);
}
