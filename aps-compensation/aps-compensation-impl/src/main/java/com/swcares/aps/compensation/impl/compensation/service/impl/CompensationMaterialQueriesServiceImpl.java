package com.swcares.aps.compensation.impl.compensation.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.apply.enums.ApplyWorkflowNodeBusiTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialQueriesMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationMaterialQueriesService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderQueriesService;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialDetailDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialListDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.compensation.vo.*;
import com.swcares.aps.component.dict.service.FindDictService;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：CommpensationRepresentation
 * @Description：箱包赔偿单查询ServiceImpl
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 16:44
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationMaterialQueriesServiceImpl extends ServiceImpl<CompensationMaterialQueriesMapper, CompensationMaterialInfoDO> implements CompensationMaterialQueriesService {

    @Autowired
    private CompensationOrderQueriesService compensationOrderQueriesService;

    @Autowired
    private FindDictService findDictService;

    @Autowired
    private WorkflowApi workflowApi;

    @Override
    public IPage<CompensationMaterialQueriesListVO> findCompensationLuggageList(CompensationMaterialListDTO dto) {
        if(!(StringUtil.isEmpty(dto.getStatus()))){
            String status = dto.getStatus();
            String[] statusSplits = status.split(",");
            dto.setStatusSplits(statusSplits);
        }
        String userId = UserContext.getUserId().toString();
        dto.setUserId(userId);
        IPage<CompensationMaterialQueriesListVO> page = baseMapper.findCompensationLuggageList(dto, dto.createPage());
        List<CompensationMaterialQueriesListVO> records = page.getRecords();
        if(CollectionUtils.isNotEmpty(records)){
            List<String> orderIds = records.stream().map(CompensationMaterialQueriesListVO::getId).map(String::valueOf).collect(Collectors.toList());
            Map<String, String> loginUserCanAuditOrderIdToTaskIdMp = compensationOrderQueriesService.getLoginUserCanAuditOrderIdToTaskIdMp(orderIds);
            records.forEach(t->{
                String orderId = String.valueOf(t.getId());
                if(loginUserCanAuditOrderIdToTaskIdMp.containsKey(orderId)){
                    t.setToExamine("Y");
                    t.setTaskId(loginUserCanAuditOrderIdToTaskIdMp.get(orderId));
                }else{
                    t.setToExamine("N");
                }
                t.setIsSponsor(userId.equals(t.getCreatedId()));
            });
        }
        return page;
    }

    @Override
    public CompensationMaterialDetailFinalVO findCompensationLuggageDetailInfo(CompensationMaterialDetailDTO dto) {
        if(dto.getId()==null || dto.getId()==""){
            throw new BusinessException(CommonErrors.PARAM_CANNOT_BE_NULL);
        }
        CompensationMaterialDetailFinalVO compensationMaterialDetailFinalVO = new CompensationMaterialDetailFinalVO();
        CompensationMaterialDetailVO compensationLuggageDetailInfo = baseMapper.findCompensationLuggageDetailInfo(dto);
        if(compensationLuggageDetailInfo==null ){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }

        //以下操作把详情信息分断展示给前端
        //塞补偿单信息
        CompensationMaterialDetailCompensationVO compensation = new CompensationMaterialDetailCompensationVO();
        compensation.setCompensationId(compensationLuggageDetailInfo.getCompensationId());
        compensation.setSumMoney(compensationLuggageDetailInfo.getSumMoney());
        compensation.setOrderNo(compensationLuggageDetailInfo.getOrderNo());
        compensation.setStatus(compensationLuggageDetailInfo.getStatus());
        compensation.setServiceCity(compensationLuggageDetailInfo.getServiceCity());
        compensation.setEnsureType(compensationLuggageDetailInfo.getEnsureType());
        compensation.setCompensateType(compensationLuggageDetailInfo.getCompensateType());
        compensation.setCompensateSubType(compensationLuggageDetailInfo.getCompensateSubType());
        compensation.setCompensateStandard(compensationLuggageDetailInfo.getCompensateStandard());
        compensation.setCommodityId(compensationLuggageDetailInfo.getCommodityId());
        compensation.setAccidentId(compensationLuggageDetailInfo.getAccidentId());
        compensation.setAccidentNo(compensationLuggageDetailInfo.getAccidentNo());
        compensation.setAccidentSubType(compensationLuggageDetailInfo.getAccidentSubType());
        compensation.setAccidentType(compensationLuggageDetailInfo.getAccidentType());
        compensation.setType(compensationLuggageDetailInfo.getType());
        compensation.setInputSource(compensationLuggageDetailInfo.getInputSource());
        compensationMaterialDetailFinalVO.setCompensationMaterialDetailCompensationVO(compensation);



        //塞航班信息
        CompensationMaterialDetailFlightVO flight= new CompensationMaterialDetailFlightVO();
        flight.setFlightNo(compensationLuggageDetailInfo.getFlightNo());
        flight.setFlightDate(compensationLuggageDetailInfo.getFlightDate());
        flight.setPaxSegment(compensationLuggageDetailInfo.getPaxSegment());
        flight.setStd(compensationLuggageDetailInfo.getStd());
        flight.setEtd(compensationLuggageDetailInfo.getEtd());
        compensationMaterialDetailFinalVO.setCompensationMaterialDetailFlightVO(flight);

        Map<String, String> idTypeMp = findDictService.findValueToLabelMp(null, FindDictService.TYPE_ID_TYPE);

        //塞补偿旅客信息
        CompensationMaterialDetailPaxVO pax = new CompensationMaterialDetailPaxVO();
        pax.setPaxName(compensationLuggageDetailInfo.getPaxName());
        pax.setIdType(compensationLuggageDetailInfo.getIdType());
        if(StringUtils.isNotBlank(compensationLuggageDetailInfo.getIdType())
                && idTypeMp.containsKey(compensationLuggageDetailInfo.getIdType())){
            pax.setIdType(idTypeMp.get(compensationLuggageDetailInfo.getIdType()));
        }
        pax.setIdType("身份证");
        pax.setIdNo(compensationLuggageDetailInfo.getIdNo());
        pax.setTktNo(compensationLuggageDetailInfo.getTktNo());
        pax.setPhone(compensationLuggageDetailInfo.getPhone());
        pax.setBaggageNo(compensationLuggageDetailInfo.getBaggageNo());
        pax.setPaxId(compensationLuggageDetailInfo.getPaxId());
        pax.setReceiveStatus(compensationLuggageDetailInfo.getReceiveStatus());
        compensationMaterialDetailFinalVO.setCompensationMaterialDetailPaxVO(pax);

        //塞补偿实物与成本信息
        CompensationMaterialDetailMaterialVO material= new CompensationMaterialDetailMaterialVO();
        material.setMaterialBrand(compensationLuggageDetailInfo.getMaterialBrand());
        material.setMaterialName(compensationLuggageDetailInfo.getMaterialName());
        material.setMaterialSize(compensationLuggageDetailInfo.getMaterialSize());
        material.setMaterialUnivalent(compensationLuggageDetailInfo.getMaterialUnivalent());
        material.setAmount(compensationLuggageDetailInfo.getAmount());
        material.setSumMoney(compensationLuggageDetailInfo.getSumMoney());
        material.setRemark(compensationLuggageDetailInfo.getRemark());
        material.setMaterialId(compensationLuggageDetailInfo.getMaterialId());
        material.setStock(compensationLuggageDetailInfo.getStock());
        compensationMaterialDetailFinalVO.setCompensationMaterialDetailMaterialVO(material);

        Map<String, String> loginUserCanAuditOrderIdToTaskIdMp = compensationOrderQueriesService.getLoginUserCanAuditOrderIdToTaskIdMp(Arrays.asList(dto.getId()));
        if(loginUserCanAuditOrderIdToTaskIdMp.containsKey(dto.getId())){
            compensationMaterialDetailFinalVO.setToExamine("Y");
            compensationMaterialDetailFinalVO.setTaskId(loginUserCanAuditOrderIdToTaskIdMp.get(dto.getId()));
        }else{
            compensationMaterialDetailFinalVO.setToExamine("N");
        }
        //判断当前节点用户，是否是发起人
        if(!CompensateStatusEnum.DRAFT.getKey().equals(compensationLuggageDetailInfo.getStatus())){
            Long userId = UserContext.getUserId();
            CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(compensationLuggageDetailInfo.getCompensationId()).build()).getData();
            log.info("【箱包补偿单详情信息】当前节点的下一审核任务【{}】", JSONUtil.toJsonStr(currentTaskActivityVO));
            if(ObjectUtils.isNotEmpty(currentTaskActivityVO.getCurrentTaskActivityDTOS())){
                CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
                compensationMaterialDetailFinalVO.setIsSponsor(ApplyWorkflowNodeBusiTypeEnum.SUBMITTER.getType().equals(currentTaskActivityDTO.getNodeKey())
                        && userId.toString().equals(compensationLuggageDetailInfo.getCreatedBy()));
            }
        }
        return compensationMaterialDetailFinalVO;
    }
}
