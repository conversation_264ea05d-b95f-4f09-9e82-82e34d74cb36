package com.swcares.aps.compensation.impl.privilege.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeItemMapperForCoordinate;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeItemServiceForCoordinate;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeItemServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/13 16:30
 * @Version 1.0
 */
@Service
public class BusinessPrivilegeItemServiceForCoordinateImpl extends ServiceImpl<BusinessPrivilegeItemMapperForCoordinate, AirlineBusinessPrivilegeItem>
        implements BusinessPrivilegeItemServiceForCoordinate {
    @Override
    public int deleteBatch(List<Long> ids) {
        return getBaseMapper().deleteBatchIds(ids);
    }
}
