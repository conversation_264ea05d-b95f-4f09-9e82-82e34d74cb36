package com.swcares.aps.compensation.impl.irregularflight.constant;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.constant <br>
 * Description：赔偿单常量 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月24日 11:03 <br>
 * @version v1.0 <br>
 */
public interface CompensationConstant {
    //文件夹
    String COMPENSATION_CODE="COMPENSATION_ORDER_AUDIT:";
    //赔偿单审核-审核操作人（部门、角色）
    String COMPENSATION_AUDITOR_KEY = COMPENSATION_CODE+"{}";

    //同意AGREE、拒绝REJECT、驳回BACK
    //审核状态-同意
    String AUDIT_AGREE = "AGREE";
    //审核状态-不同意
    String AUDIT_DISAGREE = "REJECT";
    //审核状态-驳回
    String AUDIT_REJECT = "BACK";
    //审核节点-发起人节点
    String NODE_KEY_SUBMITTER = "submitter";
    //不正常航班-审核流程key【TODO 可变】
    String AUDIT_PROCESS_KEY="Fltdelayt1";
    //节点是否为end
    String NODE_END="end";
    //节点是否为CANCEL
    String NODE_CANCEL="cancel";
    //admin结束异常流程，备注
    String ADMIN_END_REMARKS="当前审核节点未配置审核人员";
    //admin结束异常流程，备注
    String ADMIN_END="ADMIN";
    //admin结束流程，需要填写审核人id，对应admin的id
    String ADMIN_END_ID="1";
    //admin结束异常流程，前端提示
    String ADMIN_PROMPT_MESSAGE="未匹配到下一节点审核人员，系统已自动为您结束审核流程";

    //工作流业务申领单业务类型
    String IRREGULARFLIGHT_WORKFLOW_BUSINESS="irregularFlight";
    String BAGGAGE_WORKFLOW_BUSINESS="baggage";
    String COMPLAINT_WORKFLOW_BUSINESS="complaint";
    String OVERBOOK_WORKFLOW_BUSINESS="overbook";
    //赔偿单保存时的redis分布式锁过期时间
    Long SAVE_REDIS_LOCK_TIME = 120L;

    //补偿单来源 1机场/2航司
    String COMPENSATION_SOURCE_AIRPORT="1";
    String COMPENSATION_SOURCE_AIRLINE="2";
    String BAGGAGE_TRANSPORTATION_BUSINESS="transportation";
}
