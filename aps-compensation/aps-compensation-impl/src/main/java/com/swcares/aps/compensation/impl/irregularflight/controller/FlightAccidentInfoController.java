package com.swcares.aps.compensation.impl.irregularflight.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensationSubTypeEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.FlightAccidentInfoService;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.AccidentCompensationOrderVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoDetailsVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightAccidentInfoVO;
import com.swcares.aps.compensation.model.irregularflight.vo.FlightExistsAccidentVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.irregularflight.controller.DpFlightAccidentInfoController <br>
 * Description： 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/accident/info")
@Api(tags = "接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
public class FlightAccidentInfoController extends BaseController {
    @Autowired
    private FlightAccidentInfoService flightAccidentInfoService;

    @PostMapping("/save")
    @ApiOperation(value = "新建记录")
    public BaseResult<Object> save(@Validated @RequestBody FlightAccidentInfoDTO dto) {
        boolean created = flightAccidentInfoService.saveAccident(dto);
        if (!created) {
            throw new BusinessException(CommonErrors.CREATE_ERROR);
        }
        return ok(dto);
    }

    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除记录")
    public BaseResult<Object> delete(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        boolean deleted = flightAccidentInfoService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PutMapping("/toVoid/{id}")
    @ApiOperation(value = "通过ID作废记录")
    public BaseResult<Object> toVoid(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        boolean updated = flightAccidentInfoService.toVoidById(id);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }


    @PutMapping("/update")
    @ApiOperation(value = "修改记录")
    public BaseResult<Object> update(@RequestBody FlightAccidentInfoDTO dto) {
        FlightAccidentInfoDO flightAccidentInfoDO = ObjectUtils.copyBean(dto, FlightAccidentInfoDO.class);
        boolean updated = flightAccidentInfoService.update(flightAccidentInfoDO);
        if (!updated) {
            throw new BusinessException(CommonErrors.UPDATE_ERROR);
        }
        return ok();
    }

    @GetMapping("/find/{id}")
    @ApiOperation(value = "通过ID查询记录")
    public BaseResult<FlightAccidentInfoDetailsVO> findById(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(flightAccidentInfoService.findById(id));
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询记录")
    public PagedResult<List<FlightAccidentInfoVO>> page(@RequestBody FlightAccidentInfoPagedDTO dto) {
        IPage<FlightAccidentInfoVO> result = flightAccidentInfoService.page(dto);
        return ok(result);
    }

    @GetMapping("/findCompensationOrder/{id}")
    @ApiOperation(value = "通过ID查询关联赔偿单")
    public BaseResult<List<AccidentCompensationOrderVO>> findCompensationOrderById(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(flightAccidentInfoService.findCompensationOrderById(id));
    }


    @GetMapping("/findFilghtExistsAccident")
    @ApiOperation(value = "通过航班号和日期来获取是否存在事故单")
    BaseResult<List<FlightExistsAccidentVO>> findFilghtExistsAccident(@ApiParam(value = "航班号", required = true) @RequestParam(value="flightNo",required=true) String flightNo, @ApiParam(value = "航班日期", required = true) @RequestParam(value="date",required=true) String date,Long id){
        return ok(flightAccidentInfoService.findFilghtExistsAccident(flightNo,date,id));
    }

    @GetMapping("/findChoiceSegment")
    @ApiOperation(value = "通过事故单号查已选航段数据")
    BaseResult<List<Map<String,Object>>> findChoiceSegment(@RequestParam @ApiParam(value = "主键id", required = true) Long id) {
        return ok(flightAccidentInfoService.findChoiceSegment(id));
    }

    @GetMapping("/findCompensationSubTypeKey/{key}")
    @ApiOperation(value = "赔偿类型key-获取赔偿子类型数据字典key")
    BaseResult<String> findCompensationSubTypeKey(@PathVariable @ApiParam(value = "赔偿类型key", required = true) String key) {
        return ok(CompensationSubTypeEnum.build(key).getValue());
    }

    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "通过ID查询记录")
    public BaseResult<FlightAccidentInfoDO> getFlightAccidentInfoDetails(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        return ok(flightAccidentInfoService.getById(id));
    }
}
