package com.swcares.aps.compensation.impl.compensation.service.strategy;

import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.baseframe.common.exception.BusinessException;
import org.springframework.stereotype.Service;

/**
 * @ClassName：FlightCashCompensationServiceImpl
 * @Description：异常航班现金补偿单创建编辑服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 17:02
 * @version： v1.0
 */
@Service("flightCashCompensationService")
public class FlightCashCompensationServiceImpl implements CompensationAddAndEditService<CompensationCashAddCommandDTO, CompensationCashEditCommandDTO>  {

    @Override
    public String add(CompensationCashAddCommandDTO addCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"异常航班现金补偿单暂未实现");
    }

    @Override
    public String edit(CompensationCashEditCommandDTO editCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,"异常航班现金补偿单暂未实现");
    }

    @Override
    public CompensationAuditOperationVO reSubmit(CompensationCashEditCommandDTO editCompensationCommand) {

        throw new BusinessException(CompensationException.COMPENSATION_EDIT_ERROR,"异常航班现金补偿单暂未实现");
    }
}
