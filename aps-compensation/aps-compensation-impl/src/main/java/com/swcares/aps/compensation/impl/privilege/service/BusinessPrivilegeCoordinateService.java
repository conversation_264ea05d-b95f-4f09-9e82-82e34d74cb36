package com.swcares.aps.compensation.impl.privilege.service;

import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;

/**
 * <AUTHOR> <PERSON>
 * @Classname BusinessPrivilegeCoordinateService
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/7/15 15:43
 * @Version 1.0
 */
public interface BusinessPrivilegeCoordinateService {

    /**
     * @title coordinate
     * @description 应用端之间同步业务授权数据的接口
     * <AUTHOR>
     * @date 2024/7/15 14:51
     * @param businessPrivilegeDTO:
     * @return boolean
     */
    boolean coordinate(BusinessPrivilegeDTO businessPrivilegeDTO);
}
