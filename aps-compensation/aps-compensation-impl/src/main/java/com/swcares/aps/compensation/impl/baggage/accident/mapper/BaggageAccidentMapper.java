package com.swcares.aps.compensation.impl.baggage.accident.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.irregularflight.dto.PaxMaintainSearchDTO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * @ClassName：BaggageAccidentMapper
 * @Description： 异常行李事故单mapper
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/4 9:49
 * @version： v1.0
 */
@Mapper
public interface BaggageAccidentMapper extends BaseMapper<BaggageAccidentInfoDO> {

    /**
     * @title findBaggageAccidentList
     * @description 查询异常行李事故单列表信息
     * <AUTHOR>
     * @date 2022/3/10 16:01
     * @param dto
     * @param page
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO>
     */
    IPage<FindBaggageVO> findBaggageAccidentList( @Param("dto") FindBaggageDTO dto, Page<Object> page);

    /**
     * @title findBaggageDetail
     * @description 根据异常行李事故单号查找异常行李事故单详情
     * <AUTHOR>
     * @date 2022/3/10 15:59
     * @param dto
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.FindBaggageDetailVO
     */
    FindBaggageDetailVO findBaggageDetail(@Param("dto") FindBaggageDetailDTO dto);

    /**
     * @title findNo
     * @description 根据异常行李事故单号查找相对应的少收多收的异常行李事故单号集
     * <AUTHOR>
     * @date 2022/3/10 15:57
     * @param dto
     * @return java.lang.String
     */
    String findNo(@Param("dto") FindBaggageDetailDTO dto);

    /**
    * @title toLost
    * @description 根据事故单id进行少收转丢失
    * <AUTHOR>
    * @date 2022/3/8 16:27
    * @param accidentId
    * @return int
    */
//    @Update("update baggage_accident_info bai set bai.type = '274',bai.`confirm_lost_time`=#{time} " +
//            "where bai.type = '271' and id = #{id} and (bai.accident_status = '202' or bai.accident_status = '203')")
    @Update("update baggage_accident_info bai set bai.type = '25',bai.confirm_lost_time=#{time} " +
            "where bai.type = '22' and id = #{id} and (bai.accident_status = '1' or bai.accident_status = '2')")
    int toLost(@Param("id") String accidentId,@Param("time") Date time);

    /**
    * @title findById
    * @description 根据事故单id查询事故单信息
    * <AUTHOR>
    * @date 2022/3/8 16:27
    * @param accidentId
    * @return com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO
    */
    BaggageAccidentInfoVO findById(@Param("id") String accidentId);

    /**
     * @title findByAccidentNo
     * @description  通过事故单号查询异常行李事故单信息
     * <AUTHOR>
     * @date 2022/9/28 16:32
     * @param accidentNo
     * @return com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO
     */
    @Select("select * from BAGGAGE_ACCIDENT_INFO where ACCIDENT_NO = #{accidentNo} ")
    BaggageAccidentInfoVO findByAccidentNo(@Param("accidentNo") String accidentNo);

    /**
    * @title toMatch
    * @description 匹配多收、少收查询
    * <AUTHOR>
    * @date 2022/3/8 16:29
    * @param dto 匹配规则封装对象
    * @return java.util.List<com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO>
    */
    List<MatchResultVO> toMatch(@Param("dto") MatchIncomeDTO dto);

    /**
     * @title findMatchResult
     * @description 查询异常行李事故单关联的少收多收事故单信息
     * <AUTHOR>
     * @date 2022/3/10 16:02
     * @param dto
     * @return java.util.List<com.swcares.aps.compensation.model.baggage.accident.vo.MatchResultVO>
     */
    List<MatchResultVO> findMatchResult(@Param("dto") FindBaggageDetailDTO dto);
    /**
    * @title saveMatch
    * @description 匹配绑定多/少收事故单
    * <AUTHOR>
    * @date 2022/3/9 16:16
    * @param accidentId 当前事故单id
     * @param accidentNo 需要进行绑定的事故单号
    * @return int
    */
    @Update("update baggage_accident_info set match_accident_no = #{accidentNo} where id=#{id} ")
    int saveMatch(@Param("id") String accidentId,@Param("accidentNo") String accidentNo);

    /**
    * @title changeStatus
    * @description 修改异常行李事故单状态
    * <AUTHOR>
    * @date 2022/3/15 13:25
    * @param accidentId
    * @param status
    * @return int
    */
    int changeStatus(@Param("id") String accidentId,@Param("status") String status,@Param("userNo")String userNo,@Param("dateTime") LocalDateTime dateTime);

    /**
     * @title getBaggageAccidentNumber
     * @description 获取已经生成的补偿单数量
     * <AUTHOR>
     * @date 2022/3/17 16:22
     * @param dto
     * @return java.lang.Long
     */
    Integer getBaggageAccidentNumber(@Param("dto") PaxMaintainSearchDTO dto);

    /**
     * @title isBoundBaggageAccident
     * @description 根据事故单号查询该事故单是否已经被绑定了多/少收
     * <AUTHOR>
     * @date 2022/3/28 15:07
     * @param accidentNo
     * @return java.lang.Integer
     */
   /* @Select(value = "select accident_no from baggage_accident_info bai " +
            "where bai.match_accident_no like concat('%',#{accidentNo},'%')")*/
    @Select(value = "select accident_no from baggage_accident_info bai " +
            "where bai.match_accident_no like concat(concat('%',#{accidentNo}),'%') ")
    String isBoundBaggageAccident(String accidentNo);

    /**
     * @title toBoundBaggageAccident
     * @description 被绑定的事故单进行反向绑定
     * <AUTHOR>
     * @date 2022/9/22 10:21
     * @param toBoundNo 反向绑定的事故单号
     * @param beBoundNos 被绑定的事故单号
     * @return void
     */
    void toBoundBaggageAccident(@Param("toBoundNo") String toBoundNo,@Param("beBoundNos") String[] beBoundNos);

    @InterceptorIgnore(tenantLine = "true")
    boolean updBaggageAccidentStatusBatch(List<String> accidentNo, String accidentStatus);


    /**
     * @title getExistAccidentNumber
     * @description 已存在的事故单数量
     * <AUTHOR>
     * @date 2024/6/19 14:22
     * @param dto
     * @return java.lang.Integer
     */
    Integer getExistAccidentNumber(@Param("dto") VerifyAlikePaxOrderDTO dto);

    /**
     * @title getExistCompensationNumber
     * @description 已存在的补偿单数量
     * <AUTHOR>
     * @date 2024/6/19 14:22
     * @param dto
     * @return java.lang.Integer
     */
    Integer getExistCompensationNumber(@Param("dto") VerifyAlikePaxOrderDTO dto);

    /***
     * @title getOvertimeAccident
     * @description 待处理少收事故单若已超时，需要发送站内信
     * <AUTHOR>
     * @date 2024/7/26 15:14

     * @return java.util.List<com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO>
     */
    @InterceptorIgnore(tenantLine = "true")
    List<BaggageAccidentInfoDO> getOvertimeAccident();

    /**
     * 查询航班上未运输且未合并的行李事故单
     */
    List<BaggageAccidentDropdownVO> selectUndeliveredAccidentDropdown(@Param("dto") UndeliveredAccidentDTO dto);

    int  existTransport(@Param("accidentNo") String accidentNo);
}
