package com.swcares.aps.compensation.impl.baggage.luggage.enums;

/**
 * <AUTHOR>
 * @title: LuggageStockStatusEnums
 * @projectName aps
 * @description: 箱包库存动作类别
 * @date 2022/2/8 15:47
 */
public enum LuggageStockStatusEnum {
    INCREASE("3", "增加"),
    REDUCE("2", "减少"),
    NEWLY("1", "新建");

    private LuggageStockStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static LuggageStockStatusEnum build(String key) {
        return build(key, true);
    }

    public static LuggageStockStatusEnum build(String key, boolean throwEx) {
        LuggageStockStatusEnum typeEnum = null;
        for (LuggageStockStatusEnum element : LuggageStockStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + LuggageStockStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
