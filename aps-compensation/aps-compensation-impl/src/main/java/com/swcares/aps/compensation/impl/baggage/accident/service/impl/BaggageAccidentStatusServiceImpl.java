package com.swcares.aps.compensation.impl.baggage.accident.service.impl;

import com.beust.jcommander.internal.Lists;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusHandlerService;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentInfoVO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：BaggageAccidentStatusServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/15 13:12
 * @version： v1.0
 */
@Service
@Slf4j
@Transactional
public class BaggageAccidentStatusServiceImpl implements BaggageAccidentStatusService {

    private Map<String, List<String>> allowTargetStatusMp=new HashMap<>();

    @Autowired
    private BaggageAccidentMapper baggageAccidentMapper;

    @Autowired
    private List<BaggageAccidentStatusHandlerService> handlerServices;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;


    @PostConstruct
    private void init(){
        allowTargetStatusMp.put(AccidentStatusEnum.DRAFT.getValue(), Lists.newArrayList(AccidentStatusEnum.TODO.getValue()));

        allowTargetStatusMp.put(AccidentStatusEnum.CASE_CLOSED.getValue(), Lists.newArrayList(AccidentStatusEnum.PROCESS.getValue()));

        allowTargetStatusMp.put(AccidentStatusEnum.PROCESS.getValue(), Lists.newArrayList(AccidentStatusEnum.CASE_CLOSED.getValue()));

        allowTargetStatusMp.put(AccidentStatusEnum.TODO.getValue(), Lists.newArrayList(AccidentStatusEnum.CASE_CLOSED.getValue(),
                AccidentStatusEnum.TO_VOID.getValue(),AccidentStatusEnum.PROCESS.getValue()));

    }

    @Override
    public boolean changeStatus(String accidentId, String targetStatus) {
        BaggageAccidentInfoVO accidentInfoVO = baggageAccidentMapper.findById(accidentId);
        if (accidentInfoVO==null){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_NOT_EXIST);
        }
        BaggageAccidentInfoDTO dto = ObjectUtils.copyBean(accidentInfoVO,BaggageAccidentInfoDTO.class);
        verifyTargetStatus(dto,targetStatus);

        for(BaggageAccidentStatusHandlerService statusHandlerService:handlerServices){
            if(statusHandlerService.support(dto.getAccidentStatus(),targetStatus)){
                statusHandlerService.handler(dto,targetStatus);
            }
        }
        String createdBy = ApsUserUtils.getCreatedBy();
        int result = 0;
        try {
            result = baggageAccidentMapper.changeStatus(accidentId, AccidentStatusEnum.buildValue(targetStatus).getValue(),createdBy,LocalDateTime.now());
        }catch (Exception e){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_STATUS_CHANGE_FAIL);
        }
        if(result==0){
            return false;
        }
        //推送业务数据到协同中心 [草稿和作废不能绑定]
        if (!AccidentStatusEnum.DRAFT.getValue().equals(accidentInfoVO.getAccidentStatus())
                && !AccidentStatusEnum.DRAFT.getValue().equals(targetStatus)){
            businessDataPushHandler.dataStore(Long.valueOf(accidentId), BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }
        return true;
    }

    /**
    * @title verifyTargetStatus
    * @description 异常行李事故单状态扭转校验
    * <AUTHOR>
    * @date 2022/3/15 15:03
    * @param baggageAccident
     * @param targetStatus
     * @return void
     */
    private void verifyTargetStatus(BaggageAccidentInfoDTO baggageAccident, String targetStatus) {
        String sourceStatus = baggageAccident.getAccidentStatus();
        List<String> targetStatusList = allowTargetStatusMp.get(sourceStatus);
        if(CollectionUtils.isEmpty(targetStatusList) || !targetStatusList.contains(targetStatus)){
            throw new BusinessException(BaggageAccidentException.BAGGAGE_ACCIDENT_STATUS_VERIFY_FAIL,"当前状态不能修改为"+targetStatus+"状态");
        }

    }
}
