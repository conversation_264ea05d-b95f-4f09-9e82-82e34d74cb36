package com.swcares.aps.compensation.impl.baggage.luggage.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindLuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageStockInfoDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindLuggageStockInfoVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface LuggageManagementMapper extends BaseMapper<LuggageStockInfoDO> {

    /**
     * Title：updateLuggageStockDetailed <br>
     * Description： 根据id修改，增加减少箱包剩余库存数量<br>
     * author：唐康 <br>
     * date：2022/02/09 14:37 <br>
     * @param dto
     * @return
     */
    @InterceptorIgnore(tenantLine = "true")
     int updateLuggageStockDetailed(@Param("dto") LuggageStockInfoDTO dto);

    /**
     * Title：findLuggageStockDetails <br>
     * Description： 根据id 查询所有箱包库存管理记录信息<br>
     * author：唐康 <br>
     * date：2022/02/09 14:37 <br>
     * @param dto
     * @return
     */
    IPage<FindLuggageStockInfoVO> findLuggageStockDetailed(FindLuggageStockInfoDTO dto, Page<Object> page) ;

    /**
     * @title getLuggageCompensationReport
     * @description @TODO
     * <AUTHOR>
     * @date 2022/7/26 10:08
     * @param dto
     * @param page
     * @return IPage<LuggageCompensateDetailVO>
     */
    IPage<LuggageCompensateDetailVO> getLuggageCompensationReportDetail(LuggageCompensatePageDTO dto, Page<Object> page);

    IPage<LuggageCompensateDetailVO> getLuggageCompensationReport(LuggageCompensatePageDTO dto, Page<Object> page);
}
