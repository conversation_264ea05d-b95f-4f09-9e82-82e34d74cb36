package com.swcares.aps.compensation.impl.assist.constant;

/**
 * <AUTHOR>
 * @title: AssistApplyException
 * @projectName aps
 * @description:
 * @date 2022/1/20 19:08
 */
public final class AssistApplyErrors {
    //未能查询到该航班信息，请检查后重新查询
    public static int FLIGHT_NOT_FIND_ERROR = 30151;
    //[日期][航班号]下无补偿单
    public static int COMPENSATION_NOT_FIND_ERROR = 30152;
    //[日期][航班号]下无可协助领取的旅客
    public static int ASSIST_PAX_NOT_FIND_ERROR = 30153;
    //未能查询到该乘机人的信息，请检查后再次查询
    public static int PAX_NOT_MATCHING_ERROR = 30154;
    //申领信息保存失败，请重新尝试
    public static int APPLY_SAVE_ERROR = 30155;
    //需要短信提示时返回前端的code
    public static int BUSINESS_CODE = 30156;
    //[乘机人姓名]名下暂无补偿信息可领取
    public static int PAX_COMPENSATION_NOT_FIND_ERROR = 30157;
    //[乘机人姓名]名下补偿信息已被领取
    public static int PAX_APPLY_ALREADY_RECEIVED_ERROR = 30158;
    //[乘机人姓名]名下补偿信息已被冻结
    public static int PAX_APPLY_FROZEN_ERROR = 30159;
    //您提交的申领中存在补偿信息发生变更，请核实后再进行领取。将为您跳转至现金协助补偿查询页面
    public static int ASSIST_APPLY_AUTH_PAX_ORDER = 30160;

    /*下面是用的王磊的本人领取相关异常code码在此仅用于判断*/
    //未查询到相关补偿信息，请检查后再次提交！==30157
    public static int APPLY_AUTH_PAX_ERROR = 30801;
    //您在该航班中的补偿信息已被领取，请核实后再次提交！==30158
    public static int APPLY_AUTH_PAX_ALREADY_RECEIVED = 30802;
    //您在该航班中的赔偿信息存在异常，请核实后再次提交！==30159
    public static int APPLY_AUTH_PAX_ABNORMAL = 30803;
    //补偿信息发生变更，请重新验证！==30160
    public static int APPLY_AUTH_PAX_ORDER = 30806;


}
