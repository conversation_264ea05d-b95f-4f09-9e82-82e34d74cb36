package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.impl.irregularflight.mapper.TerminalInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationImplFltPaxDataService;
import com.swcares.aps.compensation.model.irregularflight.vo.CityCodeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @ClassName：CompensationImplBasicDataServiceImpl
 * @Description：赔付模块-使用的公共接口【如：三字码下拉获取】
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/15 14:53
 * @version： v1.0
 */
@Service
@Slf4j
public class CompensationImplBasicDataServiceImpl implements CompensationImplFltPaxDataService {

    @Autowired
    private TerminalInfoMapper terminalInfoMapper;

    @Autowired
    private CompensationBasicDataService compensationBasicDataService;
    @Override
    public List<CityCodeVO> getTerminal() {
        return terminalInfoMapper.getTerminal();
    }


    @Override
    public String getAlternateAndStop(String date, String flightNo) {
        StringBuffer result = new StringBuffer();
        List<SegmentFindVO> segment = compensationBasicDataService.findFltSegment(date, flightNo);
        FlightBaseQueryDTO queryDTO = new FlightBaseQueryDTO();
        queryDTO.setFlightNo(flightNo);
        queryDTO.setFlightDate(date);
        List<FlightBasicnfoVO> basicInfo = compensationBasicDataService.getFlightBasicInfo(queryDTO);

        List<String> alternates = basicInfo.stream()
                .filter(d->StringUtils.isNotEmpty(d.getAlternateAirport()) && StringUtils.isNotEmpty(d.getAlternateAirport2()))
                .map(basicnfoVO -> basicnfoVO.getAlternateAirportCh() +basicnfoVO.getAlternateAirport()
                        + "-" + basicnfoVO.getAlternateAirport2Ch()+basicnfoVO.getAlternateAirport2())
                .collect(Collectors.toList());
        //大于1说明存在经停点
        if(segment.size()>1){
            if(segment.get(0).getArrivalPort().equals(segment.get(1).getDepartPort())){
                result.append(segment.get(0).getArrivalPortCH());
            }else{
                result.append(segment.get(0).getDepartPortCH());
            }
        }
        //
        for (String alternate : alternates) {
            if(ObjectUtils.isNotEmpty(alternate.replace("-",""))){
                if(result.length()>0){
                    result.append(",").append(alternate);
                }else {
                    result.append(alternate);
                }
            }
        }
        return result.toString();
    }

    @Override
    public Set<String> getFltAirStation(String date, String flightNo, String choiceSegment) {
        //TODO 始发航站，到达航站，计划备降航站，经停站， 实际备降航站
        Set<String> set = new HashSet<>();
        FlightFindVO flight =compensationBasicDataService.getFlight(date, flightNo, choiceSegment);
        if(ObjectUtils.isNotEmpty(flight)){
            set.addAll(flight.getStopoverStation());
            set.add(flight.getPoa());
            set.add(flight.getPod());
        }
        if(set.size()>0){
            Set<String> resultSet = new HashSet<>();
            List<CityCodeVO> terminal = terminalInfoMapper.getTerminal();
            for(String code :set){
                String cityNameCode = code;
                for(CityCodeVO codeVO:terminal){
                    if(codeVO.getCityNameCode().contains(code) && StringUtils.isNotEmpty(code)){
                        cityNameCode = codeVO.getCityNameCode();
                        break;
                    }
                }
                resultSet.add(cityNameCode);
            }
            return resultSet;
        }
        return set;
    }
}
