package com.swcares.aps.compensation.impl.apply.controller;

import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.ReceivingRecordService;
import com.swcares.aps.compensation.model.apply.dto.ApplyGetReceiveDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyQueryRecordDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyRecordCaptchaDTO;
import com.swcares.aps.compensation.model.apply.dto.ReplaceFilterRecordDTO;
import com.swcares.aps.compensation.model.apply.vo.ApplyDetailsInfoVO;
import com.swcares.aps.compensation.model.apply.vo.ReceivingRecordVO;
import com.swcares.aps.compensation.model.apply.vo.ReplaceDetailsInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：领取记录模块 <br>
 * Package：com.swcares.aps.apply.impl.controller <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月07日 9:25 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/apply/receive")
@Api(tags = "自助申领记录查询")
@ApiVersion(value = "自助申领记录查询接口 v1.0")
public class ReceivingRecordController extends BaseController{

    @Autowired
    private ReceivingRecordService receivingRecordService;

    @PostMapping("/myRecord")
    @ApiOperation(value = "本人领取列表查询")
    public PagedResult<List<ReceivingRecordVO>> myRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO){
        return ok(receivingRecordService.queryRecord(applyQueryRecordDTO));
    }

    @PostMapping("/receive")
    @ApiOperation(value = "本人领取记录筛选查询")
    public PagedResult<List<ReceivingRecordVO>> getReceive(@RequestBody @Valid ApplyGetReceiveDTO applyGetReceiveDTO){
        return ok(receivingRecordService.getReceive(applyGetReceiveDTO));
    }

    @PostMapping("/queryRecord")
    @ApiOperation(value = "申领记录查询")
    public BaseResult queryRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO){
        return BaseResult.ok(receivingRecordService.myRecord(applyQueryRecordDTO));
    }

    @GetMapping("/queryDetails")
    @ApiOperation(value = "本人详情查询")
    public BaseResult<ApplyDetailsInfoVO> queryDetails(@RequestParam String recordId,@RequestParam String idCard){
        return BaseResult.ok(receivingRecordService.queryDetails(recordId,idCard));
    }

    @PostMapping("/replaceRecord")
    @ApiOperation(value = "代人领取查询")
    public PagedResult<List<ReceivingRecordVO>> replaceRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO){
        return ok(receivingRecordService.queryReplaceRecord(applyQueryRecordDTO));
    }

    @PostMapping("/replaceFilterRecord")
    @ApiOperation(value = "代人领取筛选查询")
    public PagedResult<List<ReceivingRecordVO>> replaceFilterRecord(@RequestBody @Valid ReplaceFilterRecordDTO replaceFilterRecordDTO){
        return ok(receivingRecordService.replaceFilterRecord(replaceFilterRecordDTO));
    }

    @GetMapping("/queryReplaceDetails")
    @ApiOperation(value = "代人领取详情查询")
    public BaseResult<ReplaceDetailsInfoVO> queryReplaceDetails(@RequestParam String recordId,@RequestParam String idCard){
        return BaseResult.ok(receivingRecordService.queryReplaceDetails(recordId,idCard));
    }

    @PostMapping("/getCaptcha")
    @ApiOperation(value = "申领记录查询-获取图形验证码")
    public BaseResult<Object> getCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto) {
        return receivingRecordService.getCaptchaByApplyRecord(dto);
    }
    @PostMapping("/validateCaptcha")
    @ApiOperation(value = "申领记录查询-验证图形验证码")
    public BaseResult<Object> validateCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto) {
        if(StringUtils.isEmpty(dto.getCaptchaVerification()) || StringUtils.isEmpty(dto.getToken())){
            throw new BusinessException(ApplyErrors.APPLY_PARAM_ERROR);
        }
        return receivingRecordService.validateCaptchaByApplyRecord(dto);
    }

}
