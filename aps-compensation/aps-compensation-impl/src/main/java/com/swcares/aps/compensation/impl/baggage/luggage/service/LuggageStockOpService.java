package com.swcares.aps.compensation.impl.baggage.luggage.service;

import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensateStockDTO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageStockInfoDTO;

/**
 * ClassName：com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockService <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/4 <br>
 * @version v1.0 <br>
 */
public interface LuggageStockOpService {
    /**
     * Title：补偿单添加库存 <br>
     * Description：实物（箱包）补偿单 审核不通过、关闭未领取、逾期未领取 需要补回库存 <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageCompensateStockDTO <br>
     * @return  <br>
     */
    void increaseByCompensation(LuggageCompensateStockDTO luggageCompensateStockDTO);

    /**
     * Title： 补偿单减少库存<br>
     * Description：提交实物（箱包）补偿单时需要扣减库存  <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageCompensateStockDTO <br>
     * @return  <br>
     */
    void decreaseByCompensation(LuggageCompensateStockDTO luggageCompensateStockDTO);

    /**
     * Title：箱包库存数量修改-增加/减少 <br>
     * Description：箱包库存数量修改-增加/减少 <br>
     * author：chenmd <br>
     * date：2022/3/4 <br>
     * @param luggageStockInfoDTO  <br>
     * @return  <br>
     */
    Long updateLuggageStock(LuggageStockInfoDTO luggageStockInfoDTO);
}
