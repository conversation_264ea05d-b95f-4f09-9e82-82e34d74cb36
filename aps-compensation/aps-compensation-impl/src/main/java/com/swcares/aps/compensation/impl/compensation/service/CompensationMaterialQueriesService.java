package com.swcares.aps.compensation.impl.compensation.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialDetailDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialListDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationBaseExtMaterialVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailFinalVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationMaterialQueriesListVO;
import org.springframework.stereotype.Service;

/**
 * @ClassName：CommpensationRepresentation
 * @Description：箱包赔偿单查询Service
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 唐康
 * @Date： 2022/4/13 16:44
 * @version： v1.0
 */
@Service
public interface CompensationMaterialQueriesService {

    IPage<CompensationMaterialQueriesListVO> findCompensationLuggageList(CompensationMaterialListDTO dto);

    CompensationMaterialDetailFinalVO findCompensationLuggageDetailInfo(CompensationMaterialDetailDTO dto);
}
