package com.swcares.aps.compensation.impl.datasync.service.push;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyAuditRecordMapper;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyPaxMapper;
import com.swcares.aps.compensation.impl.apply.mapper.PayRecordMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportAccidentRelMapper;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageTransportInfoMapper;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialQueriesMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationRuleConfigService;
import com.swcares.aps.compensation.impl.complaint.mapper.ComplaintAccidentInfoMapper;
import com.swcares.aps.compensation.impl.complaint.mapper.PassengerAccidentInfoMapper;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.mapper.DataPushRecordMapper;
import com.swcares.aps.compensation.impl.datasync.service.push.BasicPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.mapper.*;
import com.swcares.aps.compensation.impl.overbook.mapper.OverBookAccidentInfoMapper;
import com.swcares.aps.compensation.impl.passengerCategory.service.impl.PassengerCategoryServiceImpl;
import com.swcares.aps.compensation.impl.privilege.config.CoordinateConfig;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapperForCoordinate;
import com.swcares.aps.compensation.model.apply.entity.ApplyAuditDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportAccidentRelDO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageTransportInfoDO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.complaint.entity.ComplaintAccidentInfoEntity;
import com.swcares.aps.compensation.model.complaint.entity.PassengerAccidentInfoEntity;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.datasync.dto.BusinessDataSyncDTO;
import com.swcares.aps.compensation.model.datasync.entity.DataPushRecordEntity;
import com.swcares.aps.compensation.model.irregularflight.entity.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.compensation.model.rools.entity.CompensationRuleConfig;
import com.swcares.aps.cpe.coordinate.model.enums.DataCategoryEnum;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessDataUploadDTO;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.CustomerDTO;
import com.swcares.aps.workflow.dto.StartSyncWorkflowInfoDTO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.minio.util.MinIOUtil;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.Nullable;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.lang.model.util.ElementScanner6;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.util.*;

/**
 * @ClassName：BusinessDataPushHandler
 * @Description：【机场端业务数据-》推送给航司端】业务数据推送，数据类型有哪些参见：DataCategoryEnum
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/7/11 10:17
 * @version： v1.0
 */
@Component("businessDataPushHandler")
@Slf4j
public class BusinessDataPushHandler extends BasicPushHandler {

    @Autowired
    CoordinateConfig coordinateConfig;

    @Autowired
    DataPushRecordMapper dataPushRecordMapper;

    @Autowired
    WorkflowApi workflowApi;

    @Autowired
    ConfigurationDataPushHandler configurationDataPushHandler;

    /***
     * @title processBusinessDataPush
     * @description 处理推送业务数据
     * @See DataCategoryEnum
     * <AUTHOR>
     * @date 2024/7/11 13:57
     * @param dataSyncDTO
     * @return void
     */
    public boolean processBusinessDataPush(BusinessDataSyncDTO dataSyncDTO) throws Exception {
        log.info("【航司端-给机场端推送业务数据】 业务数据：【{}】",JSON.toJSONString(dataSyncDTO));

        BusinessDataUploadDTO coordinateDTO = buildCoordinateDTO(dataSyncDTO);

        return doDataPush(coordinateConfig, coordinateDTO, DataCategoryEnum.BUSINESS_DATA.getCode());
    }

    private BusinessDataUploadDTO buildCoordinateDTO(BusinessDataSyncDTO dataSyncDTO){
        BusinessDataUploadDTO coordinateDTO = new BusinessDataUploadDTO();
        coordinateDTO.setBusinessType(dataSyncDTO.getBusinessType());  //业务类型
        coordinateDTO.setDataType(dataSyncDTO.getDataType());          //数据类型
        coordinateDTO.setSenderCustomer(dataSyncDTO.getSenderCustomer());          //发送方租户代码
        coordinateDTO.setSenderCustomerCategory(BasicPushHandler.AIRLINE);  //发送方租户类型
        coordinateDTO.setReceiverCustomer(dataSyncDTO.getReceiverCustomer());       //接收方租户代码
        coordinateDTO.setReceiverCustomerCategory(BasicPushHandler.AIRPORT);  //接收方租户类型
        coordinateDTO.setData(JSON.toJSONString(dataSyncDTO));   //实际的业务数据，发送方的应用端和接收的应用端要事先约定好格式
        return coordinateDTO;
    }


    public void dataStore(Long businessId, String businessType, String pushType) {
        LoginUserDetails user = UserContext.getUser();
        String tenantCode="";
        Long tenantId=null;
        if(user!=null){
            tenantId=user.getTenantId();
            tenantCode=user.getTenantCode();
        }else{
            tenantId=TenantHolder.getTenant();
            Map<String, Object> mapperTenantById = compensationOrderInfoMapper.getTenantById(tenantId);
            tenantCode=(String) mapperTenantById.get("TENANT_CODE");;
        }
        dataPushRecordMapper.insert(DataPushRecordEntity.builder()
                .businessId(businessId)
                .businessType(businessType)
                .pushType(pushType)
                .tenantCode(tenantCode)
                .tenantId(tenantId)
                .createdTime(new Date())
                .pushStatus(BusinessDataSyncConstant.PUSH_STATUS_WAIT)
                .build());
    }

    private void updatePushStatus(DataPushRecordEntity dataPushRecordEntity, String pushStatusFail) {
        dataPushRecordEntity.setPushStatus(pushStatusFail);
        dataPushRecordMapper.updateById(dataPushRecordEntity);
    }

    public void coordinateDataPushTask() {
        log.info("[BusinessDataPushHandler.coordinateDataPushTask 定时任务推送业务数据]----begin：-----------");
        LambdaQueryChainWrapper<DataPushRecordEntity> query = new LambdaQueryChainWrapper<>(dataPushRecordMapper);
        Page<DataPushRecordEntity> page = query.eq(DataPushRecordEntity::getPushStatus, BusinessDataSyncConstant.PUSH_STATUS_WAIT).page(new Page<>(1, 10));
        List<DataPushRecordEntity> dataPushRecordEntities = page.getRecords();
        if (ObjectUtil.isEmpty(dataPushRecordEntities) || dataPushRecordEntities.size() == 0) {
            return;
        }
        log.info("[BusinessDataPushHandler.coordinateDataPushTask 定时任务推送业务数据]----begin：wait数据条数:{}-----------",dataPushRecordEntities.size());
        for (DataPushRecordEntity dataPushRecordEntity : dataPushRecordEntities) {
            TenantHolder.setTenant(dataPushRecordEntity.getTenantId());
            // 业务类型和推送类型来判断怎么做
            switch (dataPushRecordEntity.getBusinessType()) {
                case BusinessDataSyncConstant.BUSINESS_IRREGULAR_FLIGHT:
                    switch (dataPushRecordEntity.getPushType()) {
                        case BusinessDataSyncConstant.DATA_TYPE_ACCIDENT:
//                            flightAccidentDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_COMPENSATION:
                            flightCompensationDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_APPLY:
                            break;
                    }
                    break;
                case BusinessDataSyncConstant.BUSINESS_COMPLAINT:
                    switch (dataPushRecordEntity.getPushType()) {
                        case BusinessDataSyncConstant.DATA_TYPE_ACCIDENT:
//                            complaintAccidentDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_COMPENSATION:
                            complaintCompensationDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_APPLY:
                            break;
                    }
                    break;
                case BusinessDataSyncConstant.BUSINESS_ABNORMAL_LUGGAGE:
                    switch (dataPushRecordEntity.getPushType()) {
                        case BusinessDataSyncConstant.DATA_TYPE_ACCIDENT:
//                            baggageAccidentDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_COMPENSATION:
                            baggageCompensationDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_TRANSPORT:
                            baggageTransportDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_APPLY:
                            break;
                    }
                    break;
                case BusinessDataSyncConstant.BUSINESS_OVER_BOOKING:
                    switch (dataPushRecordEntity.getPushType()) {
                        case BusinessDataSyncConstant.DATA_TYPE_ACCIDENT:
//                            overBookAccidentDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_COMPENSATION:
                            overBookCompensationDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_APPLY:
                            break;
                    }
                    break;
                case BusinessDataSyncConstant.BUSINESS_BASE_INFO:
                    switch (dataPushRecordEntity.getPushType()){
                        case BusinessDataSyncConstant.DATA_TYPE_RULE:
                            basicDataRuleDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.SERVICE_SUPPORT:
                            serviceSupportDataPush(dataPushRecordEntity);
                            break;
                        case BusinessDataSyncConstant.DATA_TYPE_REPLACE:
                            replaceDataPush(dataPushRecordEntity);
                            break;
                    }
                    break;

                default:
                    break;
            }
            //因为可以一次申领多个业务，无需判断业务类型
            if(BusinessDataSyncConstant.DATA_TYPE_APPLY.equals(dataPushRecordEntity.getPushType())){
                applyOrderDataPush(dataPushRecordEntity);
            }
            log.info("[BusinessDataPushHandler.coordinateDataPushTask 定时任务推送业务数据]----end：-----------");
        }
    }

    @Autowired
    private ReplaceConfigService replaceConfigService;
    private void replaceDataPush(DataPushRecordEntity dataPushRecordEntity) {
        List<DataConfigDO> dataConfigDos = new ArrayList<>();
        if (dataPushRecordEntity.getBusinessId() != null && dataPushRecordEntity.getBusinessId() != 0){
            DataConfigDO dataConfigDo = replaceConfigService.getById(dataPushRecordEntity.getBusinessId());
            dataConfigDos.add(dataConfigDo);
        }else{
            dataConfigDos.addAll(replaceConfigService.list());
        }
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder().senderCustomer(dataPushRecordEntity.getTenantCode())
                .dataType(BusinessDataSyncConstant.DATA_TYPE_REPLACE)
                .dataConfigDos(dataConfigDos).build();
        try {
            configurationDataPushHandler.processConfigurationDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---DATA_TYPE_REPLACE数据，同步数据异常：businessId[{}]",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }


    @Autowired
    BusinessPrivilegeMapperForCoordinate businessPrivilegeMapper;
    @Autowired
    CompensationRuleConfigService compensationRuleConfigService;

    private void basicDataRuleDataPush(DataPushRecordEntity dataPushRecordEntity) {
        // 数据推送，首先我得知道推送给哪些机场(业务授权)，一对多的关系
        LambdaQueryWrapper<AirlineBusinessPrivilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirlineBusinessPrivilege::getGrantorCode, dataPushRecordEntity.getTenantCode());
        queryWrapper.eq(AirlineBusinessPrivilege::getTenantId, dataPushRecordEntity.getTenantId());
        LambdaQueryWrapper<CompensationRuleConfig> lq = Wrappers.lambdaQuery();
        LambdaQueryWrapper<CompensationRuleConfig> eq = lq.eq(CompensationRuleConfig::getAirlineCode, dataPushRecordEntity.getTenantCode());
        List<CompensationRuleConfig> list = compensationRuleConfigService.list(eq);
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder().senderCustomer(dataPushRecordEntity.getTenantCode())
                .dataType(BusinessDataSyncConstant.DATA_TYPE_RULE)
                .compensationRuleConfigs(list).build();
        try {
            configurationDataPushHandler.processConfigurationDataPush(dataSyncDTO);
            //processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---规则数据，同步数据异常：businessId[{}]",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Autowired
    PassengerCategoryServiceImpl passengerCategoryService;
    private void serviceSupportDataPush(DataPushRecordEntity dataPushRecordEntity) {
        // 数据推送，首先我得知道推送给哪些机场(业务授权)，一对多的关系
        LambdaQueryWrapper<PassengerCategoryConfigureDepository> lq = Wrappers.lambdaQuery();
        if (dataPushRecordEntity.getBusinessId() != null && dataPushRecordEntity.getBusinessId() != 0){
            lq = lq.eq(PassengerCategoryConfigureDepository::getId, dataPushRecordEntity.getBusinessId());
        }
        List<PassengerCategoryConfigureDepository> list = passengerCategoryService.list(lq);
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder().senderCustomer(dataPushRecordEntity.getTenantCode())
                .dataType(BusinessDataSyncConstant.SERVICE_SUPPORT)
                .passengerCategoryConfigureDepositories(list).build();
        try {
            configurationDataPushHandler.processConfigurationDataPush(dataSyncDTO);
            //processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---SERVICE_SUPPORT数据，同步数据异常：businessId[{}]",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Autowired
    FlightAccidentInfoMapper flightAccidentInfoMapper;
    private void flightAccidentDataPush(DataPushRecordEntity dataPushRecordEntity) {
        FlightAccidentInfoDO flightAccidentInfoDO = flightAccidentInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(flightAccidentInfoDO.getFlightNo().substring(0,2))
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .flightAccidentInfoDO(flightAccidentInfoDO)
                .build();
        try {
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---SERVICE_SUPPORT数据，同步数据异常：businessId[{}]",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Autowired
    CompensationOrderInfoMapper compensationOrderInfoMapper;

    @Autowired
    CompensationPaxInfoMapper compensationPaxInfoMapper;

    @Autowired
    CompensationFlightInfoMapper compensationFlightInfoMapper;

    private void flightCompensationDataPush(DataPushRecordEntity dataPushRecordEntity) {
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        FlightAccidentInfoDO flightAccidentInfoDO = flightAccidentInfoMapper.selectById(compensationOrderInfoDO.getAccidentId());
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = new LambdaQueryChainWrapper<>(compensationPaxInfoMapper).eq(CompensationPaxInfoDO::getOrderId,compensationOrderInfoDO.getId()).list();
        CompensationFlightInfoDO compensationFlightInfoDO = new LambdaQueryChainWrapper<>(compensationFlightInfoMapper).eq(CompensationFlightInfoDO::getOrderId,compensationOrderInfoDO.getId()).one();
        List<CompensationRuleRecordDO> compensationRuleRecordDOS = new LambdaQueryChainWrapper<>(compensationRuleRecordMapper).eq(CompensationRuleRecordDO::getOrderId,compensationOrderInfoDO.getId()).list();
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(compensationOrderInfoDO.getSourceTenantCode()) //接收方code 机场code
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code 航司code
                .compensationOrderInfoDO(compensationOrderInfoDO)
                .flightAccidentInfoDO(flightAccidentInfoDO)
                .compensationPaxInfoDO(compensationPaxInfoDOS)
                .compensationRuleRecordDO(compensationRuleRecordDOS)
                .compensationFlightInfoDO(compensationFlightInfoDO)
                .build();
        try {
            StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO = createStartSyncWorkflowInfoDTO(compensationOrderInfoDO);
            dataSyncDTO.setStartSyncWorkflowInfoDTO(startSyncWorkflowInfoDTO);
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }


    @Autowired
    ComplaintAccidentInfoMapper complaintAccidentInfoMapper;

    @Autowired
    PassengerAccidentInfoMapper passengerAccidentInfoMapper;
    private void complaintAccidentDataPush(DataPushRecordEntity dataPushRecordEntity) {
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = complaintAccidentInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        List<PassengerAccidentInfoEntity> passengerAccidentInfoEntities  = new LambdaQueryChainWrapper<>(passengerAccidentInfoMapper)
                .eq(PassengerAccidentInfoEntity::getAccidentPrimaryId,complaintAccidentInfoEntity.getId())
                .list();
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(complaintAccidentInfoEntity.getFlightNo().substring(0,2))
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .complaintAccidentInfoEntity(complaintAccidentInfoEntity)
                .passengerAccidentInfoEntity(passengerAccidentInfoEntities)
                .build();
        try {
            //处理附件图片
            handleComplaintFileUrl(complaintAccidentInfoEntity,dataSyncDTO);

            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }


    private void complaintCompensationDataPush(DataPushRecordEntity dataPushRecordEntity){
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        ComplaintAccidentInfoEntity complaintAccidentInfoEntity = complaintAccidentInfoMapper.selectById(compensationOrderInfoDO.getAccidentId());
        List<PassengerAccidentInfoEntity> passengerAccidentInfoEntities = new LambdaQueryChainWrapper<>(passengerAccidentInfoMapper).eq(PassengerAccidentInfoEntity::getAccidentPrimaryId,complaintAccidentInfoEntity.getId()).list();
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = new LambdaQueryChainWrapper<>(compensationPaxInfoMapper).eq(CompensationPaxInfoDO::getOrderId,compensationOrderInfoDO.getId()).list();
        CompensationFlightInfoDO compensationFlightInfoDO = new LambdaQueryChainWrapper<>(compensationFlightInfoMapper).eq(CompensationFlightInfoDO::getOrderId,compensationOrderInfoDO.getId()).one();

        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(compensationOrderInfoDO.getSourceTenantCode()) //接收方code 机场code
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .complaintAccidentInfoEntity(complaintAccidentInfoEntity)
                .compensationOrderInfoDO(compensationOrderInfoDO)
                .compensationFlightInfoDO(compensationFlightInfoDO)
                .compensationPaxInfoDO(compensationPaxInfoDOS)
                .passengerAccidentInfoEntity(passengerAccidentInfoEntities)
                .build();
        try {
            //处理附件图片
            handleComplaintFileUrl(complaintAccidentInfoEntity,dataSyncDTO);

            StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO = createStartSyncWorkflowInfoDTO(compensationOrderInfoDO);
            dataSyncDTO.setStartSyncWorkflowInfoDTO(startSyncWorkflowInfoDTO);
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Autowired
    BaggageAccidentMapper baggageAccidentMapper;

    private void baggageAccidentDataPush(DataPushRecordEntity dataPushRecordEntity) {
        BaggageAccidentInfoDO baggageAccidentInfoDO = baggageAccidentMapper.selectById(dataPushRecordEntity.getBusinessId());
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(baggageAccidentInfoDO.getPaxFlightNo().substring(0,2))
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .baggageAccidentInfoDO(baggageAccidentInfoDO)
                .build();

        try {
            //处理附件图片
            handleBaggageFileUrl(baggageAccidentInfoDO,dataSyncDTO);

            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---异常行李事故单，同步数据异常：businessId【{}】",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Autowired
    CompensationRuleRecordMapper compensationRuleRecordMapper;

    @Autowired
    CompensationMaterialQueriesMapper compensationMaterialQueriesMapper;
    private void baggageCompensationDataPush(DataPushRecordEntity dataPushRecordEntity){
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        BaggageAccidentInfoDO baggageAccidentInfoDO = baggageAccidentMapper.selectById(compensationOrderInfoDO.getAccidentId());
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = new LambdaQueryChainWrapper<>(compensationPaxInfoMapper).eq(CompensationPaxInfoDO::getOrderId,compensationOrderInfoDO.getId()).list();
        CompensationFlightInfoDO compensationFlightInfoDO = new LambdaQueryChainWrapper<>(compensationFlightInfoMapper).eq(CompensationFlightInfoDO::getOrderId,compensationOrderInfoDO.getId()).one();
        List<CompensationRuleRecordDO> compensationRuleRecordDOS = new LambdaQueryChainWrapper<>(compensationRuleRecordMapper).eq(CompensationRuleRecordDO::getOrderId,compensationOrderInfoDO.getId()).list();
        List<CompensationMaterialInfoDO> compensationMaterialInfoDOS = new LambdaQueryChainWrapper<>(compensationMaterialQueriesMapper).eq(CompensationMaterialInfoDO::getOrderId,compensationOrderInfoDO.getId()).list();
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(compensationOrderInfoDO.getSourceTenantCode()) //接收方code 机场code
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .compensationOrderInfoDO(compensationOrderInfoDO)
                .compensationFlightInfoDO(compensationFlightInfoDO)
                .compensationPaxInfoDO(compensationPaxInfoDOS)
                .compensationRuleRecordDO(compensationRuleRecordDOS)
                .compensationMaterialInfoDO(compensationMaterialInfoDOS)
                .baggageAccidentInfoDO(baggageAccidentInfoDO)
                .build();
        try {
            //处理附件图片
            handleBaggageFileUrl(baggageAccidentInfoDO,dataSyncDTO);

            StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO = createStartSyncWorkflowInfoDTO(compensationOrderInfoDO);
            dataSyncDTO.setStartSyncWorkflowInfoDTO(startSyncWorkflowInfoDTO);
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---异常行李补偿单，同步数据异常：businessId【{}】",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }


    @Autowired
    OverBookAccidentInfoMapper overBookAccidentInfoMapper;
    private void overBookAccidentDataPush(DataPushRecordEntity dataPushRecordEntity) {
        OverBookAccidentInfoDO overBookAccidentInfoDO = overBookAccidentInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(overBookAccidentInfoDO.getFlightNo().substring(0,2))
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .overBookAccidentInfoDO(overBookAccidentInfoDO)
                .build();
        try {
            //处理附件图片
            handleOverBookFileUrl(overBookAccidentInfoDO,dataSyncDTO);

            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    private void overBookCompensationDataPush(DataPushRecordEntity dataPushRecordEntity){
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        OverBookAccidentInfoDO overBookAccidentInfoDO = overBookAccidentInfoMapper.selectById(compensationOrderInfoDO.getAccidentId());
        CompensationFlightInfoDO compensationFlightInfoDO = new LambdaQueryChainWrapper<>(compensationFlightInfoMapper).eq(CompensationFlightInfoDO::getOrderId,compensationOrderInfoDO.getId()).one();
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = new LambdaQueryChainWrapper<>(compensationPaxInfoMapper).eq(CompensationPaxInfoDO::getOrderId,compensationOrderInfoDO.getId()).list();
        List<CompensationRuleRecordDO> compensationRuleRecordDOS = new LambdaQueryChainWrapper<>(compensationRuleRecordMapper).eq(CompensationRuleRecordDO::getOrderId,compensationOrderInfoDO.getId()).list();
        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(compensationOrderInfoDO.getSourceTenantCode()) //接收方code 机场code
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .compensationOrderInfoDO(compensationOrderInfoDO)
                .compensationFlightInfoDO(compensationFlightInfoDO)
                .compensationPaxInfoDO(compensationPaxInfoDOS)
                .compensationRuleRecordDO(compensationRuleRecordDOS)
                .overBookAccidentInfoDO(overBookAccidentInfoDO)
                .build();



        try {
            //处理附件图片
            handleOverBookFileUrl(overBookAccidentInfoDO,dataSyncDTO);

            StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO = createStartSyncWorkflowInfoDTO(compensationOrderInfoDO);
            dataSyncDTO.setStartSyncWorkflowInfoDTO(startSyncWorkflowInfoDTO);
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：航司端-》机场端】---航班超售，同步数据异常：businessId【{}】",dataPushRecordEntity.getBusinessId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }

    @Nullable
    private StartSyncWorkflowInfoDTO createStartSyncWorkflowInfoDTO(CompensationOrderInfoDO compensationOrderInfoDO) {
        BaseQueryParamDTO queryParamDTO = BaseQueryParamDTO.builder().businessKey(String.valueOf(compensationOrderInfoDO.getId())).build();
        CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(queryParamDTO).getData();
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        CustomerDTO customerDTO = workflowApi
                .getCustomerVariable(currentTaskActivityVO.getProcessInstanceId()).getData();
        StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO=null;
        if(customerDTO!=null){
            startSyncWorkflowInfoDTO = StartSyncWorkflowInfoDTO
                    .build(currentTaskActivityDTO,
                            customerDTO,
                            currentTaskActivityVO.getProcessInstanceId());

        }
        return startSyncWorkflowInfoDTO;
    }

    @Nullable
    private StartSyncWorkflowInfoDTO createStartSyncWorkflowInfoDTO(String businessKey) {
        BaseQueryParamDTO queryParamDTO = BaseQueryParamDTO.builder().businessKey(businessKey).build();
        CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(queryParamDTO).getData();
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        CustomerDTO customerDTO = workflowApi
                .getCustomerVariable(currentTaskActivityVO.getProcessInstanceId()).getData();
        StartSyncWorkflowInfoDTO startSyncWorkflowInfoDTO=null;
        if(customerDTO!=null){
            startSyncWorkflowInfoDTO = StartSyncWorkflowInfoDTO
                    .build(currentTaskActivityDTO,
                            customerDTO,
                            currentTaskActivityVO.getProcessInstanceId());

        }
        return startSyncWorkflowInfoDTO;
    }

    @Autowired
    ApplyOrderMapper applyOrderMapper;
    @Autowired
    ApplyPaxMapper applyPaxMapper;
    @Autowired
    ApplyAuditRecordMapper applyAuditRecordMapper;
    @Autowired
    PayRecordMapper payRecordMapper;

    private void applyOrderDataPush(DataPushRecordEntity dataPushRecordEntity){
        //申领单
        ApplyOrderDO applyOrderDO = applyOrderMapper.selectById(dataPushRecordEntity.getBusinessId());
        //申领旅客
        LambdaQueryWrapper<ApplyPaxDO> queryWrapper = Wrappers.lambdaQuery(ApplyPaxDO.class);
        queryWrapper.eq(ApplyPaxDO::getApplyId,applyOrderDO.getId());
        List<ApplyPaxDO> applyPaxDOList = applyPaxMapper.selectList(queryWrapper);
        //目前需求已改为最小颗粒保存申领单信息，一个申领单对应一个补偿单对应一个旅客applyPaxDOList只有一条数据。
        //取申领的补偿单id，获取【接收方客户代码】机场的code
        CompensationOrderInfoDO orderInfoDO = compensationOrderInfoMapper.selectById(applyPaxDOList.get(0).getOrderId());
        //申领审核记录
        LambdaQueryWrapper<ApplyAuditDO> queryWrapper1 = Wrappers.lambdaQuery(ApplyAuditDO.class);
        queryWrapper1.eq(ApplyAuditDO::getApplyId,applyOrderDO.getId());
        List<ApplyAuditDO> applyAuditDOS = applyAuditRecordMapper.selectList(queryWrapper1);
        //支付记录
        LambdaQueryWrapper<PayRecordDO> queryWrapper2 = Wrappers.lambdaQuery(PayRecordDO.class);
        queryWrapper2.eq(PayRecordDO::getApplyId,applyOrderDO.getId());
        List<PayRecordDO> payRecordDOS = payRecordMapper.selectList(queryWrapper2);
        List<CompensationPaxInfoDO> compensationPaxInfoDOS = new ArrayList<>();
        try {
            for(ApplyPaxDO applyPaxDO:applyPaxDOList){
                CompensationPaxInfoDO paxInfoDO = new LambdaQueryChainWrapper<>(compensationPaxInfoMapper)
                        .eq(CompensationPaxInfoDO::getOrderId, applyPaxDO.getOrderId())
                        .eq(CompensationPaxInfoDO::getId, applyPaxDO.getPaxInfoId()).one();
                compensationPaxInfoDOS.add(paxInfoDO);

                //处理代领旅客证件图片
                if(applyPaxDO.getPaxIdentityCardPhoto() != null){
                    applyPaxDO.setPaxIdentityCardPhoto(getImageBase64AsString(Long.valueOf(applyPaxDO.getPaxIdentityCardPhoto())));
                }

            }
            //处理申领单签名图片
            if(StringUtils.isNotEmpty(applyOrderDO.getSignFile())){
                applyOrderDO.setSignFile(getImageBase64AsString(Long.valueOf(applyOrderDO.getSignFile())));
            }
            //处理代领人证件图片
            if(StringUtils.isNotEmpty(applyOrderDO.getCollectIdentityCardPhoto())){
                applyOrderDO.setCollectIdentityCardPhoto(getImageBase64AsString(Long.valueOf(applyOrderDO.getCollectIdentityCardPhoto())));
            }

            BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                    .businessType(dataPushRecordEntity.getBusinessType())
                    .dataType(dataPushRecordEntity.getPushType())
                    .receiverCustomer(orderInfoDO.getSourceTenantCode()) //接收方客户代码  机场code
                    .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code 航司code
                    .applyOrderDO(applyOrderDO)
                    .applyPaxDOList(applyPaxDOList)
                    .applyAuditDOS(CollectionUtils.isEmpty(applyAuditDOS)?null:applyAuditDOS)
                    .payRecordDOS(payRecordDOS)
                    .compensationPaxInfoDO(compensationPaxInfoDOS)
                    .build();


            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【申领信息同步：航司端-》机场端】---同步数据异常：申领单id【{}】",applyOrderDO.getId(),e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" +e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);

    }

    private void handleBaggageFileUrl(BaggageAccidentInfoDO baggageAccidentInfoDO,BusinessDataSyncDTO dataSyncDTO){
        //处理附件图片
        BusinessDataSyncDTO.BusinessImgDO imgDO = new BusinessDataSyncDTO.BusinessImgDO();
        if(StringUtils.isNotEmpty(baggageAccidentInfoDO.getCollectIdentityBaggagePhotos())){
            List<String> fileUrl = new ArrayList<>();
            String[] split = baggageAccidentInfoDO.getCollectIdentityBaggagePhotos().split(",");
            for(String img :split){
                fileUrl.add(getImageBase64AsString(Long.valueOf(img)));
            }
            if(fileUrl.size()>0){
                imgDO.setBaggageImgs(fileUrl);
            }
        }

        if(StringUtils.isNotEmpty(baggageAccidentInfoDO.getCollectIdentityPaxPhotos())){
            List<String> fileUrl = new ArrayList<>();
            String[] split = baggageAccidentInfoDO.getCollectIdentityPaxPhotos().split(",");
            for(String img :split){
                fileUrl.add(getImageBase64AsString(Long.valueOf(img)));
            }
            if(fileUrl.size()>0){
                imgDO.setPaxImgs(fileUrl);
            }
        }
        if(StringUtils.isNotEmpty(baggageAccidentInfoDO.getCollectIdentityVoucherPhotos())){
            List<String> fileUrl = new ArrayList<>();
            String[] split = baggageAccidentInfoDO.getCollectIdentityVoucherPhotos().split(",");
            for(String img :split){
                fileUrl.add(getImageBase64AsString(Long.valueOf(img)));
            }
            if(fileUrl.size()>0){
                imgDO.setVoucherImgs(fileUrl);
            }
        }
        dataSyncDTO.setBusinessImgDO(imgDO);
    }
    private void handleOverBookFileUrl(OverBookAccidentInfoDO overBookAccidentInfoDO,BusinessDataSyncDTO dataSyncDTO){
        //处理附件图片
        if(StringUtils.isNotEmpty(overBookAccidentInfoDO.getImgUrl())){
            List<String> fileUrl = new ArrayList<>();
            String[] split = overBookAccidentInfoDO.getImgUrl().split(",");
            for(String img :split){
                fileUrl.add(getImageBase64AsString(Long.valueOf(img)));
            }
            if(fileUrl.size()>0){
                BusinessDataSyncDTO.BusinessImgDO imgDO = new BusinessDataSyncDTO.BusinessImgDO();
                imgDO.setFileUrl(fileUrl);
                dataSyncDTO.setBusinessImgDO(imgDO);
            }
        }
    }
    private void handleComplaintFileUrl(ComplaintAccidentInfoEntity complaintAccidentInfoEntity,BusinessDataSyncDTO dataSyncDTO){
        //处理附件图片
        if(StringUtils.isNotEmpty(complaintAccidentInfoEntity.getFilesUrl())){
            List<String> fileUrl = new ArrayList<>();
            String[] split = complaintAccidentInfoEntity.getFilesUrl().split(",");
            for(String img :split){
                fileUrl.add(getImageBase64AsString(Long.valueOf(img)));
            }
            if(fileUrl.size()>0){
                BusinessDataSyncDTO.BusinessImgDO imgDO = new BusinessDataSyncDTO.BusinessImgDO();
                imgDO.setFileUrl(fileUrl);
                dataSyncDTO.setBusinessImgDO(imgDO);
            }
        }
    }

    @Autowired
    BaggageTransportInfoMapper baggageTransportInfoMapper;

    @Autowired
    BaggageTransportAccidentRelMapper baggageTransportAccidentRelMapper;

    /**
     * 行李运输单数据推送
     * @param dataPushRecordEntity 数据推送记录
     */
    private void baggageTransportDataPush(DataPushRecordEntity dataPushRecordEntity) {
        BaggageTransportInfoDO baggageTransportInfoDO = baggageTransportInfoMapper.selectById(dataPushRecordEntity.getBusinessId());
        if (baggageTransportInfoDO == null) {
            log.error("---【信息同步：机场端-》航司端】---行李运输单不存在：businessId[{}]", dataPushRecordEntity.getBusinessId());
            dataPushRecordEntity.setRemarks("行李运输单不存在");
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }

        // 查询关联的事故单信息
        BaggageAccidentInfoDO baggageAccidentInfoDO = baggageAccidentMapper.selectOne(
                new LambdaQueryWrapper<BaggageAccidentInfoDO>()
                        .eq(BaggageAccidentInfoDO::getAccidentNo, baggageTransportInfoDO.getAccidentNo()));

        // 查询运输单关联的事故单列表
        List<BaggageTransportAccidentRelDO> baggageTransportAccidentRelDOS = baggageTransportAccidentRelMapper.selectList(
                new LambdaQueryWrapper<BaggageTransportAccidentRelDO>()
                        .eq(BaggageTransportAccidentRelDO::getTransportId, baggageTransportInfoDO.getId()));

        // 确定接收方航司代码
        String receiverCustomer = null;
        if (baggageAccidentInfoDO != null && StringUtils.isNotEmpty(baggageAccidentInfoDO.getPaxFlightNo())
                && baggageAccidentInfoDO.getPaxFlightNo().length() >= 2) {
            receiverCustomer = baggageAccidentInfoDO.getPaxFlightNo().substring(0, 2);
        } else if (StringUtils.isNotEmpty(baggageTransportInfoDO.getBelongAirline())) {
            receiverCustomer = baggageTransportInfoDO.getBelongAirline();
        } else {
            log.error("---【信息同步：机场端-》航司端】---无法确定接收方航司：businessId[{}]", dataPushRecordEntity.getBusinessId());
            dataPushRecordEntity.setRemarks("无法确定接收方航司");
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }

        BusinessDataSyncDTO dataSyncDTO = BusinessDataSyncDTO.builder()
                .businessType(dataPushRecordEntity.getBusinessType())
                .dataType(dataPushRecordEntity.getPushType())
                .receiverCustomer(receiverCustomer)
                .senderCustomer(dataPushRecordEntity.getTenantCode()) // 发送方的code
                .baggageTransportInfoDO(baggageTransportInfoDO)
                .baggageAccidentInfoDO(baggageAccidentInfoDO)
                .baggageTransportAccidentRelDOS(baggageTransportAccidentRelDOS)
                .build();

        try {
            processBusinessDataPush(dataSyncDTO);
        } catch (Exception e) {
            log.error("---【信息同步：机场端-》航司端】---行李运输单，同步数据异常：businessId[{}]", dataPushRecordEntity.getBusinessId(), e);
            dataPushRecordEntity.setRemarks(e.getClass().getName() + ":" + e.getMessage());
            updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_FAIL);
            return;
        }
        updatePushStatus(dataPushRecordEntity, BusinessDataSyncConstant.PUSH_STATUS_SUCCESS);
    }















    @Autowired
    private FileAttachmentService fileAttachmentService;
    @Autowired
    private MinIOUtil minioUtil;
    /***
     * @title getImageBase64
     * @description 根据图片id获取图片base64格式
     * <AUTHOR>
     * @date 2024/7/23 15:46
     * @param imgs
     * @return java.util.List<java.lang.String>
     */
    private List<String> getImageBase64AsString(List<String> imgs){
        List<String> imageBase64 = new ArrayList<>();
        for(String id:imgs){
            imageBase64.add(getImageBase64AsString(Long.valueOf(id)));
        }
        return imageBase64;

    }


    public String getImageBase64AsString(Long id) {
        FileAttachment fileAttachment = fileAttachmentService.getById(id);
        if (fileAttachment == null) {
            throw new BusinessException(40114);
        }

        log.info("文件转换开始,文件名:{},文件原名称:{},大小:{}b",
                fileAttachment.getFileName(), fileAttachment.getOriginalName(), fileAttachment.getFileSize());

        try (InputStream inputStream = this.minioUtil.download(fileAttachment.getBucketName(), fileAttachment.getFileName())) {
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                baos.write(buffer, 0, length);
            }
            baos.flush();
            byte[] imageBytes = baos.toByteArray();
            baos.close();

            String base64Image = Base64.getEncoder().encodeToString(imageBytes);
            log.info("文件转换完成,文件名:{},文件原名称:{},大小:{}",
                    fileAttachment.getFileName(), fileAttachment.getOriginalName(), fileAttachment.getFileSize());
            return "data:image/png;base64,"+base64Image;

        } catch (Exception e) {
            log.error("文件转换错误", e);
            throw new BusinessException(40118);
        }
    }

}
