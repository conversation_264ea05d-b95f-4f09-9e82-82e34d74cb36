package com.swcares.aps.compensation.impl.compensation.controller;

import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderCommandsService;
import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationMaterialEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * @ClassName：CompensationOrderCommandsController
 * @Description：补偿单命令类接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 10:44
 * @version： v1.0
 */

@RestController
@RequestMapping("/compensation/command")
@Api(tags = "补偿单命令类接口")
@ApiVersion(value = "补偿单命令类接口 v1.0.1")
@Slf4j
public class CompensationOrderCommandsController extends BaseController {

    private CompensationOrderCommandsService compensationOrderCommandsService;

    private AccidentCompensationDomainService accidentCompensationDomainService;

    @Autowired
    public CompensationOrderCommandsController(CompensationOrderCommandsService compensationOrderCommandsService,
                                               AccidentCompensationDomainService accidentCompensationDomainService){
        this.compensationOrderCommandsService=compensationOrderCommandsService;
        this.accidentCompensationDomainService=accidentCompensationDomainService;
    }

    /**
     * @title addMaterialCompensation
     * @description 创建实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("material/add")
    @ApiOperation(value = "创建实物补偿单接口")
    public BaseResult<String> addMaterialCompensation(@RequestBody @Validated CompensationMaterialAddCommandDTO request){
        log.info("【创建实物补偿单接口】提交人：[{}],前端请求参数：[{}]", ApsUserUtils.getCreatedBy(),JSON.toJSONString(request));
        return ok(compensationOrderCommandsService.addMaterialCompensation(request));
    }

    /**
     * @title editMaterialCompensation
     * @description 编辑实物补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("material/edit")
    @ApiOperation(value = "编辑实物补偿单接口")
    public BaseResult<String> editMaterialCompensation(@RequestBody @Validated CompensationMaterialEditCommandDTO request){
        log.info("【编辑实物补偿单接口】提交人：[{}],前端请求参数：[{}]",ApsUserUtils.getCreatedBy(), JSON.toJSONString(request));
        return ok(compensationOrderCommandsService.editMaterialCompensation(request));
    }


    /**
     * @title addCashCompensation
     * @description 创建现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单id
     */
    @PostMapping("cash/add")
    @ApiOperation(value = "创建现金补偿单接口")
    public BaseResult<String> addCashCompensation(@RequestBody @Validated CompensationCashAddCommandDTO request){
        log.info("【创建现金补偿单接口】提交人：[{}],前端请求参数：[{}]", ApsUserUtils.getCreatedBy(),JSON.toJSONString(request));
        return ok(compensationOrderCommandsService.addCashCompensation(request));
    }

    /**
     * @title editCashCompensation
     * @description 编辑现金补偿单
     * <AUTHOR>
     * @date 2022/3/10 16:25
     * @param request
     * @return 返回补偿单ID
     */
    @PostMapping("cash/edit")
    @ApiOperation(value = "编辑现金补偿单接口")
    public BaseResult<String> editCashCompensation(@RequestBody @Validated CompensationCashEditCommandDTO request){
        log.info("【编辑现金补偿单接口】提交人：[{}],前端请求参数：[{}]",ApsUserUtils.getCreatedBy(), JSON.toJSONString(request));
        return ok(compensationOrderCommandsService.editCashCompensation(request));
    }

    /**
     * @title submit
     * @description 提交补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:29
     * @param orderId
     * @return
     */
    @PostMapping("submit/{orderId}")
    @ApiOperation(value = "提交补偿单接口")
    public BaseResult<CompensationAuditOperationVO> submit(@PathVariable("orderId") String orderId){
        CompensationAuditOperationVO vo = accidentCompensationDomainService.submit(orderId);
        return ok(vo);
    }

    /**
     * @title delete
     * @description 删除补偿单
     * <AUTHOR>
     * @date 2022/3/14 12:30
     * @param orderId
     * @return
     */
    @GetMapping("delete")
    @ApiOperation(value = "删除补偿单接口")
    public BaseResult<String> delete(@ApiParam(value = "赔偿单id",required = true) String orderId){
        log.info("【赔偿单业务-删除补偿单接口】当前用户：【{}】 ，赔偿单id：【{}】", UserContext.getUserId(), orderId);
        accidentCompensationDomainService.delete(orderId);
        return ok(orderId);
    }

    /**
     * @title auditPassToTakeEffect
     * @description @TODO
     * <AUTHOR>
     * @date 2022/4/14 16:47
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("auditPassToTakeEffect")
    @ApiOperation(value = "确认发放接口")
    public BaseResult<String> auditPassToTakeEffect(@ApiParam(value = "赔偿单id",required = true) String orderId){
        log.info("【赔偿单业务-确认发放接口】方法auditPassToTakeEffect, 当前用户：【{}】 ，赔偿单id：【{}】", UserContext.getUserId(), orderId);
        accidentCompensationDomainService.auditPassToTakeEffect(orderId);
        return ok(orderId);
    }

    /**
     * @title closeCompensation
     * @description 关闭补偿单
     * <AUTHOR>
     * @date 2022/4/14 16:51
     * @param orderId
     * @return BaseResult<String>
     */
    @GetMapping("closeCompensation")
    @ApiOperation(value = "关闭补偿单接口")
    public BaseResult<String> closeCompensation(@ApiParam(value = "赔偿单id",required = true) String orderId){
        log.info("【赔偿单业务-关闭补偿单接口】方法closeCompensation, 当前用户：【{}】 ，赔偿单id：【{}】", UserContext.getUserId(), orderId);
        accidentCompensationDomainService.closeCompensation(orderId);
        return ok(orderId);
    }

    /**
     * @title mailGrant
     * @description 邮寄发放
     * <AUTHOR>
     * @date 2022/5/7 9:33
     * @param compensationExpressInfoDTO
     * @return BaseResult<Object>
     */
    @PostMapping("mailGrant")
    @ApiOperation(value = "邮寄发放接口")
    public BaseResult<Object> mailGrant(@RequestBody CompensationExpressInfoDTO compensationExpressInfoDTO){
        boolean bool = accidentCompensationDomainService.mailGrant(compensationExpressInfoDTO);
        return ok(bool);
    }
}
