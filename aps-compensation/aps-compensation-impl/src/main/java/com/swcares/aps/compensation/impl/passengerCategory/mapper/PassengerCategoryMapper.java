package com.swcares.aps.compensation.impl.passengerCategory.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.passengerCategory.mapper.PassengerCategoryMapper <br>;
 * Description：旅客类别配置mapper <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 15:08 <br>;
 * @version v1.0 <br>;
 */
@Mapper
public interface PassengerCategoryMapper extends BaseMapper<PassengerCategoryConfigureDepository> {

    /**
     * 获取旅客类别配置分页列表
     *
     * @param dto
     * @param page
     * @return
     */
    IPage<PassengerCategorySentPageVo> sentPage(@Param("dto") PassengerCategorySentPageDto dto, Page<Object> page);

    /**
     * Title：passengerCategory <br>;
     * Description： <br>;
     * @param:  <br>;
     * @return:  <br>;
     * <AUTHOR> <br>;
     * date 2022/6/15 16:00 <br>;
     * @throws  <br>;
     */
    List<PassengerCategoryVo> passengerCategory(PassengerCategoryChangeDto dto);

    @InterceptorIgnore(tenantLine = "true")
    Map<String,Object> getTenantById(@Param("id")Long tenantId);
}
