package com.swcares.aps.compensation.impl.constant;

/**
 * <AUTHOR> <PERSON>
 * @Classname CompensationErrors
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 14:10
 * @Version 1.0
 */
public class CompensationErrors {
    public static final int DUPLICATE_DATA_EXISTING = 3001001;
    public static final int DUPLICATE_BUSINESS_PRIVILEGE_EXISTING = 3001002;
    public static final int DATA_TOBE_UPDATED_NOT_EXISTING = 3001003;

    public static final int COORDINATE_DATA_FAILED = 3001004;

    public static final int RECEIVER_TENANT_NOT_EXISTING = 3001005;

    public static final int NO_BUSINESS_PRIVILEGE_EXISTS = 3001006;

    public static final int SAME_DATA = 3001007;

}
