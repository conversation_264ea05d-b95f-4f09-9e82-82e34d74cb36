package com.swcares.aps.compensation.impl.compensation.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialInfoMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationMaterialInfoService;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import org.springframework.stereotype.Service;

/**
 * @ClassName：CompensationMaterialInfoServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/11 21:18
 * @version： v1.0
 */
@Service
public class CompensationMaterialInfoServiceImpl extends ServiceImpl<CompensationMaterialInfoMapper, CompensationMaterialInfoDO> implements CompensationMaterialInfoService {
}
