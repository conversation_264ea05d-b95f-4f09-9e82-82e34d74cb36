package com.swcares.aps.compensation.impl.dataconfig.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.mapper.ReplaceRuleMapper <br>
 * Description： Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-10 <br>
 * @version v1.0 <br>
 */
public interface ReplaceConfigMapper extends BaseMapper<DataConfigDO> {


    /**
     * @title rejectReasonPages
     * @description 分页获取审核领取拒绝原由
     * <AUTHOR>
     * @date 2022/2/24 9:59
     * @param rejectReasonPageDTO
     * @param page
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO>
     */
    List<ReplaceRejectReasonDTO> rejectReasonPages(@Param("dto")ReplaceRejectReasonPageDTO rejectReasonPageDTO);

    /**
     * @title getAllRejectReason
     * @description 获取当前代人领取所有审核拒绝原由
     * <AUTHOR>
     * @date 2022/2/24 9:49
     * @param queryReplaceConfig
     * @return java.util.List<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO>
     */
    List<ReplaceRejectReasonDTO> getAllRejectReason(@Param("param") DataConfigDO queryReplaceConfig);
}
