
package com.swcares.aps.compensation.impl.payConfig;

import com.swcares.aps.component.pay.pay.service.chinapay.ChinaPayVerifyUserProcess;
import com.swcares.aps.component.pay.pay.service.chinapay.service.ChinaPayConfigService;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaPayProcess;
import com.swcares.aps.component.pay.pay.service.wx.WxPayProcess;
import com.swcares.aps.component.pay.pay.service.wx.service.WxPayConfigService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * ClassName：com.swcares.psi.config.WebConfig <br>
 * Description：web配置信息 <br>
 * Copyright © 2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2020年4月22日 上午11:17:40 <br>
 * @version v1.0 <br>  
 */
@Configuration
public class CompensationConfig {

    /**
     * 微信支付
     * @return
     */
    @Bean("wxPayProcess")
    public WxPayProcess initWxPayService(WxPayConfigService wxPayConfigService) {
        return new WxPayProcess(wxPayConfigService);
    }

    /**
     * 银联支付
     * @return
     */
    @Bean( name = "chinaPayProcess")
    public ChinaPayProcess initChinaPayService(ChinaPayConfigService chinaPayConfigService) {
        return new ChinaPayProcess(chinaPayConfigService);
    }

    @Bean( name = "chinaPayVerifyUserProcess")
    public ChinaPayVerifyUserProcess initChinaPayVerifyUserProcess(ChinaPayConfigService chinaPayConfigService) {
        return new ChinaPayVerifyUserProcess(chinaPayConfigService);
    }


}
