package com.swcares.aps.compensation.impl.baggage.accident.service;

/**
 * @ClassName：AccidentStatusService
 * @Description：异常行李事故单状态改变
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/15 13:06
 * @version： v1.0
 */
public interface BaggageAccidentStatusService {
    /**
    * @title changeStatus
    * @description @TODO
    * <AUTHOR>
    * @date 2022/3/15 13:30
    * @param accidentId
    * @param targetStatus
    * @return boolean
    */
    boolean changeStatus(String accidentId, String targetStatus);


}
