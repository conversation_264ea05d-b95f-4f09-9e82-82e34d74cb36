package com.swcares.aps.compensation.impl.compensation.service.handler;

import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageConsumptionReasonEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockOpService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationOrderStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensateStockDTO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @ClassName：LuggageStockDecreaseHandlerServiceImpl
 * @Description：箱包库存库存减少status状态;当前只会在补偿单提交的时候,扣减库存;如果库存扣减失败,则整个补偿单提交失败
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/14 15:24
 * @version： v1.0
 */
@Service
@Transactional
@Slf4j
public class LuggageStockDecreaseHandlerServiceImpl implements CompensationOrderStatusHandlerService {


    private final static String COMPENSATE_TYPE_CASH_DICT_ID="1"; //"211"; //现金
    private final static String COMPENSATE_TYPE_VIRTUAL_DICT_ID="2"; //"212"; //虚拟
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3"; //"213"; //实物

    private final static String MATERIAL_TYPE_LUGGAGE="11";

    @Autowired
    private LuggageStockOpService luggageStockOpService;


    @Override
    public void handler(AccidentCompensationDTO compensation, String targetStatus) {
        if(!support(compensation,targetStatus)){
            return;
        }
        List<CompensationMaterialInfoDO> luggageMaterials = compensation.getCompensationMaterialInfo().stream()
                .filter(t -> MATERIAL_TYPE_LUGGAGE.equals(t.getMaterialType()))
                .collect(Collectors.toList());

        for(CompensationMaterialInfoDO compensationMaterialInfoDO:luggageMaterials){
            LuggageCompensateStockDTO compensateStockDTO=new LuggageCompensateStockDTO();
            compensateStockDTO.setAmount(compensationMaterialInfoDO.getAmount());
            compensateStockDTO.setCompensationId(compensation.getId());
            compensateStockDTO.setCompensationNo(compensation.getOrderNo());
            compensateStockDTO.setLuggageId(compensationMaterialInfoDO.getMaterialId());
            compensateStockDTO.setLuggageNo(compensationMaterialInfoDO.getMaterialNo());
//            compensateStockDTO.setReason(targetStatus);
            compensateStockDTO.setReason(LuggageConsumptionReasonEnum.buildStatus(targetStatus).getKey());
            compensateStockDTO.setOperatorId(UserContext.getUser().getEmployeeId());
            luggageStockOpService.decreaseByCompensation(compensateStockDTO);
        }
    }
    //实物补偿类型为箱包的,且状态由草稿->审核中的才进行箱包库存扣减操作
    private boolean support(AccidentCompensationDTO compensation, String targetStatus){
        boolean result=false;
        if(CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(targetStatus)
                && CompensateStatusEnum.DRAFT.getKey().equals(compensation.getStatus())
                && COMPENSATE_TYPE_MATERIAL_DICT_ID.equals(compensation.getCompensateType())) {
            List<CompensationMaterialInfoDO> compensationMaterialInfo = compensation.getCompensationMaterialInfo();
            Optional<CompensationMaterialInfoDO> luggageMaterial = compensationMaterialInfo.stream()
                    .filter(t -> MATERIAL_TYPE_LUGGAGE.equals(t.getMaterialType()))
                    .findAny();
            if(luggageMaterial.isPresent()){
                result=true;
            }
        }
        return result;
    }
}
