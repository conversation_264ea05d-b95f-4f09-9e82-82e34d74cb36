package com.swcares.aps.compensation.impl.baggage.accident.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageAccidentExpressTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.CompensationExpressMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.CompensationExpressService;
import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.entity.CompensationExpressInfoDO;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.baseframe.utils.lang.StringUtils;
import org.springframework.stereotype.Service;

/**
 * @ClassName：CompensationExpressServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/10 11:27
 * @version： v1.0
 */
@Service
public class CompensationExpressServiceImpl extends ServiceImpl<CompensationExpressMapper, CompensationExpressInfoDO> implements CompensationExpressService {

    @Override
    public boolean saveExpress(CompensationExpressInfoDTO dto) {
        CompensationExpressInfoDO expressInfoDO = ObjectUtils.copyBean(dto,CompensationExpressInfoDO.class);
        if(StringUtils.isEmpty(expressInfoDO.getOrderNo())){
            expressInfoDO.setExpressType(BaggageAccidentExpressTypeEnum.ADD_MANUALLY.getKey());
        }else{
            expressInfoDO.setExpressType(BaggageAccidentExpressTypeEnum.OFFLINE_GRANT.getKey());
        }
        return this.save(expressInfoDO);
    }

    @Override
    public boolean removeExpress(String expressId) {
        return this.removeById(expressId);
    }


}
