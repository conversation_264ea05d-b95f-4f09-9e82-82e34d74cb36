package com.swcares.aps.compensation.impl.privilege.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.privilege.mapper.CooperativeCustomerMapper;
import com.swcares.aps.compensation.impl.privilege.service.CooperativeCustomerService;
import com.swcares.aps.compensation.model.privilege.dto.SearchCooperativeCustomerDTO;
import com.swcares.aps.compensation.model.privilege.entity.CooperativeCustomer;
import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname CooperativeCustomerServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/20 09:17
 * @Version 1.0
 */
@Service
public class CooperativeCustomerServiceImpl extends ServiceImpl<CooperativeCustomerMapper, CooperativeCustomer> implements CooperativeCustomerService {
    @Autowired
    CooperativeCustomerMapper cooperativeCustomerMapper;

    @Override
    public List<CooperativeCustomer> getCooperativeCustomerList(SearchCooperativeCustomerDTO searchCriteria) {
        List<CooperativeCustomer> cooperativeCustomerList = cooperativeCustomerMapper.getCooperativeCustomerList(searchCriteria);
        return cooperativeCustomerList;
    }

    @Override
    public CooperativeCustomer saveOrUpdate(CustomerDTO customerDTO) {
        LambdaQueryWrapper<CooperativeCustomer> wrapper = new LambdaQueryWrapper();
        wrapper.eq(CooperativeCustomer::getCode, customerDTO.getCode());
        CooperativeCustomer coorperativeCustomer = this.getOne(wrapper);

        if (null == coorperativeCustomer){
            coorperativeCustomer = new CooperativeCustomer();
        }

        coorperativeCustomer.setCode(customerDTO.getCode());
        coorperativeCustomer.setName(customerDTO.getName());
        coorperativeCustomer.setShortName(customerDTO.getShortName());
        coorperativeCustomer.setCustomerCategory(customerDTO.getCustomerCategory().getCode());
        coorperativeCustomer.setDisabled(customerDTO.isDisabled());
        coorperativeCustomer.setRemark(customerDTO.getRemark());

        saveOrUpdate(coorperativeCustomer);

        return coorperativeCustomer;
    }
}
