package com.swcares.aps.compensation.impl.overbook.controller;

import com.swcares.aps.compensation.impl.overbook.service.OverBookAccidentInfoService;
import com.swcares.aps.compensation.impl.overbook.service.OverBookConfigService;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.overbook.entity.OverBookConfigDO;
import com.swcares.aps.compensation.model.overbook.vo.*;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * @ClassName：OverBookController
 * @Description：超售业务接口定义
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/27 10:52
 * @version： v1.0
 */
@RestController
@RequestMapping("/overbook/api")
@Api(tags = "接口")
@ApiVersion(value = "超售业务接口 v1.0")
public class OverBookController extends BaseController {

    @Autowired
    OverBookAccidentInfoService overBookAccidentInfoService;

    @Autowired
    OverBookConfigService overBookConfigService;

    @GetMapping("/getOne/{id}")
    @ApiOperation(value = "超售事故单详情")
    public BaseResult<OverBookAccidentInfoDO> getOverBookDetail(@PathVariable("id") Long id){
        return ok(overBookAccidentInfoService.getById(id));
    }

    @PostMapping("/getPcList")
    @ApiOperation(value = "PC超售事故单信息列表查询")
    public PagedResult<List<OverBookPcListVO>> getPcList(@RequestBody OverBookPcSearchDTO dto){
        return ok(overBookAccidentInfoService.getPcList(dto));
    }
    @GetMapping("/getPcDetail")
    @ApiOperation(value = "PC超售事故单详情")
    public BaseResult<OverBookPcDetailsVO> getPcDetail(@RequestParam Long accidentId){

        return ok(overBookAccidentInfoService.getPcDetail(accidentId));
    }

    @PostMapping("/getCompensateNumList")
    @ApiOperation(value = "PC超售事故单详情-补偿次数列表")
    public BaseResult<List<CompensationNumListVO>> getCompensateNumList(@RequestBody OverBookPaxSearchDTO dto){
        if(StringUtils.isEmpty(dto.getAccidentType())){
            dto.setAccidentType(CompensationAccidentTypeEnum.OVERBOOKING.getKey());//查超售类型
        }
        return ok(overBookAccidentInfoService.getCompensateNumList(dto));
    }



    @PostMapping("/getH5List")
    @ApiOperation(value = "超售事故单信息列表查询")
    public PagedResult<List<OverBookH5ListVO>> getH5List(@RequestBody OverBookH5SearchDTO dto){
        //1.默认按航班日期倒序排序
        //2.航班日期相同按航班计划起飞时间正序排序
        //3.计划起飞时间相同按按事故单状态排序：草稿>待处理>处理中>已结案>作废
        //4.状态相同，随机排列
        return ok(overBookAccidentInfoService.getH5List(dto));
    }

    @GetMapping("/overBookDetailInfo")
    @ApiOperation(value = "事故单详情信息")
    public BaseResult<OverBookH5DetailsVO> overBookDetailInfo(@RequestParam Long accidentId)  {
        return ok(overBookAccidentInfoService.overBookDetailInfo(accidentId));
    }


    @PostMapping("/fltVerify")
    @ApiOperation(value = "航班验证")
    public BaseResult<List<OverBookPaxVO>> findSelectedPax(@RequestBody @Valid OverBookVerifyDTO dto) {
        return ok(overBookAccidentInfoService.findSelectedPax(dto));
    }


    @PostMapping("/verifyAlikeOrder")
    @ApiOperation(value = "校验是否存在相同类型订单")
    public BaseResult verifyAlikeOrder(@RequestBody VerifyAlikeOrderDTO dto) {
        //6.证件号下事故单数量  相同姓名，证件号，证件类型）是否已经存在相同日期相同航班相同事故类型的事故单（不包含作废状态事故单）
        //7.证件号下补偿单数量  当前航班下相同补偿单类型下是否存在 该补偿单中 存在旅客的其他补偿单（包含全部状态）
        return ok(overBookAccidentInfoService.verifyAlikeOrder(dto));
    }

    @PostMapping("/saveOverBook")
    @ApiOperation(value = "旅客超售-草稿||提交")
    public BaseResult<Map<String,Long>> saveOverBook(@RequestBody OverBookSaveDTO overBookSaveDTO) throws IOException {
        return ok(overBookAccidentInfoService.saveOverBook(overBookSaveDTO));
    }

    @PostMapping("/saveOverBookCompensation")
    @ApiOperation(value = "旅客超售补偿单-编辑保存")
    public BaseResult<Long> saveOverBookCompensation(@RequestBody OverBookCompensationSaveDTO compensationSaveDTO){
        return ok(overBookAccidentInfoService.saveOverBookCompensationOrder(compensationSaveDTO));
    }

    @PostMapping("/delOverBook")
    @ApiOperation(value = "旅客超售-删除草稿事故单")
    public BaseResult delOverBook(@RequestBody OverBookChangeDTO dto) {
        overBookAccidentInfoService.delOverBook(dto.getAccidentId());
       return ok();
    }


    @PostMapping("/toVoidOverBook")
    @ApiOperation(value = "旅客超售-作废待处理事故单")
    public BaseResult toVoidOverBook(@RequestBody OverBookChangeDTO dto) {
        overBookAccidentInfoService.toVoidOverBook(dto.getAccidentId());
        return ok();
    }

    @GetMapping("/getConfigInfo")
    @ApiOperation(value = "旅客超售-规则查询")
    public BaseResult<List<OverBookConfigVO>> getConfigInfo(@RequestParam(required = false)String time,@RequestParam(required = false)String type) {
        if(StringUtils.isEmpty(type)){
            return ok(overBookConfigService.getConfigInfo());
        }
        return ok(overBookConfigService.getConfigInfo(time,type));
    }


    @PostMapping("/saveConfigInfo")
    @ApiOperation(value = "旅客超售-规则保存")
    public BaseResult saveConfigInfo(@RequestBody List<OverBookConfigDO> doList){
        overBookConfigService.saveConfigInfo(doList);
        return ok();
    }

}
