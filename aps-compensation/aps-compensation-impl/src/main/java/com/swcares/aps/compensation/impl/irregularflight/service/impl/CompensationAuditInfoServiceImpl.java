package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationAuditInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationAuditInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.aps.component.pay.pay.util.chinapay.StringUtil;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * ClassName：com.swcares.compensation.service.impl.AuditInfoServiceImpl <br>
 * Description：赔偿单-待审核记录 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-24 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class CompensationAuditInfoServiceImpl extends ServiceImpl<CompensationAuditInfoMapper, CompensationAuditInfoDO> implements CompensationAuditInfoService {

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;

    @Autowired
    private RedisUtil redisUtil;

    @Resource
    private CompensationAuditInfoMapper compensationAuditInfoMapper;

    @Autowired
    private IrregularFlightWorkflowService irregularFlightWorkflowService;

    @Autowired(required = false)
    private SMSService smsService;

    @Autowired
    private WorkflowApi workflowApi;

    @Autowired
    private Redisson redisson;
    private static final String REDISSON_PRE_KEY = "APPLY_AUDIT_SAVE_REVIEWER_";
    private static final Long REDISSON_KEY_REDIS_LOCK_TIME = 120L;

    @Override
    public Map<String,Object> auditOperation(AuditProcessorDTO dto) throws Exception {
        return irregularFlightWorkflowService.auditOperation(dto);
    }


    @Override
    public Map<String, String> getStringToMap(String str) {
        Map<String, String> m = new HashMap<String, String>();
        if(StringUtils.isEmpty(str)){
            return m;
        }
        String[] strs = str.split(",");
        for(String s:strs){
            if(StringUtils.isNotBlank(str)){
                String[] ms = s.split(":");
                m.put(ms[0], ms[1]);
            }
        }
        return m;
    }


    //条件查询审核人，前端调用
    @Override
    public Map<String,Object> findReviewer(Long orgId, String userInfo,String taskId,Long orderId) {
        log.info("【aps-compensation】查询审核人，【下一审核节点id:{}，赔偿单id：{}】",taskId,orderId);
//        从redis中获取查询审核人的条件（cpc提供的条件）
        Map<String, String> auditorMap = redisUtil.get(StrUtil.format(CompensationConstant.COMPENSATION_AUDITOR_KEY,orderId));

        Map<String,Object> map = new HashMap<>();
        if(auditorMap==null || auditorMap.size()==0){
            return map;
        }
        log.info("【aps-compensation】查询审核人条件：【部门id：{}，人员信息：{}】,redis获取信息[{}]",orgId,userInfo,JSONUtil.toJsonStr(auditorMap));

        String[] deptIds = StringUtils.isNotEmpty(auditorMap.get("deptId"))?auditorMap.get("deptId").split(","):null;
        String[] userIds = StringUtils.isNotEmpty(auditorMap.get("userId"))?auditorMap.get("userId").split(","):null;
        String[] roleIds = StringUtils.isNotEmpty(auditorMap.get("roleId"))?auditorMap.get("roleId").split(","):null;
        List<CompensationReviewerInfoVO> list = compensationAuditInfoMapper.findReviewer(deptIds,userIds,roleIds,orgId, userInfo);
        map.put("taskId",taskId);
        map.put("userInfo",list);
        return map;
    }
    //条件查询审核人，供cpc调用
    @Override
    public List<CompensationReviewerInfoVO> getReviewer(String deptIds, String userIds,String roleIds) {
        log.info("【aps-compensation】查询审核人条件：【部门id：{}，人员id：{}，角色id：{}】",deptIds,userIds,roleIds);
        String[] deptId = null;
        String[] userId = null;
        String[] roleId = null;
        if(deptIds!=null && !deptIds.equals("")){
            Set<String> collect = Stream.of(deptIds.split(","))
                    .filter(t -> StringUtils.isNotBlank(t) && (!StringUtils.equalsIgnoreCase("null",t)))
                    .collect(Collectors.toSet());
            if(CollectionUtils.isNotEmpty(collect)){
                deptId=collect.toArray(new String[]{});
            }

        }
        if(userIds!=null && !userIds.equals("")){

            Set<String> collect = Stream.of(userIds.split(","))
                    .filter(t -> StringUtils.isNotBlank(t) && (!StringUtils.equalsIgnoreCase("null",t)))
                    .collect(Collectors.toSet());

            if(CollectionUtils.isNotEmpty(collect)){
                userId =collect.toArray(new String[]{});
            }
        }
        if(roleIds!=null && !roleIds.equals("")){
            Set<String> collect = Stream.of(roleIds.split(","))
                    .filter(t -> StringUtils.isNotBlank(t) && (!StringUtils.equalsIgnoreCase("null",t)))
                    .collect(Collectors.toSet());

            if(CollectionUtils.isNotEmpty(collect)){
                roleId = collect.toArray(new String[]{});
            }

        }
        if(deptId==null && userId==null && roleId==null){
            return new ArrayList<>();
        }

        return compensationAuditInfoMapper.findReviewer(deptId,userId,roleId,null, null);
    }

    @Override
    public CompensationAuditInfoDO findCompensationAuditInfo(CompensationAuditInfoDO auditInfoDO) {
        if(null == auditInfoDO){
            return null;
        }
        List<CompensationAuditInfoDO> list = this.getBaseMapper().selectList(Wrappers.lambdaQuery(auditInfoDO));
        if(CollectionUtils.isEmpty(list)){return null;}
        return list.get(0);
    }

    @Override
    public List<String> findReviewerIds(List<String> positions) {
        log.info("【aps-compensation】查询审核人id集合，入参：【{}】",JSONUtil.toJsonStr(positions));
        List<CompensationReviewerInfoVO> list = new ArrayList<>();
        List<String> result = new ArrayList<>();
        if(ListUtils.isEmpty(positions)){
            return result;
        }
        for (String info : positions) {
            if(StringUtils.isNotEmpty(info)){
                info = info.replaceAll("\\[","");
                info = info.replaceAll("\\]","");
                info = info.replaceAll("\\\"","");
                Map<String, String> auditorMap = getStringToMap(info);
                list = this.getReviewer(auditorMap.get("deptId"),auditorMap.get("userId"),auditorMap.get("roleId"));
            }
            if(list.size()!=0){
                for (CompensationReviewerInfoVO vo : list) {
                    result.add("userId:"+String.valueOf(vo.getReviewerId()));
                }
            }
        }
        return result;
    }


    @Override
    public void saveReviewer(CompensationAuditInfoDTO dto) {
        log.info("【aps-compensation-impl】查询审批人耗时统计——方法入口,赔偿单号【{}】", dto.getOrderId());
        log.info("【aps-compensation】储存待审核人员-begin：审核人信息【{}】",JSONUtil.toJsonStr(dto));
        RLock lock = redisson.getLock(REDISSON_PRE_KEY + dto.getOrderId());
        CurrentTaskActivityDTO currentTaskActivityDTOS = new CurrentTaskActivityDTO();
        try {
            boolean resLock = lock.tryLock(REDISSON_KEY_REDIS_LOCK_TIME, TimeUnit.SECONDS);
            if (resLock) {

                //查询当前待处理审批任务，判断保存节点任务taskid是否于与待处理任务taskid一致
                BaseResult<CurrentTaskActivityVO> currentUserTask = workflowApi.currentUserTask(BaseQueryParamDTO.builder().businessKey(String.valueOf(dto.getOrderId())).build());
                currentTaskActivityDTOS = currentUserTask.getData().getCurrentTaskActivityDTOS().get(0);
                if(!dto.getTaskId().equals(currentTaskActivityDTOS.getTaskId())){
                    log.info("【aps-compensation-impl】储存待审核人员,审核任务不存在了—赔偿单号【{}】，taskid：【{}】，当前待处理任务taskid：{} ",dto.getOrderId(),dto.getTaskId(),currentTaskActivityDTOS.getTaskId());
                    throw new BusinessException(CompensationException.CPC_TASKID_ERROR);
                }
                /*  //先查询
                LambdaQueryWrapper<CompensationAuditInfoDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(CompensationAuditInfoDO::getOrderId, dto.getOrderId());
                lambdaQueryWrapper.eq(CompensationAuditInfoDO::getTaskId, dto.getTaskId());
                Integer integer = compensationAuditInfoMapper.selectCount(lambdaQueryWrapper);
                log.info("【aps-compensation-impl】查询审批人耗时统计——查询审批记录表(compensation_audit_info)该节点是否已经被别人审批过【{}】, 赔偿单号【{}】", integer.intValue()> 0,dto.getOrderId());
                if (integer.intValue() > 0) {
                    log.info("【aps-compensation】储存待审核人员：审核人信息【{}】,查询数据库是否已存审批人员：count【】", JSONUtil.toJsonStr(dto), integer.intValue());
                    throw new BusinessException(CompensationException.AUDIT_ORDER_USER_ERROR);
                }*/
                LambdaQueryWrapper<CompensationAuditInfoDO> lambdaQueryWrapper = Wrappers.lambdaQuery();
                lambdaQueryWrapper.eq(CompensationAuditInfoDO::getOrderId, dto.getOrderId());
                lambdaQueryWrapper.eq(CompensationAuditInfoDO::getTaskId, dto.getTaskId());
                int delete = compensationAuditInfoMapper.delete(lambdaQueryWrapper);
                log.info("【aps-compensation-impl】储存待审核人员—删除已保存的审核人：删除条数：{} ，—taskid【{}】， 赔偿单号【{}】",delete, dto.getTaskId(),dto.getOrderId());

                for (Long userId : dto.getAuditorIds()) {
                    CompensationAuditInfoDO compensationAuditInfoDO = new CompensationAuditInfoDO();
                    compensationAuditInfoDO.setOrderId(dto.getOrderId());
                    compensationAuditInfoDO.setAuditorId(userId);
                    compensationAuditInfoDO.setTaskId(dto.getTaskId());
                    compensationAuditInfoMapper.insert(compensationAuditInfoDO);
                }
                log.info("【aps-compensation-impl】查询审批人耗时统计——for循环插入compensation_audit_info表，插入内容是该节点的审批人，共计【{}】个, 赔偿单号【{}】", dto.getAuditorIds().length, dto.getOrderId());
            } else {
                log.error("【aps-compensation-impl】-赔偿单工作流-保存审核人操作，获取分布式锁失败：{}", JSONUtil.toJsonStr(dto));
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-获取分布式锁失败");
            }
        }catch (Exception e){
            log.error("【aps-compensation-impl】-赔偿单工作流-保存审核人失败,异常：{}",e);
            if(e instanceof BusinessException){
                throw (BusinessException)e;
            }else {
                throw new BusinessException(CompensationException.AUDIT_ERROR, "赔偿单-保存审核人失败");
            }
        }finally {
            lock.unlock();
        }
        NodeNoticeDTO noticeDTO = new NodeNoticeDTO();
        noticeDTO.setOptionCode(CompensationConstant.AUDIT_AGREE);
        noticeDTO.setBusiKey(dto.getOrderId().toString());
        noticeDTO.setNodeKey(currentTaskActivityDTOS.getNodeKey());
        noticeDTO.setNodeName(currentTaskActivityDTOS.getNodeName());
        log.info("【aps-compensation-impl】查询审批人耗时统计——开始推送消息给消息中心, 赔偿单号【{}】", dto.getOrderId());
        smsService.sendInnerSMS(noticeDTO, null);
        log.info("【aps-compensation-impl】查询审批人耗时统计——推送消息给消息中心结束，该消息是异步处理的, 赔偿单号【{}】", dto.getOrderId());
        log.info("【aps-compensation】储存待审核人员-end：审核人信息【{}】",JSONUtil.toJsonStr(dto));

    }

    @Override
    public List<CompensationAuditRecordVO> findAuditRecord(Long orderId, String orderNo) {
        log.info("【aps-compensation】查询审核记录，赔偿单id:【{}】,赔偿单号：【{}】", orderId, orderNo);
        List<CompensationAuditRecordVO> list = new ArrayList<>();
        ZoneId zoneId = ZoneId.systemDefault();
        String taskId="";
        //历史节点+当前节点+未来节点
        //1.历史任务数据+包含待执行任务
        String businessKey = String.valueOf(orderId);
        List<HistoryTaskAuditActivityDTO> auditActivityDTOList = null;
        HistoryTaskAuditActivityVO historyTaskAuditActivityVO=null;
        try {
            historyTaskAuditActivityVO = workflowApi
                    .historyTaskAuditActivity(BaseQueryParamDTO.builder().businessKey(businessKey).build()).getData();
            auditActivityDTOList = historyTaskAuditActivityVO.getHistoryTaskAuditActivityDTOS();
        }catch (Exception e){
            log.error("【aps-compensation】查询审核记录，赔偿单id:【{"+orderId+"}】", e);
        }

        if (ListUtils.isEmpty(auditActivityDTOList)) {
            return list;
        }
        CompensationOrderInfoDO compensationOrderInfoDO = compensationOrderInfoService.getById(orderId);

        List<HistoryTaskAuditActivityDTO> historicList = new ArrayList<>();
        //驳回到发起人，发起人节点不展示，其他情况需要展示。【当前待处理节点为发起人，删除】
        for(HistoryTaskAuditActivityDTO e: auditActivityDTOList){
            if(e.getTaskEndTime() == null && e.getNodeKey().equals("submitter")){continue;}
            historicList.add(e);
        }
        String businessName = compensationOrderInfoService.getTenantNameById(TenantHolder.getTenant());

        for (HistoryTaskAuditActivityDTO taskDO : historicList) {
            CompensationAuditRecordVO vo = new CompensationAuditRecordVO();
            this.assembleAuditRecordVo(vo,taskDO,zoneId,businessName,historyTaskAuditActivityVO.getRequestBusinessName(),compensationOrderInfoDO);

            //判断是否为当前节点
            if(taskDO.getTaskStatus().equals("ready")){
                taskId = taskDO.getTaskId();
                List<CompensationReviewerInfoVO> infoVOS = compensationAuditInfoMapper.findReviewerByTaskId(taskDO.getTaskId());
                if(CollectionUtils.isEmpty(infoVOS)){
                    Map<String, Object> reviewer = findReviewer(null, null, taskId, orderId);
                    if(reviewer.containsKey("userInfo")){
                        infoVOS = (List<CompensationReviewerInfoVO>) reviewer.get("reviewer");
                    }
                }


                if(CollectionUtils.isNotEmpty(infoVOS)){
                    //拼接所有待审核人信息
                    String reviewerInfo = "";
                    String reviewerId = "";
                    for (int i = 0; i < infoVOS.size(); i++) {
                        CompensationReviewerInfoVO infoVO = infoVOS.get(i);
                        if(i == 0){
                            reviewerId = infoVO.getReviewerId().toString();
                            reviewerInfo = infoVO.getReviewerNameNo()+":"+infoVO.getReviewerPhone();
                            continue;
                        }
                        reviewerInfo = reviewerInfo+","+infoVO.getReviewerNameNo()+":"+infoVO.getReviewerPhone();
                        reviewerId = reviewerId + ","+infoVO.getReviewerId();
                    }
                    vo.setReviewerInfo(reviewerInfo);
                    vo.setReviewerId(reviewerId);

                    //判断cpc传递参数中是否需要去查询当前节点的部门（只有当传递参数是单个userId或者单个deptId才需要带出部门信息）
                    String[] assignees = taskDO.getAssignee().split(",");

                    if(assignees.length==1 && infoVOS.size()>1 ){
                        String[] str = assignees[0].split(":");
                        if(!str[0].equals("roleId")){
                            vo.setObjName(this.getOrg(taskDO.getAssignee(),taskDO.getNodeKey(),businessName,historyTaskAuditActivityVO.getRequestBusinessName()));
                        }
                    }else if(infoVOS.size()==1){
                        vo.setObjName(infoVOS.get(0).getOrgName());
                    }


                    List<CompensationAuditReviewerVO> compensationAuditReviewerVOS=new ArrayList<>(infoVOS.size());
                    infoVOS.forEach(t->{
                        CompensationAuditReviewerVO compensationAuditReviewerVO = BeanUtil.copyProperties(t, CompensationAuditReviewerVO.class);
                        compensationAuditReviewerVOS.add(compensationAuditReviewerVO);
                    });
                    vo.setWaiteAuditReviewers(compensationAuditReviewerVOS);

                }
            }
            list.add(vo);
        }

        //取消节点
        if(historicList.size()==1){
            CompensationOrderInfoDO orderInfoDO = compensationOrderInfoService.getById(orderId);
            if(CompensateStatusEnum.AUDIT_FAILED.getKey().equals(orderInfoDO.getStatus())){
                CompensationAuditRecordVO vo = new CompensationAuditRecordVO();
                vo.setNodeName("REJECT");
                vo.setReviewerInfo("ADMIN");
                vo.setRemarks("提交的补偿单不满足流程审核条件，已自动结束");
                vo.setObjName("");
                vo.setDoneDate(list.get(list.size()-1).getDoneDate());
                list.add(vo);
            }
        }
        //---------------------***3.未来节点,userTask类型***-------------------------
        if(StringUtils.isNotEmpty(taskId)){
            BaseResult<CurrentTaskActivityDTO> nextTaskInfo = workflowApi.getNextTask(taskId);
            if(ObjectUtils.isNotEmpty(nextTaskInfo.getData())){
                CompensationAuditRecordVO vo  = new CompensationAuditRecordVO();
                vo.setReviewerInfo("未审核");
                vo.setStatus("3");
                list.add(vo);
            }
        }

        return list;
    }

    private void assembleAuditRecordVo(CompensationAuditRecordVO vo,
                                       HistoryTaskAuditActivityDTO taskDO,
                                       ZoneId zoneId,
                                       String businessName,
                                       String requestBusinessName,
                                       CompensationOrderInfoDO compensationOrderInfoDO){
        vo.setStatus("ready");
        if(null != taskDO.getTaskEndTime()){
            Instant instant = taskDO.getTaskEndTime().toInstant();
            vo.setDoneDate(LocalDateTime.ofInstant(instant, zoneId));
            vo.setStatus("complete");
        }
        taskDO.setTaskStatus(vo.getStatus());
        vo.setRemarks(taskDO.getComment());

        if(StringUtil.isNotEmpty(taskDO.getAssignee()) && taskDO.getAssignee().split(":").length>=2){
            //设置审核人id
            vo.setReviewerId(taskDO.getAssignee().split(":")[1]);
        }else{
            vo.setReviewerId(null);
        }


        if(null != taskDO.getTaskEndTime() && CompensationConstant.ADMIN_END_ID.equals(vo.getReviewerId())){
            vo.setReviewerInfo(CompensationConstant.ADMIN_END);
        }else {
            //根据userId去查找审核人信息【姓名（工号）+电话】
            vo.setReviewerInfo(this.auditGetUserInfoByUserId(taskDO.getAssignee()));
        }
        //根据userId获取审核人的部门
        vo.setObjName(this.getOrg(taskDO.getAssignee(),taskDO.getNodeKey(),businessName,requestBusinessName));

        vo.setNodeName(taskDO.getOptionCode());
        //判断是否是发起节点
        if (taskDO.getNodeKey().equals("submitter")) {
            vo.setNodeName(taskDO.getNodeKey());
        }
        //发起节点 && 数据来源=机场
        if(taskDO.getNodeKey().equals("submitter") && CompensationConstant.COMPENSATION_SOURCE_AIRPORT.equals(compensationOrderInfoDO.getSource())){
            vo.setReviewerId(null);
            vo.setReviewerInfo(null);
            vo.setObjName(requestBusinessName);
        }


        //判断是不是异常取消审核
        if (taskDO.getNodeKey().equals("cancel")) {
            vo.setNodeName("REJECT");
            vo.setReviewerInfo("ADMIN");
            vo.setRemarks("提交的补偿单不满足流程审核条件，已自动结束");
            vo.setObjName("");
        }
        if(WorkflowUtils.isAutomaticTask(taskDO)){
            vo.setNodeName("AGREE");
            vo.setReviewerInfo("ADMIN");
            vo.setRemarks("提交的补偿单已自动审核通过");
            vo.setObjName("");
        }
    }

    //用于查询已审核记录的执行人信息
    public String auditGetUserInfoByUserId(String param){
        if(StringUtils.isEmpty(param)){
            return null;
        }
        if(param.contains(CompensationConstant.ADMIN_END)){
            return CompensationConstant.ADMIN_END;
        }

        String[] personInfo = param.split(":");
        String userId = personInfo[1];
        List<CompensationReviewerInfoVO> reviewer = this.getReviewer(null, userId, null);
        if(reviewer.size()>0){
            return reviewer.get(0).getReviewerNameNo()+":"+reviewer.get(0).getReviewerPhone();
        }
        return null;
    }

    //用于通过userId/OrgId查询操作人的部门
    public String getOrg(String param,String nodeKey,String currentBusinessName,String requestBusinessName){
        String businessName=currentBusinessName;
        if(WorkflowUtils.isSyncProcessTask(nodeKey)){
            businessName=requestBusinessName;
        }
        if(StringUtils.isEmpty(param)){
            return businessName;
        }
        param = param.replaceAll("\\[","");
        param = param.replaceAll("\\]","");
        param = param.replaceAll("\\\"","");
        String[] personInfo = param.split(":");
        String id = personInfo[1];
        String str = personInfo[0];
        if(str.equals("userId")){
            String[] ids  = id.split(",");
            if(StringUtils.isBlank(ids[0]) || StringUtils.equalsIgnoreCase("null",ids[0])){
                return businessName;
            }
            String organization = compensationAuditInfoMapper.findOrganization(ids[0], null);
            if(StringUtils.isNotBlank(organization)){
                return businessName+"-"+organization;
            }
            return businessName;
        }
        if(StringUtils.isBlank(id) || StringUtils.equalsIgnoreCase("null",id)){
            return businessName;
        }
        try {
            String organization = compensationAuditInfoMapper.findOrganization(null, id);
            if(StringUtils.isNotBlank(organization)){
                return businessName+"-"+organization;
            }
        }catch (Exception e){log.error("compensationAuditInfoMapper.findOrganization,["+id+"]");}

        return businessName;
    }
}
