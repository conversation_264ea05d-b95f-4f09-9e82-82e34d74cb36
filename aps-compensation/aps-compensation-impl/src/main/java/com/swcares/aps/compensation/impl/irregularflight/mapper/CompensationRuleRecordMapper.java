package com.swcares.aps.compensation.impl.irregularflight.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationRuleRecordDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationStandardVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.mapper.CompensationRuleRecordMapper <br>
 * Description： Mapper 接口 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-29 <br>
 * @version v1.0 <br>
 */
public interface CompensationRuleRecordMapper extends BaseMapper<CompensationRuleRecordDO> {

    /**
     * Title： getCompensationRuleByOrderId<br>
     * Description： 根据赔偿单号查补偿规则 <br>
     * author：傅欣荣 <br>
     * date：2021/11/2 14:03 <br>
     * @param orderId
     * @return java.util.List<com.swcares.aps.compensation.model.irregularflight.vo.CompensationStandardVO>
     */
    List<CompensationStandardVO> getCompensationRuleByOrderId(@Param("orderId") Long orderId);

}
