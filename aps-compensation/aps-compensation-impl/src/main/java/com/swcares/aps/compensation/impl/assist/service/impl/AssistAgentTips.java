package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.assist.constant.AssistApplyTips;
import com.swcares.aps.compensation.impl.assist.service.AssistAgentTipsService;
import com.swcares.aps.compensation.impl.assist.utils.SendSMSUtils;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.components.msg.CustomerMessageRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @title: AssistAgentTips
 * @projectName aps
 * @description: 代领手机号本人提示具体实现类
 * @date 2022/2/16 10:35
 */
@Service
@Slf4j
public class AssistAgentTips implements AssistAgentTipsService {

//    @Autowired
//    private CustomerMessageRemoteService customerMessageRemoteService;
    @Autowired(required = false)
    private SMSService smsService;

    @Override
    public void sendSMSToAgent(String phone) {
        log.info("【aps-compensation-impl-assist】超过6人消息推送给号主，代领人号码：【{}】",phone);
        List<String> phones = new ArrayList<>();
        phones.add(phone);
//        customerMessageRemoteService.sendNow(SendSMSUtils.sendSMSDTO(phones,String.format(AssistApplyTips.AGENT_MSG,phone)));
        smsService.sendPassengerNow(SendSMSUtils.sendSMSDTO(phones,String.format(AssistApplyTips.AGENT_MSG,phone)));
    }
}
