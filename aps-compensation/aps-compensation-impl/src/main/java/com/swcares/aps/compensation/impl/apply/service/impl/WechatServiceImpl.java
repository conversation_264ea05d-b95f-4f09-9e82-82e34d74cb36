package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.http.HttpRequest;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.WechatService;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.component.com.wechart.WechatUrlConf;
import com.swcares.aps.usercenter.remote.api.UserCenterApi;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

import static com.swcares.baseframe.utils.constants.GlobalConstants.TENANT_DEFAULT;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：WechatServiceImpl <br>
 * Package：com.swcares.aps.apply.impl.service.impl <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 11月25日 15:56 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class WechatServiceImpl implements WechatService {
    //赔付小程序登录的token存储路径
    public static final String MINI_PROGRAM_LOGIN_PREFIX = "COMPEMSATION:MINIPROGRAM:LOGIN:";

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserCenterApi userCenterApi;
    @Autowired
    CompensationOrderInfoMapper compensationOrderInfoMapper;
    /**
     * Title：getAccessTokenByOfficialAccounts <br>
     * Description：通过code换取openid接口 <br>
     * author：于琦海 <br>
     * date：2021/11/25 16:15 <br>
     * @params String code
     * @return String
     */
    @Override
    public String getAccessTokenByOfficialAccounts(String code) {
        StringBuilder url = new StringBuilder(WechatUrlConf.AUTH_OPENID)
                .append("appid=" + ConfigUtil.get("app_id").getValues().get(0).getConfigValue())
                .append("&secret=" + ConfigUtil.get("app_secret"). getValues().get(0).getConfigValue())
                .append("&code=" + code)
                .append("&grant_type=authorization_code");
        String body = HttpRequest.get(url.toString()).execute().body();
        log.info("微信公众号获取openid---请求链接【{}】,反馈结果【{}】", url, body);
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String openid = jsonObject.getStr("openid");
        if (StringUtils.isEmpty(openid)) {
            throw new BusinessException(ApplyErrors.GET_OPENID_ERROR);
        }
        return openid;
    }

    @Override
    public String getAccessTokenByMiniProgram(String tenantCode,String code) {
        Long tenantId = compensationOrderInfoMapper.getTenantIdByCode(tenantCode);
        if (tenantId == null) {
            tenantId = TENANT_DEFAULT;
        }
        log.info("微信小程序获取openid---获取当前租户Id【{}】, 获取当前配置appId【{}】", tenantId,
                ConfigUtil.getString("sys_we_chart_config", "app_id", tenantId));
        StringBuilder url = new StringBuilder(WechatUrlConf.MINI_PROGRAM_AUTH_OPENID)
                .append("appid=" + ConfigUtil.getString("sys_we_chart_config", "app_id", tenantId))
                .append("&secret=" +  ConfigUtil.getString("sys_we_chart_config", "app_secret", tenantId))
                .append("&js_code=" + code)
                .append("&grant_type=authorization_code");
        String body = HttpRequest.get(url.toString()).execute().body();
        log.info("微信小程序获取openid---请求链接【{}】,反馈结果【{}】", url, body);
        JSONObject jsonObject = JSONUtil.parseObj(body);
        String openid = jsonObject.getStr("openid");
        if (StringUtils.isEmpty(openid)) {
            throw new BusinessException(ApplyErrors.GET_OPENID_ERROR);
        }
        return openid;
    }

    @Override
    public Object miniProgramLogin(String openId, String tenantCode) {
        //1、校验openId是不是有问题
        String appId = ConfigUtil.getString("sys_we_chart_config", "app_id", TenantHolder.getTenantContext().getTenantId());
        String appSecret = ConfigUtil.getString("sys_we_chart_config", "app_secret", TenantHolder.getTenantContext().getTenantId());
        log.info("赔付微信端旅客领取->从系统配置中获取的appId为【{}】, appSecret为【{}】", appId, appSecret);
        String url = String.format("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=%s&secret=%s", appId, appSecret);
        String body = HttpRequest.get(url.toString()).execute().body();
        JSONObject j = JSONUtil.parseObj(body);
        if(ObjectUtils.isEmpty(j) || StringUtils.isEmpty(j.getStr("access_token"))){
            //根据openid获取微信的accessToken失败，抛异常
            throw new BusinessException(ApplyErrors.GET_TOKEN_ERROR);
        }

        //2.登录逻辑
        //openid存入redis <key = openId, value = accessToken>
        LoginUserDetails loginUserDetails = new LoginUserDetails();
        loginUserDetails.setUsername(openId);
        loginUserDetails.setWxOpenid(openId);
        loginUserDetails.setClientId("passenger_password_auth_mode");
        redisUtil.set(MINI_PROGRAM_LOGIN_PREFIX + openId, loginUserDetails);

        //3.调用公共模块的登录拿到token返回
        Object o = userCenterApi.getTokenBySelfToken(MINI_PROGRAM_LOGIN_PREFIX + openId, tenantCode);
        log.info("赔付微信端旅客领取->当前旅客的openid【{}】, 从公共模块返回的登录信息为【{}】", openId, JSONUtil.toJsonStr(o));
        return o;
    }

}
