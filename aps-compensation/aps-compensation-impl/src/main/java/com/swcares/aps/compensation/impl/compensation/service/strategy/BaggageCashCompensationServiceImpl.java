package com.swcares.aps.compensation.impl.compensation.service.strategy;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoDTO;
import com.swcares.aps.basic.data.businessimpl.model.vo.FocFlightInfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageInputSourceTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.compensation.enums.ReceiveStatusEnum;
import com.swcares.aps.compensation.impl.compensation.service.AccidentCompensationDomainService;
import com.swcares.aps.compensation.impl.compensation.service.CompensationAddAndEditService;
import com.swcares.aps.compensation.impl.compensation.service.CreateCompensationOrderDOService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationException;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashAddCommandDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashEditCommandDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationRuleRecordDO;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;


/**
 * @ClassName：BaggageCashCompensationServiceImpl
 * @Description：异常行李现金补偿单创建编辑服务
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 17:02
 * @version： v1.0
 */
@Service("baggageCashCompensationService")
@Transactional
@Slf4j
public class BaggageCashCompensationServiceImpl implements CompensationAddAndEditService<CompensationCashAddCommandDTO, CompensationCashEditCommandDTO> {


    private CompensationBasicDataService compensationBasicDataService;

    private AccidentCompensationDomainService accidentCompensationDomainService;

    private BaggageAccidentService baggageAccidentService;

    private CreateCompensationOrderDOService createCompensationOrderDOService;


    @Autowired
    public BaggageCashCompensationServiceImpl(CompensationBasicDataService compensationBasicDataService,
                                              BaggageAccidentService baggageAccidentService,
                                              AccidentCompensationDomainService accidentCompensationDomainService,
                                              CreateCompensationOrderDOService createCompensationOrderDOService){
        this.compensationBasicDataService=compensationBasicDataService;
        this.baggageAccidentService=baggageAccidentService;
        this.accidentCompensationDomainService=accidentCompensationDomainService;
        this.createCompensationOrderDOService=createCompensationOrderDOService;
    }

    /**
     * @title add
     * @description 添加异常行李现金补偿单
     * <AUTHOR>
     * @date 2022/3/14 9:37
     * @param command
     * @return 补偿单ID
     */
    @Override
    public String add(CompensationCashAddCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);

        String compensationOrderId = accidentCompensationDomainService.save(compensation);

        return compensationOrderId;
    }

    /**
     * @title edit
     * @description 编辑异常行李现金补偿单
     * <AUTHOR>
     * @date 2022/3/14 9:41
     * @param command
     * @return 补偿单ID
     */
    @Override
    public String edit(CompensationCashEditCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);

        accidentCompensationDomainService.edit(compensation,command.getCompensationId());

        return command.getCompensationId();

    }

    @Override
    public CompensationAuditOperationVO reSubmit(CompensationCashEditCommandDTO command) {
        AccidentCompensationDTO compensation = getAccidentCompensationDTO(command);
        CompensationAuditOperationVO result = accidentCompensationDomainService.reSubmit(compensation, command.getCompensationId());
        return result;
    }

    /**
     * @title getAccidentCompensationDTO
     * @description 创建事补偿单领域模型
     * <AUTHOR>
     * @date 2022/3/14 10:47
     * @param command
     * @return
     */
    private AccidentCompensationDTO getAccidentCompensationDTO(CompensationCashAddCommandDTO command){
        if(command.getCompensationAllMoney().doubleValue()<=0  || command.getCustomCompensationMoney().doubleValue() <=0){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"补偿金额错误");
        }
        BaggageAccidentInfoDO accident = baggageAccidentService.getById(command.getAccident().getAccidentId());
        if(accident==null){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,command.getAccident().getAccidentId()+"事故单不存在");
        }

        CompensationOrderInfoDO compensationOrderInfoDO = getCompensationOrderInfoDOByInputSource(command,accident);

        CompensationPaxInfoDO compensationPaxInfoDO = getCompensationPaxInfoDOByInputSource(command,accident);

        CompensationRuleRecordDO compensationRuleRecordDO = getCompensationRuleRecordDO(command, accident);

        CompensationFlightInfoDO compensationFlightInfoDO = getCompensationFlightInfoDOByInputSource(command,accident,compensationOrderInfoDO);

        AccidentCompensationDTO compensation = AccidentCompensationDTO.of(compensationOrderInfoDO,
                Lists.newArrayList(compensationPaxInfoDO),
                compensationFlightInfoDO,null,
                Lists.newArrayList(compensationRuleRecordDO),null);
        return compensation;
    }

    /**
     * @title getCompensationRuleRecordDO
     * @description 获取补偿规则实体类
     * <AUTHOR>
     * @date 2022/4/21 14:51
     * @param command
     * @param accident
     * @return CompensationRuleRecordDO
     */
    private CompensationRuleRecordDO getCompensationRuleRecordDO(CompensationCashAddCommandDTO command, BaggageAccidentInfoDO accident) {
        CompensationRuleRecordDO compensationRuleRecordDO = new CompensationRuleRecordDO();
        compensationRuleRecordDO.setIsCustom("1");   //1自定义金额
        compensationRuleRecordDO.setCpsNum(command.getCustomCompensationMoney());
        compensationRuleRecordDO.setBabyStd(command.getCustomCompensationMoney());
        compensationRuleRecordDO.setIsPercentageBaby(2);  //自定义金额
        compensationRuleRecordDO.setChildStd(command.getCustomCompensationMoney());
        compensationRuleRecordDO.setIsPercentageChild(2);   //自定义金额
        compensationRuleRecordDO.setAdultStd(command.getCustomCompensationMoney());
        compensationRuleRecordDO.setIsPercentageAdult(2);  //自定义金额
        return compensationRuleRecordDO;
    }

    /**
     * @title getCompensationOrderInfoDO
     * @description 根据提交信息创建补偿单实体信息
     * <AUTHOR>
     * @date 2022/3/14 9:40
     * @param command
     * @param accident
     * @return 补偿单实体信息
     */
    private CompensationOrderInfoDO getCompensationOrderInfoDO(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=SYSTEM（系统查询），走下面原逻辑
        FocFlightInfoDTO focFlightInfoDTO=new FocFlightInfoDTO();
        focFlightInfoDTO.setFlightNo(accident.getPaxFlightNo());
        focFlightInfoDTO.setFlightDate(accident.getPaxFlightDate());
        List<FocFlightInfoVO> flightInfoVOS = compensationBasicDataService.getFocFlightInfo(focFlightInfoDTO);
        if(CollectionUtils.isEmpty(flightInfoVOS)){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accident.getPaxFlightNo()+"_"+accident.getPaxFlightDate()+"航班数据不存在");
        }
//        String collect = flightInfoVOS.stream().map(FocFlightInfoVO::getSegment).collect(Collectors.joining(","));
        command.setServiceCity(command.getServiceCity().substring(command.getServiceCity().length() - 3));
//        if(!collect.contains(command.getServiceCity())){
//            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,"服务航站错误");
//        }
        CompensationOrderInfoDO compensationOrderInfoDO = createCompensationOrderDOService.createCashByBaggageAccident(command, accident, flightInfoVOS);
        return compensationOrderInfoDO;
    }


    private CompensationOrderInfoDO getCompensationOrderInfoDOByInputSource(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        if(BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(accident.getInputSource())){
            command.setServiceCity(command.getServiceCity().substring(command.getServiceCity().length() - 3));
            return createCompensationOrderDOService.createCashByBaggageAccidentManualInput(command, accident);
        } //旅客数据输入的来源=SYSTEM（系统查询）
        else if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(accident.getInputSource())){
            return getCompensationOrderInfoDO(command,accident);
        }else {
            return null;
        }
    }
    @NotNull
    private CompensationPaxInfoDO getCompensationPaxInfoDOByInputSource(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        if(BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(accident.getInputSource())){
            return getManualInputCompensationPaxInfoDO(command,accident);
        } //旅客数据输入的来源=SYSTEM（系统查询）
        else if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(accident.getInputSource())){
            return getCompensationPaxInfoDO(command,accident);
        }else {
            return null;
        }
    }

    /**
     * @title getCompensationPaxInfoDO
     * @description 根据航班旅客信息创建补偿单旅客实体信息
     * <AUTHOR>
     * @date 2022/3/14 9:38
     * @param accident
     * @return 补偿单旅客实体信息
     */
    @NotNull
    private CompensationPaxInfoDO getCompensationPaxInfoDO(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident) {
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setPaxId(accident.getPaxId());
        List<PassengerBasicInfoVO> passengerResult = compensationBasicDataService.getPassengerInfo(queryDTO);
        if(CollectionUtils.isEmpty(passengerResult)){
            throw new BusinessException(CompensationException.COMPENSATION_SAVE_ERROR,accident.getAccidentNo()+"事故单的旅客不存在");
        }
        PassengerBasicInfoVO passenger = passengerResult.get(0);

        CompensationPaxInfoDO compensationPaxInfoDO = ObjectUtils.copyBean(passenger, CompensationPaxInfoDO.class);
        compensationPaxInfoDO.setReceiveStatus(ReceiveStatusEnum.UNCLAIMED.getKey());
        compensationPaxInfoDO.setPaxStatus(passenger.getCheckStatus());
        compensationPaxInfoDO.setPkgNo(accident.getBaggageNo());
        compensationPaxInfoDO.setBabyPaxName(passenger.getBabyName());
        compensationPaxInfoDO.setCurrentAmount(command.getCustomCompensationMoney());
        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
        compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passenger.getTktDate(), dateTimeFormatter));
        compensationPaxInfoDO.setCancelTime(DateUtil.parse(passenger.getCancelTime()));
        return compensationPaxInfoDO;
    }
    @NotNull
    private CompensationPaxInfoDO getManualInputCompensationPaxInfoDO(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        //获取不到，界面没录入【值机状态、 携带婴儿姓名、购票时间、取消时间】
        CompensationPaxInfoDO compensationPaxInfoDO = new CompensationPaxInfoDO();

        compensationPaxInfoDO.setPaxId(accident.getPaxId());
        compensationPaxInfoDO.setIdNo(accident.getIdNo());
        String segment = extractAirportCodes(accident.getPaxSegment());
        compensationPaxInfoDO.setSegment(segment);
        compensationPaxInfoDO.setSegmentCh(accident.getPaxSegment());
        String[] split = segment.split("-");
        compensationPaxInfoDO.setOrgCityAirp(split[0]);
        compensationPaxInfoDO.setDstCityAirp(split[1]);
        compensationPaxInfoDO.setIdType(accident.getIdType());
        compensationPaxInfoDO.setPaxName(accident.getPaxName());
        compensationPaxInfoDO.setTktNo(accident.getTktNo());

        compensationPaxInfoDO.setReceiveStatus(ReceiveStatusEnum.UNCLAIMED.getKey());
        compensationPaxInfoDO.setPkgNo(accident.getBaggageNo());
        compensationPaxInfoDO.setCurrentAmount(command.getCustomCompensationMoney());
//        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
//        compensationPaxInfoDO.setPaxStatus(accident.getCheckStatus());
//        compensationPaxInfoDO.setBabyPaxName(passenger.getBabyName());
//        compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passenger.getTktDate(), dateTimeFormatter));
//        compensationPaxInfoDO.setCancelTime(DateUtil.parse(passenger.getCancelTime()));
        return compensationPaxInfoDO;
    }

    private CompensationFlightInfoDO getCompensationFlightInfoDOByInputSource(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident,CompensationOrderInfoDO compensationOrderInfoDO) {
        //旅客数据输入的来源=MANUAL_INPUT（人工输入）
        if(BaggageInputSourceTypeEnum.MANUAL_INPUT.getKey().equals(accident.getInputSource())){
            return getManualInputCompensationFlightInfoDO(command,accident,compensationOrderInfoDO);
        } //旅客数据输入的来源=SYSTEM（系统查询）
        else if(BaggageInputSourceTypeEnum.SYSTEM.getKey().equals(accident.getInputSource())){
            return getCompensationFlightInfoDO(command,accident,compensationOrderInfoDO);
        }else {
            return null;
        }
    }
    /***
     * @title getCompensationFlightInfoDO
     * @description  根据事故单信息创建赔付航班实体信息
     * <AUTHOR>
     * @date 2022/9/8 14:03
     * @param command
     * @param accident
     * @return com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO
     */
    private CompensationFlightInfoDO getCompensationFlightInfoDO(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident,CompensationOrderInfoDO compensationOrderInfoDO) {
        FocFlightInfoDTO focFlightInfoDTO = new FocFlightInfoDTO();
        focFlightInfoDTO.setFlightDate(accident.getPaxFlightDate());
        focFlightInfoDTO.setFlightNo(accident.getPaxFlightNo());
        List<FocFlightInfoVO> list = compensationBasicDataService.getFocFlightInfo(focFlightInfoDTO);
        log.info("【异常行李现金赔偿单】foc航班信息：【{}】", JSONUtil.toJsonStr(list));
        if(ObjectUtils.isEmpty(list)){
            return null;
        }
        //封装航班信息
        CompensationFlightInfoDO flightInfoDO = new CompensationFlightInfoDO();
        StringBuffer segment = new StringBuffer();
        StringBuffer segmentCh = new StringBuffer();
        StringBuffer flightId = new StringBuffer(list.get(0).getFlightId());

        String[] segments = accident.getPaxSegment().split("-");
        for (int i = 0; i < segments.length; i++) {
            if(i==segments.length-1){
                segment.append(segments[i].substring(segments[i].length()-3));
                segmentCh.append(segments[i].substring(0,segments[i].length()-3));
            }else {
                segment.append(segments[i].substring(segments[i].length()-3)).append("-");
                segmentCh.append(segments[i].substring(0,segments[i].length()-3)).append("-");
            }
        }
        if(list.size()>1){
            flightId.append(",").append(list.get(1).getFlightId());
        }
        flightInfoDO.setFlightId(flightId.toString());
        flightInfoDO.setFlightDate(accident.getPaxFlightDate());
        flightInfoDO.setFlightNo(accident.getPaxFlightNo());
        flightInfoDO.setSegment(segment.toString());
        flightInfoDO.setSegmentCh(accident.getPaxSegment());
        flightInfoDO.setOrderId(compensationOrderInfoDO.getId());
        flightInfoDO.setStd(accident.getStd());
        flightInfoDO.setSta(accident.getSta());
        flightInfoDO.setEtd(accident.getEtd());
        flightInfoDO.setAcType(list.get(0).getAcType());
        flightInfoDO.setPlaneCode(list.get(0).getAcReg());
        log.info("【异常行李现金赔偿单】赔付航班信息：【{}】", JSONUtil.toJsonStr(flightInfoDO));
        return flightInfoDO;
    }


    private CompensationFlightInfoDO getManualInputCompensationFlightInfoDO(CompensationCashAddCommandDTO command,BaggageAccidentInfoDO accident,CompensationOrderInfoDO compensationOrderInfoDO) {
        //封装航班信息
        CompensationFlightInfoDO flightInfoDO = new CompensationFlightInfoDO();
        flightInfoDO.setFlightId(accident.getPaxFlightId());
        flightInfoDO.setFlightDate(accident.getPaxFlightDate());
        flightInfoDO.setFlightNo(accident.getPaxFlightNo());
        flightInfoDO.setSegment(extractAirportCodes(accident.getPaxSegment()));
        flightInfoDO.setSegmentCh(accident.getPaxSegment());
        flightInfoDO.setOrderId(compensationOrderInfoDO.getId());
        flightInfoDO.setStd(accident.getStd());
        flightInfoDO.setSta(accident.getSta());
        flightInfoDO.setEtd(accident.getEtd());
        log.info("【异常行李现金赔偿单-手工录入】赔付航班信息：【{}】", JSONUtil.toJsonStr(flightInfoDO));
        return flightInfoDO;
    }

    private static String extractAirportCodes(String input) {

        //拉萨贡嘎LXA-成都双流CTU 获取LXA-CTU
        String[] split = input.split("-");

        return split[0].substring(split[0].length() - 3) + "-" + split[1].substring(split[1].length() - 3);
    }

}
