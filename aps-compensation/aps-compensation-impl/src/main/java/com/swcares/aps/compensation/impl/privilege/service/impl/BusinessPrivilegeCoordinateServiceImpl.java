package com.swcares.aps.compensation.impl.privilege.service.impl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.compensation.impl.constant.CompensationErrors;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapperForCoordinate;
import com.swcares.aps.compensation.impl.privilege.service.*;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeChangeHistory;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilegeItem;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Wang Yi
 * @Classname BusinessPrivilegeCoordinateServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/7/15 15:45
 * @Version 1.0
 */
@Service
@Slf4j
public class BusinessPrivilegeCoordinateServiceImpl implements BusinessPrivilegeCoordinateService {
//    @Autowired
//    BusinessPrivilegeItemMapper coordinateBusinessPrivilegeItemMapper;

    @Autowired
    BusinessPrivilegeMapperForCoordinate businessPrivilegeMapper;

    @Autowired
    BusinessPrivilegeChangeHistoryService businessPrivilegeChangeHistoryService;

    @Autowired
    BusinessPrivilegeServiceForCoordinate businessPrivilegeService;

    @Autowired
    BusinessPrivilegeItemServiceForCoordinate businessPrivilegeItemService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Override
    @Transactional
    public boolean coordinate(BusinessPrivilegeDTO businessPrivilegeDTO) {

        try {
            return updateBusinessPrivilegeFromCoordinateDTO(businessPrivilegeDTO);
        } catch (BusinessException e){
            log.error("同步业务授权数据失败", e);
            throw e;
        }
        catch (Exception e){
            log.error("同步业务授权数据失败", e);
            throw new BusinessException(CommonErrors.UNKNOW_ERROR, e.getMessage());
        }
    }

    private boolean updateBusinessPrivilegeFromCoordinateDTO(BusinessPrivilegeDTO businessPrivilegeDTO) {
        //获取接收方的租户信息
        Long tenantId = businessPrivilegeMapper.getTeanantIdByTenantCode(businessPrivilegeDTO.getRecipientCode().toUpperCase());

        if (tenantId == null || tenantId.equals(0L)){
            log.error("【业务授权协同】创建业务授权失败，接收方租户不存在，租户编码：{}", businessPrivilegeDTO.getRecipientCode());
            throw new BusinessException(CompensationErrors.RECEIVER_TENANT_NOT_EXISTING, businessPrivilegeDTO.getRecipientCode());
        }


        log.info("【业务授权】更新业务授权{}", JSONUtil.toJsonStr(businessPrivilegeDTO));

        //查找原有的业务授权数据
        LambdaQueryWrapper<AirlineBusinessPrivilege> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AirlineBusinessPrivilege::getRecipientCode, businessPrivilegeDTO.getRecipientCode());
        queryWrapper.eq(AirlineBusinessPrivilege::getGrantorCode, businessPrivilegeDTO.getGrantorCode());
        queryWrapper.eq(AirlineBusinessPrivilege::getTenantId, tenantId);
        AirlineBusinessPrivilege existingBusinessPrivilege = businessPrivilegeMapper.selectOne(queryWrapper);

        if (existingBusinessPrivilege == null){
            //不存在原有的数据，创建新的业务授权
            return createBusinessPrivilegeFromCoordinateDTO(businessPrivilegeDTO);
        }

        LambdaQueryWrapper<AirlineBusinessPrivilegeItem> itemQuery = new LambdaQueryWrapper<>();
        itemQuery.eq(AirlineBusinessPrivilegeItem::getBusinessPrivilegeId, existingBusinessPrivilege.getId());
        itemQuery.eq(AirlineBusinessPrivilegeItem::getTenantId, tenantId);
        // 目前存在的
        List<AirlineBusinessPrivilegeItem> existingBusiessPrivilegeItems = businessPrivilegeItemService.list(itemQuery);

        Map<AirlineBusinessPrivilegeItem, AirlineBusinessPrivilegeItem> toBeUpdatedBusinessPrivilegeItems = new HashMap<>();

        AirlineBusinessPrivilege newBusinessPrivilege = generateBusinessPrivilege(businessPrivilegeDTO, tenantId);
        newBusinessPrivilege.setId(existingBusinessPrivilege.getId());
        businessPrivilegeService.updateById(newBusinessPrivilege);
        log.info("【业务授权】更新业务授权，结果为{}", JSONUtil.toJsonStr(newBusinessPrivilege));

        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = generateBusinessPrivilegeItems(businessPrivilegeDTO, newBusinessPrivilege, tenantId);

        AirlineBusinessPrivilegeChangeHistory changeHistory =
                AirlineBusinessPrivilegeChangeHistory.createUpdateBusinessPrivilegeHistory(existingBusinessPrivilege,
                        newBusinessPrivilege, existingBusiessPrivilegeItems, newBusinessPrivilegeItems);

        //比较原有的和新的数据
        for(AirlineBusinessPrivilegeItem newData : newBusinessPrivilegeItems){
            existingBusiessPrivilegeItems.stream().forEach(existingData ->
                    {
                        if (existingData.getBusinessTypeCode().equalsIgnoreCase(newData.getBusinessTypeCode())){
                            newData.setId(existingData.getId());
                            toBeUpdatedBusinessPrivilegeItems.put(existingData, newData);
                        }
                    }
            );
        }
        // 补偿规则推送
        businessDataPushHandler.dataStore(0L, BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_RULE);
        businessDataPushHandler.dataStore(0L, BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_REPLACE);
        // 旅客类别推送
        businessDataPushHandler.dataStore(0L, BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.SERVICE_SUPPORT);
        //删除不再有的业务授权
        existingBusiessPrivilegeItems.removeAll(toBeUpdatedBusinessPrivilegeItems.keySet());
        List<Long> deletedIds = existingBusiessPrivilegeItems.stream().map(AirlineBusinessPrivilegeItem::getId).collect(Collectors.toList());
        if (deletedIds != null && !deletedIds.isEmpty()) {
            businessPrivilegeItemService.deleteBatch(deletedIds);
            log.info("【业务授权】更新业务授权-删除不再有的业务授权，被删除的数据{}", JSONUtil.toJsonStr(existingBusinessPrivilege));
        }


        //添加要增加的业务授权
        newBusinessPrivilegeItems.removeAll(toBeUpdatedBusinessPrivilegeItems.values());
        if (!newBusinessPrivilegeItems.isEmpty()) {
            businessPrivilegeItemService.saveBatch(newBusinessPrivilegeItems);
            log.info("【业务授权】更新业务授权-添加要增加的业务授权，新增的数据{}", JSONUtil.toJsonStr(newBusinessPrivilegeItems));
        }

        //更新要修改的业务授权
        if (!toBeUpdatedBusinessPrivilegeItems.isEmpty()) {
            businessPrivilegeItemService.updateBatchById(toBeUpdatedBusinessPrivilegeItems.values());
            log.info("【业务授权】更新业务授权-更新要修改的业务授权，修改的数据{}", JSONUtil.toJsonStr(toBeUpdatedBusinessPrivilegeItems));
        }


        //保存所有的业务授权变更历史
        businessPrivilegeChangeHistoryService.save(changeHistory);
        log.info("【业务授权】更新业务授权-保存所有的业务授权变更历史，变更历史数据{}", JSONUtil.toJsonStr(changeHistory));
        log.info("【业务授权】更新业务授权-更新业务授权成功");

        return true;
    }

    private boolean createBusinessPrivilegeFromCoordinateDTO(BusinessPrivilegeDTO businessPrivilegeDTO) {
        log.info("【业务授权协同】创建业务授权{}", JSONUtil.toJsonStr(businessPrivilegeDTO));
        //获取接收方的租户信息
        Long tenantId = businessPrivilegeMapper.getTeanantIdByTenantCode(businessPrivilegeDTO.getRecipientCode().toUpperCase());

        if (tenantId == null || tenantId.equals(0L)){
            log.error("【业务授权协同】创建业务授权失败，接收方租户不存在，租户编码：{}", businessPrivilegeDTO.getRecipientCode());
            throw new BusinessException(CompensationErrors.RECEIVER_TENANT_NOT_EXISTING, businessPrivilegeDTO.getRecipientCode());
        }

        //插入业务授权数据
        AirlineBusinessPrivilege businessPrivilege = generateBusinessPrivilege(businessPrivilegeDTO, tenantId);
        boolean saveResult = businessPrivilegeService.save(businessPrivilege);

        log.info("【业务授权协同】创建业务授权，结果为{}", JSONUtil.toJsonStr(businessPrivilege));
        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = generateBusinessPrivilegeItems(businessPrivilegeDTO, businessPrivilege, tenantId);

        businessPrivilegeItemService.saveBatch(newBusinessPrivilegeItems);
        log.info("【业务授权】创建业务授权明细，结果为{}", JSONUtil.toJsonStr(newBusinessPrivilegeItems));



        //插入业务授权历史数据
        AirlineBusinessPrivilegeChangeHistory changeHistory = AirlineBusinessPrivilegeChangeHistory.creatAddBusinessPrivilegeHistory(businessPrivilege, newBusinessPrivilegeItems);
        changeHistory.setTenantId(tenantId);
        businessPrivilegeChangeHistoryService.save(changeHistory);
        log.info("【业务授权】创建业务授权历史数据，结果为{}", JSONUtil.toJsonStr(changeHistory));

        return true;
    }

    @NotNull
    private static List<AirlineBusinessPrivilegeItem> generateBusinessPrivilegeItems(BusinessPrivilegeDTO businessPrivilegeDTO, AirlineBusinessPrivilege businessPrivilege, Long tenantId) {
        List<AirlineBusinessPrivilegeItem> newBusinessPrivilegeItems = new ArrayList<>();

        businessPrivilegeDTO.getBusinessPrivilegeItemList().forEach(businessPrivilegeItemDTO -> {
            AirlineBusinessPrivilegeItem businessPrivilegeItem = new AirlineBusinessPrivilegeItem();
            businessPrivilegeItem.setBusinessPrivilegeId(businessPrivilege.getId());
            businessPrivilegeItem.setBusinessTypeCode(businessPrivilegeItemDTO.getBusinessTypeCode());
            businessPrivilegeItem.setGrantBankroll(businessPrivilegeItemDTO.isGrantBankroll());
            businessPrivilegeItem.setCreatedTime(LocalDateTime.now());
            businessPrivilegeItem.setCreatedBy(businessPrivilege.getCreatedBy());
            businessPrivilegeItem.setUpdatedBy(businessPrivilege.getUpdatedBy());
            businessPrivilegeItem.setUpdatedTime(LocalDateTime.now());
            businessPrivilegeItem.setTenantId(tenantId);
            newBusinessPrivilegeItems.add(businessPrivilegeItem);

        });
        return newBusinessPrivilegeItems;
    }

    @NotNull
    private static AirlineBusinessPrivilege generateBusinessPrivilege(BusinessPrivilegeDTO businessPrivilegeDTO, Long tenantId) {
        AirlineBusinessPrivilege businessPrivilege = new AirlineBusinessPrivilege();

        businessPrivilege.setGrantorCode(businessPrivilegeDTO.getGrantorCode().toUpperCase());
        businessPrivilege.setGrantorCategory(businessPrivilegeDTO.getGrantorCategory());
        businessPrivilege.setGrantorName(businessPrivilegeDTO.getGrantorName());
        businessPrivilege.setRecipientCode(businessPrivilegeDTO.getRecipientCode());
        businessPrivilege.setRecipientCategory(businessPrivilegeDTO.getRecipientCategory());
        businessPrivilege.setRecipientName(businessPrivilegeDTO.getRecipientName());
        businessPrivilege.setDisabled(businessPrivilegeDTO.isDisabled());
        businessPrivilege.setRemark(businessPrivilegeDTO.getRemark());
        businessPrivilege.setUpdaterEmployeeName(businessPrivilegeDTO.getUpdaterEmployeeName());
        businessPrivilege.setUpdaterJobNumber(businessPrivilegeDTO.getUpdaterJobNumber());
        businessPrivilege.setTenantId(tenantId);
        businessPrivilege.setCreatedBy("coordinate");
        businessPrivilege.setCreatedTime(LocalDateTime.now());
        businessPrivilege.setUpdatedBy("coordinate");
        businessPrivilege.setUpdatedTime(LocalDateTime.now());
        return businessPrivilege;
    }


}
