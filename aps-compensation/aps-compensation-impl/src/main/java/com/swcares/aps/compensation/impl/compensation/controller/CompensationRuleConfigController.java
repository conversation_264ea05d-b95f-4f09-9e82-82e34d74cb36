package com.swcares.aps.compensation.impl.compensation.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationRuleConfigMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationRuleConfigService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.model.rools.dto.*;
import com.swcares.aps.compensation.model.rools.entity.CompensationRuleConfig;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/compensation/rule")
@Api(tags = "补偿单规则配置接口")
@ApiVersion(value = "补偿单规则配置接口 v1.0.1")
public class CompensationRuleConfigController extends BaseController {

    @Autowired
    CompensationRuleConfigMapper compensationRuleConfigMapper;
    @Autowired
    CompensationRuleConfigService compensationRuleConfigService;
    @Autowired
    BusinessDataPushHandler businessDataPushHandler;
    @GetMapping("/getAll")
    @ApiOperation(value = "补偿单规则查询")
    public BaseResult<List<Map<String,CompensationRuleFactoryDTO>>> getAll() {
        List<CompensationRuleConfig> compensationRuleConfigs = compensationRuleConfigMapper.selectList(
                Wrappers.lambdaQuery(CompensationRuleConfig.class)
                        .eq(CompensationRuleConfig::getAirlineCode, UserContext.getCurrentUser().getTenantCode())
        );
        if (compensationRuleConfigs.isEmpty()){
            return ok();
        }
        List<Map<String,CompensationRuleFactoryDTO>> result = new ArrayList<>();
        Map<String, List<CompensationRuleConfig>> collect = compensationRuleConfigs.stream().collect(Collectors.groupingBy(CompensationRuleConfig::getAirlineCode));
        if (collect.get(UserContext.getCurrentUser().getTenantCode())==null){
            collect.put(UserContext.getCurrentUser().getTenantCode(),new ArrayList<>());
        }
        collect.forEach((k, v)->{
            Map<String,CompensationRuleFactoryDTO> map = new HashMap<>();
            CompensationRuleFactoryDTO compensationRuleFactoryDTO = new CompensationRuleFactoryDTO();
            v.forEach(rule ->{
                compensationRuleFactoryDTO.setAirlineCode(rule.getAirlineCode());
                compensationRuleFactoryDTO.setAirlineName(rule.getAirlineName());
                if (rule.getType() == 1){
                    compensationRuleFactoryDTO.setAbnormalFlight(JSON.parseObject(rule.getContent(), AbnormalFlightDTO.class));
                }
                if (rule.getType() == 2){
                    compensationRuleFactoryDTO.setAbnormalBaggage(JSON.parseObject(rule.getContent(), AbnormalBaggageDTO.class));
                }
                if (rule.getType() == 3){
                    compensationRuleFactoryDTO.setOverBook(JSON.parseObject(rule.getContent(), OverBookDTO.class));
                }
                if (rule.getType() == 4){
                    compensationRuleFactoryDTO.setComplaint(JSON.parseObject(rule.getContent(), ComplaintDTO.class));
                }
                if (rule.getType() == 5){
                    compensationRuleFactoryDTO.setCashExchange(StrUtil.isBlank(rule.getContent()) ? BigDecimal.ZERO : new BigDecimal(rule.getContent()));
                }
            });
            map.put(compensationRuleFactoryDTO.getAirlineCode(), compensationRuleFactoryDTO);
            result.add(map);
        });
        return ok(result);
    }

    @PostMapping("/save")
    @ApiOperation(value = "补偿单规则保存")
    public BaseResult<CompensationRuleFactoryDTO> save(@RequestBody CompensationRuleFactoryDTO request) {
        if (!request.check()) {
            throw new BusinessException(CommonErrors.UNKNOW_ERROR);
        }
        LambdaQueryWrapper<CompensationRuleConfig> lq = Wrappers.lambdaQuery();
        LambdaQueryWrapper<CompensationRuleConfig> eq = lq.eq(CompensationRuleConfig::getType, request.getType())
                .eq(CompensationRuleConfig::getAirlineCode, UserContext.getCurrentUser().getTenantCode());
        CompensationRuleConfig compensationRuleConfig = compensationRuleConfigService.getOne(eq);
        if (compensationRuleConfig == null){
            compensationRuleConfig = new CompensationRuleConfig();
        }
        compensationRuleConfig.setUpdatedBy(UserContext.getCurrentUser().getUsername());
        compensationRuleConfig.setUpdatedTime(LocalDateTime.now());
        compensationRuleConfig.setAirlineCode(UserContext.getCurrentUser().getTenantCode());
        compensationRuleConfig.setAirlineName(UserContext.getCurrentUser().getTenantName());
        if (request.getType() == 1){
            compensationRuleConfig.setType(request.getType());
            compensationRuleConfig.setContent(JSON.toJSONString(request.getAbnormalFlight()));
            compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
        }
        if (request.getType() == 2){
            compensationRuleConfig.setType(request.getType());
            compensationRuleConfig.setContent(JSON.toJSONString(request.getAbnormalBaggage()));
            compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
        }
        if (request.getType() == 3){
            compensationRuleConfig.setType(request.getType());
            compensationRuleConfig.setContent(JSON.toJSONString(request.getOverBook()));
            compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
        }
        if (request.getType() == 4){
            compensationRuleConfig.setType(request.getType());
            compensationRuleConfig.setContent(JSON.toJSONString(request.getComplaint()));
            compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
        }
        if (request.getType() == 5){
            compensationRuleConfig.setType(request.getType());
            compensationRuleConfig.setContent(request.getCashExchange().toString());
            compensationRuleConfigService.saveOrUpdate(compensationRuleConfig);
        }
        // 补偿规则推送
        businessDataPushHandler.dataStore(compensationRuleConfig.getId(), BusinessDataSyncConstant.BUSINESS_BASE_INFO, BusinessDataSyncConstant.DATA_TYPE_RULE);
        return ok();
    }

}
