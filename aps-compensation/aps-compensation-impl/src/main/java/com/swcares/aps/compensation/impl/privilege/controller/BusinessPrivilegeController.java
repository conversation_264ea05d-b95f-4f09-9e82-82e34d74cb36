package com.swcares.aps.compensation.impl.privilege.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeCoordinateService;
import com.swcares.aps.compensation.impl.privilege.service.BusinessPrivilegeService;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeRequestDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import com.swcares.baseframe.common.annotation.ApiVersion;


import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname PriviledgeController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 08:48
 * @Version 1.0
 */
@RestController
@RequestMapping("/businessPrivilege")
@Api(tags = "业务授权接口")
@ApiVersion(value = "航司端给机场业务授权 v1.0")
@Valid
public class BusinessPrivilegeController extends BaseController {
    @Autowired
    BusinessPrivilegeService businessPrivilegeService;

    @Autowired
    BusinessPrivilegeCoordinateService businessPrivilegeCoordinateService;

    @ApiOperation(value = "新建对机场的业务授权")
    @PostMapping("/v1/create")
    public BaseResult<Boolean> createAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto)
    {
        Boolean result = businessPrivilegeService.create(dto);
        return ok(result);
    }

    @ApiOperation(value = "修改对机场的业务授权")
    @PostMapping("/v1/update")
    public BaseResult<Boolean> updateAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto)
    {
        Boolean result = businessPrivilegeService.update(dto);
        return ok(result);
    }

    @ApiOperation(value="分页查询业务授权列表")
    @PostMapping("/v1/list")
    public PagedResult<List<BusinessPrivilegeDTO>> list(@RequestBody SearchBusinessPrivilegeDTO searchCriteria)
    {
        IPage<BusinessPrivilegeDTO> result = businessPrivilegeService.search(searchCriteria);

        return ok(result);
    }

    @ApiOperation(value="查询对指定的机场的业务授权详情")
    @RequestMapping("/v1/detail")
    public BaseResult<BusinessPrivilegeDTO> getDetail(@RequestParam(value = "id") Long id)
    {
        BusinessPrivilegeDTO detail = businessPrivilegeService.getDetail(id);
        return ok(detail);
    }

    @RequestMapping("/v1/detailByAirportCode")
    public BaseResult<BusinessPrivilegeDTO> getDetailByAirportCode(@RequestParam(value = "airportCode") String airportCode){
        BusinessPrivilegeDTO detail = businessPrivilegeService.getDetailByAirport(airportCode);
        return ok(detail);
    }

    @ApiOperation(value="启用")
    @PostMapping("/v1/enable")
    public BaseResult<Boolean> enableBusinessPrivileges(@RequestBody List<Long> ids)
    {
        boolean result = businessPrivilegeService.switchStatus(ids, false);
        return ok(result);
    }

    @ApiOperation(value="停用")
    @PostMapping("/v1/disable")
    public BaseResult<Boolean> disableBusinessPrivileges(@RequestBody List<Long> ids)
    {
        boolean result = businessPrivilegeService.switchStatus(ids, true);
        return ok(result);
    }


    @ApiOperation(value = "同步航司对机场的授权数据")
    @PostMapping("/v1/coordinate")
    public BaseResult<Boolean> updateAirportBusinessPrivilege(@RequestBody BusinessPrivilegeDTO dto)
    {
        Boolean result = businessPrivilegeCoordinateService.coordinate(dto);
        return ok(result);
    }

    @ApiOperation(value = "航司判断业务是否被授权机场")
    @GetMapping("/verify/{code}")
    public BaseResult<Boolean> verify(@RequestParam("code") String typeCode,@RequestParam("airCode") String airCode){
        return ok(businessPrivilegeService.getAirportPrivilege(typeCode,airCode));
    }
}
