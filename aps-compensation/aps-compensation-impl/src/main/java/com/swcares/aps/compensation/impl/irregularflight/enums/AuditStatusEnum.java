package com.swcares.aps.compensation.impl.irregularflight.enums;

/**
 * ClassName：irregularflight.enums <br>
 * Description：审核状态<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 10月14日 14:28 <br>
 * @version v1.0 <br>
 */
public enum AuditStatusEnum {
    //同意AGREE、拒绝REJECT、驳回BACK
    SUBMIT("SUBMIT","发起流程"),
    AGREE("AGREE", "同意"),
    DISAGREE("REJECT", "不同意"),
    REJECT("BACK", "驳回");

    private AuditStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static AuditStatusEnum build(String key) {
        return build(key, true);
    }

    public static AuditStatusEnum build(String key, boolean throwEx) {
        AuditStatusEnum typeEnum = null;
        for (AuditStatusEnum element : AuditStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + AuditStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }

}
