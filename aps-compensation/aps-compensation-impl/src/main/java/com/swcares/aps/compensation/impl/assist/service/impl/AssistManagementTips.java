package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.assist.constant.AssistApplyTips;
import com.swcares.aps.compensation.impl.assist.service.AssistManagementTipsService;
import com.swcares.aps.compensation.impl.assist.utils.SendSMSUtils;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceConfigService;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationAuditInfoMapper;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.aps.compensation.model.dataconfig.entity.DataConfigDO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.components.msg.CustomerMessageRemoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @title: AssistManagementTips
 * @projectName aps
 * @description: 管理层提示具体实现类
 * @date 2022/2/16 10:35
 */
@Service
@Slf4j
public class AssistManagementTips implements AssistManagementTipsService {

//    @Autowired
//    private CustomerMessageRemoteService customerMessageRemoteService;

    @Autowired
    private ReplaceConfigService configService;
    @Autowired
    private CompensationAuditInfoMapper compensationAuditInfoMapper;

    @Autowired(required = false)
    private SMSService smsService;

    @Override
    public void sendSMSToManagement(String phone) {
        log.info("【aps-compensation-impl-assist】超过6人消息推送给管理员，代领人号码：【{}】",phone);
        // 获取管理层手机号
        DataConfigDO byTypeAndSubType = configService.findByTypeAndSubType(AssistApplyTips.ASSISTS_CONFIG_TYPE, AssistApplyTips.ASSISTS_MANAGER_ROLE);
        String[] roleIds = byTypeAndSubType.getValue().split(",");
        List<CompensationReviewerInfoVO> infoVOS = compensationAuditInfoMapper.findReviewer(null, null, roleIds, null, null);
        List<String> phones = infoVOS.stream().map(CompensationReviewerInfoVO::getReviewerPhone).collect(Collectors.toList());
//        customerMessageRemoteService.sendNow(SendSMSUtils.sendSMSDTO(phones,String.format(AssistApplyTips.MANAGEMENT_MSG,phone)));
//        List<CustomerMessageDepositoryDTO> list = SendSMSUtils.sendSMSDTO(phones,String.format(AssistApplyTips.MANAGEMENT_MSG,phone));
        smsService.sendPassengerNow(SendSMSUtils.sendSMSDTO(phones,String.format(AssistApplyTips.MANAGEMENT_MSG,phone)));
    }
}
