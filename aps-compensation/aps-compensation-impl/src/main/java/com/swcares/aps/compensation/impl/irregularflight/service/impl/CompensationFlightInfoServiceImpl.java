package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationFlightInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import org.springframework.stereotype.Service;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/modelCompensationFlightInfo.java/irregularflight.service.impl.CompensationPaxInfoServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Service
public class CompensationFlightInfoServiceImpl extends ServiceImpl<CompensationFlightInfoMapper, CompensationFlightInfoDO> implements CompensationFlightInfoService {

}
