package com.swcares.aps.compensation.impl.datasync.constant;

/**
 * @ClassName：OverBookConstant
 * @Description：数据同步常量
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 15:22
 * @version： v1.0
 */
public interface BusinessDataSyncConstant {

    /*---------------业务类型-----------------------*/
    // 不正常航班
    String BUSINESS_IRREGULAR_FLIGHT = "IRREGULAR_FLIGHT";
    // 异常行李
    String BUSINESS_ABNORMAL_LUGGAGE = "ABNORMAL_LUGGAGE";
    // 超售
    String BUSINESS_OVER_BOOKING = "OVER_BOOKING";
    // 旅客投诉
    String BUSINESS_COMPLAINT = "COMPLAINT";

    String BUSINESS_BASE_INFO = "BASE";

    /*---------------------------------------------*/

    /*---------------数据类型-----------------------*/

    String DATA_TYPE_ACCIDENT = "ACCIDENT";

    String DATA_TYPE_COMPENSATION = "COMPENSATION";

    String DATA_TYPE_APPLY = "APPLY";

    String DATA_TYPE_RULE = "RULE";

    String DATA_TYPE_REPLACE = "REPLACE";

    String DATA_TYPE_TRANSPORT = "TRANSPORT";

    String SERVICE_SUPPORT = "SERVICE_SUPPORT";

    String CABIN_TYPE = "CABIN";

    String PUSH_STATUS_WAIT = "WAIT";

    String PUSH_STATUS_FAIL = "FAIL";



    String PUSH_STATUS_SUCCESS = "SUCCESS";
}
