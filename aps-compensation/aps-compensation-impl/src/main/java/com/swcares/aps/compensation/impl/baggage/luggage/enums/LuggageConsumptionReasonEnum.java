package com.swcares.aps.compensation.impl.baggage.luggage.enums;

/**
 * <AUTHOR>
 * @title: LuggageStockStatusEnums
 * @projectName aps
 * @description: 箱包库存动作类别
 * @date 2022/2/8 15:47
 */
public enum LuggageConsumptionReasonEnum {
    SUBMIT_COMPENSATION("1", "提交补偿单","2"),
    EXAMINE_FAIL("2", "审核不通过","7"),
    CLOSE_UNCLAIMED("3", "关闭未领取","5"),
    OVERDUE_UNCLAIMED("4", "逾期未领取","6"),//生效逾期
    PASS_CLOSE_UNCLAIMED("5", "关闭未领取","8"),
    PASS_OVERDUE_UNCLAIMED("6", "逾期未领取","9"),
    REJECT_STARTER("7", "驳回到发起人","1"),
    ACT_PASS_OVERDUE_UNCLAIMED("6", "逾期未领取","3"),//审核通过逾期
    EFFECTIVE_OVERDUE_UNCLAIMED("4", "逾期未领取","4");//生效->逾期;

    private LuggageConsumptionReasonEnum(String key, String value, String compensateStatus) {
        this.key = key;
        this.value = value;
        this.compensateStatus=compensateStatus;
    }

    private String key;

    private String value;

    private String compensateStatus;

    public String getStatus() {
        return compensateStatus;
    }

    public void setStatus(String compensateStatus) {
        this.compensateStatus = compensateStatus;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static LuggageConsumptionReasonEnum build(String key) {
        return build(key, true);
    }

    public static LuggageConsumptionReasonEnum build(String key, boolean throwEx) {
        LuggageConsumptionReasonEnum typeEnum = null;
        for (LuggageConsumptionReasonEnum element : LuggageConsumptionReasonEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + LuggageConsumptionReasonEnum.class.getSimpleName());
        }
        return typeEnum;
    }

    public static LuggageConsumptionReasonEnum buildStatus(String compensateStatus) {
        return buildStatus(compensateStatus, true);
    }

    public static LuggageConsumptionReasonEnum buildStatus(String compensateStatus, boolean throwEx) {
        LuggageConsumptionReasonEnum typeEnum = null;
        for (LuggageConsumptionReasonEnum element : LuggageConsumptionReasonEnum.values()) {
            if (element.getStatus().equals(compensateStatus)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + compensateStatus + ",请核对" + LuggageConsumptionReasonEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
