package com.swcares.aps.compensation.impl.sms.impl;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderService;
import com.swcares.aps.compensation.impl.irregularflight.constant.CompensationConstant;
import com.swcares.aps.compensation.impl.irregularflight.enums.AuditStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationAuditInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.mapper.CompensationOrderInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.msg.AuditTemplateMsg;
import com.swcares.aps.compensation.impl.sms.SendMessageAsyncService;
import com.swcares.aps.compensation.impl.sms.constant.CompensationSmsConstant;
import com.swcares.aps.compensation.impl.sms.service.SMSService;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationSMSDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO;
import com.swcares.aps.component.dict.model.vo.DictCacheVO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.msg.api.EmployeeSnsRecordApi;
import com.swcares.aps.msg.api.PassengerSmsRecordApi;
import com.swcares.aps.msg.api.PassengerWechatRecordApi;
import com.swcares.aps.msg.model.dto.AssistantSmsSendDTO;
import com.swcares.aps.msg.model.dto.PassengerWechatSubscribeMsgSendDTO;
import com.swcares.aps.msg.model.vo.SnsSendResultVO;
import com.swcares.aps.usercenter.model.innermail.dto.InnerMailSendDto;
import com.swcares.aps.usercenter.model.uc.vo.ReaptvUserDetailVO;
import com.swcares.aps.usercenter.remote.api.innermail.CustomerMessageApi;
import com.swcares.aps.usercenter.remote.api.innermail.MessageInnerMailApi;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvDictApi;
import com.swcares.aps.usercenter.remote.api.uc.ReaptvUserApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.components.msg.dto.CustomerMessageDepositoryDTO;
import com.swcares.components.msg.dto.InteriorMessageReceiveDTO;
import com.swcares.components.msg.dto.InteriorMessageReceiveUserDTO;
import com.swcares.components.msg.dto.InteriorMessageSendDTO;
import com.swcares.components.msg.entity.InteriorMessageDepository;
import com.swcares.components.msg.enums.ReceiveModeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;


/**
 * @ClassName：SMSServiceimpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 谭睿
 * @Date： 2022/9/1 15:23
 * @version： v1.0
 */
@Service
@Slf4j
public class SMSServiceImpl implements SMSService {

    @Autowired
    private ApplyOrderService applyOrderService;

    @Autowired
    private MessageInnerMailApi messageInnerMailApi;

    @Autowired
    private CustomerMessageApi customerMessageApi;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private EmployeeSnsRecordApi employeeSnsRecordApi;

    @Autowired
    private CompensationOrderInfoMapper compensationOrderInfoMapper;

    @Autowired
    private CompensationAuditInfoMapper compensationAuditInfoMapper;

    @Autowired
    private CompensationAuditInfoService compensationAuditInfoService;

    @Autowired
    private PassengerWechatRecordApi passengerWechatRecordApi;

    @Autowired
    PassengerSmsRecordApi passengerSmsRecordApi;

    @Autowired
    SendMessageAsyncService sendMessageAsyncService;

    @Autowired
    ReaptvUserApi userApi;
    @Autowired
    ReaptvDictApi reaptvDictApi;

    @Value(value = "${official.receive.retry}")
    private boolean RETRY;

    @Override
    public void sendSMS(CompensationSMSDTO dto) {
        Map<String, List<DictCacheVO>> data = reaptvDictApi.getDict("accident_type").getData();
        log.info("SMSServiceImpl查询accident_type数据字典记录：【{}】", JSONUtil.toJsonStr(data));
        String accidentSubType = null;
        for (Map.Entry<String, List<DictCacheVO>> entry : data.entrySet()) {
            List<DictCacheVO> valueList = entry.getValue();
            DictCacheVO dictCacheVO = valueList.stream()
                    .filter(obj -> obj.getDictValue().equals(dto.getAccidentSubType()))
                    .findFirst()
                    .orElse(null);
            if(dictCacheVO != null){
                accidentSubType = dictCacheVO.getDictLabel();
                break;
            }
        }
        //内部消息发送对象
        InteriorMessageSendDTO sendDTO = new InteriorMessageSendDTO();
        //设置内部消息对象
        String msgTitle = String.format(CompensationSmsConstant.AUDIT_AGREE_TITLE,dto.getOrderNo());
        String msgContent = String.format(String.format(CompensationSmsConstant.AUDIT_AGREE_CONTENT,dto.getFlightDate(),dto.getFlightNo()
                ,dto.getOrderNo(),dto.getSumMoney(), accidentSubType));
        InteriorMessageDepository interiorMessageDepository = getInteriorMessageDepository(msgTitle, msgContent, dto.getH5Url(), dto.getWebUrl());
        sendDTO.setInteriorMessageDepository(interiorMessageDepository);
        List<CompensationAuditReviewerVO> compensationAuditReviewerVOS = new ArrayList<>();
        //通过userId查询用户信息
        CompensationAuditReviewerVO userBasicInfo = getUserBasicInfo(dto.getUserId());
        compensationAuditReviewerVOS.add(userBasicInfo);
        List<InteriorMessageReceiveDTO> interiorMessageReceiveDTOList = getInteriorMessageReceiveDTOList(compensationAuditReviewerVOS);
        sendDTO.setReceiveList(interiorMessageReceiveDTOList);
        //发送站内信
        messageInnerMailApi.send(sendDTO);
    }

    @Override
    public void sendPassengerSMS(CompensationSMSDTO dto) {
        Authentication authentication = UserContext.getAuthentication();
        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)authentication.getDetails();
        String tokenValue = details.getTokenValue();
        String tokenSms = redisUtil.get(CompensationSmsConstant.AUTH_TOKEN_SMS_PREFIX + tokenValue);
        if(StringUtils.isNotEmpty(tokenSms)){
            //redis不为空，说明还在5分钟内，token频繁调用短信接口
            throw new BusinessException(ApplyErrors.SMS_FREQUENT_CALL_ERROR);
        }

        String key = CompensationSmsConstant.AUTH_PHONE_PREFIX+dto.getPhone();
        String oldCode = redisUtil.get(key);

        if(StringUtils.isNotEmpty(oldCode)){
            //redis不为空，说明还在5分钟内，直接弹窗给用户说还在使用期
            throw new BusinessException(ApplyErrors.SMS_CODE_NOT_EXPIRED);
        }

        String newCode = String.format("%06d", new Random().nextInt(999999));
        redisUtil.set(key,newCode,CompensationSmsConstant.AUTH_PHONE_PREFIX_PAST_DUE);

        LoginUserDetails user = UserContext.getUser();

        String content = String.format(CompensationSmsConstant.PASSENGER_SMS_CONTENT,user.getTenantName(),newCode,user.getTenantName());

        //调用王哥新写的旅客短信接口（不含业务意义的）--给旅客发送验证码
        AssistantSmsSendDTO assistantSmsSendDTO = new AssistantSmsSendDTO();
        assistantSmsSendDTO.setPhoneNumber(dto.getPhone());
        assistantSmsSendDTO.setContent(content);
        passengerSmsRecordApi.sendAssistantSms(assistantSmsSendDTO);

        //短信发送后，将token保存，有效时间5分钟
        redisUtil.set(CompensationSmsConstant.AUTH_TOKEN_SMS_PREFIX+tokenValue,newCode,CompensationSmsConstant.AUTH_PHONE_PREFIX_PAST_DUE);

        //短信获取成功后,重置短信验证失败次数
        redisUtil.del(CompensationSmsConstant.AUTH_TOKEN_SMS_CODE_FAIL_PREFIX + tokenValue+ dto.getPhone());
    }

    @Override
    public void sendInnerSMS(NodeNoticeDTO noticeDTO, String auditStatus){
        try{
            String orderId = noticeDTO.getBusiKey();

            LambdaQueryWrapper<CompensationOrderInfoDO> wrapper = new LambdaQueryWrapper<CompensationOrderInfoDO>().eq(CompensationOrderInfoDO::getId, orderId);
            CompensationOrderInfoDO dto = compensationOrderInfoMapper.selectOne(wrapper);
            log.info("【工作人员消息推送-站内信&公众号】赔偿单id:{},审核状态auditStatus:{},OrderInfoDO:{},noticeDTO:{}",orderId,auditStatus, JSON.toJSONString(dto),JSON.toJSONString(noticeDTO));
            if(StringUtils.isNotEmpty(auditStatus)){dto.setStatus(auditStatus);}

            InnerMailSendDto mailSendDto = new InnerMailSendDto();
            InteriorMessageDepository interiorMessageDepository = new InteriorMessageDepository();
            List<CompensationAuditReviewerVO> compensationAuditReviewerVOS = new ArrayList<>();
            //消息内容获取
            String modelId = getMsgContentByAudit(mailSendDto,noticeDTO,dto,interiorMessageDepository,compensationAuditReviewerVOS);

            //当出现标题或内容为空，则属于其他情况，不发送站内信
            if(interiorMessageDepository.getMsgContent() == null
                    || interiorMessageDepository.getMsgTitle() == null
                    || CollectionUtils.isEmpty(compensationAuditReviewerVOS)) {
                return;
            }

            List<InteriorMessageReceiveDTO> receiveDTOList = getInteriorMessageReceiveDTOList(compensationAuditReviewerVOS);
            InteriorMessageSendDTO sendDTO = new InteriorMessageSendDTO();
            sendDTO.setInteriorMessageDepository(interiorMessageDepository);
            sendDTO.setReceiveList(receiveDTOList);

            sendMessageAsyncService.sendWeChatMessageHandle(dto, compensationAuditReviewerVOS, interiorMessageDepository, modelId);

//            sendMessageAsyncService.messageInnerMailHandle(sendDTO);


            mailSendDto.setInteriorMessageDepository(interiorMessageDepository);
            mailSendDto.setReceiveList(receiveDTOList);
            messageInnerMailApi.sendMessageAsUser(mailSendDto);

        }catch (Exception e){
            log.error("【工作人员消息推送-站内信&公众号】站内信消息推送失败！noticeDTO【{"+ JSONObject.toJSONString(noticeDTO) +"}】， 失败原因：【{}】", e);
        }
    }

    private String getMsgContentByAudit(InnerMailSendDto mailSendDto,NodeNoticeDTO noticeDTO,CompensationOrderInfoDO dto,InteriorMessageDepository messageDepository,List<CompensationAuditReviewerVO> compensationAuditReviewerVOS){
        if(StringUtils.isBlank(dto.getCreatedBy())){
            dto.setCreatedBy("");
        }else{
            dto.setCreatedBy(compensationAuditInfoMapper.getUserId(dto.getCreatedBy()));
        }

        if(StringUtils.isBlank(noticeDTO.getReviewer()) ||  WorkflowUtils.isSyncProcessTask(noticeDTO.getNodeKey())){
            mailSendDto.setSenderName("system");
        }else {
            mailSendDto.setSenderName(noticeDTO.getReviewer());
        }

        messageDepository.setSystemCode(CompensationSmsConstant.SYSTEM_CODE);
        messageDepository.setBusinessType(CompensationSmsConstant.STAFF_BUSINESS_TYPE);
        messageDepository.setMasterId(0L);
        messageDepository.setUrgency("2");
        messageDepository.setSendType(0);
        //公众号消息推送模板id,默认推送结果通知模板
        String modelId =  CompensationSmsConstant.OFFICIAL_RESULT_MODEL;
        //获取审核记录
        List<CompensationAuditRecordVO> auditRecords = compensationAuditInfoService.findAuditRecord(dto.getId(), dto.getOrderNo());
        log.info("SMSServiceImpl查询审核记录：【{}】", JSONUtil.toJsonStr(auditRecords));
        Map<String, List<DictCacheVO>> data = reaptvDictApi.getDict("accident_type").getData();
        log.info("SMSServiceImpl查询accident_type数据字典记录：【{}】", JSONUtil.toJsonStr(data));
//        String accidentSubType = null;
        String accidentType = null;
        for (Map.Entry<String, List<DictCacheVO>> entry : data.entrySet()) {
            List<DictCacheVO> valueList = entry.getValue();
            DictCacheVO dictCacheVO = valueList.stream()
                    .filter(obj -> obj.getDictValue().equals(dto.getAccidentType()))
                    .findFirst()
                    .orElse(null);
            if(dictCacheVO != null){
                accidentType = dictCacheVO.getDictLabel();
                break;
            }
        }

        int index = auditRecords.size() - 1;

        switch (noticeDTO.getOptionCode()) {
            case CompensationConstant.AUDIT_DISAGREE:
                messageDepository.setMsgTitle(String.format(CompensationSmsConstant.AUDIT_DISAGREE_TITLE,dto.getOrderNo()));
                messageDepository.setMsgContent(String.format(CompensationSmsConstant.AUDIT_DISAGREE_CONTENT,dto.getFlightDate(),dto.getFlightNo()
                        ,dto.getOrderNo(),dto.getSumMoney(), accidentType));
                CompensationAuditReviewerVO userBasicInfo = getUserBasicInfo(dto.getCreatedBy());
                log.info("补偿单审核不通过，通过创建人查询对应的用户信息【{}】",JSONUtil.toJsonStr(userBasicInfo));
                if(userBasicInfo!=null){
                    compensationAuditReviewerVOS.add(userBasicInfo);
                }
                break;
            case CompensationConstant.AUDIT_AGREE:
                log.info("上一节点操作人执行结果为同意，判断下一节点状态。当前的审核记录为：{}", JSONUtil.toJsonStr(auditRecords));
                if(CompensationConstant.NODE_END.equals(noticeDTO.getNodeKey())){
                    messageDepository.setMsgTitle(String.format(CompensationSmsConstant.AUDIT_AGREE_END_TITLE,dto.getOrderNo()));
                    messageDepository.setMsgContent(String.format(CompensationSmsConstant.AUDIT_AGREE_END_CONTENT,dto.getFlightDate(),dto.getFlightNo()
                            ,dto.getOrderNo(),dto.getSumMoney(), accidentType));

                    CompensationAuditReviewerVO ub=getUserBasicInfo(dto.getCreatedBy());
                    if(ub!=null){
                        compensationAuditReviewerVOS.add(ub);
                    }

                }
                //不为结束节点，直接查询节点状态为ready的节点
                else{
                    modelId = CompensationSmsConstant.OFFICIAL_TODO_MODEL;
                    messageDepository.setMsgTitle(String.format(CompensationSmsConstant.AUDIT_AGREE_TITLE,dto.getOrderNo()));
                    messageDepository.setMsgContent(String.format(CompensationSmsConstant.AUDIT_AGREE_CONTENT,dto.getFlightDate(),dto.getFlightNo()
                            ,dto.getOrderNo(),dto.getSumMoney(), accidentType));
                    try{
                        BaseResult<ReaptvUserDetailVO> userApiById = userApi.getById(Long.valueOf(dto.getCreatedBy()));
                        ReaptvUserDetailVO detailVO = userApiById.getData();
                        mailSendDto.setSenderName(detailVO.getName());

                    }catch (Exception e){
                        log.error("userApi.getById",e);
                        mailSendDto.setSenderName("system");
                    }
                    while (index != 0){
                        if(auditRecords.get(index).getStatus().equals("ready")){
                            break;
                        } ;
                        index--;
                    }
                    List<CompensationAuditReviewerVO> waiteAuditReviewers = auditRecords.get(index).getWaiteAuditReviewers();
                    if(CollectionUtils.isNotEmpty(waiteAuditReviewers)){
                        List<CompensationAuditReviewerVO> waites=waiteAuditReviewers
                                .stream()
                                .filter(t->StringUtils.isNotBlank(t.getReviewerPhone()))
                                .collect(Collectors.toList());
                        if(CollectionUtils.isNotEmpty(waites)){
                            compensationAuditReviewerVOS.addAll(waites);
                        }
                    }

                }
                break;
            case CompensationConstant.AUDIT_REJECT:
                log.info("上一节点操作人执行结果为驳回。当前的审核记录为：{}", JSONUtil.toJsonStr(auditRecords));
                modelId = CompensationSmsConstant.OFFICIAL_TODO_MODEL;
                messageDepository.setMsgTitle(String.format(CompensationSmsConstant.AUDIT_REJECT_TITLE,dto.getOrderNo()));
                messageDepository.setMsgContent(String.format(CompensationSmsConstant.AUDIT_REJECT_CONTENT,dto.getFlightDate(),dto.getFlightNo()
                        ,dto.getOrderNo(),dto.getSumMoney(), accidentType));
                while(index != 0){
                    if(AuditStatusEnum.REJECT.getKey().equals(auditRecords.get(index).getNodeName()) && index == auditRecords.size()-1) {
                        index = 0;
                        break;
                    }
                    if(auditRecords.get(index).getStatus().equals("ready") && AuditStatusEnum.REJECT.getKey().equals(auditRecords.get(index-1).getNodeName())){
                        break;
                    }
                    index--;
                }
                CompensationAuditReviewerVO userBasicInfo1 = getUserBasicInfo(auditRecords.get(index).getReviewerId());
                if(userBasicInfo1!=null){
                    compensationAuditReviewerVOS.add(userBasicInfo1);
                }


                break;
        }
        //根据补偿单类型，传递对应的url链接
        if(CompensationAccidentTypeEnum.IRREGULAR_FLIGHT.getKey().equals(dto.getAccidentType())){
            //不正常航班
            messageDepository.setTargetUrl(String.format(CompensationSmsConstant.WEB_URL_SUFFIX, dto.getAccidentId().toString(), dto.getChoiceSegment().replaceAll(",", "%2C"), dto.getId().toString(), dto.getStatus(),dto.getOrderNo(),dto.getAccidentType()));
            messageDepository.setH5TargetUrl(String.format(CompensationSmsConstant.H5_COMPENSATION_SUFFIX, dto.getId().toString(),dto.getOrderNo()));

        }
        else if(CompensationAccidentTypeEnum.ABNORMAL_BAGGAGE.getKey().equals(dto.getAccidentType())){
            //异常行李
            messageDepository.setTargetUrl(String.format(CompensationSmsConstant.WEB_URL_SUFFIX, dto.getAccidentId().toString(), dto.getChoiceSegment().replaceAll(",", "%2C"), dto.getId().toString(), dto.getStatus(),dto.getOrderNo(),dto.getAccidentType()));
            messageDepository.setH5TargetUrl(String.format(CompensationSmsConstant.H5_COMPENSATION_SUFFIX, dto.getId().toString(),dto.getOrderNo()));

        }else if(CompensationAccidentTypeEnum.OVERBOOKING.getKey().equals(dto.getAccidentType())){
            //超售
            messageDepository.setTargetUrl(String.format(CompensationSmsConstant.WEB_URL_SUFFIX, dto.getAccidentId().toString(), dto.getChoiceSegment().replaceAll(",", "%2C"), dto.getId().toString(), dto.getStatus(),dto.getOrderNo(),dto.getAccidentType()));
            messageDepository.setH5TargetUrl(String.format(CompensationSmsConstant.H5_COMPENSATION_SUFFIX, dto.getId().toString(),dto.getOrderNo()));

        }else if(CompensationAccidentTypeEnum.COMPLAINT.getKey().equals(dto.getAccidentType())){
            //旅客投诉
            messageDepository.setTargetUrl(String.format(CompensationSmsConstant.WEB_URL_SUFFIX, dto.getAccidentId().toString(), dto.getChoiceSegment().replaceAll(",", "%2C"), dto.getId().toString(), dto.getStatus(),dto.getOrderNo(),dto.getAccidentType()));
            messageDepository.setH5TargetUrl(String.format(CompensationSmsConstant.H5_COMPENSATION_SUFFIX, dto.getId().toString(),dto.getOrderNo()));

        }
        return modelId;
    }

    //向旅客发送短信消息
    @Override
    public void sendPassengerNow(List<CustomerMessageDepositoryDTO> sendSMSDTO) {
        customerMessageApi.sendCustomerMobile(sendSMSDTO);
    }

    @Override
    public void sendMiniProgramToPsg(ApplyOrderDO applyOrderDO, boolean flag) {
        //推送服务消息，微信小程序端是不需要航班号和日期的，加上这两个字数超过20发不出去，但是消息中心需要
//        ApplyInfoDetailsVO applyInfoDetailsVO = applyOrderService.findFlightById(applyOrderDO.getId() + "");

        PassengerWechatSubscribeMsgSendDTO dto = buildMiniMsg(null, applyOrderDO, flag);

        log.info("【reaptv-compensation-impl】开始调用消息模块推送旅客端的服务消息：申领人姓名【{}】，申领结果【{}】，推送给消息模块的（请求参数）【{}】", applyOrderDO.getApplyUser(),
                flag, JSONUtil.toJsonStr(dto));
        BaseResult<SnsSendResultVO> br = null;
        try{
            br = passengerWechatRecordApi.sendSubscribeMessage(dto);
        }catch (Exception e){
            //只做日志记录，不影响申领单保存
            log.error("【reaptv-compensation-impl】调用消息模块发送服务消息出错，请求参数【{}】，异常：", JSONUtil.toJsonStr(dto), e);
        }
        log.info("【reaptv-compensation-impl】调用消息模块推送旅客端的服务消息：申领人姓名【{}】，申领结果【{}】，推送给消息模块的（响应结果）【{}】", applyOrderDO.getApplyUser(),
                flag, JSONUtil.toJsonStr(br));
    }

    /**
     * @title buildMiniMsg
     * @description 构建推送给旅客的服务消息模板
     * <AUTHOR>
     * @date 2022/11/10 9:31
     * @param applyInfoDetailsVO
     * @param applyOrderDO
     * @param flag 审核结果
     * @return com.swcares.reaptv.msg.model.dto.PassengerWechatSubscribeMsgSendDTO
     */
    private PassengerWechatSubscribeMsgSendDTO buildMiniMsg(ApplyInfoDetailsVO applyInfoDetailsVO, ApplyOrderDO applyOrderDO, boolean flag){
        PassengerWechatSubscribeMsgSendDTO dto = new PassengerWechatSubscribeMsgSendDTO();
        dto.setClientId(CompensationSmsConstant.MINI_CLIENT_ID);
        dto.setBusinessType(CompensationSmsConstant.BUSINESS_TYPE);
        dto.setOpenid(applyOrderDO.getCreatedBy());
        dto.setTemplateId(CompensationSmsConstant.COMPENSATION_AUDIT_MSG_TEMPLATE_ID);
        dto.setTriggerType(0);
        dto.setTitle(CompensationSmsConstant.MINI_TITLE);
        dto.setFlightDate(LocalDate.parse(applyOrderDO.getFlightDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        dto.setFlightNumber(applyOrderDO.getFlightNo());
        dto.setPassengerName(applyOrderDO.getApplyUser());
        dto.setPassengerPhone(applyOrderDO.getTelephone());
        dto.setPassengerIdNo(applyOrderDO.getIdNo());

        Map<String,String> params = new HashMap<>();
        params.put("thing11", applyOrderDO.getApplyUser());
        params.put("phrase1", flag? "通过": "不通过");
        params.put("thing7", buildRemarkTemplate(applyOrderDO, null, null, flag));
        dto.setParams(params);
        return dto;
    }

    /**
     * @title buildRemarkTemplate
     * @description 构建消息备注内容
     * <AUTHOR>
     * @date 2022/11/10 10:00
     * @param applyOrderDO 申领单对象
     * @param flightNo 航班号
     * @param flightDate 航班日期
     * @param res 审核结果
     * @return java.lang.String 返回最终的备注内容
     */
    private String buildRemarkTemplate(ApplyOrderDO applyOrderDO, String flightNo, String flightDate, boolean res){
        /*模板是按照领取方式，成功失败来用不同的报表*/
        String finalTemplate = "";
        log.info("【reaptv-compensation-impl】applyOrderDO信息为：【{}】", JSONUtil.toJsonStr(applyOrderDO));
        //判断领取方式: 领取方式(本人0,代领1,协助2)
        if("0".equals(applyOrderDO.getApplyWay())){
            //本人领取默认是走自动过审，只有成功
            finalTemplate = String.format(AuditTemplateMsg.RECEIVE_MESSAGE_TEMPLATE, flightNo);
        }else if("1".equals(applyOrderDO.getApplyWay())){
            //代领判断审核结果
            if(res){
                finalTemplate = String.format(AuditTemplateMsg.AGENT_RECEIVE_SUCCESS, flightNo);
            }else {
                finalTemplate = String.format(AuditTemplateMsg.AGENT_RECEIVE_FAIL, flightNo);
            }
        }
        //不考虑协助，他是走现金

        return finalTemplate;
    }

    /**
     * @title authPassengerCode
     * @description 验证手机验证码
     * <AUTHOR>
     * @date 2022/11/14 14:58
     * @param dto
     * @return void
     */
    @Override
    public void authPassengerCode(CompensationSMSDTO dto) {
        Authentication authentication = UserContext.getAuthentication();
        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)authentication.getDetails();
        String tokenValue = details.getTokenValue();

        String key = CompensationSmsConstant.AUTH_PHONE_PREFIX + dto.getPhone();
        String authCode = redisUtil.get(key);
        String authFailNumKey= CompensationSmsConstant.AUTH_TOKEN_SMS_CODE_FAIL_PREFIX + tokenValue+ dto.getPhone();
        if(!dto.getAuthCode().equals(authCode)){
            Object authFailNumObj = redisUtil.get(authFailNumKey);
            int authFailNum = authFailNumObj == null ?0:Integer.parseInt(authFailNumObj.toString());
            //验证码失败大于5次,就重新获取验证码
            if(authFailNum+1 > CompensationSmsConstant.AUTH_TOKEN_SMS_CODE_FAIL_NUM){
                //短信验证码删除 = 失效
                redisUtil.del(key);
                //token获取短信验证码5分钟有效标识key删除,作用:可以重新发送短信验证码
                redisUtil.del(CompensationSmsConstant.AUTH_TOKEN_SMS_PREFIX + tokenValue);
                //短信验证码多次验证失败,短信验证码已失效,请重新获取短信验证码
                throw new BusinessException(ApplyErrors.SMS_FREQUENTLY_VALIDATE_FAIL);
            }else {
                redisUtil.set(authFailNumKey, authFailNum + 1);
            }

            throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_PHONE);
        }
        redisUtil.del(key);
        redisUtil.del(authFailNumKey);//短信验证成功,删除验证失败次数
        //验证短信验证码成功后，将token保存，有效时间5分钟
        redisUtil.set(CompensationSmsConstant.AUTH_TOKEN_SMS_PASS_PREFIX+tokenValue,CompensationSmsConstant.AUTH_TOKEN_SMS_CODE_PASS,CompensationSmsConstant.AUTH_PHONE_PREFIX_PAST_DUE);
        log.info("短信验证码校验成功");
    }

    /**
     * @title getInteriorMessageDepository
     * @description 封装站内信的内部信息
     * <AUTHOR>
     * @date 2022/11/14 14:59
     * @param msgTitle
     * @param msgContent
     * @param completeH5Url
     * @param completeWebUrl
     * @return com.swcares.components.msg.entity.InteriorMessageDepository
     */
    public InteriorMessageDepository getInteriorMessageDepository(String msgTitle,String msgContent,String completeH5Url,String completeWebUrl){
        InteriorMessageDepository interiorMessageDepository = new InteriorMessageDepository();
        interiorMessageDepository.setSystemCode(CompensationSmsConstant.SYSTEM_CODE);
        interiorMessageDepository.setBusinessType(CompensationSmsConstant.STAFF_BUSINESS_TYPE);
        interiorMessageDepository.setMasterId(0L);
        interiorMessageDepository.setMsgTitle(msgTitle);
        interiorMessageDepository.setMsgContent(msgContent);
        interiorMessageDepository.setUrgency("2");
        interiorMessageDepository.setSendType(0);
        interiorMessageDepository.setH5TargetUrl(completeH5Url);
        interiorMessageDepository.setTargetUrl(completeWebUrl);
        return interiorMessageDepository;
    }


    /**
     * @title getInteriorMessageReceiveDTOList
     * @description 封装站内信接受者基本信息
     * <AUTHOR>
     * @date 2022/11/14 14:59
     * @param compensationAuditReviewerVOS
     * @return java.util.List<com.swcares.components.msg.dto.InteriorMessageReceiveDTO>
     */
    public List<InteriorMessageReceiveDTO> getInteriorMessageReceiveDTOList(List<CompensationAuditReviewerVO> compensationAuditReviewerVOS){
        InteriorMessageReceiveDTO receiveDTO = new InteriorMessageReceiveDTO();
        List<InteriorMessageReceiveUserDTO> receiveUserDTOList = new ArrayList<>();
        List<InteriorMessageReceiveDTO> receiveDTOList = new ArrayList<>();
        compensationAuditReviewerVOS.forEach(compensationAuditReviewerVO ->{
            InteriorMessageReceiveUserDTO receiveUserDTO = new InteriorMessageReceiveUserDTO();
            receiveUserDTO.setReceiveNum(compensationAuditReviewerVO.getReviewerPhone());
            receiveUserDTO.setRecipient(compensationAuditReviewerVO.getUserName());
            receiveUserDTOList.add(receiveUserDTO);
        });
        receiveDTO.setReceiveList(receiveUserDTOList);
        receiveDTO.setReceiveMode(ReceiveModeEnum.INNER);
        receiveDTOList.add(receiveDTO);
        return receiveDTOList;
    }

    /**
     * @title getUserBasicInfo
     * @description 获取用户基本信息
     * <AUTHOR>
     * @date 2022/11/7 15:32
     * @param userId
     * @return com.swcares.aps.compensation.model.irregularflight.vo.CompensationReviewerInfoVO
     */
    private CompensationAuditReviewerVO getUserBasicInfo(String userId) {
        List<CompensationReviewerInfoVO> reviewer = compensationAuditInfoMapper.findReviewer(null, new String[]{userId}, null, null, null);
        CompensationReviewerInfoVO compensationReviewerInfoVO = reviewer.get(0);

        CompensationAuditReviewerVO compensationAuditReviewerVO = new CompensationAuditReviewerVO();

        compensationAuditReviewerVO.setUserName(compensationReviewerInfoVO.getUserName());
        compensationAuditReviewerVO.setReviewerPhone(compensationReviewerInfoVO.getReviewerPhone());
        compensationAuditReviewerVO.setReviewerId(Long.valueOf(userId));
        return compensationAuditReviewerVO;
    }
}
