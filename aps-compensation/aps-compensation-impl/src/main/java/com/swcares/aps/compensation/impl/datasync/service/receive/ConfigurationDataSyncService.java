package com.swcares.aps.compensation.impl.datasync.service.receive;

import com.swcares.aps.cpe.coordinate.model.receiver.dto.BusinessConfigureDataCoordinateDTO;

/**
 * @ClassName：BusinessDataSyncService
 * @Description：航司端同步机场端
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/7/16 10:47
 * @version： v1.0
 */
public interface ConfigurationDataSyncService {

    /**
     * @title ConfigurationDataSyncService.java
     * @description 业务配置数据，舱位、规则等
     * <AUTHOR>
     * @date 2024/10/8 14:24
     * @param businessDataDTO
     * @return void
     */
    void compensationConfigurationDataSync(BusinessConfigureDataCoordinateDTO businessDataDTO);
}
