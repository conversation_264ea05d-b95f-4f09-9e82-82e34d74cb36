package com.swcares.aps.compensation.impl.baggage.luggage.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageConsumptionDetailPageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.entity.LuggageConsumptionDetailDO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO;

/**
 * <AUTHOR>
 * @title: LuggageConsumptionService
 * @projectName aps
 * @description:
 * @date 2022/2/8 14:11
 */
public interface LuggageConsumptionService extends IService<LuggageConsumptionDetailDO> {
    /**
     * @title findDetailed
     * @description 箱包消耗明细分页查询
     * <AUTHOR>
     * @date 2022/2/11 10:05
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageConsumptionDetailVO>
     */
    IPage<LuggageConsumptionDetailVO> findDetailed(LuggageConsumptionDetailPageDTO dto);
}