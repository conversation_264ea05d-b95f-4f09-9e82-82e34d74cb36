package com.swcares.aps.compensation.impl.privilege.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname CoordinateConfig
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/19 11:19
 * @Version 1.0
 */
@Configuration
@ConfigurationProperties(prefix = "swcares.coordinate")
@Data
public class CoordinateConfig {
    /**
     * 协同中心的访问地址
     */
    private String coordinateCenterUrl;

    /**
     * 上传权限数据到协同中心的地址
     */
    private String privilegeDataUploadUrl;

    /**
     * 上传配置数据到协同中心的地址
     */
    private String configurationDataUploadUrl;

    /**
     * 和协同中心交互时使用的客户端id
     */
    private String appClientId;

    /**
     * 和协同中心交互时使用的密钥
     */
    private String appSecretKey;

    /**
     * 和协同中心交互的时候使用的密钥的版本
     */
    private String appSecretKeyVersion;

    /**
     * 本平台的客户类型，航司还是机场
     */
    private String tenantCustomerCategory;

}
