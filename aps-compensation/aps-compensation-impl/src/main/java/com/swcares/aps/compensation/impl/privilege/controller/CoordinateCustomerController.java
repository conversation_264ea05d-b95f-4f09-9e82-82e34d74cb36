package com.swcares.aps.compensation.impl.privilege.controller;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.privilege.service.CoordinateCustomerService;
import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import com.swcares.aps.cpe.coordinate.model.dispatcher.dto.BasicDataDispatchDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR> <PERSON>
 * @Classname CoordinateCustomerController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/27 14:13
 * @Version 1.0
 */
@RestController
@RequestMapping("/coordinate/customer")
@Api(
        tags = {"客户信息同步接口"}
)
@ApiVersion({"客户信息同步接口 v1.0"})
@Slf4j
public class CoordinateCustomerController extends BaseController {
    @Autowired
    CoordinateCustomerService coordinateCustomerService;


    @PostMapping("/saveOrUpdate")
    public BaseResult<Boolean> saveOrUPdate(@RequestBody BasicDataDispatchDTO dispatchDTO){

        log.info("【同步Customer数据】{}", JSONUtil.toJsonStr(dispatchDTO));
        String data = dispatchDTO.getData();
        CustomerDTO customerDTO = JSONUtil.toBean(data, CustomerDTO.class);

        coordinateCustomerService.saveOrUpdate(customerDTO);

        return BaseResult.ok(Boolean.TRUE);
    }
}
