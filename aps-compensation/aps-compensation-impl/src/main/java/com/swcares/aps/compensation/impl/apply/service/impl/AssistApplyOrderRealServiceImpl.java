package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyTypeCodeEnum;
import com.swcares.aps.compensation.impl.apply.enums.PayStatusEnum;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfOrderService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.apply.util.ApplyCodeUtils;
import com.swcares.aps.compensation.impl.apply.util.DistributedUniqueIndexGenerator;
import com.swcares.aps.compensation.impl.constant.CompensationConstants;
import com.swcares.aps.compensation.impl.dataconfig.service.ReplaceRuleService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.CompensationBehalfOrderInfoDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.AuthPaxVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.component.com.util.ApsDesensitizedUtil;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * <AUTHOR>
 * @title: AssistApplyOrderServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/2/16 17:30
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
//@PropertySource("classpath:application.yml")
//@ConfigurationProperties(prefix = "swcares.minio")
public class AssistApplyOrderRealServiceImpl {
    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    @Autowired
    private ApplyPaxService applyPaxService;

    @Autowired
    private PayRecordService payRecordService;

    @Autowired
    private ReplaceRuleService replaceRuleServiceImpl;

    @Autowired
    private CompensationPaxInfoService compensationPaxInfoService;

    @Autowired
    private Redisson redisson;

    @Autowired
    private FileAttachmentService fileAttachmentService;

    @Autowired
    private ApplyOrderMapper applyOrderMapper;

    @Autowired
    private ApplyBehalfOrderService applyBehalfOrderService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;
    /**
     * @title saveAssistApply
     * @description 保存申领单
     * <AUTHOR>
     * @date 2021/11/29 11:00
     * @param dto
     * @return boolean
     */
    public boolean saveAssistApplyOrder(ApplyBehalfOrderDTO dto) {
        //整个申领单保存的标记位
        boolean bool = false;

        log.info("【aps-apply-impl协助领取开始保存代人领取的申领单信息，当前用的为【{}】请求参数【{}】",
                JSONUtil.toJsonStr(UserContext.getCurrentUser()), JSONUtil.toJsonStr(dto));

        ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
        ApplyBehalfOrderDTO irregularFlightOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, irregularFlightOrderDto, new String[]{"applyAmount"});
        irregularFlightOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        irregularFlightOrderDto.setFile(dto.getFile());
        Set irregularFlightSet = new HashSet();
        ApplyBehalfOrderDTO abnormalLuggageOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, abnormalLuggageOrderDto, new String[]{"applyAmount"});
        abnormalLuggageOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        abnormalLuggageOrderDto.setFile(dto.getFile());
        Set abnormalLuggageSet = new HashSet();
        ApplyBehalfOrderDTO overbookingOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, overbookingOrderDto, new String[]{"applyAmount"});
        overbookingOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        overbookingOrderDto.setFile(dto.getFile());
        Set overbookingSet = new HashSet();
        ApplyBehalfOrderDTO complaintOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, complaintOrderDto, new String[]{"applyAmount"});
        complaintOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        complaintOrderDto.setFile(dto.getFile());
        Set complaintSet = new HashSet();
        try {
            //循环后分类领取单数据
            for (CompensationBehalfOrderInfoDTO compensationBehalfOrderInfoDTO : dto.getOrderInfoVOS()) {
                AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
                authPaxOrderDTO.setOrderId(compensationBehalfOrderInfoDTO.getOrderId());
                authPaxOrderDTO.setPaxId(compensationBehalfOrderInfoDTO.getPaxId());
                AuthPaxVO authPaxVO = applyOrderMapper.findApplyAuthPax(authPaxOrderDTO);
                //判断单子状态如果有问题直接抛错
                if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                    throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
                } else {
                    CompensationPaxInfoDO paxInfo = compensationPaxInfoService.getById(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                    if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.IRREGULAR_FLIGHT)) {//不正常航班
                        irregularFlightSet.add(paxInfo.getIdNo());
                        irregularFlightOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        irregularFlightOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(irregularFlightOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : irregularFlightOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.ABNORMAL_LUGGAGE)) {//异常行李
                        abnormalLuggageSet.add(paxInfo.getIdNo());
                        abnormalLuggageOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        abnormalLuggageOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(abnormalLuggageOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : abnormalLuggageOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.OVER_BOOKING)) {//超售
                        overbookingSet.add(paxInfo.getIdNo());
                        overbookingOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        overbookingOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(overbookingOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : overbookingOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.COMPLAINT)) {//投诉
                        complaintSet.add(paxInfo.getIdNo());
                        complaintOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        complaintOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(complaintOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : complaintOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    }
                }
            }
            //根据事故单类型来保存申领单
            if (ListUtils.isNotEmpty(irregularFlightOrderDto.getOrderInfoVOS())) {
                bool = processAssistApplyOrder(irregularFlightOrderDto);
            }
            if (ListUtils.isNotEmpty(abnormalLuggageOrderDto.getOrderInfoVOS())) {
                bool = processAssistApplyOrder(abnormalLuggageOrderDto);
            }
            if (ListUtils.isNotEmpty(overbookingOrderDto.getOrderInfoVOS())) {
                bool = processAssistApplyOrder(overbookingOrderDto);
            }
            if (ListUtils.isNotEmpty(complaintOrderDto.getOrderInfoVOS())) {
                bool = processAssistApplyOrder(complaintOrderDto);
            }
        } catch (BusinessException e) {
            throw e;
        }catch (Exception e) {
            log.error("【aps-apply-impl上传文件异常，整个请求参数为【{}】, 异常信息【{}】", JSONUtil.toJsonStr(dto), e);
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }

        return bool;
    }

    /**
     * Title：processAssistApplyOrder <br>
     * Description：协助领取具体保存逻辑 <br>
     * author：王磊 <br>
     * date：2021/12/8 13:46 <br>
     * @param dto 保存申领单的传输对象 <br>
     * @return <br>
     */
    public boolean processAssistApplyOrder(ApplyBehalfOrderDTO dto) throws Exception {
        LoginUserDetails user = UserContext.getUser();
        boolean bool = false;

        //用于更新赔偿单旅客状态的旅客id集合，更新为领取中
        List<Long> paxIds = new ArrayList<Long>();
        List<String> applyCodeList = new ArrayList<String>();

        //如果图片上传失败，是否应该抛异常中断业务
        FileAttachment fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(dto.getFile()), bucketName,null,null);

        FileAttachment signFileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(dto.getSignFile()), bucketName,null,null);


        for (CompensationBehalfOrderInfoDTO compensationBehalfOrderInfoDTO : dto.getOrderInfoVOS()) {
            AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
            authPaxOrderDTO.setOrderId(compensationBehalfOrderInfoDTO.getOrderId());
            authPaxOrderDTO.setPaxId(compensationBehalfOrderInfoDTO.getPaxId());
            //保存前获取赔偿单的旅客信息用于校验赔偿单用户是否可用
            AuthPaxVO authPaxVO = applyOrderMapper.findApplyAuthPax(authPaxOrderDTO);

            //判断赔偿单上是否有个旅客 || 该旅客状态是否被冻结 || 旅客是申领单不是未领取状态
            if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                log.error("【aps-apply-impl】协助领取申领单保存验证赔偿单状态失败,申领人{},申领人身份证:{},申领单号:{}",dto.getApplyUser(),ApsDesensitizedUtil.idCardNum(dto.getIdNo()),dto.getApplyCode());
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
            } else {

                ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);

                //生成申领单编号
                CompensationBehalfOrderInfoDTO orderInfoDTO = dto.getOrderInfoVOS().get(0);
                String accidentTypeCode = ApplyTypeCodeEnum.build(orderInfoDTO.getAccidentType()).getValue();
                //生成申领单编号
                String applyCode = ApplyCodeUtils.createApplyCode(ApplyConstants.APPLY_TYPE_H,accidentTypeCode,user.getTenantCode());
                applyOrderDO.setApplyCode(applyCode);
                applyOrderDO.setCollectIdentityCardPhoto(fileAttachment.getId().toString());
                applyOrderDO.setSignFile(String.valueOf(signFileAttachment.getId()));
                applyOrderDO.setAccidentType(compensationBehalfOrderInfoDTO.getAccidentType());
                //数据来源  1机场/2航司
                applyOrderDO.setSource(CompensationConstants.BUSINESS_DATA_SOURCE_AIRPORT);
                //申领单归属航司
                applyOrderDO.setBelongAirline(dto.getFlightNo().substring(0, 2));
                //申领金额
                applyOrderDO.setApplyAmount(authPaxVO.getMoney());
                applyOrderDO.setApplyCustNum(1);
                //【保存申领单】
                log.info("【aps-apply-impl】协助领取申领单保存,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(), ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                bool = applyBehalfOrderService.save(applyOrderDO);
                throwSaveError(bool);


                fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(compensationBehalfOrderInfoDTO.getFile()),bucketName,null,null);
                //旅客信息
                ApplyPaxDO applyPaxDO = new ApplyPaxDO();
                applyPaxDO.setPaxIdentityCardPhoto(fileAttachment.getId().toString());
                applyPaxDO.setApplyCode(applyCode);
                applyPaxDO.setApplyId(applyOrderDO.getId());
                applyPaxDO.setOrderId(new Long(compensationBehalfOrderInfoDTO.getOrderId()));
                applyPaxDO.setPaxInfoId(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                applyPaxDO.setApplyPaxStatus(ApplyConstants.APPLY_PAX_STATUS_SUCCESS);
                //【保存申领单旅客信息】
                log.info("【aps-apply-impl】协助领取申领单保存,保存申领旅客,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                bool = applyPaxService.save(applyPaxDO);
                throwSaveError(bool);

                //支付记录
                PayRecordDO payRecordDO = new PayRecordDO();
                payRecordDO.setId(DistributedUniqueIndexGenerator.generateUniqueIndex());
                payRecordDO.setApplyId(applyOrderDO.getId());
                payRecordDO.setApplyPaxId(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                payRecordDO.setOrderId(new Long(compensationBehalfOrderInfoDTO.getOrderId()));
                payRecordDO.setPayStatus(PayStatusEnum.PAY_SUCCESS.getKey());
                payRecordDO.setPayType(applyOrderDO.getGetMoneyWay());
                payRecordDO.setTransAmount(authPaxVO.getMoney());
                //设置支付开始时间为pay_start_time
                payRecordDO.setPayStartTime(LocalDateTime.now());
                //可以删除
                payRecordDO.setCreatedTime(LocalDateTime.now());
                //【保存支付记录(创建待支付记录)】
                log.info("【aps-apply-impl】协助领取申领单保存,保存申领支付记录,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),ApsDesensitizedUtil.idCardNum(applyOrderDO.getIdNo()),applyOrderDO.getApplyCode());
                bool = payRecordService.save(payRecordDO);
                throwSaveError(bool);


                //事故单类型对应的业务code
                String businessCode = AccidentTypeBusinessCodeEnum.build(applyOrderDO.getAccidentType()).getValue();
                businessDataPushHandler.dataStore(applyOrderDO.getId(), businessCode, BusinessDataSyncConstant.DATA_TYPE_APPLY);


                paxIds.add(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                applyCodeList.add(applyCode);
            }
        }

        //【调用赔偿单api更新赔偿单旅客数据】
        log.info("【aps-apply-impl】协助领取申领单保存,更新赔偿单,申领人{},申领人身份证:{},申领单号:{}",dto.getApplyUser(),ApsDesensitizedUtil.idCardNum(dto.getIdNo()),applyCodeList.toString());
        boolean result = compensationPaxInfoService.updatePaxReceiveInfo(paxIds, dto.getApplyWay(), dto.getGetMoneyWay(), ApplyConstants.ALREADY_RECEIVE_STATUS);
        throwSaveError(result);

        return bool;
    }

    /**
     * Title：createApplyCode <br>
     * Description：构建申领单code <br>
     * author：王磊 <br>
     * date：2021/12/1 11:28 <br>
     * <br>
     *
     * @return <br>
     */
    private String createApplyCode(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        StringBuffer stringBuffer = new StringBuffer(localDateTime.format(formatter));
        stringBuffer.append(String.format("%03d", new Random().nextInt(999)));
        return stringBuffer.toString();
    }

    /**
     * Title：throwSaveError <br>
     * Description：保存时抛出错误 <br>
     * author：王磊 <br>
     * date：2021/12/2 11:20 <br>
     *
     * @param bool <br>
     * @return <br>
     */
    private void throwSaveError(boolean bool) {
        if (!bool) {
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }
    }
}
