package com.swcares.aps.compensation.impl.baggage.accident.service;

import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;

/**
 * @ClassName：BaggageAccidentStatusHandlerService
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 王蓝辉
 * @Date： 2022/3/15 15:22
 * @version： v1.0
 */
public interface BaggageAccidentStatusHandlerService {
    /**
     * @title support
     * @description 当前ServiceHandler支持的业务处理
     * <AUTHOR>
     * @date 2022/3/15 15:50
     * @param sourceStatus 当前事故单状态
     * @param targetStatus 希望更改后的状态
     * @return
     */
    boolean support(String sourceStatus,String targetStatus);

    /**
     * @title check
     * @description 判断当前业务状态是否能更改状态至targetStatus，如果不能就抛错
     * <AUTHOR>
     * @date 2022/3/15 15:49
     * @param accidentInfo
     * @param targetStatus
     * @return
     */
    void check(BaggageAccidentInfoDTO accidentInfo, String targetStatus);
    /**
     * @title execute
     * @description 执行业务逻辑
     * <AUTHOR>
     * @date 2022/3/15 15:48
     * @param accidentInfo
     * @param targetStatus
     * @return
     */
    default void execute(BaggageAccidentInfoDTO accidentInfo, String targetStatus){

    }
    /**
    * @title handler
    * @description @TODO
    * <AUTHOR>
    * @date 2022/3/15 15:27
    * @param accidentInfo
    * @param targetStatus
    * @return void
    */
    default void handler(BaggageAccidentInfoDTO accidentInfo, String targetStatus){
        check(accidentInfo,targetStatus);
        execute(accidentInfo,targetStatus);
    }

}
