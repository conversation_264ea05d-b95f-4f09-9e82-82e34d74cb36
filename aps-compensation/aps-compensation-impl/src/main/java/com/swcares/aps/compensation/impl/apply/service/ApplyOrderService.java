package com.swcares.aps.compensation.impl.apply.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderPagedDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyPaxBaseInfoDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyUpdateStatusDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderVO;
import com.swcares.aps.component.pay.pay.service.wx.service.v3pay.model.WxMchTransferReceiveConfirmInfo;

import java.util.List;

/**
 * ClassName：com.swcares.compensation.service.ApplyOrderService <br>
 * Description：航延补偿申领单信息表 服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
public interface ApplyOrderService extends IService<ApplyOrderDO> {

    /**
     * @title logicRemoveById
     * @description 逻辑删除
     * <AUTHOR>
     * @date 2021-11-25
     * @param id
     * @return boolean
     */
    boolean logicRemoveById(Long id);

    /**
     * @title page
     * @description 分页查询
     * <AUTHOR>
     * @date 2021-11-25
     * @param dto
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.apply.model.vo.ApplyOrderVO>
     */
    IPage<ApplyOrderVO> page(ApplyOrderPagedDTO dto);

    /**
     * Title：saveApplyOrder <br>
     * Description：保存申领单 <br>
     * author：王磊 <br>
     * date：2021/11/29 11:00 <br>
     * @param dto <br>
     * @return <br>
     */
    boolean saveApplyOrder(ApplyOrderDTO dto);

    /**
     * @title updateAuditStatus
     * @description 修改订单状态
     * <AUTHOR>
     * @date 2022/2/23 9:18
     * @param updateStatusDTO
     * @return void
     */
    void updateAuditStatus(ApplyUpdateStatusDTO updateStatusDTO);

    /**
     * @title findPaxBaseInfoByApplyId
     * @description 获取乘机人信息
     * <AUTHOR>
     * @date 2022/2/23 9:19
     * @param applyOrderId
     * @return List<ApplyPaxBaseInfoDTO>
     */
    List<ApplyPaxBaseInfoDTO> findPaxBaseInfoByApplyId(Long applyOrderId);

    /**
     * @title findFlightByApplyId
     * @description 通过申领单id获取航班数据
     * <AUTHOR>
     * @date 2022/7/28 9:40
     * @return com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO
     */
    ApplyInfoDetailsVO findFlightByApplyId(String applyId);

    /**
     * @title findFlightById
     * @description 通过申领单id获取航班数据
     * <AUTHOR>
     * @date 2022/7/28 9:40
     * @return com.swcares.aps.compensation.model.apply.vo.ApplyInfoDetailsVO
     */
    ApplyInfoDetailsVO findFlightById(String id);

    WxMchTransferReceiveConfirmInfo getReceiveConfirmInfo(String applyId);
}
