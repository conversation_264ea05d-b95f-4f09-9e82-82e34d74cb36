package com.swcares.aps.compensation.impl.baggage.accident.service.handler;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.swcares.aps.compensation.impl.baggage.accident.enums.BaggageAccidentTypeEnum;
import com.swcares.aps.compensation.impl.baggage.accident.mapper.BaggageAccidentMapper;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentStatusHandlerService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.entity.BaggageAccidentInfoDO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * ClassName： ChangeDraftToTodoStatusHandlerServiceImpl
 * Description：草稿状态转换为待处理状态服务类
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved.
 * Company：Aviation Cares Of Southwest Chen Du LTD
 *
 * <AUTHOR>
 * Date 2022/3/16 14:52
 * @version v1.0
 */
@Service
public class ChangeDraftToTodoStatusHandlerServiceImpl implements BaggageAccidentStatusHandlerService {

    @Autowired
    private BaggageAccidentMapper baggageAccidentMapper;


    @Override
    public boolean support(String sourceStatus, String targetStatus) {
        return AccidentStatusEnum.DRAFT.getValue().equals(sourceStatus)
                && AccidentStatusEnum.TODO.getValue().equals(targetStatus);
    }

    @Override
    public void check(BaggageAccidentInfoDTO accidentInfo, String targetStatus) {
        //TODO 添加相关逻辑判断，如果逻辑判断不通过，请直接抛businessexception错误；如还有业务处理，请重写BaggageAccidentStatusHandlerService接口的execute实现
    }

    public void execute(BaggageAccidentInfoDTO accidentInfo, String targetStatus){
        BaggageAccidentInfoDO baggageAccidentInfoDO = new BaggageAccidentInfoDO();
        baggageAccidentInfoDO.setAccidentNo(accidentInfo.getAccidentNo());
        LocalDateTime now = LocalDateTime.now();
        baggageAccidentInfoDO.setTodoTime(now);
//        if(accidentInfo.getType().equals(BaggageAccidentTypeEnum.LESS_INCOME.getKey()))
        if(accidentInfo.getType().equals(BaggageAccidentTypeEnum.LESS_INCOME.getValue()))
        baggageAccidentInfoDO.setOverTime(now.plusDays(accidentInfo.getReminderTime()));
        LambdaUpdateWrapper<BaggageAccidentInfoDO> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.eq(BaggageAccidentInfoDO::getAccidentNo,baggageAccidentInfoDO.getAccidentNo());
        baggageAccidentMapper.update(baggageAccidentInfoDO,lambdaUpdateWrapper);
    }
}
