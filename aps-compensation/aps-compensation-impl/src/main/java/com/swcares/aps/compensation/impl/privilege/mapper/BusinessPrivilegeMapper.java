package com.swcares.aps.compensation.impl.privilege.mapper;

import com.baomidou.mybatisplus.annotation.SqlParser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeMapper
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 09:04
 * @Version 1.0
 */
@Mapper
public interface BusinessPrivilegeMapper extends BaseMapper<AirlineBusinessPrivilege> {
    public IPage<AirlineBusinessPrivilege> getBusinessPrivilegePage(@Param("dto") SearchBusinessPrivilegeDTO dto, Page<AirlineBusinessPrivilege> page);

    @SqlParser(filter = true)
    AirlineBusinessPrivilege getBusinessPrivilegeBetweenCustomer(@Param("grantorCode")String grantorCode, @Param("recipientCode") String recipientCode);

    @SqlParser(filter = true)
    Long getTeanantIdByTenantCode(@Param("tenantCode") String upperCase);

    List<String> getAirlinePrivilegaeList(@Param("tenantCode") String tenantCode, @Param("typeCode") String typeCode,@Param("airCode") String airCode);
}
