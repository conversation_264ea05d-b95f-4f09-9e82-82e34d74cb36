package com.swcares.aps.compensation.impl.apply.service.impl;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyPaxMapper;
import com.swcares.aps.compensation.impl.apply.mapper.PayRecordMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderQuartzService;
import com.swcares.aps.compensation.model.apply.dto.ApplyOrderPaxStatusUpdDTO;
import com.swcares.aps.compensation.model.apply.dto.OrderPaxStatusPayUpdDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO;
import com.swcares.aps.component.pay.pay.bean.PayConstant;
import com.swcares.aps.component.pay.utils.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Optional;

/**
 * ClassName：com.swcares.scgsi.flight.service.impl <br>
 * Description：申领单定时任务 - 处理<br>
 * Copyright  2020 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2020年 04月08日 20:40 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ApplyOrderQuartzServiceImpl implements ApplyOrderQuartzService {

    @Resource
    private ApplyOrderMapper applyOrderMapper;
    @Resource
    private ApplyPaxMapper applyPaxMapper;
    @Resource
    private PayRecordMapper payRecordMapper;


    @Override
    public PayRecordDO findById(Long id) {
        return payRecordMapper.selectById(id);
    }

    @Override
    public List<ApplyOrderPayVO> findUnpaidOrderInfo() {
        return applyOrderMapper.findUnpaidOrderInfo();
    }

    @Override
    public int updateById(PayRecordDO payRecordDO) {
        return payRecordMapper.doUpdateById(payRecordDO);
    }

    @Override
    public void updatePayRecordById(PayRecordDO payRecordDO) {
        LambdaUpdateWrapper<PayRecordDO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(PayRecordDO::getId,payRecordDO.getId());
        payRecordMapper.update(payRecordDO,lambdaUpdateWrapper);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updApplyOrderPaxStatus(ApplyOrderPaxStatusUpdDTO dto) {
        return applyPaxMapper.updApplyOrderPaxStatus(dto);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updOrderPaxStatus(OrderPaxStatusPayUpdDTO dto) {
        if(!ApplyConstants.ALREADY_RECEIVE_STATUS.equals(dto.getReceiveStatus())){
            dto.setReceiveTime(null);
        }
        return applyOrderMapper.updOrderPaxStatus(dto.getPaxInfoId(),dto.getOrderId(),dto.getReceiveStatus(),dto.getReceiveTime());
    }


    @Override
    public void updApplyOrderInfo(String applyCode, LocalDateTime receieveTime) {
        ApplyOrderDO applyOrderDO = new ApplyOrderDO();
        applyOrderDO.setApplyCode(applyCode);
//        applyOrderDO.setReceieveTime(receieveTime);收款时间[目前协助领取在设置值，本人和代领没有用。]
        LambdaUpdateWrapper<ApplyOrderDO> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(ApplyOrderDO::getApplyCode,applyCode);
        applyOrderMapper.update(applyOrderDO,lambdaUpdateWrapper);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void changeStatus(PayRecordDO aoTransRecord, ApplyOrderPayVO orderInfo) {
        if(StringUtils.isEmpty(aoTransRecord.getPayStatus())) return;

        //转账日期赋值
        String receiveTime = DateUtils.parseDateToStr(new Date(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        if (ObjectUtils.isNotEmpty(aoTransRecord.getPayReturnTime())) {
            receiveTime = DateUtils.parseLocalDateTimeToString(aoTransRecord.getPayReturnTime(), DateUtils.YYYY_MM_DD_HH_MM_SS);
        }

        if(PayConstant.PAY_STATUS_PAID.equals(aoTransRecord.getPayStatus())){
            //1.成功的处理逻辑（1.支付记录表更新  2.申领旅客表的申领状态 - 已领取  3.赔偿单的旅客表状态 - 已领取）
            handlerSuccess(orderInfo, aoTransRecord,  receiveTime);
        }else if(PayConstant.PAY_STATUS_FAIL.equals(aoTransRecord.getPayStatus())){
            //2.失败的逻辑处理（1.支付记录表更新  2.申领旅客表的申领状态 - 失败  3.赔偿单的旅客表状态 - 待领取）
            handlerFailed(orderInfo, aoTransRecord, receiveTime);
        }else if(PayConstant.PAY_STATUS_INPROCESS.equals(aoTransRecord.getPayStatus())){
            // 3.处理中的逻辑（1.支付记录表更新  2.申领旅客表的申领状态 - 处理中 3.赔偿单的旅客表状态不用更新 - 表里面已经是领取中，此处不改，领取中的状态是建申领单的时候写进去的）
            handlerProcessing(orderInfo, aoTransRecord, receiveTime);
        }

        log.info("------->>>航延补偿支付定时任务，处理订单结束-订单号：{}", orderInfo.getApplyCode());
    }

    private void handlerSuccess(ApplyOrderPayVO orderInfo, PayRecordDO aoTransRecord, String receiveTime){
        //赋值 赔偿单的旅客表为已领取 & 申领旅客表领取成功
        String receiveStatus = PayConstant.RECEIVE_STATUS_PAID;
        String applyReceiveStatus = PayConstant.APPLY_RECEIVE_STATUS_SUCCESS;
        String paxReceiveState = PayConstant.PAX_RECEIVE_STATE_RECEIVED;
        aoTransRecord.setPaxReceiveState(paxReceiveState);
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{},旅客收款状态：{},payTypeApiVersion:{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), aoTransRecord.getPayStatus(),paxReceiveState,aoTransRecord.getPayTypeApiVersion());
        int count = this.updateById(aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，执行结束【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{},payTypeApiVersion:{}, 执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), aoTransRecord.getPayStatus(),aoTransRecord.getPayTypeApiVersion(), count);


        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},支付时间{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus, receiveTime);
        boolean count1 = this.updOrderPaxStatus(OrderPaxStatusPayUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .receiveStatus(receiveStatus)
                .receiveTime(receiveTime)
                .build());
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，执行结束【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},支付时间{},执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus, receiveTime, count1);


        log.info("------->>>航延补偿支付定时任务【SUCCESS】，正在执行【更新COMPENSATION_APPLY_PAX】-支付id订单号：{}, 对应申领单号：{},领取状态：{}, 错误原因：{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), applyReceiveStatus, aoTransRecord.getErrCodeDes());
        int count2 = this.updApplyOrderPaxStatus(ApplyOrderPaxStatusUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .applyId(orderInfo.getId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .applyPaxStatus(applyReceiveStatus)
                .applyPaxError(aoTransRecord.getErrCodeDes())
                .build());
        log.info("------->>>航延补偿支付定时任务【SUCCESS】，执行结束【更新COMPENSATION_APPLY_PAX】-支付id订单号：{}, 对应申领单号：{},领取状态：{}, 错误原因：{},执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), applyReceiveStatus, aoTransRecord.getErrCodeDes(), count2);
    }

    private void handlerFailed(ApplyOrderPayVO orderInfo, PayRecordDO aoTransRecord, String receiveTime){
        //赋值 赔偿单的旅客表为未领取 & 申领旅客表领取失败
        String receiveStatus = PayConstant.RECEIVE_STATUS_UNPAID;
        String applyReceiveStatus = PayConstant.APPLY_RECEIVE_STATUS_ERROR;
        String paxReceiveState = PayConstant.PAX_RECEIVE_STATE_FAIL;
        aoTransRecord.setPaxReceiveState(paxReceiveState);

        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{},旅客收款状态：{},payTypeApiVersion:{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(),aoTransRecord.getPayStatus(),paxReceiveState,aoTransRecord.getPayTypeApiVersion());
        int count = this.updateById(aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【FAILED】，执行结束【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{},payTypeApiVersion:{},执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(),aoTransRecord.getPayStatus(),aoTransRecord.getPayTypeApiVersion(),count);


        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},payTypeApiVersion:{},支付时间{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus,aoTransRecord.getPayTypeApiVersion(),receiveTime);
        boolean count1 = this.updOrderPaxStatus(OrderPaxStatusPayUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .receiveStatus(receiveStatus)
                .receiveTime(receiveTime)
                .build());
        log.info("------->>>航延补偿支付定时任务【FAILED】，执行结束【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},支付时间{},执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus,receiveTime, count1);


        log.info("------->>>航延补偿支付定时任务【FAILED】，正在执行【更新COMPENSATION_APPLY_PAX】-支付id订单号：{},对应申领单号：{},领取状态：{},错误原因：{}",
                orderInfo.getRecordId(),orderInfo.getApplyCode(), applyReceiveStatus,aoTransRecord.getErrCodeDes());
        int count2 = this.updApplyOrderPaxStatus(ApplyOrderPaxStatusUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .applyId(orderInfo.getId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .applyPaxStatus(applyReceiveStatus)
                .applyPaxError(aoTransRecord.getErrCodeDes())
                .build());
        log.info("------->>>航延补偿支付定时任务【FAILED】，执行结束【更新COMPENSATION_APPLY_PAX】-支付id订单号：{},对应申领单号：{},领取状态：{},错误原因：{},执行结果count【{}】",
                orderInfo.getRecordId(),orderInfo.getApplyCode(), applyReceiveStatus,aoTransRecord.getErrCodeDes(), count2);
    }

    private void handlerProcessing(ApplyOrderPayVO orderInfo, PayRecordDO aoTransRecord, String receiveTime) {
        //赋值 赔偿单的旅客表为领取中 & 申领旅客表领取中
        String receiveStatus = PayConstant.RECEIVE_STATUS_ING;
        String applyReceiveStatus = PayConstant.APPLY_RECEIVE_STATUS_ING;
        processOtherApiVersion(orderInfo, aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，正在执行【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{},旅客收款状态：{},payTypeApiVersion:{},wxReceivePackageInfo:{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), aoTransRecord.getPayStatus(),aoTransRecord.getPaxReceiveState(),aoTransRecord.getPayTypeApiVersion(),aoTransRecord.getWxReceivePackageInfo());
        int count = this.updateById(aoTransRecord);
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，执行结束【添加COMPENSATION_PAY_RECORD】-支付id订单号：{},对应申领单号：{},支付状态：{}, payTypeApiVersion:{},wxReceivePackageInfo:{}执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), aoTransRecord.getPayStatus(),aoTransRecord.getPayTypeApiVersion(),aoTransRecord.getWxReceivePackageInfo(), count);


        log.info("------->>>航延补偿支付定时任务【PROCESSING】，正在执行【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},支付时间{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus, receiveTime);
        boolean count1 = this.updOrderPaxStatus(OrderPaxStatusPayUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .receiveStatus(receiveStatus)
                .receiveTime(receiveTime)
                .build());
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，执行结束【更新COMPENSATION_PAX_INFO】-支付id订单号：{},对应申领单号：{},领取状态：{},支付时间{}, 执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), receiveStatus, receiveTime, count1);


        log.info("------->>>航延补偿支付定时任务【PROCESSING】，正在执行【更新COMPENSATION_APPLY_PAX】-支付id订单号：{},对应申领单号：{},领取状态：{},错误原因：{}",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), applyReceiveStatus, aoTransRecord.getErrCodeDes());
        int count2 = this.updApplyOrderPaxStatus(ApplyOrderPaxStatusUpdDTO.builder()
                .orderId(orderInfo.getOrderId())
                .applyId(orderInfo.getId())
                .paxInfoId(orderInfo.getApplyPaxId())
                .applyPaxStatus(applyReceiveStatus)
                .applyPaxError(aoTransRecord.getErrCodeDes())
                .build());
        log.info("------->>>航延补偿支付定时任务【PROCESSING】，执行结束【更新COMPENSATION_APPLY_PAX】-支付id订单号：{},对应申领单号：{},领取状态：{},错误原因：{}, 执行结果count【{}】",
                orderInfo.getRecordId(), orderInfo.getApplyCode(), applyReceiveStatus, aoTransRecord.getErrCodeDes(), count2);

    }

    private void processOtherApiVersion(ApplyOrderPayVO orderInfo, PayRecordDO aoTransRecord) {
        if(!PayConstant.PAY_STATUS_INPROCESS.equals(aoTransRecord.getPayStatus())){return;}
        if(!StringUtils.equalsIgnoreCase(aoTransRecord.getPayTypeApiVersion(),
                PayConstant. PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){ return; }
        PayRecordDO oldPayRecordDO = this.findById(aoTransRecord.getId());
        if(StringUtils.isNotBlank(oldPayRecordDO.getPaxReceiveState())){return;}
        Optional<String> optional = PayConstant.MCH_TRANSFER_ALLOW_CONFIRM_SATES
                .stream()
                .filter(t -> StringUtils.equals(t, aoTransRecord.getTransCode()))
                .findAny();
        if(!optional.isPresent()){return;}
        //说明是商户转账V3接口；第一次变为需要等待旅客确认领取
        aoTransRecord.setPaxReceiveState(PayConstant.PAX_RECEIVE_STATE_WAITE_CONFIRM);
    }

}
