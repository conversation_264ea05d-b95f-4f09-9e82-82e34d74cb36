package com.swcares.aps.compensation.impl.passengerCategory.service.impl;


import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.passengerCategory.mapper.PassengerCategoryMapper;
import com.swcares.aps.compensation.impl.passengerCategory.service.PassengerCategoryService;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryStateDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.errors.PassengerCategoryErrors;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.passengerCategory.service.impl.PassengerCategoryServiceImpl <br>;
 * Description：旅客类别服务实现 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 16:33 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@Service("passengerCategoryServiceImpl")
public class PassengerCategoryServiceImpl extends ServiceImpl<PassengerCategoryMapper, PassengerCategoryConfigureDepository> implements PassengerCategoryService {

    @Resource
    private PassengerCategoryMapper passengerCategoryMapper;

    @Override
    public IPage<PassengerCategorySentPageVo> sentPage(PassengerCategorySentPageDto dto) {
        IPage<PassengerCategorySentPageVo> page =  passengerCategoryMapper.sentPage(dto, dto.createPage());
        return page;
    }

    @Override
    public void savePassengerCategoryConfigure(PassengerCategoryChangeDto dto) {

        if (passengerCategoryRepeatCheck(dto)) {
            throw new BusinessException(PassengerCategoryErrors.PASSENGER_CATEGORY_DUPLICATE);
        }

        LoginUserDetails user = UserContext.getCurrentUser();

        PassengerCategoryConfigureDepository newPassCategoryConfig = new PassengerCategoryConfigureDepository();
        newPassCategoryConfig.setSysDictionaryDataId(dto.getSysDictionaryDataId());
        newPassCategoryConfig.setType(dto.getType());
        newPassCategoryConfig.setCategory(dto.getCategory());
        newPassCategoryConfig.setCode(dto.getCode());
        newPassCategoryConfig.setRemarks(dto.getRemarks());
        newPassCategoryConfig.setState(dto.getState());
        newPassCategoryConfig.setCreatedBy(user.getUsername());
        newPassCategoryConfig.setCreatedTime(LocalDateTime.now());
        newPassCategoryConfig.setUpdatedBy(user.getUsername());
        newPassCategoryConfig.setUpdatedTime(LocalDateTime.now());
        newPassCategoryConfig.setAirCode(user.getTenantCode());
        newPassCategoryConfig.setAirName(user.getTenantName());

        passengerCategoryMapper.insert(newPassCategoryConfig);
    }

    @Override
    public void updatePassengerCategoryConfigure(PassengerCategoryChangeDto dto) {
        if (null == dto.getId()) {
            log.error("[{}]:更新旅客类别配置信息时未传递id。更新对象[{}]", log.getName(), dto);
            throw new ValidationException("参数异常");
        }

        LambdaQueryWrapper<PassengerCategoryConfigureDepository> queryWrapper = Wrappers.lambdaQuery(PassengerCategoryConfigureDepository.class);
        queryWrapper.eq(PassengerCategoryConfigureDepository::getType, dto.getType());
        queryWrapper.eq(PassengerCategoryConfigureDepository::getCategory, dto.getCategory());

        List<PassengerCategoryConfigureDepository> list = this.list(queryWrapper);

        if (CollectionUtils.isNotEmpty(list) && list.get(0).getId() != dto.getId()) {
            throw new BusinessException(PassengerCategoryErrors.PASSENGER_CATEGORY_DUPLICATE);
        }

        LoginUserDetails user = UserContext.getCurrentUser();

        PassengerCategoryConfigureDepository newPassCategoryConfig = new PassengerCategoryConfigureDepository();
        newPassCategoryConfig.setType(dto.getType());
        newPassCategoryConfig.setCategory(dto.getCategory());
        newPassCategoryConfig.setCode(dto.getCode());
        newPassCategoryConfig.setRemarks(dto.getRemarks());
        newPassCategoryConfig.setState(dto.getState());
        newPassCategoryConfig.setId(dto.getId());
        newPassCategoryConfig.setSysDictionaryDataId(dto.getSysDictionaryDataId());
        newPassCategoryConfig.setUpdatedBy(user.getUsername());
        newPassCategoryConfig.setUpdatedTime(LocalDateTime.now());
        passengerCategoryMapper.updateById(newPassCategoryConfig);
    }

    @Override
    @Transactional
    public ValidationException updatePassengerCategoryConfigureState(PassengerCategoryStateDto dto) {
        ArrayList<PassengerCategoryConfigureDepository> passengerCategoryConfigureDepositories = new ArrayList<>();

        LoginUserDetails user = UserContext.getCurrentUser();

        if (dto == null || dto.getIds().isEmpty()) {
            log.error("修改状态时出错，更新对象【{}】", dto);
            return new ValidationException("");
        }

        dto.getIds().forEach(o -> {
            PassengerCategoryConfigureDepository newPassCategoryConfigureDepository = new PassengerCategoryConfigureDepository();
            newPassCategoryConfigureDepository.setState(dto.getState());
            newPassCategoryConfigureDepository.setId(o);
            newPassCategoryConfigureDepository.setUpdatedBy(user.getUsername());
            newPassCategoryConfigureDepository.setUpdatedTime(LocalDateTime.now());

            passengerCategoryConfigureDepositories.add(newPassCategoryConfigureDepository);
        });

        this.updateBatchById(passengerCategoryConfigureDepositories);
        return null;
    }

    @Override
    public List<PassengerCategoryVo> passengerCategory(PassengerCategoryChangeDto dto) {
        return passengerCategoryMapper.passengerCategory(dto);
    }

    @Override
    public List<PassengerCategoryConfigureDepository> listPassengerCategoryConfigure(List<String> codes) {
        LambdaQueryWrapper<PassengerCategoryConfigureDepository> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.in(PassengerCategoryConfigureDepository::getCode,codes);
        List<PassengerCategoryConfigureDepository> results = passengerCategoryMapper.selectList(queryWrapper);

        return results;
    }

    private Boolean passengerCategoryRepeatCheck(PassengerCategoryChangeDto dto) {
        LambdaQueryWrapper<PassengerCategoryConfigureDepository> queryWrapper = Wrappers.lambdaQuery(PassengerCategoryConfigureDepository.class);
        queryWrapper.eq(PassengerCategoryConfigureDepository::getType, dto.getType());
        queryWrapper.eq(PassengerCategoryConfigureDepository::getCategory, dto.getCategory());

        if (passengerCategoryMapper.selectCount(queryWrapper) == null || passengerCategoryMapper.selectCount(queryWrapper).equals(0)) {
            return false;
        }

        return true;
    }
}
