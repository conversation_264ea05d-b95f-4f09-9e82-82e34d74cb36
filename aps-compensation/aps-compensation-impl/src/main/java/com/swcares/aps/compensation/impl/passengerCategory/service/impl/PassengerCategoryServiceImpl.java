package com.swcares.aps.compensation.impl.passengerCategory.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.passengerCategory.mapper.PassengerCategoryMapper;
import com.swcares.aps.compensation.impl.passengerCategory.service.PassengerCategoryService;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryStateDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.errors.PassengerCategoryErrors;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ValidationException;
import java.time.LocalDateTime;
import java.util.*;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.passengerCategory.service.impl.PassengerCategoryServiceImpl <br>;
 * Description：旅客类别服务实现 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 16:33 <br>;
 * @version v1.0 <br>;
 */
@Slf4j
@Service("passengerCategoryServiceImpl")
public class PassengerCategoryServiceImpl extends ServiceImpl<PassengerCategoryMapper, PassengerCategoryConfigureDepository> implements PassengerCategoryService {

    @Override
    public IPage<PassengerCategorySentPageVo> sentPage(PassengerCategorySentPageDto dto) {
        return null;
    }

    @Override
    public void savePassengerCategoryConfigure(PassengerCategoryChangeDto dto) {

    }

    @Override
    public void updatePassengerCategoryConfigure(PassengerCategoryChangeDto dto) {

    }

    @Override
    public ValidationException updatePassengerCategoryConfigureState(PassengerCategoryStateDto dto) {
        return null;
    }

    @Override
    public List<PassengerCategoryVo> passengerCategory(PassengerCategoryChangeDto dto) {
        return Collections.emptyList();
    }

    @Override
    public List<PassengerCategoryConfigureDepository> listPassengerCategoryConfigure(List<String> codes) {
        return Collections.emptyList();
    }
}
