package com.swcares.aps.compensation.impl.baggage.luggage.enums;

/**
 * <AUTHOR>
 * @title: LuggageStockStatusEnums
 * @projectName aps
 * @description: 箱包库存动作类别
 * @date 2022/2/8 15:47
 */
public enum LuggageConsumptionStatusEnum {
    BACK("2", "退回"),
    CONSUME("1", "消耗");


    private LuggageConsumptionStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static LuggageConsumptionStatusEnum build(String key) {
        return build(key, true);
    }

    public static LuggageConsumptionStatusEnum build(String key, boolean throwEx) {
        LuggageConsumptionStatusEnum typeEnum = null;
        for (LuggageConsumptionStatusEnum element : LuggageConsumptionStatusEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + LuggageConsumptionStatusEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
