package com.swcares.aps.compensation.impl.compensation.mapper;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationBaseInfoPageDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationStandardInfoDTO;
import com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationBaseInfoVO;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

/**
 * @ClassName：CompensationOrderQueriesMapper
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/9 14:52
 * @version： v1.0
 */
public interface CompensationOrderQueriesMapper {

    IPage<CompensationBaseInfoVO> compensationBaseInfoPage(CompensationBaseInfoPageDTO dto, Page<Object> page);

    String selectPaxName(Long compensationId);

    List<CompensationStandardInfoDTO> selectCompensationStandard(@PathVariable("compensationId") Long compensationId);

    List<String> selectIsCustom(Long id);

    List<String> selectCpsNum(Long orderId);

    IPage<CashBusinessCostsDetailVO> getCashBusinessCostsDetail(CompensationCashReportDTO dto, Page<Object> page);

    IPage<CashPayDetailVO> getCashPayDetail(CompensationCashReportDTO dto, Page<Object> page);

    List<CashBusinessCostsDetailVO> getCashBusinessCostsDetailReport(@Param("dto") CompensationCashReportDTO dto);

    IPage<CashPayDetailVO> getCashPayDetailReport(CompensationCashReportDTO dto, Page<Object> page);
}
