package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.enums.ApplyTypeCodeEnum;
import com.swcares.aps.compensation.impl.apply.enums.PayStatusEnum;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.service.ApplyBehalfOrderService;
import com.swcares.aps.compensation.impl.apply.service.ApplyChinaPayService;
import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.apply.service.PayRecordService;
import com.swcares.aps.compensation.impl.apply.util.ApplyCodeUtils;
import com.swcares.aps.compensation.impl.apply.util.DistributedUniqueIndexGenerator;
import com.swcares.aps.compensation.impl.apply.workflow.ApplyWorkflowService;
import com.swcares.aps.compensation.impl.constant.CompensationConstants;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationPaxInfoService;
import com.swcares.aps.compensation.model.apply.dto.ApplyBehalfOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxOrderDTO;
import com.swcares.aps.compensation.model.apply.dto.CompensationBehalfOrderInfoDTO;
import com.swcares.aps.compensation.model.apply.entity.ApplyOrderDO;
import com.swcares.aps.compensation.model.apply.entity.ApplyPaxDO;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.AuthPaxVO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.aps.component.pay.pay.PayTypeEnum;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaAuthUserInfoService;
import com.swcares.aps.component.pay.pay.service.chinapay.bean.ChinaPayAuthRequestDTO;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * ClassName：com.swcares.compensation.service.impl.ApplyOrderServiceImpl <br>
 * Description：航延补偿申领单信息表 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-11-25 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class ApplyBehalfOrderServiceImpl extends ServiceImpl<ApplyOrderMapper, ApplyOrderDO> implements ApplyBehalfOrderService {

    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    @Autowired
    private ApplyPaxService applyPaxService;

    @Autowired
    private PayRecordService payRecordService;

    @Autowired
    private Redisson redisson;

    @Autowired
    private FileAttachmentService fileAttachmentService;

    @Autowired
    private ApplyWorkflowService applyWorkflowService;

    @Autowired
    private CompensationPaxInfoService compensationPaxInfoService;

    //协助领取保存实现类
    @Autowired
    private AssistApplyOrderRealServiceImpl assistApplyOrderService;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    private ChinaAuthUserInfoService chinaAuthUserInfoService;

    @Autowired
    private CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    ApplyChinaPayService applyChinaPayService;


    /**
     * @title saveBehalfApplyOrder
     * @description 保存申领单
     * <AUTHOR>
     * @date 2021/11/29 11:00
     * @param dto
     * @return boolean
     */
    @Override
    public boolean saveBehalfApplyOrder(ApplyBehalfOrderDTO dto) {
        log.info("代人领取耗时，"+dto.getApplyUser()+"开始: "+System.currentTimeMillis());
        //整个申领单保存的标记位
        boolean bool = false;
        if(dto.getGetMoneyWay().equals(PayTypeEnum.WECHAT.getKey())){
            log.info("【aps-apply-impl】当前代人领取选择的微信支付，当前用户的openId为【{}】", UserContext.getCurrentUser().getWxOpenid());
            dto.setGetMoneyAccount(UserContext.getCurrentUser().getWxOpenid());
        }
        log.info("【aps-apply-impl】代人领取开始保存代人领取的申领单信息，当前用户为【{}】请求参数【{}】",
                JSONUtil.toJsonStr(UserContext.getCurrentUser()), JSONUtil.toJsonStr(dto));


        //【安测问题修复】水平越权--同数据篡改的问题一样【设计如此，知道证件号后可以创建申领单，但是微信打款失败，银联打款是页面输入-需要修改传参方式】
        //账号A可以越权申领账户B的补偿
        if(PayTypeEnum.UNIONPAY.getKey().equals(dto.getCompensateSubType())) {
            ChinaPayAuthRequestDTO authRequestDTO = new ChinaPayAuthRequestDTO();
            authRequestDTO.setApplyUser(dto.getApplyUser());
            authRequestDTO.setCardNo(dto.getGetMoneyAccount());
            authRequestDTO.setIdType("身份证");
            authRequestDTO.setIdNo(dto.getIdNo());
            applyChinaPayService.isTokenChinaPayPass(authRequestDTO);
           /* Map map = new HashMap<String, String>();
            map.put("cardNo", dto.getGetMoneyAccount());
            map.put("idType", "身份证");
            map.put("idNo", dto.getIdNo());
            map.put("userName", dto.getApplyUser());
            try {
                BaseResult<Object> chinaAuthResult = chinaAuthUserInfoService.authCarUserInfo(JSONObject.toJSONString(map), dto.getAccidentType());
                if(BaseResult.OK_CODE != chinaAuthResult.getCode() || !"true".equals(chinaAuthResult.getData().toString())){
                    throw new BusinessException(PayErrors.RETURN_MSG_FAILED);
                }
            }catch (Exception e) {
                throw new BusinessException(PayErrors.RETURN_MSG_FAILED);
            }*/
        }

        ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
        ApplyBehalfOrderDTO irregularFlightOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, irregularFlightOrderDto, new String[]{"applyAmount"});
        irregularFlightOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        irregularFlightOrderDto.setFile(dto.getFile());
        Set irregularFlightSet = new HashSet();
        ApplyBehalfOrderDTO abnormalLuggageOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, abnormalLuggageOrderDto, new String[]{"applyAmount"});
        abnormalLuggageOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        abnormalLuggageOrderDto.setFile(dto.getFile());
        Set abnormalLuggageSet = new HashSet();
        ApplyBehalfOrderDTO overbookingOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, overbookingOrderDto, new String[]{"applyAmount"});
        overbookingOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        overbookingOrderDto.setFile(dto.getFile());
        Set overbookingSet = new HashSet();

        ApplyBehalfOrderDTO complaintOrderDto = new ApplyBehalfOrderDTO();
        ObjectUtils.copyBean(applyOrderDO, complaintOrderDto, new String[]{"applyAmount"});
        complaintOrderDto.setOrderInfoVOS(new ArrayList<CompensationBehalfOrderInfoDTO>());
        complaintOrderDto.setFile(dto.getFile());
        Set complaintSet = new HashSet();
        try {
            //循环后分类领取单数据
            for (CompensationBehalfOrderInfoDTO compensationBehalfOrderInfoDTO : dto.getOrderInfoVOS()) {
                AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
                authPaxOrderDTO.setOrderId(compensationBehalfOrderInfoDTO.getOrderId());
                authPaxOrderDTO.setPaxId(compensationBehalfOrderInfoDTO.getPaxId());
                AuthPaxVO authPaxVO = this.getBaseMapper().findApplyAuthPax(authPaxOrderDTO);
                //判断单子状态如果有问题直接抛错
                if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                    throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
                } else {
                    CompensationPaxInfoDO paxInfo = compensationPaxInfoService.getById(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                    if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.IRREGULAR_FLIGHT)) {//不正常航班
                        irregularFlightSet.add(paxInfo.getIdNo());
                        irregularFlightOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        irregularFlightOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(irregularFlightOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : irregularFlightOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.ABNORMAL_LUGGAGE)) {//异常行李
                        abnormalLuggageSet.add(paxInfo.getIdNo());
                        abnormalLuggageOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        abnormalLuggageOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(abnormalLuggageOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : abnormalLuggageOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.OVER_BOOKING)) {//超售
                        overbookingSet.add(paxInfo.getIdNo());
                        overbookingOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        overbookingOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(overbookingOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : overbookingOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    } else if (compensationBehalfOrderInfoDTO.getAccidentType().equals(ApplyConstants.COMPLAINT)) {//投诉
                        complaintSet.add(paxInfo.getIdNo());
                        complaintOrderDto.getOrderInfoVOS().add(compensationBehalfOrderInfoDTO);
                        complaintOrderDto.setApplyAmount(
                                ObjectUtils.isEmpty(complaintOrderDto.getApplyAmount()) ? new BigDecimal(0).add(authPaxVO.getMoney())
                                        : complaintOrderDto.getApplyAmount().add(authPaxVO.getMoney()));
                    }

                }
            }
            log.info("代人领取耗时，"+dto.getApplyUser()+"根据类型保存申领单: "+System.currentTimeMillis());
            //根据事故单类型来保存申领单
            if (ListUtils.isNotEmpty(irregularFlightOrderDto.getOrderInfoVOS())) {
                bool = processBehalfApplyOrder(irregularFlightOrderDto);
            }
            if (ListUtils.isNotEmpty(abnormalLuggageOrderDto.getOrderInfoVOS())) {
                bool = processBehalfApplyOrder(abnormalLuggageOrderDto);
            }
            if (ListUtils.isNotEmpty(overbookingOrderDto.getOrderInfoVOS())) {
                bool = processBehalfApplyOrder(overbookingOrderDto);
            }
            if (ListUtils.isNotEmpty(complaintOrderDto.getOrderInfoVOS())) {
                bool = processBehalfApplyOrder(complaintOrderDto);
            }

            log.info("代人领取耗时，"+dto.getApplyUser()+"结束: "+System.currentTimeMillis());
        } catch (BusinessException e) {
            throw e;
        } catch (Exception e) {
            log.error("【aps-apply-impl代人领取上传文件异常，整个请求参数为【{}】", JSONUtil.toJsonStr(dto), e);
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }

        return bool;
    }

    /**
     * Title：processBehalfApplyOrder <br>
     * Description：申领单不正常航班保存 <br>
     * author：王磊 <br>
     * date：2021/12/8 13:46 <br>
     * @param dto 保存申领单的传输对象 <br>
     * @return <br>
     */
    public boolean processBehalfApplyOrder(ApplyBehalfOrderDTO dto) throws Exception {
        LoginUserDetails user = UserContext.getUser();
        boolean bool = false;

        //用于更新赔偿单旅客状态的旅客id集合，更新为领取中
        List<Long> paxIds = new ArrayList<Long>();
        List<String> applyCodeList = new ArrayList<String>();
        //数据来源=机场端  申领单集合
        List<ApplyOrderDO> applyOrderDOAirPortList = new ArrayList<>();
        //如果图片上传失败，是否应该抛异常中断业务
        FileAttachment fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(dto.getFile()), bucketName,null,null);
        log.info("代人领取耗时，代领人图片上传:"+System.currentTimeMillis());

        for (CompensationBehalfOrderInfoDTO compensationBehalfOrderInfoDTO : dto.getOrderInfoVOS()) {
            AuthPaxOrderDTO authPaxOrderDTO = new AuthPaxOrderDTO();
            authPaxOrderDTO.setOrderId(compensationBehalfOrderInfoDTO.getOrderId());
            authPaxOrderDTO.setPaxId(compensationBehalfOrderInfoDTO.getPaxId());
            //保存前获取赔偿单的旅客信息用于校验赔偿单用户是否可用
            AuthPaxVO authPaxVO = this.getBaseMapper().findApplyAuthPax(authPaxOrderDTO);

            //判断赔偿单上是否有个旅客 || 该旅客状态是否被冻结 || 旅客是申领单不是未领取状态
            if (ObjectUtils.isEmpty(authPaxVO) || ApplyConstants.AUTH_PAX_SWITCH_OFF.equals(authPaxVO.getSwitchOff()) || !ApplyConstants.AUTH_PAX_RECEIVE_STATUS.equals(authPaxVO.getReceiveStatus())) {
                log.error("【aps-apply-impl】代人领取申领单保存验证赔偿单状态失败,申领人{},申领人身份证:{}",dto.getApplyUser(),dto.getIdNo());
                throw new BusinessException(ApplyErrors.APPLY_AUTH_PAX_ORDER);
            } else {

                ApplyOrderDO applyOrderDO = ObjectUtils.copyBean(dto, ApplyOrderDO.class);
                log.info("【aps-apply-impl】代领申领单保存,UserDetails信息：{},申领人{},申领人身份证:{},申领单号:{}", JSON.toJSONString(user),applyOrderDO.getApplyUser(),applyOrderDO.getIdNo(),applyOrderDO.getApplyCode());
                //一定会有赔偿单信息,直接取
                CompensationBehalfOrderInfoDTO orderInfoDTO = dto.getOrderInfoVOS().get(0);
                String accidentTypeCode = ApplyTypeCodeEnum.build(orderInfoDTO.getAccidentType()).getValue();
                //生成申领单编号
                String applyCode = ApplyCodeUtils.createApplyCode(ApplyConstants.APPLY_TYPE_R,accidentTypeCode,user.getTenantCode());
                applyOrderDO.setApplyCode(applyCode);
                applyOrderDO.setCollectIdentityCardPhoto(fileAttachment.getId().toString());
                applyOrderDO.setAccidentType(orderInfoDTO.getAccidentType());
                //数据来源  1机场/2航司
                applyOrderDO.setSource(CompensationConstants.BUSINESS_DATA_SOURCE_AIRLINE);
                //申领金额
                applyOrderDO.setApplyAmount(authPaxVO.getMoney());
                //【保存申领单】
                log.info("【aps-apply-impl】代领申领单保存,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),applyOrderDO.getIdNo(),applyOrderDO.getApplyCode());
                //存当前值的目的是保存当前申领人的微信openid，目前用于消息推送
                applyOrderDO.setCreatedBy(UserContext.getCurrentUser().getWxOpenid());
                bool = this.save(applyOrderDO);
                throwSaveError(bool);


                fileAttachment = fileAttachmentService.uploadFile(BASE64DecodedMultipartFile.base64ToMultipart(compensationBehalfOrderInfoDTO.getFile()),bucketName,null,null);
                log.info("代人领取耗时，被代领人图片上传:"+System.currentTimeMillis());
                //旅客信息
                ApplyPaxDO applyPaxDO = new ApplyPaxDO();
                applyPaxDO.setPaxIdentityCardPhoto(fileAttachment.getId().toString());
                applyPaxDO.setApplyCode(applyCode);
                applyPaxDO.setApplyId(applyOrderDO.getId());
                applyPaxDO.setOrderId(new Long(compensationBehalfOrderInfoDTO.getOrderId()));
                applyPaxDO.setPaxInfoId(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                applyPaxDO.setApplyPaxStatus(ApplyConstants.APPLY_PAX_STATUS_OPERATION);
                //【保存申领单旅客信息】
                log.info("【aps-apply-impl】代人领取申领单保存,保存申领旅客,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),applyOrderDO.getIdNo(),applyOrderDO.getApplyCode());
                bool = applyPaxService.save(applyPaxDO);
                throwSaveError(bool);


                //支付记录
                PayRecordDO payRecordDO = new PayRecordDO();
                payRecordDO.setId(DistributedUniqueIndexGenerator.generateUniqueIndex());
                payRecordDO.setApplyId(applyOrderDO.getId());
                payRecordDO.setApplyPaxId(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                payRecordDO.setOrderId(new Long(compensationBehalfOrderInfoDTO.getOrderId()));
                payRecordDO.setPayStatus(PayStatusEnum.NON_PAYMENT.getKey());
                payRecordDO.setPayType(applyOrderDO.getGetMoneyWay());
                payRecordDO.setTransAmount(authPaxVO.getMoney());
                //可以删除
                payRecordDO.setCreatedTime(LocalDateTime.now());
                //【保存支付记录(创建待支付记录)】
                log.info("【aps-apply-impl】代人领取申领单保存,保存申领支付记录,申领人{},申领人身份证:{},申领单号:{}",applyOrderDO.getApplyUser(),applyOrderDO.getIdNo(),applyOrderDO.getApplyCode());
                bool = payRecordService.save(payRecordDO);
                throwSaveError(bool);

                log.info("代人领取耗时，发起审核流程开始,id:"+applyOrderDO.getId()+",耗时:"+System.currentTimeMillis());
                //发起流程
                applyWorkflowService.startWorkflow(applyOrderDO.getId().toString(),"-1");

                log.info("代人领取耗时，发起审核流程结束,id:"+applyOrderDO.getId()+",耗时:"+System.currentTimeMillis());


                paxIds.add(new Long(compensationBehalfOrderInfoDTO.getPaxId()));
                applyCodeList.add(applyCode);

                CompensationOrderInfoDO orderDetailsVO = compensationOrderInfoService.getById(Long.valueOf(compensationBehalfOrderInfoDTO.getOrderId()));
                if(orderDetailsVO.getSource().equals(CompensationConstants.BUSINESS_DATA_SOURCE_AIRPORT)){
                    applyOrderDOAirPortList.add(applyOrderDO);
                }
            }
        }

        log.info("代人领取耗时，循环保存数据后:"+System.currentTimeMillis());
        //【调用赔偿单api更新赔偿单旅客数据】
        log.info("【aps-apply-impl】代人领取申领单保存,更新赔偿单,申领人{},申领人身份证:{},申领单号集合:{}",dto.getApplyUser(),dto.getIdNo(),applyCodeList.toString());
        boolean result = compensationPaxInfoService.updatePaxReceiveInfo(paxIds, dto.getApplyWay(), dto.getGetMoneyWay(), ApplyConstants.GET_RECEIVE_STATUS);
        throwSaveError(result);
        log.info("代人领取耗时，更新旅客状态:"+System.currentTimeMillis());
        //推送赔偿单数据来源=机场，申领数据回传给机场端
        if(applyOrderDOAirPortList.size() >0){
            applyOrderDOAirPortList.forEach(d->{
                businessDataPushHandler.dataStore(d.getId(), AccidentTypeBusinessCodeEnum.build(d.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_APPLY);
            });
        }

        return bool;
    }

    /**
     * @title saveAssistApply
     * @description 协助领取保存申领单
     * <AUTHOR>
     * @date 2022/2/16 17:27
     * @param dto
     * @return boolean
     */
    @Override
    public boolean saveAssistApply(ApplyBehalfOrderDTO dto) {
        return assistApplyOrderService.saveAssistApplyOrder(dto);
    }

    /**
     * @title createApplyCode
     * @description 构建申领单code
     * <AUTHOR>
     * @date 2021/12/1 11:28

     * @return java.lang.String
     */
    private String createApplyCode(LocalDateTime localDateTime) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmssSSS");
        StringBuffer stringBuffer = new StringBuffer(localDateTime.format(formatter));
        stringBuffer.append(String.format("%03d", new Random().nextInt(999)));
        return stringBuffer.toString();
    }

    /**
     * @title throwSaveError
     * @description 保存时抛出错误
     * <AUTHOR>
     * @date 2021/12/2 11:20
     * @param bool
     * @return void
     */
    private void throwSaveError(boolean bool) {
        if (!bool) {
            throw new BusinessException(ApplyErrors.APPLY_SAVE_ERROR);
        }
    }
}
