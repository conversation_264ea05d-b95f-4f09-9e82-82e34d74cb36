package com.swcares.aps.compensation.impl.irregularflight.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.toolkit.SqlHelper;
import com.swcares.aps.compensation.impl.baggage.accident.constant.BaggageAccidentException;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.RowPermissionTableName;
import com.swcares.aps.compensation.impl.irregularflight.service.FlightAccidentInfoService;
import com.swcares.aps.compensation.impl.irregularflight.mapper.FlightAccidentInfoMapper;
import com.swcares.aps.compensation.impl.irregularflight.service.AccidentRecordService;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.FlightAccidentInfoPagedDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.AccidentRecordDO;
import com.swcares.aps.compensation.model.irregularflight.entity.FlightAccidentInfoDO;
import com.swcares.aps.compensation.model.irregularflight.vo.*;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.permission.util.RowPermissionUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * ClassName：com.swcares.irregularflight.service.impl.DpFlightAccidentInfoServiceImpl <br>
 * Description： 服务实现类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-10-14 <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class FlightAccidentInfoServiceImpl extends ServiceImpl<FlightAccidentInfoMapper, FlightAccidentInfoDO> implements FlightAccidentInfoService {

    private static final String ACCIDENT_NO_PREFIX = "IFA";

    @Resource
    AccidentRecordService accidentRecordService;

    @Autowired
    private Redisson redisson;

    @Autowired
    private FlightAccidentInfoServiceImpl flightAccidentInfoService;

    private static final String FLIGHT_ACCIDENT_SAVE_LOCK_PREFIX = "flight_accident_save_lock_prefix";


    @Override
    public FlightAccidentInfoDetailsVO findById(Long id) {
        return getBaseMapper().findById(id);
    }

    @Override
    public boolean logicRemoveById(Long id) {
        FlightAccidentInfoDO flightAccidentInfoDO = new FlightAccidentInfoDO();
        flightAccidentInfoDO.setId(id);
        flightAccidentInfoDO.setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
        LambdaQueryWrapper<FlightAccidentInfoDO> wrapper = Wrappers.lambdaQuery(flightAccidentInfoDO);
        return SqlHelper.retBool(getBaseMapper().delete(wrapper));
    }

    @Override
    public boolean toVoidById(Long id) {
        //set
        FlightAccidentInfoDO accidentInfo = new FlightAccidentInfoDO();
        accidentInfo.setAccidentStatus(AccidentStatusEnum.TO_VOID.getValue());
        accidentInfo.setTovoidTime(LocalDateTime.now());
        accidentInfo.setTovoidId(ApsUserUtils.getCreatedBy());

        //where 待处理状态下作废
        FlightAccidentInfoDO accidentInfo1 = new FlightAccidentInfoDO();
        accidentInfo1.setAccidentStatus(AccidentStatusEnum.TODO.getValue());
        accidentInfo1.setId(id);
        LambdaQueryWrapper<FlightAccidentInfoDO> wrapper = Wrappers.lambdaQuery(accidentInfo1);

        return SqlHelper.retBool(getBaseMapper().update(accidentInfo, wrapper));
    }

    @Override
    public boolean update(FlightAccidentInfoDO flightAccidentInfoDO) {
        flightAccidentInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        flightAccidentInfoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        //草稿状态下，编辑
        FlightAccidentInfoDO accidentInfo = new FlightAccidentInfoDO();
        accidentInfo.setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
        accidentInfo.setId(flightAccidentInfoDO.getId());
        LambdaQueryWrapper<FlightAccidentInfoDO> wrapper = Wrappers.lambdaQuery(accidentInfo);
        boolean state = SqlHelper.retBool(getBaseMapper().update(flightAccidentInfoDO, wrapper));
        //记录修改操作人
        if (state) {
            AccidentRecordDO recordDO = new AccidentRecordDO();
            recordDO.setAccidentId(flightAccidentInfoDO.getId());
            recordDO.setAccidentNo(flightAccidentInfoDO.getAccidentNo());
            recordDO.setOperationId(ApsUserUtils.getCreatedBy());//作废人
            recordDO.setOperationTime(LocalDateTime.now());
            accidentRecordService.save(recordDO);
        }
        return state;
    }

    @Override
    public IPage<FlightAccidentInfoVO> page(FlightAccidentInfoPagedDTO dto) {

       /* if(StringUtils.isNotBlank(dto.getAccidentStatus())){
            String accStatus= dto.getAccidentStatus().replace("\"","");
            List<String> dictList = Arrays.asList(accStatus.substring(1,accStatus.length()-1).split(","));
            dto.setAccidentStatusList(dictList);
        }*/
        //将场站范围，事故类型传入
        String tableName= RowPermissionTableName.ACCIDENT;
        List<String> idTypes = new ArrayList<>();
        idTypes.add(RowPermissionTableName.ACCIDENTTYPES);
        idTypes.add(RowPermissionTableName.WORKSTATIONS);
        Map<String,List<String>> mapTypes = RowPermissionUtil.getRowValues(tableName,idTypes);
        if(ObjectUtils.isNotEmpty(mapTypes)){
            List<String> accidentTypes = mapTypes.get(RowPermissionTableName.ACCIDENTTYPES);
            List<String> workStations = mapTypes.get(RowPermissionTableName.WORKSTATIONS);
            if(ObjectUtils.isNotEmpty(accidentTypes)){
                dto.setAccidentType(accidentTypes);
            }
            if(ObjectUtils.isNotEmpty(workStations)){
                dto.setWorkStations(workStations);
            }
        }
        return baseMapper.page(dto, dto.createPage());
    }

    @Override
    public boolean saveAccident(FlightAccidentInfoDTO dto){
        log.info("【不正常航班-事故单创建】加锁对象：航班号【{}】，航班日期【{}】", dto.getFlightNo() , dto.getFlightDate() );
        String lockKey = FLIGHT_ACCIDENT_SAVE_LOCK_PREFIX+dto.getFlightNo() + dto.getFlightDate() ;
        RLock r = redisson.getLock(lockKey);
        boolean locked = false;
        try {
            locked = r.tryLock(0, 5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("【不正常航班-事故单创建】获取锁异常，lockKey: {}", lockKey, e);
            throw new BusinessException(BaggageAccidentException.SYSTEM_BUSY);
        }
        if (!locked) {
            log.info("【不正常航班-事故单创建】lockKey: {},加锁对象：航班号【{}】，航班日期【{}】，获取锁失败，说明该单子已经被创建了~", lockKey, dto.getFlightNo() , dto.getFlightDate() );
            throw new BusinessException(BaggageAccidentException.ACCIDENT_DUPLICATE_SUBMISSION);
        }
        log.info("【不正常航班-事故单创建】lockKey: {},加锁对象：航班号【{}】，航班日期【{}】，获取锁成功，执行业务逻辑~", lockKey, dto.getFlightNo() , dto.getFlightDate() );
        // 由于设置了leaseTime，锁会自动释放，无需手动unlock
        return flightAccidentInfoService.doSaveAccident(dto);
    }

    public boolean doSaveAccident(FlightAccidentInfoDTO dto) {
        FlightAccidentInfoDO flightAccidentInfoDO = ObjectUtils.copyBean(dto, FlightAccidentInfoDO.class);
        flightAccidentInfoDO.setAccidentNo(CompensationOrderNoUtils.getOrderNumber(UserContext.getCurrentUser().getTenantCode(), ACCIDENT_NO_PREFIX));
        flightAccidentInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        flightAccidentInfoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        flightAccidentInfoDO.setBelongAirline(dto.getFlightNo().substring(0,2));
        flightAccidentInfoDO.setAccidentSource("2");
        flightAccidentInfoDO.setCreatedTime(LocalDateTime.now());
        flightAccidentInfoDO.setUpdatedTime(LocalDateTime.now());
        flightAccidentInfoDO.setBelongAirlineAbbr(UserContext.getCurrentUser().getTenantName());
        boolean bool = this.save(flightAccidentInfoDO);
        dto.setId(flightAccidentInfoDO.getId());
        return bool;
    }

    @Override
    public List<FlightExistsAccidentVO> findFilghtExistsAccident( String flightNo,String date,Long id) {
        return baseMapper.findFilghtExistsAccident(flightNo,date,id);
    }

    @Override
    public List<AccidentCompensationOrderVO> findCompensationOrderById(Long id) {
        return  baseMapper.getCompensationOrderById(id);
    }

    @Override
    public List<Map<String,Object>> findChoiceSegment(Long id) {
        CompensationFlightInfoVO compensationFlightInfoVO = baseMapper.findFlightCompensationInfo(id);
        List<String> segmentValues = Arrays.asList(compensationFlightInfoVO.getSegment().split(","));
        List<String> segmentKeys = Arrays.asList(compensationFlightInfoVO.getSegmentCh().split(","));
        List<Map<String, Object>> list = new ArrayList<>();
        for (String key:segmentKeys) {
            Map<String,Object> map = new HashMap<>();
            for (String value:segmentValues) {
                String[] values = value.split("-");
                if(key.contains(values[0]) && key.contains(values[1])){
                    map.put("segmentChKey",key);
                    map.put("segmentChValue",value);
                    break;
                }
            }
            list.add(map);
        }
        return list;
    }

    @Override
    public boolean updAccidentStatusToDo(Long accidentId, String accidentStatus) {
        return baseMapper.updAccidentStatusToDo(accidentId,accidentStatus);
    }

    @Override
    public boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus) {
        return baseMapper.updAccidentStatusBatch(accidentNo,accidentStatus);
    }

    /**
     * Title：createAccidentNo <br>
     * Description：创建事故单号 <br>
     * author：王磊 <br>
     * date：2021/10/27 19:32 <br>
     * <br>
     *
     * @return <br>
     */
    private static String createAccidentNo() {
        StringBuffer accidentNo = new StringBuffer(ACCIDENT_NO_PREFIX);
        Calendar cal = Calendar.getInstance();
        SimpleDateFormat sdfMonth = new SimpleDateFormat("MM");
        SimpleDateFormat sdfDate = new SimpleDateFormat("dd");
        accidentNo.append(cal.get(Calendar.YEAR));
        accidentNo.append(sdfMonth.format(cal.getTime()));
        accidentNo.append(sdfDate.format(cal.getTime()));
        accidentNo.append(String.format("%04d",new Random().nextInt(9999)));
        return accidentNo.toString();
    }

}
