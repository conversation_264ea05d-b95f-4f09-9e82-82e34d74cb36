package com.swcares.aps.compensation.impl.overbook.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.conditions.query.LambdaQueryChainWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.basic.data.businessimpl.model.vo.FlightFindVO;
import com.swcares.aps.basic.data.businessimpl.service.CompensationBasicDataService;
import com.swcares.aps.basic.data.remoteapi.model.dto.FlightBaseQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.FlightBasicnfoVO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationImplFltPaxDataService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.impl.CompensationPaxInfoServiceImpl;
import com.swcares.aps.compensation.impl.overbook.constant.OverBookConstant;
import com.swcares.aps.compensation.impl.overbook.constant.OverBookException;
import com.swcares.aps.compensation.impl.overbook.mapper.OverBookAccidentInfoMapper;
import com.swcares.aps.compensation.impl.overbook.service.OverBookAccidentInfoService;
import com.swcares.aps.compensation.impl.overbook.service.OverBookConfigService;
import com.swcares.aps.compensation.impl.privilege.mapper.BusinessPrivilegeMapper;
import com.swcares.aps.compensation.impl.util.CompensationOrderNoUtils;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationFlightInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationPaxInfoDO;
import com.swcares.aps.compensation.model.irregularflight.enums.CompensationAccidentTypeEnum;
import com.swcares.aps.compensation.model.overbook.dto.*;
import com.swcares.aps.compensation.model.overbook.entity.OverBookAccidentInfoDO;
import com.swcares.aps.compensation.model.overbook.vo.*;
import com.swcares.aps.compensation.model.privilege.entity.AirlineBusinessPrivilege;
import com.swcares.aps.component.com.security.ApsUserUtils;
import com.swcares.aps.component.com.util.BASE64DecodedMultipartFile;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.uc.entity.FileAttachment;
import com.swcares.components.uc.service.FileAttachmentService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @ClassName：OverBookAccidentInfoServiceImpl
 * @Description：超售事故单
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/5/30 11:27
 * @version： v1.0
 */
@Slf4j
@Service
public class OverBookAccidentInfoServiceImpl extends ServiceImpl<OverBookAccidentInfoMapper, OverBookAccidentInfoDO> implements OverBookAccidentInfoService {
    @Autowired
    OverBookConfigService overBookConfigService;
    @Autowired
    private CompensationBasicDataService compensationBasicDataService;
    @Autowired
    private CompensationImplFltPaxDataService compensationImplFltPaxDataService;
    @Autowired
    CompensationOrderInfoService compensationOrderInfoService;

    @Autowired
    CompensationPaxInfoServiceImpl compensationPaxInfoService;
    @Autowired
    CompensationFlightInfoService compensationFlightInfoService;
    @Autowired
    private FieldEncryptor fieldEncryptor;
    @Autowired
    private FileAttachmentService fileAttachmentService;
    @Value("${swcares.minio.bucketName}")
    private String bucketName;

    final String ACCIDENT_NO_CODE="FOA";
    final String ORDER_NO_CODE="FOC";

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Autowired
    BusinessPrivilegeMapper businessPrivilegeMapper;

    @Override
    public IPage<OverBookPcListVO> getPcList(OverBookPcSearchDTO dto) {
        return baseMapper.getPcPages(dto, dto.createPage());
    }

    @Override
    public List<CompensationNumListVO> getCompensateNumList(OverBookPaxSearchDTO dto) {
        return baseMapper.getCompensateNumList(dto);
    }

    @Override
    public OverBookPcDetailsVO getPcDetail(Long accidentId) {
        OverBookPcDetailsVO detailsVO = new OverBookPcDetailsVO();
        OverBookAccidentInfoDO accidentInfoDO = baseMapper.selectById(accidentId);
        OverBookAccidentDetailsVO accidentDetailsVO = new OverBookAccidentDetailsVO();
        BeanUtil.copyProperties(accidentInfoDO,accidentDetailsVO);
        accidentDetailsVO.setAccidentId(accidentId);
        // 格式化LocalDateTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedTime = accidentInfoDO.getCreatedTime().format(formatter);
        accidentDetailsVO.setCreatedTime(formattedTime);
        accidentDetailsVO.setToVoidTime(ObjectUtils.isNotEmpty(accidentInfoDO.getToVoidTime())?accidentInfoDO.getToVoidTime().format(formatter):null);
        accidentDetailsVO.setAccidentType(CompensationAccidentTypeEnum.OVERBOOKING.getKey());
        //旅客信息
        OverBookBasicPaxVO bookBasicPaxVO = getBaseMapper().getBookBasicPaxVO(accidentId, accidentInfoDO.getPaxId());

        detailsVO.setAccidentDetailsVO(accidentDetailsVO);
        detailsVO.setPaxVO(bookBasicPaxVO);
        //补偿单信息
        detailsVO.setCompensationDetailsVOs(this.getBaseMapper().getObCompensationDetails(accidentId));
        return detailsVO;
    }

    @Override
    public OverBookH5DetailsVO overBookDetailInfo(Long accidentId) {
        OverBookH5DetailsVO detailsVO = new OverBookH5DetailsVO();

        OverBookAccidentInfoDO accidentInfoDO = baseMapper.selectById(accidentId);
        OverBookAccidentDetailsVO accidentDetailsVO = new OverBookAccidentDetailsVO();
        BeanUtil.copyProperties(accidentInfoDO,accidentDetailsVO);
        accidentDetailsVO.setAccidentId(accidentId);
        // 格式化LocalDateTime
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedTime = accidentInfoDO.getCreatedTime().format(formatter);
        accidentDetailsVO.setCreatedTime(formattedTime);
        accidentDetailsVO.setToVoidTime(ObjectUtils.isNotEmpty(accidentInfoDO.getToVoidTime())?accidentInfoDO.getToVoidTime().format(formatter):null);
        //旅客信息
        OverBookBasicPaxVO bookBasicPaxVO = getBaseMapper().getBookBasicPaxVO(accidentId, accidentInfoDO.getPaxId());
        detailsVO.setAccidentDetailsVO(accidentDetailsVO);
        detailsVO.setPaxVO(bookBasicPaxVO);
        //补偿单信息
        LambdaQueryChainWrapper<CompensationOrderInfoDO> wrapper = compensationOrderInfoService.lambdaQuery();
        wrapper.eq(CompensationOrderInfoDO::getAccidentId,accidentId);
        List<CompensationOrderInfoDO> orderInfoDOList = wrapper.list();
        if(ObjectUtils.isNotEmpty(orderInfoDOList)){
            List<OverBookH5CompensationDetailsVO> compensationDetailsVOs = new ArrayList<>();
            for(CompensationOrderInfoDO orderInfoDO:orderInfoDOList){
                OverBookH5CompensationDetailsVO detailsVO1 = new OverBookH5CompensationDetailsVO();
                BeanUtil.copyProperties(orderInfoDO,detailsVO1);
                detailsVO1.setOrderId(orderInfoDO.getId().toString());
                compensationDetailsVOs.add(detailsVO1);
            }
            detailsVO.setCompensationDetailsVOs(compensationDetailsVOs);
        }

        return detailsVO;
    }

    @Override
    public IPage<OverBookH5ListVO> getH5List(OverBookH5SearchDTO dto) {
        if(StringUtils.isNotEmpty(dto.getAccidentStatus())){
            dto.setAccidentStatusList(Arrays.asList(dto.getAccidentStatus().split(",")));
        }
        if(StringUtils.isNotEmpty(dto.getType())){
            dto.setTypeList(Arrays.asList(dto.getType().split(",")));
        }
        if(OverBookConstant.SEARCH_TYPE_1.equals(dto.getSearchType())){
            dto.setSearchInfo(fieldEncryptor.encrypt(dto.getSearchInfo()));
        }
        return baseMapper.getH5List(dto,dto.createPage());
    }

    @Override
    public List<OverBookPaxVO> findSelectedPax(OverBookVerifyDTO dto) {
        List<OverBookPaxVO> paxVOList = new ArrayList<>();
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setFlightDate(dto.getFlightDate());
        queryDTO.setFlightNo(dto.getFlightNo());

        if(OverBookConstant.SEARCH_TYPE_1.equals(dto.getSearchType())){
            queryDTO.setIdNo(dto.getSearchInfo());
        }
        if(OverBookConstant.SEARCH_TYPE_2.equals(dto.getSearchType())){
            queryDTO.setTktNo(dto.getSearchInfo());
        }
        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(queryDTO);
       if(ObjectUtils.isEmpty(passengerInfo)){
            throw new BusinessException(OverBookException.NOT_FOUND_PAX);
       }
       for(PassengerBasicInfoVO pax:passengerInfo){
           OverBookPaxVO paxVO = new OverBookPaxVO();
           BeanUtil.copyProperties(pax,paxVO);
           FlightFindVO flight = compensationBasicDataService.getFlight(dto.getFlightDate(), dto.getFlightNo(), pax.getOrgCityAirp()+"-"+pax.getDstCityAirp());
           if(flight != null){
               //出发到达时间：优先级进行判断和展示，优先实际，然后预计，最后计划。
               paxVO.setStd(StringUtils.isNotEmpty(flight.getAtd())?flight.getAtd():(StringUtils.isNotEmpty(flight.getEtd())?flight.getEtd():flight.getStd()));
               paxVO.setSta(StringUtils.isNotEmpty(flight.getAta())?flight.getAta():(StringUtils.isNotEmpty(flight.getEta())?flight.getEta():flight.getSta()));
               paxVO.setSegment(flight.getSegment());//包含经停
               paxVO.setSegmentCh(flight.getSegmentCh());
           }
           paxVOList.add(paxVO);
       }
       return paxVOList;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void toVoidOverBook(Long accidentId) {
        // 创建LambdaUpdateWrapper实例
        LambdaUpdateWrapper<OverBookAccidentInfoDO> updateWrapper = new LambdaUpdateWrapper<>();
        // 设置更新条件：根据id更新
        updateWrapper.eq(OverBookAccidentInfoDO::getId, accidentId);
        updateWrapper.eq(OverBookAccidentInfoDO::getAccidentStatus, AccidentStatusEnum.TODO.getValue());
        updateWrapper.set(OverBookAccidentInfoDO::getAccidentStatus, AccidentStatusEnum.TO_VOID.getValue());
        updateWrapper.set(OverBookAccidentInfoDO::getToVoidBy,  ApsUserUtils.getCreatedBy());
        updateWrapper.set(OverBookAccidentInfoDO::getToVoidTime, LocalDateTime.now());
        // 执行更新操作
        int update = getBaseMapper().update(null, updateWrapper);
        if(update==0){
            throw new BusinessException(OverBookException.UPDATE_DATA_ERROR);
        }

        //推送业务数据到协同中心
        businessDataPushHandler.dataStore(accidentId, BusinessDataSyncConstant.BUSINESS_OVER_BOOKING, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void delOverBook(Long accidentId) {
        // 创建LambdaQueryWrapper实例
        LambdaQueryWrapper<OverBookAccidentInfoDO> wrapper = new LambdaQueryWrapper<>();

        wrapper.eq(OverBookAccidentInfoDO::getId, accidentId);
        wrapper.eq(OverBookAccidentInfoDO::getAccidentStatus, AccidentStatusEnum.DRAFT.getValue());
        // 执行删除操作
        int deletedCount = getBaseMapper().delete(wrapper);

        if(deletedCount==0){
            throw new BusinessException(OverBookException.UPDATE_DATA_ERROR);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public Map<String,Long> saveOverBook(OverBookSaveDTO overBookSaveDTO) throws IOException {
        Map<String,Long> resultMap = new HashMap<>();
        log.info("【H5超售事故单创建-saveOverBook】前端传入参数，事故单信息:[{}]", JSON.toJSONString(overBookSaveDTO));


        //设置事故单状态 and 赔偿单状态
        setState(overBookSaveDTO);
        int saveType = overBookSaveDTO.getSaveType();
        OverBookAccidentSaveDTO accident = overBookSaveDTO.getAccidentSaveDTO();
        OverBookPaxSaveDTO paxVO = overBookSaveDTO.getPaxVO();
        OverBookCompensationSaveDTO compensationSaveDTO = overBookSaveDTO.getCompensationSaveDTO();
        //旅客信息查询
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setPaxId(paxVO.getPaxId());
        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(queryDTO);
        if(ObjectUtils.isEmpty(passengerInfo)){
            throw new BusinessException(OverBookException.NOT_FOUND_PAX);
        }
        PassengerBasicInfoVO pax = passengerInfo.get(0);
        pax.setTelephone(paxVO.getTelephone());
        //保存事故单
        OverBookAccidentInfoDO accidentInfoDO = saveAccidentOrder(pax, accident);
        if(accidentInfoDO.getId()==null){
            throw new BusinessException(OverBookException.UPDATE_DATA_ERROR);
        }
        //新建保存赔偿单、赔偿单旅客、赔偿单航班
        if(saveType==2 || saveType==3){
            compensationSaveDTO.setAccidentId(accidentInfoDO.getId());
            Long orderId = saveOverBookCompensationOrder(compensationSaveDTO);
            resultMap.put("orderId",orderId);
        }
        resultMap.put("accidentId",accidentInfoDO.getId());
        return resultMap;
    }

    @Override
    public String verifyAlikeOrder(VerifyAlikeOrderDTO dto){
        String checkType = dto.getCheckType();
        if("1".equals(checkType)){
            int i = getBaseMapper().verifyAlikeAccidentOrder(dto);
            if(i>0){
                //  //此外还需判断该事故单下旅客（相同姓名，证件号，证件类型）是否已经存在相同日期相同航班相同事故类型的事故单（不包含作废状态事故单），
                //        // 如果有，则弹框提示：该购票证件号在系统中已有 x 单航班超售事故单，是否继续创建
//                throw new BusinessException(OverBookException.TIPS_ALIKE_ACCIDENT,i);
                return  "该购票证件号在系统中已有 "+i+" 单航班超售事故单，是否继续创建";
            }
        }
        if("2".equals(checkType)){
        // 旅客 当前航班下相同补偿单类型下是否存在该补偿单中存在旅客的其他补偿单（包含全部状态），
        // 若存在，则先进行弹框提示：该购票证件号在系统中已有 x 单航班超售补偿单，是否继续创建
            int i = getBaseMapper().verifyAlikeOrder(dto);
            if(i>0){
                return "该购票证件号在系统中已有 "+i+" 单航班超售补偿单，是否继续创建";
            }
        }
        return null;
    }

    /**
     * @title setState
     * @description 保存类型设置事故单状态||补偿单状态
     * <AUTHOR>
     * @date 2024/5/31 16:20
     * @param overBookSaveDTO
     * @return void
     */
    private void setState(OverBookSaveDTO overBookSaveDTO){
        int saveType = overBookSaveDTO.getSaveType();
        //0保存草稿 1.只创建事故单 2创建事故单及生成补偿单草稿 3创建事故单及补偿单
        switch (saveType) {
            case 0:
                overBookSaveDTO.getAccidentSaveDTO().setAccidentStatus(AccidentStatusEnum.DRAFT.getValue());
                break;
            case 1:
                overBookSaveDTO.getAccidentSaveDTO().setAccidentStatus(AccidentStatusEnum.TODO.getValue());
                break;
            case 2:
                overBookSaveDTO.getAccidentSaveDTO().setAccidentStatus(AccidentStatusEnum.PROCESS.getValue());
                overBookSaveDTO.getCompensationSaveDTO().setStatus(CompensateStatusEnum.DRAFT.getKey());
                break;
            case 3:
                overBookSaveDTO.getAccidentSaveDTO().setAccidentStatus(AccidentStatusEnum.PROCESS.getValue());
                overBookSaveDTO.getCompensationSaveDTO().setStatus(CompensateStatusEnum.DRAFT.getKey());//另外调提交接口所以这里就不用改为审核中
                //校验金额
                CompensationAmountCheckDTO checkDTO = new CompensationAmountCheckDTO();
                BeanUtil.copyProperties(overBookSaveDTO.getCompensationSaveDTO(),checkDTO);
                checkDTO.setAccidentNo(overBookSaveDTO.getAccidentSaveDTO().getAccidentNo());
                checkDTO.setType(overBookSaveDTO.getAccidentSaveDTO().getType());
                checkDTO.setFullEconomyFare(overBookSaveDTO.getAccidentSaveDTO().getFullEconomyFare());
                checkAmount(checkDTO);
                break;
        }

    }




    private OverBookAccidentInfoDO saveAccidentOrder(PassengerBasicInfoVO paxVO ,OverBookAccidentSaveDTO accident) throws IOException {

        //0保存草稿 1.只创建事故单 2创建事故单及生成补偿单草稿 3创建事故单及补偿单
        OverBookAccidentInfoDO accidentInfoDO = new OverBookAccidentInfoDO();
        BeanUtil.copyProperties(accident,accidentInfoDO);
        BeanUtil.copyProperties(paxVO,accidentInfoDO);
        accidentInfoDO.setAccidentSource("1");
        accidentInfoDO.setBelongAirline(paxVO.getFlightNo().substring(0,2));
        accidentInfoDO.setPaxStatus(paxVO.getCheckStatus());
        if(StringUtils.isNotEmpty(paxVO.getTktDate())){
            // 创建一个DateTimeFormatter实例，匹配输入字符串的格式
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            // 使用parse方法将字符串转换为LocalDateTime
            LocalDateTime localDateTime = LocalDateTime.parse(paxVO.getTktDate(), formatter);
            accidentInfoDO.setTktIssueDate(localDateTime);
        }
        if(accident.getAccidentId()!=null){
            accidentInfoDO.setId(accident.getAccidentId());
            this.delOverBook(accident.getAccidentId());
        }

        //TODO 生成事故单号 航司二字码+FOA+年+月+日+五位数随机数
        String accidentNo = CompensationOrderNoUtils.getOrderNumber(UserContext.getCurrentUser().getTenantCode(), CompensationOrderNoUtils.overBookAccidentCode);
        accidentInfoDO.setAccidentNo(StringUtils.isNotEmpty(accident.getAccidentNo())?accident.getAccidentNo():accidentNo);

        //上传图片
        if(ObjectUtils.isNotEmpty(accident.getImgUrl())){
            String overBookImg = "";
            List<MultipartFile> result = new ArrayList<>();
            accident.getImgUrl().forEach(e->{
                MultipartFile f = BASE64DecodedMultipartFile.base64ToMultipart(e);
                result.add(f);
            });
            List<FileAttachment> imgFiles = fileAttachmentService.uploadFile(result,bucketName,null);
            for (int i = 0; i < imgFiles.size(); i++) {
                if(i==0){
                    overBookImg = overBookImg+imgFiles.get(i).getId();
                }else {
                    overBookImg = overBookImg+","+imgFiles.get(i).getId();
                }
            }
            accidentInfoDO.setImgUrl(overBookImg);
        }
        accidentInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        accidentInfoDO.setCreatedTime(LocalDateTime.now());
        accidentInfoDO.setUpdatedBy(ApsUserUtils.getCreatedBy());
        accidentInfoDO.setUpdatedTime(LocalDateTime.now());
        AirlineBusinessPrivilege businessPrivilege = businessPrivilegeMapper.getBusinessPrivilegeBetweenCustomer(accidentInfoDO.getBelongAirline(), UserContext.getCurrentUser().getTenantCode());
        accidentInfoDO.setBelongAirlineAbbr(businessPrivilege.getGrantorName());
        log.info("【H5超售事故单创建-saveOverBook】前端传入参数，旅客姓名:[{}],保存事故单信息[{}]", paxVO.getPaxName(),JSON.toJSONString(accidentInfoDO));
        //保存事故单信息
        this.save(accidentInfoDO);

        //推送业务数据到协同中心
        if (!AccidentStatusEnum.DRAFT.getValue().equals(accidentInfoDO.getAccidentStatus())){
            businessDataPushHandler.dataStore(accidentInfoDO.getId(), BusinessDataSyncConstant.BUSINESS_OVER_BOOKING, BusinessDataSyncConstant.DATA_TYPE_ACCIDENT);
        }

        return accidentInfoDO;
    }


    /**
     * @title saveOverBookCompensationOrder
     * @description 2创建事故单及生成补偿单草稿 3创建事故单及补偿单  补偿单编辑草稿保存
     * <AUTHOR>
     * @date 2024/6/6 16:04
     * @param compensationSaveDTO
     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveOverBookCompensationOrder(OverBookCompensationSaveDTO compensationSaveDTO){
        log.info("【H5超售事故补偿单创建-开始执行保存】事故单号:[{}],补偿单信息:[{}]",compensationSaveDTO.getAccidentId()
                , JSON.toJSONString(compensationSaveDTO));

        //TODO 1.建补偿单 新增判断《当前服务航站，是否已经授权给其他【机场端】，如果已经授权，不能进行创建补偿单。只能由【机场端】创建补偿端》


        OverBookAccidentInfoDO accidentInfoDO = this.getBaseMapper().selectById(compensationSaveDTO.getAccidentId());
        if(accidentInfoDO == null){
            throw new BusinessException(OverBookException.NOT_FIND_DATA_ERROR);
        }

        final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern(DateUtils.PTN_YMD_HMS);
        //金额校验
        CompensationAmountCheckDTO checkDTO = new CompensationAmountCheckDTO();
        BeanUtil.copyProperties(compensationSaveDTO,checkDTO);
        checkDTO.setAccidentNo(accidentInfoDO.getAccidentNo());
        checkDTO.setType(accidentInfoDO.getType());
        checkDTO.setFullEconomyFare(accidentInfoDO.getFullEconomyFare());
        checkAmount(checkDTO);

        String belongAirline = UserContext.getCurrentUser().getTenantCode();
        /*航司二字码+FOC+四位年+两位月+两位日+五位随机数*/
        String orderNo = CompensationOrderNoUtils.getOrderNumber(belongAirline, CompensationOrderNoUtils.overBookCompensationCode);
        CompensationOrderInfoDO compensationOrderInfoDO = new CompensationOrderInfoDO();
        compensationOrderInfoDO.setId(ObjectUtils.isNotEmpty(compensationSaveDTO.getOrderId())?compensationSaveDTO.getOrderId():null);
        compensationOrderInfoDO.setOrderNo(StringUtils.isNotEmpty(compensationSaveDTO.getOrderNo())?compensationSaveDTO.getOrderNo():orderNo);
        compensationOrderInfoDO.setAccidentId(accidentInfoDO.getId());
        compensationOrderInfoDO.setAccidentNo(accidentInfoDO.getAccidentNo());
        compensationOrderInfoDO.setCompensateType(compensationSaveDTO.getCompensateType());
        compensationOrderInfoDO.setCompensateSubType(compensationSaveDTO.getCompensateSubType());
        compensationOrderInfoDO.setSumMoney(new BigDecimal(compensationSaveDTO.getSumMoney()));
        compensationOrderInfoDO.setStatus(CompensateStatusEnum.DRAFT.getKey());
        compensationOrderInfoDO.setFlightId(accidentInfoDO.getFlightId());
        compensationOrderInfoDO.setRemark(compensationSaveDTO.getRemark());
        compensationOrderInfoDO.setFlightNo(accidentInfoDO.getFlightNo());
        compensationOrderInfoDO.setFlightDate(accidentInfoDO.getFlightDate());
        compensationOrderInfoDO.setAccidentType(CompensationAccidentTypeEnum.OVERBOOKING.getKey());//超售
        compensationOrderInfoDO.setAccidentSubType(accidentInfoDO.getType());
        compensationOrderInfoDO.setServiceCity(compensationSaveDTO.getServiceCity());
        compensationOrderInfoDO.setChoiceSegment(accidentInfoDO.getSegment());
        compensationOrderInfoDO.setChoiceSegmentCh(accidentInfoDO.getSegmentCh());
        compensationOrderInfoDO.setEnsureType("0");
        compensationOrderInfoDO.setCompensateStandard(compensationSaveDTO.getRuleType());
        compensationOrderInfoDO.setStandardSelectType(compensationSaveDTO.getSelectType());
        compensationOrderInfoDO.setBelongAirline(accidentInfoDO.getBelongAirline());
        compensationOrderInfoDO.setBelongAirlineAbbr(accidentInfoDO.getBelongAirlineAbbr());
        compensationOrderInfoDO.setSource(accidentInfoDO.getAccidentSource());
        compensationOrderInfoDO.setSourceTenantCode(UserContext.getCurrentUser().getTenantCode());
        compensationOrderInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
        compensationOrderInfoDO.setCreatedTime(LocalDateTime.now());
        compensationOrderInfoService.saveOrUpdate(compensationOrderInfoDO);
        log.info("【H5超售事故补偿单创建-保存更新补偿单信息】事故单号:[{}],补偿单号:[{}],补偿信息[{}]",compensationOrderInfoDO.getAccidentNo(),compensationOrderInfoDO.getOrderNo()
                ,JSON.toJSONString(compensationOrderInfoDO));

        if(ObjectUtils.isNotEmpty(compensationSaveDTO.getOrderId())){
            // 删除航班信息和旅客信息
            List<CompensationFlightInfoDO> flightInfoDOS = compensationFlightInfoService.lambdaQuery().eq(CompensationFlightInfoDO::getOrderId, compensationSaveDTO.getOrderId()).list();
            if (flightInfoDOS != null && flightInfoDOS.size() > 0){
                compensationFlightInfoService.removeByIds(flightInfoDOS.stream().map(CompensationFlightInfoDO::getId).collect(Collectors.toList()));
            }
            List<CompensationPaxInfoDO> paxInfoDOS = compensationPaxInfoService.lambdaQuery().eq(CompensationPaxInfoDO::getOrderId, compensationSaveDTO.getOrderId()).list();
            if (paxInfoDOS != null && paxInfoDOS.size() > 0){
                compensationPaxInfoService.removeByIds(paxInfoDOS.stream().map(CompensationPaxInfoDO::getId).collect(Collectors.toList()));
            }

        }
        //保存旅客信息
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        queryDTO.setPaxId(accidentInfoDO.getPaxId());

        List<PassengerBasicInfoVO> passengerInfo = compensationBasicDataService.getPassengerInfo(queryDTO);
        CompensationPaxInfoDO compensationPaxInfoDO = new CompensationPaxInfoDO();
        if (passengerInfo != null && passengerInfo.size() > 0) {
            compensationPaxInfoDO.setPaxId(passengerInfo.get(0).getPaxId());
            compensationPaxInfoDO.setPaxStatus(passengerInfo.get(0).getCheckStatus());
            compensationPaxInfoDO.setPaxName(passengerInfo.get(0).getPaxName());
            compensationPaxInfoDO.setIdNo(passengerInfo.get(0).getIdNo());
            compensationPaxInfoDO.setIdType(passengerInfo.get(0).getIdType());
            compensationPaxInfoDO.setSex(passengerInfo.get(0).getSex());
            compensationPaxInfoDO.setTelephone(passengerInfo.get(0).getTelephone());
            compensationPaxInfoDO.setSegment(passengerInfo.get(0).getSegment());
            compensationPaxInfoDO.setSegmentCh(passengerInfo.get(0).getSegmentCh());
            compensationPaxInfoDO.setOrgCityAirp(passengerInfo.get(0).getOrgCityAirp());
            compensationPaxInfoDO.setDstCityAirp(passengerInfo.get(0).getDstCityAirp());
            compensationPaxInfoDO.setIsCancel(passengerInfo.get(0).getIsCancel());
            compensationPaxInfoDO.setMainClass(passengerInfo.get(0).getMainClass());
            compensationPaxInfoDO.setSubClass(passengerInfo.get(0).getSubClass());
            compensationPaxInfoDO.setTktNo(passengerInfo.get(0).getTktNo());
            compensationPaxInfoDO.setWithBaby(passengerInfo.get(0).getWithBaby());
            compensationPaxInfoDO.setBabyPaxName(passengerInfo.get(0).getBabyName());
            compensationPaxInfoDO.setTktIssueDate(LocalDateTime.parse(passengerInfo.get(0).getTktDate(), dateTimeFormatter));
            compensationPaxInfoDO.setCurrentAmount(new BigDecimal(compensationSaveDTO.getSumMoney()));
            compensationPaxInfoDO.setOrderId(compensationOrderInfoDO.getId());
            compensationPaxInfoDO.setIsChild(passengerInfo.get(0).getIsChild());
            compensationPaxInfoDO.setSwitchOff("0");
            compensationPaxInfoDO.setPnr(passengerInfo.get(0).getPnr());
            compensationPaxInfoDO.setCreatedBy( ApsUserUtils.getCreatedBy());
            compensationPaxInfoDO.setCreatedTime(LocalDateTime.now());
            log.info("【H5超售事故补偿单创建-新增或更新补偿单-保存补偿旅客信息】事故单号:[{}],补偿单号:[{}],旅客信息[{}]",compensationOrderInfoDO.getAccidentNo(),compensationOrderInfoDO.getOrderNo()
                    ,JSON.toJSONString(compensationPaxInfoDO));
            compensationPaxInfoService.save(compensationPaxInfoDO);
        }


        //保存赔付单航班信息
        FlightBaseQueryDTO flightQuery = new FlightBaseQueryDTO();
        flightQuery.setFlightId(compensationOrderInfoDO.getFlightId());
        List<FlightBasicnfoVO> flightBasicInfo = compensationBasicDataService.getFlightBasicInfo(flightQuery);
        if (flightBasicInfo != null && flightBasicInfo.size() > 0){
            CompensationFlightInfoDO flightInfoDO = new CompensationFlightInfoDO();
            flightInfoDO.setOrderId(compensationOrderInfoDO.getId());
            flightInfoDO.setFlightDate(compensationOrderInfoDO.getFlightDate());
            flightInfoDO.setFlightNo(compensationOrderInfoDO.getFlightNo());
            flightInfoDO.setFlightId(compensationOrderInfoDO.getFlightId());
            flightInfoDO.setSegment(accidentInfoDO.getSegment());
            flightInfoDO.setSegmentCh(accidentInfoDO.getSegmentCh());
            flightInfoDO.setStd(formatEtdStd(accidentInfoDO.getStd()));
            flightInfoDO.setEtd(formatEtdStd(flightBasicInfo.get(0).getEtd()));
            flightInfoDO.setAcType(flightBasicInfo.get(0).getAcType());
            flightInfoDO.setPlaneCode(flightBasicInfo.get(0).getAcReg());
            flightInfoDO.setSta(formatEtdStd(flightBasicInfo.get(0).getSta()));
            flightInfoDO.setEta(formatEtdStd(flightBasicInfo.get(0).getEta()));
            flightInfoDO.setCreatedBy(ApsUserUtils.getCreatedBy());
            flightInfoDO.setCreatedTime(LocalDateTime.now());
            log.info("【H5超售事故补偿单创建-保存补偿航班信息】事故单号:[{}],补偿单号:[{}],航班信息[{}]",compensationOrderInfoDO.getAccidentNo(),compensationOrderInfoDO.getOrderNo()
                    ,JSON.toJSONString(flightInfoDO));
            compensationFlightInfoService.save(flightInfoDO);
        }

        log.info("【H5超售事故补偿单创建-保存成功】事故单号:[{}],补偿单号:[{}]，补偿单id[{}],补偿旅客表主键[{}]",compensationOrderInfoDO.getAccidentNo(),compensationOrderInfoDO.getOrderNo()
                ,compensationOrderInfoDO.getId(),compensationPaxInfoDO.getId());

        //更新事故单状态-处理中
        List<String>  accidentStatus = new ArrayList<>();
        accidentStatus.add(AccidentStatusEnum.TODO.getValue());
        accidentStatus.add(AccidentStatusEnum.CASE_CLOSED.getValue());
        LambdaUpdateWrapper<OverBookAccidentInfoDO> updateWrapper = Wrappers.lambdaUpdate();
        updateWrapper.set(OverBookAccidentInfoDO::getAccidentStatus, AccidentStatusEnum.PROCESS.getValue());
        updateWrapper.eq(OverBookAccidentInfoDO::getId, compensationOrderInfoDO.getAccidentId());
        updateWrapper.in(OverBookAccidentInfoDO::getAccidentStatus, accidentStatus);

        this.update(updateWrapper);


        //推送业务数据到协同中心
        if (!"0".equals(compensationOrderInfoDO.getStatus())){
            businessDataPushHandler.dataStore(compensationOrderInfoDO.getId(), BusinessDataSyncConstant.BUSINESS_OVER_BOOKING, BusinessDataSyncConstant.DATA_TYPE_COMPENSATION);
        }

        return compensationOrderInfoDO.getId();
    }

    private void checkAmount(CompensationAmountCheckDTO checkDTO){
        //校验金额
        boolean flag = checkCompensationAmount(checkDTO);
        if(!flag){
            throw new BusinessException(OverBookException.CHECK_AMOUNT_ERROR);
        }
    }

    private void checkAmount(OverBookAccidentInfoDO accidentInfoDO, OverBookCompensationSaveDTO compensationSaveDTO){
        //校验金额
        CompensationAmountCheckDTO checkDTO = new CompensationAmountCheckDTO();
        BeanUtil.copyProperties(compensationSaveDTO,checkDTO);
        checkDTO.setAccidentNo(accidentInfoDO.getAccidentNo());
        checkDTO.setType(accidentInfoDO.getType());
        checkDTO.setFullEconomyFare(accidentInfoDO.getFullEconomyFare());
        boolean flag = checkCompensationAmount(checkDTO);
        if(!flag){
            throw new BusinessException(OverBookException.CHECK_AMOUNT_ERROR);
        }
    }


    /** 
     * @title checkCompensationAmount
     * @description Check the compensation amount
     * <AUTHOR>
     * @date 2024/6/5 13:42
     * @param checkDTO
     * @return boolean
     */
    private boolean checkCompensationAmount(CompensationAmountCheckDTO checkDTO){
        //标准金额
        if("1".equals(checkDTO.getRuleType())){
            double sumMoney = Double.parseDouble(checkDTO.getSumMoney());
            double computeSumMoney=0;
            List<OverBookConfigVO> configInfo = null;
            //改签
            if("1".equals(checkDTO.getType())){
                //计算改签时长
                String overBookTime = calculateTimeDifference(checkDTO.getStd(),checkDTO.getOverBookStd());
                //根据延误时长-获取对应符合条件的规则。
                //根据选择类型计算金额
                configInfo = overBookConfigService.getConfigInfo(overBookTime.replace(":","."), checkDTO.getType());

            }else{//退票
                configInfo = overBookConfigService.getConfigInfo(null, checkDTO.getType());
            }
            if (ObjectUtils.isEmpty(configInfo)){
                throw new BusinessException(OverBookException.SELECTED_RULE_RULE_NOT_FIND);
            }
            //补偿选择类型 1百分比 2固定金额
            String selectType = checkDTO.getSelectType();
            OverBookConfigVO configVO = configInfo.get(0);
            log.info("【H5超售事故补偿单创建-校验补偿金额】事故单号:[{}],补偿单号:[{}],超售类型（1改签，2退票）:[{}],匹配对应规则:[{}]",checkDTO.getAccidentNo(),checkDTO.getOrderNo(),
                    checkDTO.getType(),JSON.toJSONString(configVO));
            if("1".equals(selectType)){
//                舱位价格 × 百分比 / 100。
                double fullPrice =  StringUtils.isNotEmpty(checkDTO.getFullEconomyFare())?Double.parseDouble(checkDTO.getFullEconomyFare()):0;
                int fullPriceRatio =  StringUtils.isNotEmpty(configVO.getFullPrice())?Integer.parseInt(configVO.getFullPrice()):0;
                computeSumMoney = calculateAmount(fullPrice,fullPriceRatio);

            }else if("2".equals(selectType)){
                computeSumMoney = Double.parseDouble(configVO.getFixedMoney());
            }
            log.info("【H5超售事故补偿单创建-校验补偿金额】事故单号:[{}],补偿单号:[{}],后台计算金额:[{}],前端计算金额:[{}]",checkDTO.getAccidentNo(),checkDTO.getOrderNo(),
                    computeSumMoney,sumMoney);
            return sumMoney==computeSumMoney;
        }
        return true;
    }
    public double calculateAmount(double cabinPrice, int percentage) {
        // 确保百分比转换为小数形式 200*100
        double ratio = (double) percentage / 100.0;
        // 计算金额
        double amount = cabinPrice * ratio;
        return amount;
    }

    public String formatEtdStd(String td) {
        String date = td;
        if(ObjectUtils.isNotEmpty(td)){
            date = DateUtils.formatDate(DateUtils.parseDate(td), "HH:mm");
        }
        return date;
    }


    /**
     * @title calculateTimeDifference
     * @description 计算时差
     * <AUTHOR>
     * @date 2024/6/3 14:13
     * @param dateTime1Str
     * @param dateTime2Str
     * @return java.lang.String
     */
    public String calculateTimeDifference(String dateTime1Str, String dateTime2Str) {
        if(StringUtils.isNotEmpty(dateTime1Str) && StringUtils.isNotEmpty(dateTime2Str) ){
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime dateTime1 = LocalDateTime.parse(dateTime1Str, formatter);
            LocalDateTime dateTime2 = LocalDateTime.parse(dateTime2Str, formatter);

            Duration duration = Duration.between(dateTime1, dateTime2);
            long hours = duration.toHours();
            long minutes = duration.minusHours(hours).toMinutes();
            return hours + ":" + (String.valueOf(minutes).length()==1?"0"+minutes:minutes) ;
        }
        return "0";


    }

    @Override
    public boolean updAccidentStatusBatch(List<String> accidentNo, String accidentStatus) {
        return this.getBaseMapper().updAccidentStatusBatch(accidentNo,accidentStatus);
    }
}
