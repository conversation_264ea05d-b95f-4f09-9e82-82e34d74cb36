package com.swcares.aps.compensation.impl.dataconfig.controller;

import com.swcares.aps.compensation.impl.dataconfig.service.CompensationConfigService;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;

/**
 * ClassName： DamageTypeController <br>
 * Description： <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/7 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation/config")
@Api(tags = "赔付系统业务配置")
@ApiVersion(value = "赔付系统业务配置 v1.0")
public class CompensationConfigController extends BaseController {

	@Autowired
	private CompensationConfigService compensationConfigService;

	@GetMapping("/getCompensationConfigByType")
	public BaseResult<Map<String, List<CompensationConfigVO>>> getCompensationConfigByType(String type){
		Map<String, List<CompensationConfigVO>> compensationConfig = compensationConfigService.getCompensationConfigByType(type);
		return ok(compensationConfig);
	}

	@PostMapping("/getByTypes")
	public BaseResult<List<CompensationConfigVO>> getByTypes(@RequestBody List<String> types){

		List<CompensationConfigVO> compensationConfigVOS = compensationConfigService.getCompensationConfigByTypes(types);
		return ok(compensationConfigVOS);
	}
}
