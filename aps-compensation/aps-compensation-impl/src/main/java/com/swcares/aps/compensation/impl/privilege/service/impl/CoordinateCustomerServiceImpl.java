package com.swcares.aps.compensation.impl.privilege.service.impl;

import com.swcares.aps.compensation.impl.privilege.config.CoordinateConfig;
import com.swcares.aps.compensation.impl.privilege.service.CooperativeCustomerService;
import com.swcares.aps.compensation.impl.privilege.service.CoordinateCustomerService;
import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import com.swcares.aps.cpe.coordinate.model.enums.CustomerCategoryEnum;
import com.swcares.aps.usercenter.remote.api.tenant.TenantInfoApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>
 * @Classname CoordinateCustomerServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/27 14:41
 * @Version 1.0
 */
@Service
public class CoordinateCustomerServiceImpl implements CoordinateCustomerService {

    @Autowired
    private TenantInfoApi tenantInfoApi;

    @Autowired
    private CooperativeCustomerService cooperativeCustomerService;


    @Autowired
    CoordinateConfig coordinateConfig;
    @Override
    public void saveOrUpdate(CustomerDTO customerDTO) {


        if (isAirLine(customerDTO)){
            //如果是来的数据和当前的平台的租户客户类型相同，则创建租户
            tenantInfoApi.coordinateCustomer(customerDTO);
        } else {
            //否则创建coorperativeCustomer
            cooperativeCustomerService.saveOrUpdate(customerDTO);
        }
    }

    private boolean isAirLine(CustomerDTO customerDTO){
        return CustomerCategoryEnum.AIRLINE.getCode().equalsIgnoreCase(customerDTO.getCustomerCategory().getCode());
    }
}
