package com.swcares.aps.compensation.impl.dataconfig.service;

import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.impl.replace.service.ReplaceRejectReasonService <br>
 * Description： 代领拒绝原由服务类 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022-01-12 <br>
 * @version v1.0 <br>
 */
public interface ReplaceRejectReasonService {

    /**
     * Title：pages <br>
     * Description：分页获取当前代人领取拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022-01-12 <br>
     *
     * @return com.baomidou.mybatisplus.core.metadata.IPage<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO> <br>
     */
    Map<String, RuleManageDTO> pages();

    /**
     * Title：getAllRejectReason <br>
     * Description：获取当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022-01-12 <br>
     * @return java.util.List<com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO> <br>
     */
    List<ReplaceRejectReasonDTO> getAllRejectReason();

    /**
     * Title：delete <br>
     * Description：删除当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022-01-12 <br>
     * @return <br>
     * @param boolean deleteDTO
     */
    boolean delete(ReplaceConfigDeleteDTO deleteDTO);

    /**
     * Title：update <br>
     * Description：修改当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022-01-12 <br>
     * @return boolean <br>
     */
    boolean update(ReplaceRejectReasonDTO rejectReasonDTO);

    /**
     * Title：create <br>
     * Description：创建当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022-01-12 <br>
     * @return boolean <br>
     */
    boolean create(ReplaceRejectReasonDTO rejectReasonDTO);
}
