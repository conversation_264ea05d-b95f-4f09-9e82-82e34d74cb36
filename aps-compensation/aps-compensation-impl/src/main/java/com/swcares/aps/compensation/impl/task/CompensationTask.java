package com.swcares.aps.compensation.impl.task;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.baggage.accident.service.BaggageAccidentService;
import com.swcares.aps.compensation.impl.baggage.luggage.enums.LuggageConsumptionReasonEnum;
import com.swcares.aps.compensation.impl.baggage.luggage.service.LuggageStockOpService;
import com.swcares.aps.compensation.impl.compensation.mapper.CompensationMaterialInfoMapper;
import com.swcares.aps.compensation.impl.compensation.service.CompensationMaterialInfoService;
import com.swcares.aps.compensation.impl.complaint.service.impl.ComplaintAccidentService;
import com.swcares.aps.compensation.impl.irregularflight.enums.AccidentStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.enums.CompensateStatusEnum;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationAuditInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationOrderInfoService;
import com.swcares.aps.compensation.impl.irregularflight.service.FlightAccidentInfoService;
import com.swcares.aps.compensation.impl.irregularflight.workflow.IrregularFlightWorkflowService;
import com.swcares.aps.compensation.impl.overbook.service.OverBookAccidentInfoService;
import com.swcares.aps.compensation.impl.util.TokenUtils;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensateStockDTO;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.entity.CompensationOrderInfoDO;
import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.component.workflow.dto.NodeExtVarsDTO;
import com.swcares.aps.component.workflow.dto.NodeNoticeDTO;
import com.swcares.aps.component.workflow.enums.AuditStatusEnum;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.remote.api.WorkflowApi;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.feign.FeignTokenContext;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.baseframe.utils.lang.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * @ClassName：CompensationTask
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： 傅欣荣
 * @Date： 2022/9/16 9:44
 * @version： v1.0
 */
@Slf4j
@Component
public class CompensationTask {
    @Autowired
    Redisson redisson;
    @Autowired
    CompensationOrderInfoService compensationOrderInfoService;
    @Autowired
    FlightAccidentInfoService flightAccidentInfoService;
    @Autowired
    BaggageAccidentService baggageAccidentService;
    @Autowired
    CompensationMaterialInfoService compensationMaterialInfoService;
    @Autowired
    CompensationMaterialInfoMapper compensationMaterialInfoMapper;
    @Autowired
    private LuggageStockOpService luggageStockOpService;
    @Autowired
    OverBookAccidentInfoService overBookAccidentInfoService;
    @Autowired
    ComplaintAccidentService complaintAccidentService;
    @Autowired
    WorkflowApi workflowApi;
    @Autowired
    IrregularFlightWorkflowService irregularFlightWorkflowService;
    @Autowired
    CompensationAuditInfoService compensationAuditInfoService;

    /**
     * "213"; 实物
     */
    private final static String COMPENSATE_TYPE_MATERIAL_DICT_ID ="3";
    /***
     * @title overdueTask
     * @description 赔偿单逾期状态定时任务。
     * <AUTHOR>
     * @date 2022/9/16 9:46

     * @return void
     */
    @Transactional(rollbackFor = Exception.class)
    @Scheduled(cron = "0 0/5 * * * ?")
    public void overdueTask(){
        RLock lock = redisson.getLock("OVERDUE_TASK_AL");
        try {
            boolean resLock = lock.tryLock(30, TimeUnit.SECONDS);
            if (resLock) {
                doOverdueTask();
            }
        }catch (Exception e){
        }finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }

    }

    public void doOverdueTask(){
        log.info("【aps-compensation-impl】查询赔偿单是否逾期定时任务-------》》"+ LocalDateTime.now());
        //1.查询逾期订单
        List<CompensationOrderInfoDO> overdueOrder = compensationOrderInfoService.findOverdueOrder();

        log.info("【aps-compensation-impl】开始更新【赔偿单逾期】数据---------->-size【{}】，数据【{}】",overdueOrder.size(), JSON.toJSONString(overdueOrder));
        if(CollectionUtils.isNotEmpty(overdueOrder)){
            List<Long> takeEffectList = new ArrayList<>();
            List<Long> passList = new ArrayList<>();
            overdueOrder.forEach(a->{
                if(CompensateStatusEnum.TAKE_EFFECT.getKey().equals(a.getStatus())){
                    takeEffectList.add(a.getId());
                }else if(CompensateStatusEnum.AUDIT_PASS.getKey().equals(a.getStatus())){
                    passList.add(a.getId());
                }
            });

            //更新赔偿单为逾期
            log.info("【aps-compensation-impl】开始更新【赔偿单逾期】数据---------->-size【{}】，单号id:生效【{}】，通过【{}】",overdueOrder.size(),takeEffectList.toString(),passList.toString());
            if(CollectionUtils.isNotEmpty(takeEffectList)){
                compensationOrderInfoService.updateOverdueOrder(takeEffectList,CompensateStatusEnum.OVERDUE.getKey());
            }

            if(CollectionUtils.isNotEmpty(passList)){
                compensationOrderInfoService.updateOverdueOrder(passList,CompensateStatusEnum.AUDIT_PASS_OVERDUE.getKey());
            }

            log.info("【aps-compensation-impl】开始处理符合更新库存的【赔偿单逾期】数据---------------start------>");
            //符合修改库存的订单[实物、异常行李、审核中、审核通过]
            for(CompensationOrderInfoDO dto:overdueOrder){
                if(COMPENSATE_TYPE_MATERIAL_DICT_ID.equals(dto.getCompensateType()) && ApplyConstants.ABNORMAL_LUGGAGE.equals(dto.getAccidentType())
                        && (CompensateStatusEnum.AUDIT_PASS.getKey().equals(dto.getStatus())
                        || CompensateStatusEnum.AUDIT_PROCESS.getKey().equals(dto.getStatus())
                        || CompensateStatusEnum.TAKE_EFFECT.getKey().equals(dto.getStatus()))){
                    //查询赔偿单实物信息 &
                    List<CompensationMaterialInfoDO> luggageMaterials =compensationMaterialInfoMapper.findByOrderId(dto.getId());
                    for(CompensationMaterialInfoDO compensationMaterialInfoDO:luggageMaterials){
                        LuggageCompensateStockDTO compensateStockDTO=new LuggageCompensateStockDTO();
                        compensateStockDTO.setAmount(compensationMaterialInfoDO.getAmount());
                        compensateStockDTO.setCompensationId(dto.getId());
                        compensateStockDTO.setCompensationNo(dto.getOrderNo());
                        compensateStockDTO.setLuggageId(compensationMaterialInfoDO.getMaterialId());
                        compensateStockDTO.setLuggageNo(compensationMaterialInfoDO.getMaterialNo());
                        compensateStockDTO.setReason(LuggageConsumptionReasonEnum.buildStatus(dto.getStatus()).getKey());
                        compensateStockDTO.setOperatorId(30293L);
                        compensateStockDTO.setTenantId(dto.getTenantId());
                        luggageStockOpService.increaseByCompensation(compensateStockDTO);
                    }
                }
            }
            log.info("【aps-compensation-impl】开始处理符合更新库存的【赔偿单逾期】数据---------------end------>");
        }



        log.info("【aps-compensation-impl】开始处理【事故单已结案】数据----------------start------>");
        //更新事故单已结案
        List<CompensationOrderInfoDO> closeAccidentOrder = compensationOrderInfoService.findCloseAccidentOrder();
        log.info("【aps-compensation-impl】开始处理【事故单已结案】数据---size【{}】",closeAccidentOrder.size());
        //两种类型：异常行李、不正常航班 分别是两个事故单表
        Map<String, List<CompensationOrderInfoDO>> collect1 = closeAccidentOrder.stream().collect(Collectors.groupingBy(d -> d.getAccidentType()));
        Set<String> accidentTypeSet = collect1.keySet();
        for(String accidentType:accidentTypeSet) {
            List<CompensationOrderInfoDO> compensationOrderInfoDOS = collect1.get(accidentType);
            log.info("【aps-compensation-impl】处理【事故单已结案】数据---事故类型【{}】,共计条数：【{}】",accidentType,compensationOrderInfoDOS.size());
            List<String> accidentNoList = compensationOrderInfoDOS.stream().map(e -> e.getAccidentNo()).collect(Collectors.toList());
            if(ApplyConstants.ABNORMAL_LUGGAGE.equals(accidentType)){
                log.info("【aps-compensation-impl】处理【事故单已结案】数据---异常行李-事故类型，更新事故单号【{}】",accidentNoList.toString());
                baggageAccidentService.updBaggageAccidentStatusBatch(accidentNoList, AccidentStatusEnum.CASE_CLOSED.getValue());
            }
            if(ApplyConstants.IRREGULAR_FLIGHT.equals(accidentType)){
                log.info("【aps-compensation-impl】处理【事故单已结案】数据---不正常航班-事故类型，更新事故单号【{}】",accidentNoList.toString());
                flightAccidentInfoService.updAccidentStatusBatch(accidentNoList, AccidentStatusEnum.CASE_CLOSED.getValue());
            }
            if(ApplyConstants.OVER_BOOKING.equals(accidentType)){
                log.info("【aps-compensation-impl】处理【事故单已结案】数据---航班超售-事故类型，更新事故单号【{}】",accidentNoList.toString());
                overBookAccidentInfoService.updAccidentStatusBatch(accidentNoList, AccidentStatusEnum.CASE_CLOSED.getValue());
            }
            if(ApplyConstants.COMPLAINT.equals(accidentType)){
                log.info("【aps-compensation-impl】处理【事故单已结案】数据---旅客投诉-事故类型，更新事故单号【{}】",accidentNoList.toString());
                complaintAccidentService.updAccidentStatusBatch(accidentNoList, AccidentStatusEnum.CASE_CLOSED.getValue());
            }
        }
        log.info("【aps-compensation-impl】开始处理【事故单已结案】数据----------------end------>");
    }
    @Scheduled(fixedDelay = 30000)
    public void workflowTask(){
        RLock lock = redisson.getLock("REFRESH_WORKFLOW_USER_INFO_TASK");
        try {
            boolean resLock = lock.tryLock(20, TimeUnit.SECONDS);
            if (resLock) {
                doWorkflowTask();
            }
        }catch (Exception e){
        }finally {
            if(lock!=null && lock.isHeldByCurrentThread()){
                lock.unlock();
            }
        }
    }

    public void doWorkflowTask(){
        log.info("【aps-compensation-impl】刷新补偿单审核人信息定时任务-------》》"+ LocalDateTime.now());

        List<CompensationOrderInfoDO> orderInfoDOS = compensationOrderInfoService.findNeedRefreshWorkflowUserInfoOrder();
        if(CollectionUtils.isEmpty(orderInfoDOS)){
            log.info("【aps-compensation-impl】刷新补偿单审核人信息定时任务,获取到任务为空-------》》");
            return;
        }
        for(CompensationOrderInfoDO order:orderInfoDOS){
            BaseQueryParamDTO queryParam = BaseQueryParamDTO.builder().businessKey(String.valueOf(order.getId())).build();
            try {
                CurrentTaskActivityVO currentTaskActivityVO = workflowApi.currentUserTask(queryParam).getData();
                List<CurrentTaskActivityDTO> currentTaskActivityDTOS = currentTaskActivityVO.getCurrentTaskActivityDTOS();
                log.info("【aps-compensation-impl】刷新补偿单审核人信息定时任务,获取流程当前啊任务信息，orderId:{},result:{}",order.getId(), JSONObject.toJSONString(currentTaskActivityDTOS));
                CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityDTOS.get(0);
                TenantHolder.setTenant(order.getTenantId());
                FeignTokenContext.setToken(TokenUtils.getToken());
                //【更新赔偿单状态 = 驳回】当前赔偿单状态=审核中 && 当前节点 = 发起节点 && 上一个节点审批状态 = 驳回
                if(WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey()) &&
                        AuditStatusEnum.REJECT.getKey().equals(currentTaskActivityVO.getPreOptionCode())){
                    //处理航司端与机场端，驳回到发起人节点，补偿单状态不一致问题。
                    log.info("【aps-compensation-impl】刷新补偿单审核人信息定时任务,上一个节点审批状态为驳回，当前节点为发起人节点，更新赔偿单状态为驳回，orderId:{},result:{}",order.getId(), JSONObject.toJSONString(currentTaskActivityDTOS));

                    NodeNoticeDTO nodeNoticeDTO = new NodeNoticeDTO();
                    nodeNoticeDTO.setOptionCode(currentTaskActivityVO.getPreOptionCode());
                    nodeNoticeDTO.setBusiKey(currentTaskActivityVO.getBusiKey());
                    nodeNoticeDTO.setModelCode(currentTaskActivityVO.getModelCode());
                    nodeNoticeDTO.setReviewer(currentTaskActivityVO.getPreAssignee());
                    nodeNoticeDTO.setNodeKey(currentTaskActivityDTO.getNodeKey());
                    nodeNoticeDTO.setNodeName(currentTaskActivityDTO.getNodeName());
                    nodeNoticeDTO.setTaskId(currentTaskActivityDTO.getTaskId());
                    nodeNoticeDTO.setAssignees(currentTaskActivityDTO.getAssignees());
                    irregularFlightWorkflowService.updCompensateStatusByAuditStatus(nodeNoticeDTO);
                    continue;
                }
                if(WorkflowUtils.isSyncProcessTask(currentTaskActivityDTO.getNodeKey())
                        || WorkflowUtils.isSubmitterTask(currentTaskActivityDTO.getNodeKey())){
                    continue;
                }
                if(currentTaskActivityDTO.getIsEndActivity()){
                    doRefreshEndInfo(currentTaskActivityVO);
                }else{
                    doRefreshWorkflowUserInfo(currentTaskActivityVO,order);
                }

            }catch (Exception e){
                log.error("【aps-compensation-impl】刷新补偿单审核人信息定时任务,调用流程引擎查询节点出错-------》》",e);
            }

        }
    }

    public void doRefreshEndInfo(CurrentTaskActivityVO currentTaskActivityVO) throws Exception{
        cn.hutool.json.JSONObject jsonObject = currentTaskActivityVO.getPreTaskExtVars();
        CurrentTaskActivityDTO taskActivityDTO = currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0);
        NodeExtVarsDTO extVarsDTO = JSONUtil.toBean(jsonObject, NodeExtVarsDTO.class);
        NodeNoticeDTO nodeNoticeDTO = ObjectUtils.copyBean(taskActivityDTO, NodeNoticeDTO.class);
        nodeNoticeDTO.setExtVars(extVarsDTO);
        nodeNoticeDTO.setBusiKey(currentTaskActivityVO.getBusiKey());
        /**当前审批状态传入下一个节点*/
        if (StringUtils.isNotEmpty(currentTaskActivityVO.getPreOptionCode())) {
            nodeNoticeDTO.setOptionCode(currentTaskActivityVO.getPreOptionCode());
        }
        irregularFlightWorkflowService.endWorkflow(nodeNoticeDTO);
    }

    public void doRefreshWorkflowUserInfo(CurrentTaskActivityVO currentTaskActivityVO,
                                          CompensationOrderInfoDO order) throws Exception{

        List<CurrentTaskActivityDTO> currentTaskActivityDTOS = currentTaskActivityVO.getCurrentTaskActivityDTOS();
        CurrentTaskActivityDTO currentTaskActivityDTO = currentTaskActivityDTOS.get(0);
        NodeNoticeDTO nodeNoticeDTO = ObjectUtils.copyBean(currentTaskActivityDTO, NodeNoticeDTO.class);
        nodeNoticeDTO.setBusiKey(currentTaskActivityVO.getBusiKey());
        /**当前审批状态传入下一个节点*/
        if (StringUtils.isNotEmpty(currentTaskActivityVO.getPreOptionCode())) {
            nodeNoticeDTO.setOptionCode(currentTaskActivityVO.getPreOptionCode());
        }
        if(CollectionUtils.isEmpty(nodeNoticeDTO.getAssignees())){
            log.error("【aps-compensation-impl】-刷新补偿单审核人信息定时任务，节点没有没有执行人,notice:{}", JSONUtil.toJsonStr(nodeNoticeDTO));
        }else{
            Map<String, Object> commonWorkflow = irregularFlightWorkflowService.commonWorkflow(nodeNoticeDTO);
            List<AuditorInfoDTO> orderAuditorList = (List<AuditorInfoDTO>)commonWorkflow.get("orderAuditorList");
            if(CollectionUtils.size(orderAuditorList)<=1){ return; }
            List<Long> collect = orderAuditorList.stream().map(t->t.getReviewerId()).collect(Collectors.toList());
            CompensationAuditInfoDTO auditInfoDTO=CompensationAuditInfoDTO.builder()
                    .orderId(order.getId())
                    .taskId(currentTaskActivityDTO.getTaskId())
                    .auditorIds(collect.toArray(new Long[]{})).build();
            compensationAuditInfoService.saveReviewer(auditInfoDTO);
        }
    }
}
