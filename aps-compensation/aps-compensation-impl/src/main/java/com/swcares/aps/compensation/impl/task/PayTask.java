package com.swcares.aps.compensation.impl.task;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.swcares.aps.compensation.impl.apply.service.ApplyOrderQuartzService;
import com.swcares.aps.compensation.impl.board.service.CapitalPoolService;
import com.swcares.aps.compensation.impl.datasync.constant.BusinessDataSyncConstant;
import com.swcares.aps.compensation.impl.datasync.enums.AccidentTypeBusinessCodeEnum;
import com.swcares.aps.compensation.impl.datasync.service.push.BusinessDataPushHandler;
import com.swcares.aps.compensation.model.apply.entity.PayRecordDO;
import com.swcares.aps.compensation.model.apply.vo.ApplyOrderPayVO;
import com.swcares.aps.compensation.model.board.dto.DeductionCapitalPoolDto;
import com.swcares.aps.component.pay.aop.AllowPaymentNode;
import com.swcares.aps.component.pay.pay.PayTypeEnum;
import com.swcares.aps.component.pay.pay.api.BasePayService;
import com.swcares.aps.component.pay.pay.bean.PayConstant;
import com.swcares.aps.component.pay.pay.bean.chinapay.ChinaPayConstant;
import com.swcares.aps.component.pay.pay.bean.chinapay.ChinaPayInqueryResult;
import com.swcares.aps.component.pay.pay.bean.chinapay.dto.ChinaPayContentV2Dto;
import com.swcares.aps.component.pay.pay.bean.chinapay.dto.ChinaPayV2Dto;
import com.swcares.aps.component.pay.pay.bean.chinapay.dto.ChinaPayV2Encryption;
import com.swcares.aps.component.pay.pay.bean.chinapay.vo.ChinaPayV2TransResponse;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxQueryResult;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxTransferOrder;
import com.swcares.aps.component.pay.pay.bean.wxpay.WxTransferResult;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaPayProcess;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaPayService;
import com.swcares.aps.component.pay.pay.service.chinapay.ChinaPayV2Service;
import com.swcares.aps.component.pay.pay.service.wx.WxPayProcess;
import com.swcares.aps.component.pay.pay.service.wx.WxPayService;
import com.swcares.aps.component.pay.pay.service.wx.WxTransactionType;
import com.swcares.aps.component.pay.pay.util.chinapay.StringUtil;
import com.swcares.aps.component.pay.utils.DateUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.tenant.TenantHolder;
import com.swcares.common.constants.UserCenterErrors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.Redisson;
import org.redisson.api.RLock;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.swcares.aps.component.pay.pay.bean.PayConstant.*;

/**
 * @ClassName：PayTask
 * @Description：服务补偿的支付定时任务，限制了ip才能访问
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/6/7 15:50
 * @version： v1.0
 */
@Component
@Slf4j
public class PayTask {

    @Value("${spring.profiles.active}")
    private String profiles;

    @Value("${pay.test-money-enable:false}")
    private boolean testMoneyEnable;

    @Resource(name = "wxPayProcess")
    private WxPayProcess wxPayProcess;

    @Resource(name = "chinaPayProcess")
    private ChinaPayProcess chinaPayProcess;

    @Autowired
    private ApplyOrderQuartzService applyOrderQuartzService;

    @Autowired
    private CapitalPoolService capitalPoolService;

    //支付备注
    private static final String PAY_REMARK = "补偿金";

    private static final String REDISSON_PRE_KEY="APPLY_ORDER_PAY_";

    //姓名校验出错、余额不足、无法给未实名用户付款、今日付款次数超过限制、没有该接口权限、用户账户收款异常、Openid错误
    String errorCode = "NAME_MISMATCH*NOTENOUGH*V2_ACCOUNT_SIMPLE_BAN*SEND_MONEY_LIMIT*NO_AUTH*PAYEE_ACCOUNT_ABNORMAL*MONEY_LIMIT";

    @Autowired
    private Redisson redisson;

    @Autowired
    BusinessDataPushHandler businessDataPushHandler;

    @Scheduled(cron = "0/30 * * * * ?")
    @AllowPaymentNode
    public void pay(){
        log.info("【aps-pay-impl】服务补偿定时任务开始---");
        RLock lock = redisson.getLock(REDISSON_PRE_KEY);
        if(lock.tryLock()){
            try{
                PayTask currentPayTask = (PayTask) AopContext.currentProxy();
                currentPayTask.doPay();
            }catch (Exception e){
                log.error("【服务补偿】服务补偿支付定时任务异常，信息：", e);
            }finally {
                lock.unlock();
            }
        }
        log.info("【服务补偿】服务补偿定时任务结束---");
    }

    public void doPay(){
        //查询0和3的状态。支付状态(0未支付,1支付成功,2支付失败3支付中)
        List<ApplyOrderPayVO> dataList = applyOrderQuartzService.findUnpaidOrderInfo();
        log.info("【服务补偿】------->>>航延补偿支付定时任务开始，查询待支付订单共计【{}】条", dataList.size());
        for (ApplyOrderPayVO orderInfo : dataList) {
            try {
                log.info("【服务补偿】------->>>航延补偿支付定时任务，开始处理订单-申领人：【{}】, 支付id订单号：【{}】, 对应申领单号：【{}】, 申领单支付状态(0未支付,1支付成功,2支付失败,3支付中)：【{}】", orderInfo.getApplyUser(), orderInfo.getRecordId(),
                        orderInfo.getApplyCode(), orderInfo.getPayStatus());
                doPay(orderInfo);
            }catch (Exception e){
                log.error("【服务补偿】------->>>航延补偿支付定时任务执行出错，处理订单-申领人：【{}】, 支付id订单号：【{}】, 对应申领单号：【{}】, 申领单支付状态(0未支付,1支付成功,2支付失败,3支付中)：【{}】", orderInfo.getApplyUser(), orderInfo.getRecordId(),
                        orderInfo.getApplyCode(), orderInfo.getPayStatus(),e);
            }
            log.info("【服务补偿】------->>>航延补偿支付定时任务，结束处理订单-申领人：【{}】, 支付id订单号：【{}】, 对应申领单号：【{}】, 申领单支付状态(0未支付,1支付成功,2支付失败,3支付中)：【{}】", orderInfo.getApplyUser(), orderInfo.getRecordId(),
                    orderInfo.getApplyCode(), orderInfo.getPayStatus());
        }
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void doPay(ApplyOrderPayVO orderInfo) {

        if(testMoneyEnable){
            log.info("【服务补偿】------->>>航延补偿支付定时任务，开启测试金额支付（只支付1元），当前环境【{}】， testMoneyEnable 【{}】", profiles, testMoneyEnable);
            orderInfo.setTransAmount(new BigDecimal("1"));
        }
        TenantHolder.setTenant(orderInfo.getTenantId());

        PayRecordDO aoTransRecord = new PayRecordDO();
        aoTransRecord.setId(orderInfo.getRecordId());
        aoTransRecord.setTransDate(orderInfo.getTransDate());
        aoTransRecord.setPayTypeApiVersion(orderInfo.getPayTypeApiVersion());
        //非银联的测试和开发环境mock
        if (profiles.equals("prod") || orderInfo.getCompensateSubType().equals(PayTypeEnum.UNIONPAY.getKey())) {
            /**真实支付逻辑*/
            //查询订单状态只有：未支付、支付中情况
            //未支付进入支付逻辑，其他状态进行第三方查询
            aoTransRecord.setPayStartTime(DateUtil.toLocalDateTime(new Date()));//支付发起时间

            if (PAY_STATUS_UNPAID.equals(orderInfo.getPayStatus())) {
                log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【添加COMPENSATION_PAY_RECORD】-支付id订单号：【{}】,对应申领单号：【{}】,状态：【{}】",
                        orderInfo.getRecordId(), orderInfo.getApplyCode(), aoTransRecord.getPayStatus());

                aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);//支付中
                aoTransRecord.setErrCode(null);
                aoTransRecord.setErrCodeDes(null);
                //此处事务未提交  有租户影响，这个可以删掉？？@TODO
                applyOrderQuartzService.updatePayRecordById(aoTransRecord);

                /* 第三方支付 */
                conductPayment(aoTransRecord, orderInfo);
            } else {
                /* 第三方查询 */
                confirmHasPaid(aoTransRecord, orderInfo);
            }
        }else{
            // 测试和开发环境mock数据
            mockPayRecordByTest(aoTransRecord, orderInfo);
        }

        /* 更新订单状态 */
        applyOrderQuartzService.changeStatus(aoTransRecord, orderInfo);

        //支付成功后扣资金池余额
        if(PAY_STATUS_PAID.equals(aoTransRecord.getPayStatus())){
            DeductionCapitalPoolDto deductionCapitalPoolDto = new DeductionCapitalPoolDto();
            try{
                deductionCapitalPoolDto.setDeductionAmount(orderInfo.getTransAmount());
                deductionCapitalPoolDto.setPayType(orderInfo.getCompensateSubType());
                deductionCapitalPoolDto.setApplyCode(orderInfo.getApplyCode());
                deductionCapitalPoolDto.setTenantId(orderInfo.getTenantId());
                deductionCapitalPoolDto.setBusinessType(orderInfo.getAccidentType());

                String mchId = PayTypeEnum.WECHAT.getKey().equals(orderInfo.getCompensateSubType()) ? ((WxPayService) Objects.requireNonNull(getPayService(orderInfo))).getWxPayConfigStorage().getMchId()
                        :  ((ChinaPayService) Objects.requireNonNull(getPayService(orderInfo))).merId;

                deductionCapitalPoolDto.setMchId(mchId);
                capitalPoolService.deduction(deductionCapitalPoolDto);
            }catch (Exception e){
                log.error("【服务补偿】------->>>航延补偿支付定时任务，扣减资金池失败，参数【{}】，资金池仅为记录，不做任何业务判断，该异常信息需要吃掉，异常信息", JSONUtil.toJsonStr(deductionCapitalPoolDto), e);
            }
        }

        try{
            //支付状态有变化，则需要将最新申领数据推送给航司。
            if(!aoTransRecord.getPayStatus().equals(orderInfo.getPayStatus())){
                //设置租户id
                businessDataPushHandler.dataStore(orderInfo.getId(), AccidentTypeBusinessCodeEnum.build(orderInfo.getAccidentType()).getValue(), BusinessDataSyncConstant.DATA_TYPE_APPLY);
            }
        }catch (Exception e){
            log.error("【服务补偿】------->>>航延补偿支付定时任务,存推送记录数据，申领单id【{}】，只保存一条，需要推送的记录，该异常信息需要吃掉，异常信息", orderInfo.getId(), e);
        }

    }

    private void mockPayRecordByTest(PayRecordDO aoTransRecord, ApplyOrderPayVO orderInfo){
        aoTransRecord.setId(orderInfo.getRecordId());
        aoTransRecord.setPayStartTime(DateUtil.toLocalDateTime(new Date()));//支付发起时间
        aoTransRecord.setPayStatus(PAY_STATUS_PAID);//已支付
        aoTransRecord.setPayReturnTime(DateUtil.toLocalDateTime(new Date()));
        aoTransRecord.setReturnSerialNo("4200002350202407024301151074");
        aoTransRecord.setErrCodeDes("开发测试环境mock数据:" + DateUtil.date());
    }

    /**
     * Title： confirmHasPaid<br>
     * Description： 查询订单支付记录 <br>
     * author：傅欣荣 <br>
     * date：2020/4/7 20:18 <br>
     *
     * @param
     * @return
     */
    private boolean confirmHasPaid(PayRecordDO aoTransRecord, ApplyOrderPayVO applyOrderInfo) {
        log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【查询】-支付id订单号：【{}】,对应申领单号：【{}】，支付类型：【{}】",
                applyOrderInfo.getRecordId(),applyOrderInfo.getApplyCode(), applyOrderInfo.getCompensateSubType());
        boolean flag = false;
        String payOrderNo = String.valueOf(applyOrderInfo.getRecordId());
        if (PayTypeEnum.WECHAT.getKey().equals(applyOrderInfo.getCompensateSubType())) {
            WxQueryResult wxQueryResult = (WxQueryResult) getPayService(applyOrderInfo).query(null, payOrderNo,
                    WxTransactionType.TRANSFERS_QUERY, WxQueryResult.class);
            aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);

            log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【查询】-支付id订单号：【{}】,对应申领单号：【{}】，支付类型：【{}】，查询反馈结果【{}】",
                    applyOrderInfo.getRecordId(),applyOrderInfo.getApplyCode(), applyOrderInfo.getCompensateSubType(), wxQueryResult.toString());

            //支付成功的判断逻辑： status == SUCCESS
            if (PAY_SUCCESS.equalsIgnoreCase(wxQueryResult.getStatus())) {
                aoTransRecord.setPayStatus(PAY_STATUS_PAID);
                flag = true;

                //支付中的判断逻辑： status == PAY_PROCESSING
            }else if(PAY_PROCESSING.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PAY_STATUS_INPROCESS);

                //支付失败的判断逻辑： status == FAILED
            }else if(PAY_FAILED.equalsIgnoreCase(wxQueryResult.getStatus())){
                aoTransRecord.setPayStatus(PAY_STATUS_FAIL);
            }

            encapsulatedByWeiXin(aoTransRecord, wxQueryResult);
        }
        //aoTransRecord.getTransDate() 是 v2版本银联查询必须的参数
        if (PayTypeEnum.UNIONPAY.getKey().equals(applyOrderInfo.getCompensateSubType()) && StringUtils.isNotEmpty(aoTransRecord.getTransDate())) {
            //默认支付中
            String payStatus = PayConstant.PAY_STATUS_INPROCESS;

            ChinaPayV2Dto chinaPayV2Dto = buildQueryParam(applyOrderInfo);

            ChinaPayV2TransResponse v2Response = ((ChinaPayV2Service)getPayService(applyOrderInfo)).query(chinaPayV2Dto, ChinaPayV2TransResponse.class);
            log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【查询】-支付id订单号：【{}】,对应申领单号：【{}】，支付类型：【{}】，查询反馈结果：【{}】",
                    applyOrderInfo.getRecordId(),applyOrderInfo.getApplyCode(), applyOrderInfo.getCompensateSubType(), v2Response.toString());

            // responseCode == 00  交易成功  && msgCode == 00000 处理成功（这个处理msgCode可以不要，待讨论）
            if(PayConstant.CHINAPAY_V2_SUCCESS_CODE.equals(v2Response.getResponseCode()) && PayConstant.CHINAPAY_V2_MSG_SUCCESS_CODE.equals(v2Response.getMsgCode())){
                payStatus = PayConstant.PAY_STATUS_PAID;
                flag = true;
            }else if(PayConstant.CHINAPAY_V2_FAIL_CODE.equals(v2Response.getResponseCode())){
                // responseCode == 10  交易结果失败
                payStatus = PayConstant.PAY_STATUS_FAIL;
            }else{
                payStatus = PayConstant.PAY_STATUS_INPROCESS;
            }

            aoTransRecord.setErrCodeDes(v2Response.getMsgDesc());
            aoTransRecord.setErrCode(v2Response.getMsgDesc());

            aoTransRecord.setReturnSerialNo(""); //银联V2新接口没有了
            aoTransRecord.setTransCode(v2Response.getResponseCode());
            aoTransRecord.setTransMsg(v2Response.getResponseMsg());
            aoTransRecord.setTransSubCode(v2Response.getMsgCode());
            aoTransRecord.setTransSubMsg(v2Response.getMsgDesc());

            aoTransRecord.setPayStatus(payStatus);
            aoTransRecord.setPayReturnTime(DateUtil.toLocalDateTime(DateUtil.date()));

        }

        return flag;
    }

    private ChinaPayV2Dto buildQueryParam(ApplyOrderPayVO order){

        ChinaPayContentV2Dto content = new ChinaPayContentV2Dto();
        content.setTransDate(order.getTransDate());
        //这儿需要改申领单生成这个的规则
        content.setOrderNo(String.valueOf(order.getRecordId()));

        ChinaPayV2Dto v2 = new ChinaPayV2Dto();
        v2.setContent(JSONUtil.toJsonStr(content));
        v2.setMethod("3");
        v2.setVersion(ChinaPayConstant.VERSION);
        v2.setChkValue(ChinaPayV2Service.builderSignData(v2));

        return v2;
    }


    /**
     * Title：conductPayment <br>
     * Description：进行支付处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 15:20 <br>
     *
     * @param aoTransRecord
     * @param applyOrderInfo
     * @return
     */
    private void conductPayment(PayRecordDO aoTransRecord, ApplyOrderPayVO applyOrderInfo) {
        log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【支付】-支付id订单号：【{}】,对应申领单号：【{}】，支付类型：【{}】",
                applyOrderInfo.getRecordId(),applyOrderInfo.getApplyCode(), applyOrderInfo.getCompensateSubType());
        if (PayTypeEnum.WECHAT.getKey().equals(applyOrderInfo.getCompensateSubType())) {
            payWeiXinHandler(aoTransRecord, applyOrderInfo);
        }

        if (PayTypeEnum.UNIONPAY.getKey().equals(applyOrderInfo.getCompensateSubType())) {
            Date payDate= Date.from(aoTransRecord.getPayStartTime().atZone(ZoneId.systemDefault()).toInstant());
            String merDate = DateUtils.parseDateToStr(payDate, DateUtils.YYYYMMDD);
            payChinaPayHandler(aoTransRecord, applyOrderInfo,merDate);
        }

    }

    private BasePayService getPayService(ApplyOrderPayVO applyOrderInfo){
        String tenantIdAndType = applyOrderInfo.getTenantId() + applyOrderInfo.getAccidentType();
        if(PayTypeEnum.WECHAT.getKey().equals(applyOrderInfo.getCompensateSubType())){
            WxPayService wxPayService = wxPayProcess.getWxPayService(tenantIdAndType);
            if(ObjectUtils.isEmpty(wxPayService)) {
                //租户不存在，获取不到支付服务，抛异常
                throw new BusinessException(UserCenterErrors.TENANT_NOT_EXIST);
            }
            return wxPayService;
        }else if(PayTypeEnum.UNIONPAY.getKey().equals(applyOrderInfo.getCompensateSubType())){
            BasePayService chinaPayService = chinaPayProcess.getChinaPayService(tenantIdAndType);
            if(ObjectUtils.isEmpty(chinaPayService)) {
                //租户不存在，获取不到支付服务，抛异常
                throw new BusinessException(UserCenterErrors.TENANT_NOT_EXIST);
            }
            return chinaPayService;
        }
        return null;
    }

    /**
     * Title：payWeiXinHandler <br>
     * Description： 微信支付逻辑处理 <br>
     * author：傅欣荣 <br>
     * date：2020/4/8 18:57 <br>
     *
     * @param
     * @return
     */
    private void payWeiXinHandler(PayRecordDO aoTransRecord, ApplyOrderPayVO applyOrderInfo) {
        WxTransferOrder tranOder = new WxTransferOrder();
        tranOder.setOutNo(String.valueOf(applyOrderInfo.getRecordId()));
        tranOder.setAmount(applyOrderInfo.getTransAmount());
        tranOder.setPayeeAccount(applyOrderInfo.getGetMoneyAccount());
        tranOder.setPayeeName(applyOrderInfo.getApplyUser());
        tranOder.setRemark(PAY_REMARK);
        tranOder.setTransactionType(WxTransactionType.TRANSFERS);
        WxTransferResult transferResult = (WxTransferResult) getPayService(applyOrderInfo).transfer(tranOder, WxTransferResult.class);
        log.info("【服务补偿】------->>>航延补偿支付定时任务，正在执行【支付】-支付id订单号：【{}】,申领订单号：【{}】，支付类型：【{}】，支付反馈结果【{}】",
                applyOrderInfo.getRecordId(),applyOrderInfo.getApplyCode(), applyOrderInfo.getCompensateSubType(), transferResult.toString());

        if(StringUtils.isEmpty(transferResult.getPayment_time())){
            transferResult.setPayment_time(DateUtil.now());
        }

        //支付结果默认是处理中
        String payStatus = PAY_STATUS_INPROCESS;

        //参考支付文档：https://pay.weixin.qq.com/wiki/doc/api/tools/mch_pay.php?chapter=14_2
        //支付【成功】的判断逻辑： return_code == SUCCESS && result_code == SUCCESS
        if (PAY_SUCCESS.equalsIgnoreCase(transferResult.getResult_code())
                && PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())) {

            aoTransRecord.setPayReturnTime(DateUtils.parseStringToLocalDateTime(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));
            aoTransRecord.setReturnSerialNo(transferResult.getPayment_no());
            //支付状态设置为已支付
            payStatus = PAY_STATUS_PAID;
        }

        //失败 return_code == SUCCESS && result_code == FAIL && errCode包含以上
        if (PAY_FAIL.equalsIgnoreCase(transferResult.getResult_code())
                && PAY_SUCCESS.equalsIgnoreCase(transferResult.getReturn_code())
                && (errorCode.contains(transferResult.getErr_code().toUpperCase())
                || StringUtils.isNotEmpty(transferResult.getApi_version_error_code()))) {

            aoTransRecord.setPayReturnTime(DateUtils.parseStringToLocalDateTime(
                    transferResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS));

            //支付失败
            payStatus = PAY_STATUS_FAIL;
        }
        if(StringUtils.isNotBlank(transferResult.getPayment_no())){
            aoTransRecord.setReturnSerialNo(transferResult.getPayment_no());
        }
        aoTransRecord.setTransCode(transferResult.getReturn_code());
        aoTransRecord.setTransMsg(transferResult.getReturn_msg());
        aoTransRecord.setErrCode(transferResult.getErr_code());
        aoTransRecord.setErrCodeDes(transferResult.getErr_code_des());
        aoTransRecord.setPayStatus(payStatus);
        aoTransRecord.setPayTypeApiVersion(transferResult.getPay_type_api_version());
        aoTransRecord.setWxReceivePackageInfo(transferResult.getWx_receive_package_info());
        aoTransRecord.setTransSubCode(transferResult.getTrans_sub_code());

    }

    /**
     * @title payChinaPayHandler
     * @description 银联支付
     * <AUTHOR>
     * @date 2022/7/26 10:18
     * @param aoTransRecord
     * @param order
     * @param merDate
     * @return void
     */
    private void payChinaPayHandler(PayRecordDO aoTransRecord, ApplyOrderPayVO order, String merDate) {
        String payStatus = PayConstant.PAY_STATUS_INPROCESS;

        ChinaPayV2Dto chinaPayV2Dto = buildPayParam(order);

        ChinaPayV2TransResponse v2Response = ((ChinaPayV2Service)getPayService(order)).transfer(chinaPayV2Dto, ChinaPayV2TransResponse.class);
        log.info("【服务补偿】——进行银联支付-->订单号【{}】,调用支付接口完毕，转账参数：【{}】,支付返回参数：【{}】", order.getRecordId(), JSON.toJSONString(chinaPayV2Dto), JSON.toJSONString(v2Response));

        // responseCode == 00  交易成功  && msgCode == 00000 处理成功（这个处理msgCode可以不要，待讨论）
        if(PayConstant.CHINAPAY_V2_SUCCESS_CODE.equals(v2Response.getResponseCode()) && PayConstant.CHINAPAY_V2_MSG_SUCCESS_CODE.equals(v2Response.getMsgCode())){

            payStatus = PayConstant.PAY_STATUS_PAID;
        }else if(PayConstant.CHINAPAY_V2_FAIL_CODE.equals(v2Response.getResponseCode())){
            // responseCode == 10  交易结果失败
            payStatus = PayConstant.PAY_STATUS_FAIL;
        }else{
            payStatus = PayConstant.PAY_STATUS_INPROCESS;
        }

        aoTransRecord.setErrCodeDes(v2Response.getMsgDesc());
        aoTransRecord.setErrCode(v2Response.getMsgCode());

        aoTransRecord.setTransCode(v2Response.getResponseCode());
        aoTransRecord.setTransMsg(v2Response.getResponseMsg());
        aoTransRecord.setTransSubCode(v2Response.getMsgCode());
        aoTransRecord.setTransSubMsg(v2Response.getMsgDesc());

        //为空会报错 org.apache.ibatis.type.TypeException:  Error setting null for parameter #1 with JdbcType OTHER .
        aoTransRecord.setReturnSerialNo(""); //支付流水号，银联V2新接口没有了
        aoTransRecord.setPayStatus(payStatus);
        aoTransRecord.setPayReturnTime(DateUtil.toLocalDateTime(DateUtil.date()));
        aoTransRecord.setTransDate(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN));

        log.info("【服务补偿】——  订单：【{}】------------>封装转账记录表数据【{}】", order.getRecordId(), JSON.toJSONString(aoTransRecord));
    }

    private ChinaPayV2Dto buildPayParam(ApplyOrderPayVO order){
        ChinaPayV2Encryption en = new ChinaPayV2Encryption();
        en.setAccountName(order.getApplyUser());
        en.setAccountNo(order.getGetMoneyAccount());
        en.setTermType(ChinaPayConstant.TERM_TYPE);

        ChinaPayContentV2Dto content = new ChinaPayContentV2Dto();
        //账户信息加密
        content.setEncryption(ChinaPayV2Service.builderEncryptionData(en));
        content.setTransDate(DateUtil.format(DateUtil.date(), DatePattern.PURE_DATE_PATTERN));
        //这儿需要改申领单生成这个的规则
        content.setOrderNo(String.valueOf(order.getRecordId()));
        content.setCuryId(ChinaPayConstant.CURY_ID);
        if(profiles.equals("prod")){
            //银联要求传分
            content.setTransAmt(String.valueOf(order.getTransAmount().intValue() * 100));
        }else {
            //测试环境和开发环境，银联的规则：金额偶数是失败，奇数是成功，为啥这儿单独写，因为*100之后肯定是偶数
            content.setTransAmt(String.valueOf(order.getTransAmount().intValue()));
        }

        content.setOpenBank(StringUtil.str2Unicode(order.getOpenBankName()));
        content.setProv(StringUtil.str2Unicode(ChinaPayConstant.PROV));
        content.setCity(StringUtil.str2Unicode(ChinaPayConstant.CITY));
        content.setPurpose(StringUtil.str2Unicode(PAY_REMARK));
        content.setPayMode(ChinaPayConstant.PAY_MODE);

        ChinaPayV2Dto v2 = new ChinaPayV2Dto();
        v2.setContent(JSONUtil.toJsonStr(content));
        v2.setMethod("2");
        v2.setVersion(ChinaPayConstant.VERSION);
        v2.setChkValue(ChinaPayV2Service.builderSignData(v2));
        return v2;
    }

    private void encapsulatedByChinaPay(PayRecordDO aoTransRecord, ChinaPayInqueryResult cpr) {
        aoTransRecord.setTransCode(cpr.getCode());
        aoTransRecord.setReturnSerialNo(cpr.getCpSeqId());
        aoTransRecord.setTransSubCode(cpr.getStat());
        if (StringUtils.isNotEmpty(cpr.getMerDate())) {
            aoTransRecord.setPayReturnTime(parsePayReturnTime(cpr.getMerDate()));
        }
        if (StringUtils.isNotEmpty(cpr.getCpDate())) {
            aoTransRecord.setPayReturnTime(parsePayReturnTime(cpr.getCpDate()));
        }
    }

    /**
     * Title： encapsulatedByWeiXin <br>
     * Description：封装转账记录表对象-微信反馈<br>
     * author：傅欣荣 <br>
     * date：2020/4/7 18:37 <br>
     *
     * @param
     * @return
     */
    private void encapsulatedByWeiXin(PayRecordDO aoTransRecord, WxQueryResult wxQueryResult) {
        if(StringUtils.isNotEmpty(wxQueryResult.getPayment_time())){
            aoTransRecord.setPayReturnTime(DateUtils.parseStringToLocalDateTime(
                    wxQueryResult.getPayment_time(), DateUtils.YYYY_MM_DD_HH_MM_SS)
            );
        }

        aoTransRecord.setReturnSerialNo(wxQueryResult.getDetail_id());
        aoTransRecord.setTransCode(wxQueryResult.getReturn_code());
        aoTransRecord.setTransMsg(wxQueryResult.getReturn_msg());
        aoTransRecord.setErrCode(wxQueryResult.getErr_code());
        aoTransRecord.setErrCodeDes(wxQueryResult.getErr_code_des());
        aoTransRecord.setTransSubCode(wxQueryResult.getTrans_sub_code());

    }

    private LocalDateTime parsePayReturnTime(String date){
        String s = DateUtils.formatStrToStr(date);
        Date pDate = DateUtils.parseStrToDate(s, DateUtils.YYYY_MM_DD);
        return DateUtil.toLocalDateTime(pDate);
    }
}
