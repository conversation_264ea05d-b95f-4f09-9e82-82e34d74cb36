package com.swcares.aps.compensation.impl.apply.service.impl;

import cn.hutool.core.util.ArrayUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.mapper.ApplyOrderMapper;
import com.swcares.aps.compensation.impl.apply.service.ReceivingRecordService;
import com.swcares.aps.compensation.impl.irregularflight.service.CompensationFlightInfoService;
import com.swcares.aps.compensation.model.apply.dto.ApplyGetReceiveDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyQueryRecordDTO;
import com.swcares.aps.compensation.model.apply.dto.ApplyRecordCaptchaDTO;
import com.swcares.aps.compensation.model.apply.dto.ReplaceFilterRecordDTO;
import com.swcares.aps.compensation.model.apply.vo.*;
import com.swcares.aps.usercenter.remote.api.UserCenterApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.collect.ListUtils;
import com.swcares.captcha.core.model.CaptchaVO;
import com.swcares.components.encrypt.FieldEncryptor;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * All rights Reserved, Designed By http://xnky.travelsky.net/ <br>
 * Title：ReceivingRecordServiceImpl <br>
 * Package：com.swcares.aps.apply.impl.service.impl <br> 
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * date 2021年 12月07日 13:36 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class ReceivingRecordServiceImpl implements ReceivingRecordService {

    @Resource
    private ApplyOrderMapper applyOrderMapper;
    @Autowired
    private FieldEncryptor fieldEncryptor;
    @Autowired 
    CompensationFlightInfoService compensationFlightInfoService;
    //图形验证码校验结果key
    public static final String APPLY_VALIDATE_CODE_RESULT = "weixin:validate_code_result:";
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private UserCenterApi userCenterApi;
    /**
     * Title：queryRecord <br>
     * Description：查询申领记录 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @param applyQueryRecordDTO
     * @return List<ReceivingRecordVO>
     */
    @Override
    public IPage<ReceivingRecordVO> queryRecord(ApplyQueryRecordDTO applyQueryRecordDTO) {
        applyQueryRecordDTO.setIdCard(fieldEncryptor.encrypt(applyQueryRecordDTO.getIdCard()));
        return applyOrderMapper.queryRecord(applyQueryRecordDTO,applyQueryRecordDTO.createPage());
    }

    /**
     * Title：queryRecord <br>
     * Description：查询申领记录 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @param applyQueryRecordDTO ApplyQueryRecordDTO
     * @return int
     */
    @Override
    public int myRecord(ApplyQueryRecordDTO applyQueryRecordDTO) {
        applyQueryRecordDTO.setIdCard(fieldEncryptor.encrypt(applyQueryRecordDTO.getIdCard()));
        //是否进行图形验证码校验
        this.isApplyValidateCodePass();
        int selfCount = applyOrderMapper.queryRecordCount(applyQueryRecordDTO, "0");
        int replaceCount = applyOrderMapper.queryRecordCount(applyQueryRecordDTO, "1");
        if (selfCount == 0 && replaceCount == 0) {
            throw new BusinessException(ApplyErrors.QUERY_RECORD_ERROR);
        }
        // count 1本人页面
        if (selfCount >0 && replaceCount ==0){
            return 1;
        }
        // count -1代领页面
        if (selfCount ==0 && replaceCount >=0){
            return -1;
        }
        return 1;
    }

    /**
     * Title：queryRecord <br>
     * Description：领取记录查询 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @return List<ReceivingRecordVO>
     * @param applyGetReceiveDTO
     */
    @Override
    public IPage<ReceivingRecordVO> getReceive(ApplyGetReceiveDTO applyGetReceiveDTO) {
        applyGetReceiveDTO.setIdCard(fieldEncryptor.encrypt(applyGetReceiveDTO.getIdCard()));
        return applyOrderMapper.getReceive(applyGetReceiveDTO,applyGetReceiveDTO.createPage());
    }

    /**
     * Title：queryDetails <br>
     * Description：查询申领单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param recordId
     * @return ApplyDetailsInfoVO
     */
    @Override
    public ApplyDetailsInfoVO queryDetails(String recordId,String idCard) {
        if(StringUtils.isEmpty(idCard) || StringUtils.isEmpty(recordId)){
            return new ApplyDetailsInfoVO();
        }
        String idCardEncrypt = fieldEncryptor.encrypt(idCard);
        // 公务舱  其他的就是经济舱
        String[] bMainClasssign = ConfigUtil.get("B_MAIN_CLASSSIGN").getValues().get(0).getConfigValue().split(",");
        String[] bClasssign = ConfigUtil.get("B_CLASSSIGN").getValues().get(0).getConfigValue().split(",");

        ApplyInfoDetailsVO applyInfoDetails = applyOrderMapper.getApplyInfoDetails(recordId,idCardEncrypt);



        List<CompensateDetailsVO> compensateDetails = applyOrderMapper.getCompensateDetails(recordId,idCardEncrypt);
        compensateDetails.forEach(compensateDetailsVO -> {
            if (ArrayUtil.contains(bMainClasssign, compensateDetailsVO.getMainClass())
                    || ArrayUtil.contains(bClasssign, compensateDetailsVO.getSubClass())) {
                compensateDetailsVO.setClassDes("公务舱");
            } else {
                compensateDetailsVO.setClassDes("经济舱");
            }
        });
        ApplyDetailsInfoVO applyDetailsInfoVO = new ApplyDetailsInfoVO();
        applyDetailsInfoVO.setApplyInfoDetailsVO(applyInfoDetails);
        applyDetailsInfoVO.setCompensateDetailsVOList(compensateDetails);
        return applyDetailsInfoVO;
    }

    /**
     * Title：queryReplaceRecord <br>
     * Description：代人领取申领单查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param applyQueryRecordDTO ApplyQueryRecordDTO
     * @return IPage<ReceivingRecordVO>
     */
    @Override
    public IPage<ReceivingRecordVO> queryReplaceRecord(ApplyQueryRecordDTO applyQueryRecordDTO) {
        applyQueryRecordDTO.setIdCard(fieldEncryptor.encrypt(applyQueryRecordDTO.getIdCard()));
        return applyOrderMapper.queryReplaceRecord(applyQueryRecordDTO, applyQueryRecordDTO.createPage());
    }

    /**
     * Title：replaceFilterRecord <br>
     * Description：代人领取申领单筛选查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param replaceFilterRecordDTO ReplaceFilterRecordDTO
     * @return IPage<ReceivingRecordVO>
     */
    @Override
    public IPage<ReceivingRecordVO> replaceFilterRecord(ReplaceFilterRecordDTO replaceFilterRecordDTO) {
        replaceFilterRecordDTO.setIdCard(fieldEncryptor.encrypt(replaceFilterRecordDTO.getIdCard()));
        return applyOrderMapper.replaceFilterRecord(replaceFilterRecordDTO, replaceFilterRecordDTO.createPage());
    }

    /**
     * Title：queryReplaceDetails <br>
     * Description：代人领取旅客申领单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param recordId String
     * @return ReplaceDetailsInfoVO
     */
    @Override
    public ReplaceDetailsInfoVO queryReplaceDetails(String recordId,String idCard) {
        if(StringUtils.isEmpty(idCard) || StringUtils.isEmpty(recordId)){
            return new ReplaceDetailsInfoVO();
        }
        String idCardEncrypt = fieldEncryptor.encrypt(idCard);
        // 商务舱  其他的就是经济舱
        String[] bMainClasssign = ConfigUtil.get("B_MAIN_CLASSSIGN").getValues().get(0).getConfigValue().split(",");
        String[] bClasssign = ConfigUtil.get("B_CLASSSIGN").getValues().get(0).getConfigValue().split(",");
        // 创建返回对象
        ReplaceDetailsInfoVO replaceDetailsInfoVO = new ReplaceDetailsInfoVO();
        // 将头部赔偿但信息放入
        ReplaceInfoDetailsVO replaceApplyInfoDetails = applyOrderMapper.getReplaceApplyInfoDetails(recordId,idCardEncrypt);

        replaceDetailsInfoVO.setReplaceInfoDetailsVO(replaceApplyInfoDetails); 
        // 旅客详情信息获取
        List<ReplaceCompensateDetailsVO> replaceCompensateDetails =
                applyOrderMapper.getReplaceCompensateDetails(recordId,idCardEncrypt);
        if (ListUtils.isNotEmpty(replaceCompensateDetails)) {
            replaceCompensateDetails.forEach(replaceCompensateDetailsVO -> {
                if (ArrayUtil.contains(bMainClasssign, replaceCompensateDetailsVO.getMainClass())
                        || ArrayUtil.contains(bClasssign, replaceCompensateDetailsVO.getSubClass())) {
                    replaceCompensateDetailsVO.setClassDes("公务舱");
                } else {
                    replaceCompensateDetailsVO.setClassDes("经济舱");
                }
            });
            List<ReplaceSubDetailsInfoVO> replaceSubDetailsInfoVOS = new ArrayList<>();
            Map<String, List<ReplaceCompensateDetailsVO>> collect =
                    replaceCompensateDetails.stream().collect(Collectors.groupingBy(ReplaceCompensateDetailsVO::getIdNo));
            collect.forEach((k, v) -> {
                ReplaceSubDetailsInfoVO replaceSub = new ReplaceSubDetailsInfoVO();
                replaceSub.setReplaceCompensateDetailsVOS(v);
                replaceSubDetailsInfoVOS.add(replaceSub);
            });
            replaceDetailsInfoVO.setReplaceSubDetailsInfoVOS(replaceSubDetailsInfoVOS);
        }
        return replaceDetailsInfoVO;
    }


    @Override
    public BaseResult<Object> getCaptchaByApplyRecord(ApplyRecordCaptchaDTO dto) {
        //初始化校验图形验证码标识，为false未校验
        String validateCodeResultKey = getApplyValidateCodeResultKey();
        redisUtil.set(validateCodeResultKey,false);
        
        //发送图形验证码
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(dto.getCaptchaType());
        BaseResult<Object> result = userCenterApi.getCaptcha(captchaVO);
        return result;
    }

    @Override
    public BaseResult<Object> validateCaptchaByApplyRecord(ApplyRecordCaptchaDTO dto) {
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(dto.getCaptchaType());
        captchaVO.setToken(dto.getToken());
        captchaVO.setCaptchaVerification(dto.getCaptchaVerification());

        BaseResult<Object> checkResult = userCenterApi.checkCaptcha(captchaVO);
        if(checkResult != null && BaseResult.OK_CODE == checkResult.getCode() ){
            String validateCodeResultKey = getApplyValidateCodeResultKey();
            //图形验证码校验成功，redis存校验成功标识,有效期3小时
            redisUtil.set(validateCodeResultKey,true,60 * 60);
        }
        return checkResult;
    }

    private String getApplyValidateCodeResultKey(){
//        标识组成由：航班号+航班日期+用户id
        Authentication authentication = UserContext.getAuthentication();
        Object principal = authentication.getCredentials();
        String userId = String.valueOf(principal);
        return APPLY_VALIDATE_CODE_RESULT+userId;
    }


    private void isApplyValidateCodePass(){
        //判断是否已验证过短信
        String validateCodeResultKey = getApplyValidateCodeResultKey();
        Object validateResult = redisUtil.get(validateCodeResultKey);
        log.info("申领记录查询，是否已验证过手机验证码key:" + validateCodeResultKey+",验证结果："+ validateResult);
        if(validateResult == null || !(Boolean) validateResult){
            throw new BusinessException(ApplyErrors.PAX_TELEPHONE_VALIDATE_FAIL);
        }
    }

}
