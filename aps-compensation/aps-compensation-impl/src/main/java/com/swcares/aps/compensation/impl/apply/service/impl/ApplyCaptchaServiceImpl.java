package com.swcares.aps.compensation.impl.apply.service.impl;

import com.swcares.aps.compensation.impl.apply.constants.ApplyConstants;
import com.swcares.aps.compensation.impl.apply.constants.ApplyErrors;
import com.swcares.aps.compensation.impl.apply.service.ApplyCaptchaService;
import com.swcares.aps.compensation.impl.sms.constant.CompensationSmsConstant;
import com.swcares.aps.compensation.model.apply.dto.ApplyCaptchaDTO;
import com.swcares.aps.usercenter.remote.api.UserCenterApi;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.util.RedisUtil;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.captcha.core.model.CaptchaVO;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.oauth2.provider.authentication.OAuth2AuthenticationDetails;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static com.swcares.components.coupler.sms.SmsConstants.SMS_CONFIG;
import static com.swcares.components.coupler.sms.SmsConstants.SMS_SWITCH_CONFIG;

/**
 * @ClassName：ApplyCaptchaServiceImpl
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date： 2025/2/26 15:42
 * @version： v1.0
 */
@Slf4j
@Service
public class ApplyCaptchaServiceImpl implements ApplyCaptchaService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private UserCenterApi userCenterApi;

    public boolean smsSwitchByTenant(){
        LoginUserDetails user = UserContext.getUser();
        //调用租户配置，发送短信开关
        boolean smsSwitch = ConfigUtil.getBoolean(SMS_CONFIG, SMS_SWITCH_CONFIG, user.getTenantId());
        log.info("【调用租户配置，获取发送短信开关】租户id:[{}] ，smsSwitch:[{}]",user.getTenantId(),smsSwitch);
        return smsSwitch;
    }

    @Override
    public BaseResult<Object> getCaptchaConfigByTenant() {
        Map<String,Object> tenantConfig = new HashMap<>();
        tenantConfig.put(SMS_SWITCH_CONFIG,this.smsSwitchByTenant());
        return BaseResult.ok(tenantConfig);
    }

    @Override
    public BaseResult<Object> getCaptchaByApply(ApplyCaptchaDTO dto) {
        //初始化校验图形验证码标识，为false未校验
        String applyCaptchaPassKey = getApplyCaptchaPassKey();
        redisUtil.set(applyCaptchaPassKey,false);

        //发送图形验证码
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(dto.getCaptchaType());
        BaseResult<Object> result = userCenterApi.getCaptcha(captchaVO);
        return result;
    }

    @Override
    public BaseResult<Object> validateCaptchaByApply(ApplyCaptchaDTO dto) {
        CaptchaVO captchaVO = new CaptchaVO();
        captchaVO.setCaptchaType(dto.getCaptchaType());
        captchaVO.setToken(dto.getToken());
        captchaVO.setCaptchaVerification(dto.getCaptchaVerification());

        BaseResult<Object> checkResult = userCenterApi.checkCaptcha(captchaVO);
        if(checkResult != null && BaseResult.OK_CODE == checkResult.getCode() ){
            //图形验证码校验成功，redis存校验成功标识,有效期5分钟
            setTokenCaptchaPass();
        }
        return checkResult;
    }

    @Override
    public void verifyTokenCaptchaPassByTenant(){
        //根据租户的短信开关，开：界面只进行短信验证。需判断短信验证码是否验证通过，关：屏蔽短信功能，只进行图形校验，需判断图形验证码是否验证通过
        if(this.smsSwitchByTenant()){
            this.isTokenSmsPass();
        }else {
            this.isTokenCaptchaPass();
        }
    }

    @Override
    public void isTokenCaptchaPass() {
        String applyCaptchaPassKey = getApplyCaptchaPassKey();
        String isTokenCaptchaPass= redisUtil.get(applyCaptchaPassKey);
        if(StringUtils.isEmpty(isTokenCaptchaPass) ||
                (StringUtils.isNotEmpty(isTokenCaptchaPass) && !ApplyConstants.AUTH_TOKEN_CAPTCHA_CODE_PASS.equals(isTokenCaptchaPass))){
            throw new BusinessException(ApplyErrors.CAPTCH_VERIFICATION_ERROR);
        }
    }

    @Override
    public void setTokenCaptchaPass() {
        String applyCaptchaPassKey = getApplyCaptchaPassKey();
        //验证短信验证码成功后，将token保存，有效时间5分钟
        redisUtil.set(applyCaptchaPassKey,ApplyConstants.AUTH_TOKEN_CAPTCHA_CODE_PASS,CompensationSmsConstant.AUTH_PHONE_PREFIX_PAST_DUE);
    }

    @Override
    public String getApplyCaptchaPassKey() {
        //避免越过[短信或图形验证码]验证步骤
        Authentication authentication = UserContext.getAuthentication();
        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)authentication.getDetails();
        String tokenValue = details.getTokenValue();
        return ApplyConstants.AUTH_TOKEN_CAPTCHA_PASS_PREFIX + tokenValue;
    }


    @Override
    public void isTokenSmsPass(){
        //避免越过短信验证步骤
        Authentication authentication = UserContext.getAuthentication();
        OAuth2AuthenticationDetails details = (OAuth2AuthenticationDetails)authentication.getDetails();
        String tokenValue = details.getTokenValue();
        //验证短信验证码成功后，将token保存，有效时间5分钟
        String isTokenSmsPass= redisUtil.get(CompensationSmsConstant.AUTH_TOKEN_SMS_PASS_PREFIX + tokenValue);
        if(StringUtils.isEmpty(isTokenSmsPass) ||
                (StringUtils.isNotEmpty(isTokenSmsPass) && !CompensationSmsConstant.AUTH_TOKEN_SMS_CODE_PASS.equals(isTokenSmsPass))){
            throw new BusinessException(ApplyErrors.SMS_VERIFICATION_ERROR);
        }
    }

}
