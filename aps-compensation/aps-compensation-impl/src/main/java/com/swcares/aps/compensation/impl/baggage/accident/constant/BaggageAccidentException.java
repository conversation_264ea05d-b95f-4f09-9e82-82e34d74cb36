package com.swcares.aps.compensation.impl.baggage.accident.constant;

/**
 * ClassName：BaggageAccidentException <br>
 * Description：异常事故单错误码 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
public class BaggageAccidentException {

    //异常行李事故单不存在
    public final static int BAGGAGE_ACCIDENT_NOT_EXIST = 30175;

    //异常行李事故单状态修改失败
    public final static int BAGGAGE_ACCIDENT_STATUS_CHANGE_FAIL = 30176;

    //异常行李事故单状态扭转校验失败
    public final static int BAGGAGE_ACCIDENT_STATUS_VERIFY_FAIL = 30177;

    //异常行李事故单信息不完善，请检查后再试
    public final static int BAGGAGE_ACCIDENT_INCOMPLETE_INFORMATION = 30178;

    //该异常行李事故单不支持该操作
    public final static int BAGGAGE_ACCIDENT_UNSUPPORTED_OPERATION= 30179;

    //该异常行李事故单已被绑定
    public final static int BAGGAGE_ACCIDENT_HAS_BAND = 30180;

    //无权限编辑
    public final static int NO_AUTHORITY_EDIT = 30181;

    //无航班
    public final static int NO_PAX_FLIGHT = 30182;

    //运输单不存在
    public final static int BAGGAGE_TRANSPORT_NOT_FOUND = 30183;

    //运输单无法提交
    public final static int BAGGAGE_TRANSPORT_CANNOT_SUBMIT = 30184;

    //运输单已复核
    public final static int BAGGAGE_TRANSPORT_ALREADY_REVIEWED = 30185;

}