package com.swcares.aps.compensation.impl.irregularflight.enums;

import org.checkerframework.checker.units.qual.C;

/**
 * ClassName：com.swcares.aps.compensation.impl.irregularflight.enums <br>
 * Description：赔偿子类型 <br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2021年 11月18日 11:22 <br>
 * @version v1.0 <br>
 */
public enum CompensationSubTypeEnum {
    IRREGULAR_FLIGHT("1", "fc_type"),
    ABNORMAL_BAGGAGE("2", "abnormal_baggage_subtype"),
    OVERBOOKING("3", "overbooking_subtype");

    private CompensationSubTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    private String key;

    private String value;

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public static CompensationSubTypeEnum build(String key) {
        return build(key, true);
    }

    public static CompensationSubTypeEnum build(String key, boolean throwEx) {
        CompensationSubTypeEnum typeEnum = null;
        for (CompensationSubTypeEnum element : CompensationSubTypeEnum.values()) {
            if (element.getKey().equals(key)) {
                typeEnum = element;
                break;
            }
        }
        if (throwEx && typeEnum == null) {
            throw new IllegalArgumentException("未知的枚举类型：" + key + ",请核对" + CompensationSubTypeEnum.class.getSimpleName());
        }
        return typeEnum;
    }
}
