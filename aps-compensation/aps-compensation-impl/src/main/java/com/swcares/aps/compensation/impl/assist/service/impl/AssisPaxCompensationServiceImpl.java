package com.swcares.aps.compensation.impl.assist.service.impl;

import com.swcares.aps.compensation.impl.apply.service.ApplyPaxService;
import com.swcares.aps.compensation.impl.assist.mapper.AssistPaxListMapper;
import com.swcares.aps.compensation.impl.assist.service.AssisPaxCompensationService;
import com.swcares.aps.compensation.model.apply.dto.AuthPaxDTO;
import com.swcares.aps.compensation.model.apply.vo.CompensationInfoVO;
import com.swcares.aps.compensation.model.assist.dto.FindPaxParamDTO;
import com.swcares.aps.compensation.model.assist.vo.AssistPaxListVO;
import com.swcares.components.sys.util.ConfigUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @title: AssisPaxServiceImpl
 * @projectName aps
 * @description:
 * @date 2022/1/20 14:08
 */
@Service
@Slf4j
public class AssisPaxCompensationServiceImpl implements AssisPaxCompensationService {

    @Autowired
    private AssistPaxListMapper mapper;

    @Autowired
    private ApplyPaxService applyPaxService;

    @Override
    public CompensationInfoVO getPaxCompensation(AuthPaxDTO dto) {
        log.info("【aps-compensation-impl-assist】查询旅客可申领信息参数:[{}]",dto.toString());
        //调用王磊本人领取代码
        return applyPaxService.findCompensationOrder(dto);
    }

    @Override
    public void sendSMSCode(String phoneNum) {
        log.info("【aps-compensation-impl-assist】发送验证码手机号:[{}]",phoneNum);
        applyPaxService.sendSMS(phoneNum);
    }

    @Override
    public void verificationSMSCode(String phoneNum, String code) {
        log.info("【aps-compensation-impl-assist】验证手机号以及验证码:[{},{}]",phoneNum,code);
        applyPaxService.verificationSMS(phoneNum, code);
    }

    @Override
    public List<AssistPaxListVO> getPaxCompensationByFlightInfo(String flightNo, String flightDate,String name) {
        StringBuffer mainClass = new StringBuffer(ConfigUtil.get("B_MAIN_CLASSSIGN").getValues().get(0).getConfigValue());
        String bClass = ConfigUtil.get("B_CLASSSIGN").getValues().get(0).getConfigValue();
        if(ObjectUtils.isNotEmpty(mainClass)&&ObjectUtils.isNotEmpty(bClass)){
            mainClass.append(",").append(bClass);
        }
        FindPaxParamDTO dto = new FindPaxParamDTO();
        dto.setFlightNo(flightNo);
        dto.setMainClass(mainClass.toString());
        dto.setFlightDate(flightDate);
        dto.setName(name);
        log.info("【aps-compensation-impl-assist】查询旅客列表参数:[{}]", dto.toString());
        return mapper.getPaxList(dto);
    }

    @Override
    public String getPaxName(String flightDate, String flightNo, String idNo) {
        return mapper.getPaxName(flightDate, flightNo, idNo);
    }
}
