package com.swcares.aps.compensation.impl.baggage.luggage.constant;

/**
 * <AUTHOR>
 * @title: ExceptionEnum
 * @projectName aps
 * @description:
 * @date 2021/11/11 16:40
 */
public class LuggageException {
    //当前航站已有此箱包，请进行核实！
    public  static int ALREADY_EXIST_LUGGAGE = 30161;
    //每日添加箱包信息不能超过999条
    public  static int MAX_NUMBER_REACHED = 30162;
    //不允许存在剩余库存低于0
    public  static int OUT_OF_STOCK = 30163;
    //库存操作失败
    public  static int OP_STOCK_ERROR = 30164;

}
