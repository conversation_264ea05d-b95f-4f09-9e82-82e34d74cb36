package com.swcares.aps.compensation.impl.compensation.service;

import com.swcares.aps.compensation.model.compensation.dto.AccidentCompensationDTO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditOperationVO;
import com.swcares.aps.compensation.model.compensation.vo.CompensationAuditReviewerVO;
import com.swcares.aps.compensation.model.irregularflight.dto.AuditProcessorDTO;
import com.swcares.aps.compensation.model.irregularflight.dto.CompensationAuditInfoDTO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationAuditRecordVO;

import java.util.List;
/**
 * @ClassName：CompensationAuditService
 * @Description：补偿单统一审核接口
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/5/9 19:29
 * @version： v1.0
 */
public interface CompensationAuditService {
    /**
     * @title startAuditProcess
     * @description 发起流程
     * <AUTHOR>
     * @date 2022/5/10 15:34
     * @param compensation
     * @return
     */
    CompensationAuditOperationVO startAuditProcess(AccidentCompensationDTO compensation);

    /**
     * @title submitterAuditProcess
     * @description 提交流程
     * <AUTHOR>
     * @date 2022/5/10 15:35
     * @param compensation
     * @return
     */
    CompensationAuditOperationVO submitterAuditProcess(AccidentCompensationDTO compensation);

    /**
     * @title findReviewer
     * @description条件查询可选审核人
     * <AUTHOR>
     * @date 2022/5/9 19:30
     * @param orgId 查询部门ID
     * @param userInfo 前端传递条件查询审核人信息（姓名/工号）
     * @param taskId
     * @param orderId
     * @return
     */
    CompensationAuditReviewerVO findReviewer(Long orgId, String userInfo, String taskId, Long orderId);

    /**
     * @title findAuditRecord
     * @description 查看审核记录
     * <AUTHOR>
     * @date 2022/5/9 19:50
     * @param orderId 补偿单ID
     * @param orderNo 补偿单单号
     * @return
     */
    List<CompensationAuditRecordVO> findAuditRecord(Long orderId, String orderNo);

    /**
     * @title saveReviewer
     * @description @TODO
     * <AUTHOR>
     * @date 2022/5/9 20:00
     * @param compensationAuditInfoDTO 审核人确认
     * @return
     */
    void saveReviewer(CompensationAuditInfoDTO compensationAuditInfoDTO);

    /**
     * @title auditOperation
     * @description 审核操作
     * <AUTHOR>
     * @date 2022/5/9 20:48
     * @param auditProcessorDTO 审核参数
     * @return
     */
    CompensationAuditOperationVO auditOperation(AuditProcessorDTO auditProcessorDTO) throws Exception;
}
