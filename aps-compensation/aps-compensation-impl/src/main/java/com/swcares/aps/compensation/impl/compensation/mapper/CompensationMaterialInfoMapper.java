package com.swcares.aps.compensation.impl.compensation.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.swcares.aps.compensation.model.compensation.entity.CompensationMaterialInfoDO;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * @ClassName：CompensationLuggageInfoMapper
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/10 11:08
 * @version： v1.0
 */
public interface CompensationMaterialInfoMapper extends BaseMapper<CompensationMaterialInfoDO> {
    @InterceptorIgnore(tenantLine = "true")
    @Select(value = "select * from compensation_material_info   " +
            "where  order_id = #{orderId} ")
    List<CompensationMaterialInfoDO> findByOrderId(Long orderId);
}
