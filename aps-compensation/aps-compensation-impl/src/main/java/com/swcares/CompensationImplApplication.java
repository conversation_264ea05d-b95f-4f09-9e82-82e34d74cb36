package com.swcares;

import com.alicp.jetcache.anno.config.EnableCreateCacheAnnotation;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.amqp.RabbitAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.security.core.context.SecurityContextHolder;

@SpringBootApplication(exclude = RabbitAutoConfiguration.class, scanBasePackages = {CompensationImplApplication.DEFAULT_PACKAGE})
@EnableCreateCacheAnnotation
@MapperScan(CompensationImplApplication.DEFAULT_PACKAGE+".**.mapper")
@EnableDiscoveryClient
@EnableFeignClients
@EnableScheduling
@EnableAspectJAutoProxy(exposeProxy = true)
public class CompensationImplApplication {


    public static final String DEFAULT_PACKAGE = "com.swcares";
    public static void main(String[] args) {
        SpringApplication.run(CompensationImplApplication.class, args);
        SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
    }
}
