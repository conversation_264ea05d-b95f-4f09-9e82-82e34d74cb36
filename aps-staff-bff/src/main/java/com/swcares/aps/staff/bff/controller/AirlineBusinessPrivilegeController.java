package com.swcares.aps.staff.bff.controller;

import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.aps.compensation.remote.api.privilege.AirlineBusinessPrivilegeApi;
import com.swcares.aps.compensation.remote.api.privilege.CoordinateCustomerAPI;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/airline/businessPrivilege")
@Api(tags = "机场端业务授权接口")
@ApiVersion(value = "机场端业务授权 v1.0")
public class AirlineBusinessPrivilegeController extends BaseController {

    @Autowired
    AirlineBusinessPrivilegeApi airlineBusinessPrivilegeApi;

    @ApiOperation(value = "机场端获取事故单补偿单的创建授权")
    @GetMapping("/verify/{code}")
    public BaseResult<Boolean> verify(@RequestParam("code") String typeCode){
        return airlineBusinessPrivilegeApi.verify(typeCode);
    }

    @ApiOperation(value = "机场端获取授权航司二字码")
    @GetMapping("/getBelongAirline/{code}")
    public BaseResult<List<BelongAirlineDTO>> getBelongAirline(@RequestParam("code") String typeCode){
        return airlineBusinessPrivilegeApi.getBelongAirline(typeCode);
    }
}