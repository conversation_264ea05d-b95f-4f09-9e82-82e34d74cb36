package com.swcares.aps.staff.bff.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.basic.data.remoteapi.model.vo.SegmentFindVO;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportDetailVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.component.dict.mapper.RegionMapper;
import com.swcares.aps.component.dict.model.entity.Region;
import com.swcares.aps.staff.bff.enums.AccidentStatusEnum;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;
import java.util.List;

/**
 * ClassName：BaggageAccidentController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/h5")
@Api(tags = "异常行李事故单相关接口")
@ApiVersion(value = "工作人员端v1.0")
public class BaggageAccidentController extends BaseController {
    @Autowired
    CompensationInfoApi compensationInfoApi;


    @GetMapping("/findTerminal")
    @ApiOperation(value = "获取服务航站")
    public BaseResult<Object> getTerminal() {
        return compensationInfoApi.getTerminal();
    }

    @PostMapping("/findBaggageAccidentList")
    @ApiOperation(value = "箱包事故单信息列表查询")
    public PagedResult<List<FindBaggageVO>> findBaggageAccidentList(@RequestBody FindBaggageDTO dto){
        return compensationInfoApi.findBaggageAccidentList(dto);
    }

    @PostMapping("/baggageAccidentDetailInfo")
    @ApiOperation(value = "箱包事故单详情信息")
    public BaseResult<BaggageDetailFinalVO> baggageAccidentDetailInfo(@RequestBody FindBaggageDetailDTO dto){
        return compensationInfoApi.baggageAccidentDetailInfo(dto);
    }

    //-----------------------快递相关----------------
    @PostMapping("/accident/baggage/express/save")
    @ApiOperation(value = "新建异常行李快递信息")
    public BaseResult<Object> saveExpress(@Validated @RequestBody CompensationExpressInfoDTO dto){
        return compensationInfoApi.saveExpress(dto);
    }

    @DeleteMapping("/accident/baggage/express/remove")
    @ApiOperation(value = "根据id删除一条快递信息")
    public BaseResult<Object> removeExpress(String expressId){
        return compensationInfoApi.removeExpress(expressId);
    }

    //-----------------------事故单相关----------------
    @GetMapping("/findSegment")
    @ApiOperation(value = "航段查询接口")
    public BaseResult<Object> findSegment(@ApiParam(value = "日期", required = true) String date, @ApiParam(value = "航班号", required = true) String flightNo) {
        List<SegmentFindVO> list = compensationInfoApi.getSegment(date, flightNo).getData();
        if(ObjectUtils.isEmpty(list)){
            return ok();
        }
        StringBuffer segment = new StringBuffer();
        if(list.size()>1){
            if(list.get(0).getArrivalPortCH().equals(list.get(1).getDepartPortCH())){
                segment.append(list.get(0).getDepartPortCH()).append("-")
                       .append(list.get(0).getArrivalPortCH()).append("-")
                       .append(list.get(1).getArrivalPortCH());
            }else{
                segment.append(list.get(1).getDepartPortCH()).append("-")
                        .append(list.get(1).getArrivalPortCH()).append("-")
                        .append(list.get(0).getArrivalPortCH());
            }
        }else{
            segment.append(list.get(0).getDepartPortCH()).append("-").append(list.get(0).getArrivalPortCH());
        }
        return ok(segment);
    }

    @PostMapping("/accident/baggage/save")
    @ApiOperation(value = "新建异常行李事故单")
    public BaseResult<Object> save(@Validated @RequestBody BaggageAccidentInfoDTO dto){
        return compensationInfoApi.baggageSave(dto);
    }

    @GetMapping("/accident/baggage/edit")
    @ApiOperation(value = "编辑异常行李事故单回显")
    public BaseResult<Object> edit(@Validated @RequestParam String accidentId) {
        return compensationInfoApi.edit(accidentId);
    }

    @GetMapping("/accident/baggage/toLost")
    @ApiOperation(value = "少收转丢失")
    public BaseResult<Object> toLost(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toLost(accidentId);
    }

    @GetMapping("/accident/baggage/toMatch")
    @ApiOperation(value = "匹配多/少收")
    public BaseResult<Object> toMatch(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toMatch(accidentId);
    }

    @GetMapping("/accident/baggage/saveMatch")
    @ApiOperation(value = "匹配绑定多/少收")
    public BaseResult<Object> saveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "须绑定的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.saveMatch(accidentId, accidentNo);
    }

    @GetMapping("/accident/baggage/relieveMatch")
    @ApiOperation(value = "解除匹配绑定多/少收")
    public BaseResult<Object> relieveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "解除匹配的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.relieveMatch(accidentId, accidentNo);
    }

    @DeleteMapping("/accident/baggage/delete/{id}")
    @ApiOperation(value = "通过id删除异常行李单")
    public BaseResult<Object> deleteById(@PathVariable @ApiParam(value = "主键id",required = true)Long id){
        return compensationInfoApi.deleteById(id);
    }

    @GetMapping("/accident/baggage/submit")
    @ApiOperation(value = "提交异常行李事故单(状态转换)")
    public BaseResult<Object> submit(@ApiParam(value = "事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TODO.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toVoidStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转作废")
    public BaseResult<Object> toVoidStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TO_VOID.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toCloseStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转结案")
    public BaseResult<Object> toCloseStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.CASE_CLOSED.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }


    @PostMapping("/existOrderNumberByPax")
    @ApiOperation(value = "根据旅客信息查询：已存在的事故单或者补偿单数量")
    public BaseResult<Integer> getExistOrderNumberByPax(@Validated @RequestBody VerifyAlikePaxOrderDTO dto){
        if("1".equals(dto.getCheckType())){
            return compensationInfoApi.getExistAccidentNumber(dto);
        }else if("2".equals(dto.getCheckType())){
            return compensationInfoApi.getExistCompensationNumber(dto);
        }
        return ok();
    }

    @PostMapping("/undeliveredDropdown")
    @ApiOperation(value = "获取未交付的异常行李事故单下拉列表")
    public BaseResult<List<BaggageAccidentDropdownVO>> getUndeliveredAccidentDropdown(@RequestBody UndeliveredAccidentDTO dto) {
        return compensationInfoApi.getUndeliveredAccidentDropdown(dto);
    }

    @PostMapping("/canEditTransport/{accidentNo}")
    @ApiOperation(value = "判断是否可以编辑运输单")
    public BaseResult<Boolean> canEditTransport(@PathVariable(value = "accidentNo") String accidentNo) {
        return compensationInfoApi.canEditTransport(accidentNo);
    }

    @PostMapping("/transport/save")
    @ApiOperation(value = "保存或修改运输单信息")
    public BaseResult<Object> saveTransportInfo(@Validated @RequestBody BaggageTransportInfoDTO dto){
        return compensationInfoApi.saveTransportInfo(dto);
    }

    @PostMapping("/transport/list")
    @ApiOperation(value = "查询异常行李运输单列表")
    PagedResult<List<BaggageTransportListVO>> queryBaggageTransportList(@RequestBody @Valid BaggageTransportQueryDTO dto){
        return compensationInfoApi.queryBaggageTransportList(dto);
    }

    @GetMapping("/transport/detail/{transportId}")
    @ApiOperation(value = "查询运输单详情")
    public BaseResult<BaggageTransportDetailVO> getTransportDetail(@PathVariable(value = "transportId") Long transportId){
        return compensationInfoApi.getTransportDetail(transportId);
    }

    @Autowired
    private RegionMapper regionMapper;

    /**
     * 根据pid和deep查询区域数据
     * @param pid 父级ID (必填)
     * @param deep 层级深度 (必填)
     * @return 符合条件的区域列表
     */
    @GetMapping("/region/query")
    public BaseResult<List<Region>> getRegionsByPidAndDeep(
            @RequestParam Long pid,
            @RequestParam Integer deep) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Region::getPid, pid)
                .eq(Region::getDeep, deep)
                .orderByAsc(Region::getId);
        return ok(regionMapper.selectList(queryWrapper));
    }

    // ----------------------- 运输单工作流相关接口 -----------------------

    @PostMapping("/transport/submit/{transportId}")
    @ApiOperation(value = "提交运输单到工作流")
    public BaseResult<Object> submitTransport(@PathVariable(value = "transportId") Long transportId) {
        return compensationInfoApi.submitTransport(transportId);
    }

    @PostMapping("/transport/audit")
    @ApiOperation(value = "审核运输单")
    public BaseResult<Object> auditTransport(@Valid @RequestBody BaggageTransportAuditDTO dto) {
        return compensationInfoApi.auditTransport(dto);
    }

    @GetMapping("/transport/reviewers/{transportId}")
    @ApiOperation(value = "查询运输单可选审核人")
    public BaseResult<Object> getTransportReviewers(@PathVariable(value = "transportId") Long transportId,
            @ApiParam(value = "任务ID，可选") @RequestParam(required = false) String taskId) {
        return compensationInfoApi.getTransportReviewers(transportId, taskId);
    }

    @PostMapping("/transport/reviewers/save")
    @ApiOperation(value = "保存运输单审核人")
    public BaseResult<Object> saveTransportReviewers(@Valid @RequestBody BaggageTransportReviewerSaveDTO dto) {
        return compensationInfoApi.saveTransportReviewers(dto);
    }
}
