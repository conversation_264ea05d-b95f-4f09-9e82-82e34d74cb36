package com.swcares.aps.staff.bff.controller.ground;

import com.swcares.aps.component.workflow.dto.AuditorInfoDTO;
import com.swcares.aps.ground.AssuranceQueryApi;
import com.swcares.aps.ground.models.assurance.domain.AssuranceOrderDomain;
import com.swcares.aps.ground.models.assurance.dto.AssuranceOrderServiceListRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderFullDetailVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceDetailsVo;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderServiceListVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditHistoryVO;
import com.swcares.aps.ground.models.assurance.vo.AssuranceWorkflowAuditorOrderVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @ClassName：AssuranceQueryController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： fxr
 * @Date： 2024/9/27 16:30
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/verifyQuery")
@Api(tags = {"保障单业务信息查询接口"})
@ApiVersion({"保障单相关Api"})
public class AssuranceQueryController extends BaseController {

    @Autowired
    private AssuranceQueryApi assuranceQueryApi;

    @PostMapping("/h5Page")
    @ApiOperation(value = "H5分页查询供保障单列表信息")
    public PagedResult<List<AssuranceOrderServiceListVO>> h5PageQueryOrderList(@RequestBody AssuranceOrderServiceListRequest request){
        return assuranceQueryApi.pageQueryOrderServiceList(request);
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "根据保障单服务项id获取保障单详情信息")
    public BaseResult<AssuranceOrderServiceDetailsVo> getOrderServiceDetails(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        BaseResult<AssuranceOrderServiceDetailsVo> result = assuranceQueryApi.getOrderServiceDetails(id);
        AssuranceOrderServiceDetailsVo orderServiceDetailsVo = result.getData();
        if(ObjectUtils.isEmpty(orderServiceDetailsVo)){return result;}
        String orderId = orderServiceDetailsVo.getOrderInfoDO().getId();
        List<AssuranceWorkflowAuditorOrderVO> workflowAuditorOrderVOS = assuranceQueryApi.
                getCurrentUserAuthOrders(Arrays.asList(orderId)).getData();
        if(CollectionUtils.isEmpty(workflowAuditorOrderVOS)){return result;}

        Map<String, String> orderIdToTaskId = workflowAuditorOrderVOS.
                stream().
                collect(Collectors.toMap(t -> t.getOrderId(), t -> t.getTaskId(), (o, n) -> n));
        String taskId = orderIdToTaskId.get(orderId);
        if(StringUtils.isNotEmpty(taskId)){
            orderServiceDetailsVo.setTaskId(taskId);
            orderServiceDetailsVo.setNeedAudit("Y");
        }
        return result;
    }

    @GetMapping("/getAssuranceOrderDetail/{id}")
    @ApiOperation(value = "查看详情时获取保障单信息")
    public BaseResult<AssuranceOrderFullDetailVO> getFullAssuranceOrderInfo(@PathVariable @ApiParam(value = "主键id", required = true) String id){
        AssuranceOrderDomain orderDomain = assuranceQueryApi.getFullAssuranceOrderInfo(id).getData();
        if(orderDomain==null){return ok(); }
        AssuranceOrderFullDetailVO vo = AssuranceOrderFullDetailVO.createPcDetailVOByDomain(orderDomain);
        return ok(vo);
    }

    @GetMapping("/workflow/findAuditHistory")
    @ApiOperation(value = "查看审核记录")
    public BaseResult<List<AssuranceWorkflowAuditHistoryVO>> findAuditHistory(@ApiParam(value = "保障单id")String orderId) {
        return assuranceQueryApi.findAuditHistory(orderId);
    }

    @GetMapping("/workflow/findReviewer")
    @ApiOperation(value = "条件查询可选审核人，前端调用")
    public BaseResult<List<AuditorInfoDTO>> findReviewer(@ApiParam(value = "部门id") Long orgId, @ApiParam(value = "节点id",required=true) String taskId, @ApiParam(value = "审核人信息：姓名/工号") String userInfo, @ApiParam(value = "赔偿单id")String orderId) {
        BaseResult<List<AuditorInfoDTO>> reviewerResult = assuranceQueryApi.findReviewer(orderId);
        List<AuditorInfoDTO> reviewerVOS = reviewerResult.getData();
        if(CollectionUtils.isEmpty(reviewerVOS)){return reviewerResult;}

        if(orgId!=null){
            reviewerVOS=reviewerVOS.stream().filter(t->t.getOrgId().longValue()==orgId.longValue()).collect(Collectors.toList());
            reviewerResult.setData(reviewerVOS);
        }

        if(CollectionUtils.isEmpty(reviewerVOS)){return reviewerResult;}
        if(StringUtils.isNotBlank(userInfo)){
            reviewerVOS = reviewerVOS.stream().filter(t -> StringUtils.contains(t.getReviewerNameNo(), userInfo)).collect(Collectors.toList());
            reviewerResult.setData(reviewerVOS);
        }

        return reviewerResult;
    }
}
