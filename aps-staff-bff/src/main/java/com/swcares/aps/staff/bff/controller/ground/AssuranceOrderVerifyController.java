package com.swcares.aps.staff.bff.controller.ground;

import com.swcares.aps.ground.models.assurance.dto.AssuranceVerifyPaxScanQueryRequest;
import com.swcares.aps.ground.models.assurance.vo.AssuranceOrderVerifyQueryResultVO;
import com.swcares.aps.staff.bff.service.AssuranceOrderVerifyQueryService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * @ClassName：AssuranceCommandController
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/8/23 10:18
 * @version： v1.0
 */
@RestController
@RequestMapping("/assurance/busi/orderVerify")
@Api(tags = {"保障单业务操作接口"})
@ApiVersion({"保障单相关Api"})
@Slf4j
public class AssuranceOrderVerifyController extends BaseController {

    @Autowired
    private AssuranceOrderVerifyQueryService assuranceOrderVerifyQueryService;



    @PostMapping("/verify/scan")
    @ApiOperation(value = "扫码获取核销详情【H5扫登机牌或核销码】")
    public PagedResult<List<AssuranceOrderVerifyQueryResultVO>> assuranceWaitVerifyByScan(@RequestBody AssuranceVerifyPaxScanQueryRequest request){
        return ok(assuranceOrderVerifyQueryService.assuranceWaitVerifyByScan(request));
    }

    @GetMapping("/verify/byIdNo")
    @ApiOperation(value = "输入证件号获取核销详情")
    public PagedResult<List<AssuranceOrderVerifyQueryResultVO>> assuranceVerifyQueryByIdNo(String idNo){
        return ok(assuranceOrderVerifyQueryService.assuranceWaitVerifyByIdNo(idNo));
    }


    @PostMapping("/verify/waitVerifyNum")
    @ApiOperation(value = " 该旅客+航班+航班日期+服务项下剩余待核销数量 ")
    public BaseResult<Object> waitVerifyNum(@RequestBody AssuranceVerifyPaxScanQueryRequest request){
        return ok(assuranceOrderVerifyQueryService.waitVerifyNum(request));
    }

}
