package com.swcares.aps.usercenter.impl.tenant.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.cpe.coordinate.model.basicdata.dto.CustomerDTO;
import com.swcares.aps.usercenter.impl.tenant.service.CoordinateTenantService;
import com.swcares.baseframe.common.enums.StatusEnum;
import com.swcares.common.sys.dto.TenantAllDTO;
import com.swcares.common.sys.dto.TenantDTO;
import com.swcares.common.sys.entity.Tenant;
import com.swcares.common.sys.service.TenantService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR> <PERSON>
 * @Classname CoordinateTenantServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/27 15:10
 * @Version 1.0
 */
@Service
@Slf4j
public class CoordinateTenantServiceImpl implements CoordinateTenantService {
    @Autowired
    TenantService tenantService;

    @Value("${swcares.tenantConfig.baseDomain}")
    String basicDomain;
    @Override
    public TenantDTO saveOrUpdateTenant(CustomerDTO customerDTO) {
        customerDTO.setCode(customerDTO.getCode().toUpperCase());
        LambdaQueryWrapper<Tenant> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tenant::getTenantCode, customerDTO.getCode());
        Tenant tenant = tenantService.getOne(queryWrapper);

        TenantDTO tenantDTO = new TenantDTO();
        if (tenant != null){
            BeanUtil.copyProperties(tenant, tenantDTO);
        }

        tenantDTO.setTenantCode(customerDTO.getCode());
        tenantDTO.setTenantName(customerDTO.getShortName() == null ? customerDTO.getName() : customerDTO.getShortName());
        tenantDTO.setUserName("admin_" + tenantDTO.getTenantCode());
        tenantDTO.setDomainName(basicDomain + "/" + customerDTO.getCode().toLowerCase());
        tenantDTO.setCompanyName(customerDTO.getName());
        tenantDTO.setContactor(customerDTO.getContactPerson());
        tenantDTO.setContactorPhone(customerDTO.getContactPhone());
        tenantDTO.setContactorEmail(customerDTO.getContactEmail());
        tenantDTO.setStatus(customerDTO.isDisabled() ? StatusEnum.DISABLED : StatusEnum.ENABLE);
        tenantDTO.setRemark(customerDTO.getRemark());

        TenantAllDTO allDto = new TenantAllDTO();
        allDto.setTenant(tenantDTO);

        if (tenantDTO.getId() == null) {
            tenantService.saveAll(allDto);
        } else {
            tenantService.saveTenant(tenantDTO);
        }
        return tenantDTO;
    }
}
