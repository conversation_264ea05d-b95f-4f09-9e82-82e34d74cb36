package com.swcares.aps.usercenter.impl.tenant.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.swcares.aps.usercenter.impl.tenant.mapper.TenantAdditionalInfoMapper;
import com.swcares.aps.usercenter.impl.tenant.service.TenantAdditionalInfoService;
import com.swcares.aps.usercenter.model.tenant.entity.TenantAdditionalInfo;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> <PERSON>
 * @Classname TenantAdditionalInfoServiceImpl
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/25 11:48
 * @Version 1.0
 */
@Service
public class TenantAdditionalInfoServiceImpl extends ServiceImpl<TenantAdditionalInfoMapper, TenantAdditionalInfo> implements TenantAdditionalInfoService {
    @Override
    public TenantAdditionalInfo getItem(String tenantId, String item) {
        LambdaQueryWrapper<TenantAdditionalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantAdditionalInfo::getTenantId, tenantId)
                .eq(TenantAdditionalInfo::getAdditionalInfoItem, item);
        return this.getOne(queryWrapper);
    }

    @Override
    public List<TenantAdditionalInfo> getAll(String tenantId) {
        LambdaQueryWrapper<TenantAdditionalInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(TenantAdditionalInfo::getTenantId, tenantId);
        return this.list(queryWrapper);
    }
}
