package com.swcares.aps.usercenter.impl.passengerCategory.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.usercenter.impl.passengerCategory.service.PassengerCategoryService;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryChangeDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto;
import com.swcares.aps.usercenter.model.passengerCategory.dto.PassengerCategoryStateDto;
import com.swcares.aps.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategorySentPageVo;
import com.swcares.aps.usercenter.model.passengerCategory.vo.PassengerCategoryVo;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.passengerCategory.controller.PassengerCategoryController <br>;
 * Description：旅客类别配置控制层 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 16:36 <br>;
 * @version v1.0 <br>;
 */
@RestController
@RequestMapping({"/bd/passCategory"})
@Api(tags = {"旅客类别配置"})
@ApiVersion({"PassengerCategory Api v1.0"})
public class PassengerCategoryController extends BaseController {

    @Resource
    private PassengerCategoryService passengerCategoryService;

    @GetMapping("/send_pass_category_page")
    @ApiOperation("旅客类别配置分页列表查询")
    public PagedResult<List<PassengerCategorySentPageVo>> getPassengerCategoryConfigurepage(PassengerCategorySentPageDto dto) {
        IPage<PassengerCategorySentPageVo> result = passengerCategoryService.sentPage(dto);
        if(result==null){
            return PagedResult.ok(null,
                    0l, 0l,
                    dto.getPageSize(), dto.getPageNumber());
        }
        return this.ok(result);
    }

    @PostMapping({"/save_pass_category"})
    @ApiOperation("新增旅客类别配置")
    public BaseResult<String> savePassengerCategoryConfigure(@Validated(PassengerCategoryChangeDto.save.class) @RequestBody PassengerCategoryChangeDto dto) {
        passengerCategoryService.savePassengerCategoryConfigure(dto);
        return this.ok();
    }

    @PostMapping({"/update_pass_category"})
    @ApiOperation("更新旅客类别配置")
    public BaseResult<String> updatePassengerCategoryConfigure(@Validated(PassengerCategoryChangeDto.save.class) @RequestBody PassengerCategoryChangeDto dto) {
        passengerCategoryService.updatePassengerCategoryConfigure(dto);
        return this.ok();
    }

    @PostMapping({"/update_pass_category_state"})
    @ApiOperation("更新旅客类别配置状态")
    public BaseResult<String> updatePassengerCategoryConfigureState(@Validated @RequestBody PassengerCategoryStateDto dto) {
        passengerCategoryService.updatePassengerCategoryConfigureState(dto);
        return this.ok();
    }

    @PostMapping("/passengerCategory")
    @ApiOperation(value = "旅客类别下拉框")
    BaseResult<List<PassengerCategoryVo>> passengerCategory(@RequestBody PassengerCategoryChangeDto dto) {
        return ok(passengerCategoryService.passengerCategory(dto));
    }

    @PostMapping({"/list_pass_category"})
    @ApiOperation("旅客类别配置列表查询")
    public BaseResult<List<PassengerCategoryConfigureDepository>> listPassengerCategoryConfigure(@RequestBody List<String> codes) {
        List<PassengerCategoryConfigureDepository> results=passengerCategoryService.listPassengerCategoryConfigure(codes);
        return this.ok(results);
    }
}
