package com.swcares.aps.usercenter.impl.uc.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.swcares.aps.usercenter.impl.uc.service.ReaptvUserService;
import com.swcares.aps.usercenter.model.uc.dto.UserBelongToOrgPageDTO;
import com.swcares.aps.usercenter.model.uc.vo.*;
import com.swcares.aps.usercenter.vo.UserBriefInfoVO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.annotation.Desensitized;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.cons.CommonErrors;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import com.swcares.baseframe.utils.constants.GlobalConstants;
import com.swcares.common.uc.dto.*;
import com.swcares.common.uc.vo.UserEnterpriseAdminVO;
import com.swcares.common.uc.vo.UserVO;
import com.swcares.components.sys.auditlog.aop.NoSysAuditLogAnnotation;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.constraints.Size;
import java.util.Iterator;
import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.impl.uc.controller.UserController <br>;
 * Description：藏航 用户中心 前端控制器 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/26 17:50 <br>;
 * @version v1.0 <br>;
 */
@RestController
@RequestMapping("/uc/reaptv_user")
@Api(tags = "用户接口")
@ApiVersion(value = "ReaptvUser v1.0")
@Validated
public class ReaptvUserController extends BaseController {
    @Resource(name = "reaptvUserServiceImpl")
    private ReaptvUserService userService;


    @PostMapping("/save")
    @ApiOperation(value = "新建用户记录 ")
    public BaseResult<String> save(@Validated @RequestBody UserAddDTO dto) {
        String password = userService.save(dto);
        return ok(password);
    }

    @PostMapping("/delete/{id}")
    @ApiOperation(value = "通过ID删除用户记录")
    public BaseResult<Object> delete(@PathVariable @ApiParam(value = "主键id") Long id) {
        boolean deleted = userService.logicRemoveById(id);
        if (!deleted) {
            throw new BusinessException(CommonErrors.DELETE_ERROR);
        }
        return ok();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "通过ID删除用户记录，URL传参")
    public BaseResult<Object> deleteById(@ApiParam(value = "主键id") Long id) {
        return delete(id);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改用户记录")
    public BaseResult<?> update(@Validated @RequestBody UserUpdateDTO dto) {
        userService.update(dto);
        return ok();
    }

    @GetMapping("/get/{id}")
    @ApiOperation(value = "用户详情显示接口")
    public BaseResult<ReaptvUserDetailVO> get(@PathVariable @ApiParam(value = "主键id", required = true) Long id) {
        ReaptvUserDetailVO userDetailVO = userService.getUserDetail(id);
        return ok(userDetailVO);
    }

    @GetMapping("/get")
    @ApiOperation(value = "用户详情显示接口，URL传参")
    public BaseResult<ReaptvUserDetailVO> getById(@RequestParam @ApiParam(value = "主键id", required = true) Long id) {
        ReaptvUserDetailVO userDetailVO = userService.getUserDetail(id);
        return ok(userDetailVO);
    }

    @PostMapping("/page")
    @ApiOperation(value = "条件分页查询用户记录")
    @Desensitized(clazz = UserVO.class)
    public PagedResult<List<ReaptvUserVO>> page(@RequestBody UserPagedDTO dto) {
        IPage<ReaptvUserVO> result = userService.page(dto);
        determineReaptvUserVO(result);
        return ok(result);
    }

    @PostMapping("/page_ca")
    @ApiOperation(value = "条件分页查询用户记录")
    public PagedResult<List<ReaptvUserVO>> pageCa(@RequestBody UserPagedCADTO dto) {
        IPage<ReaptvUserVO> result = userService.pageCa(dto);
        determineReaptvUserVO(result);
        return ok(result);
    }

    @PostMapping("/batch_auth")
    @ApiOperation(value = "批量授权")
    public BaseResult<?> authorize(@Validated @RequestBody UserAuthorizeDTO dto) {
        userService.authorize(dto);
        return ok();
    }

    @PostMapping("/batch_auth_delete")
    @ApiOperation(value = "批量删除授权")
    public BaseResult<?> authorizeDelete(@Validated @RequestBody UserAuthorizeDTO dto) {
        userService.authorizeDelete(dto);
        return ok();
    }

    @PostMapping("/reset_pwd/{id}")
    @ApiOperation(value = "用户密码重置接口")
    public BaseResult<String> resetPassword(@PathVariable @ApiParam(value = "用户ID") Long id) {
        String password = userService.resetPassword(id);
        return ok(password);
    }

    @PostMapping("/reset_pwd")
    @ApiOperation(value = "用户密码重置接口，URL传参")
    public BaseResult<String> resetPasswordById(@ApiParam(value = "用户ID") Long id) {
        return resetPassword(id);
    }

    @PostMapping("/reset_pwd_by_name")
    @ApiOperation(value = "通过用户名重置用户密码接口")
    public BaseResult<String> resetPasswordByName(@RequestParam @ApiParam(value = "用户名") String username) {
        String password = userService.resetPasswordByName(username);
        return ok(password);
    }

    @PostMapping("/change_status")
    @ApiOperation(value = "用户状态变更（启用/停用/解锁)")
    public BaseResult<?> changeStatus(@Validated @RequestBody UserChangeStatusDTO dto) {
        userService.changeStatus(dto);
        return ok();
    }

    @PostMapping("/change_status_by_name")
    @ApiOperation(value = "通过用户名修改用户状态（启用/停用/解锁)")
    public BaseResult<?> changeStatusByName(@Validated @RequestBody UserChangeStatusByNameDTO dto) {
        userService.changeStatusByName(dto);
        return ok();
    }

    @PostMapping("/modify_pwd")
    @NoSysAuditLogAnnotation
    @ApiOperation(value = "用户修改密码")
    public BaseResult<?> modifyPassword(@Validated @RequestBody UserChangePasswordDTO dto) {
        userService.changePassword(dto);
        return ok();
    }

    @GetMapping("/ent_admin")
    @ApiOperation(value = "获取企业管理员", notes = "在header中传租户ID：tenantId")
    public BaseResult<UserEnterpriseAdminVO> entAdmin() {
        UserEnterpriseAdminVO adminVO = userService.getEnterpriseAdministrator();
        return ok(adminVO);
    }

    @PostMapping("/choose_page")
    @ApiOperation(value = "选择用户条件分页查询")
    public PagedResult<List<ReaptvUserEmployeeVO>> choosePage(@RequestBody UserPagedDTO dto) {
        IPage<ReaptvUserEmployeeVO> result = userService.choosePage(dto);
        determineReaptvUserEmployeeVO(result);
        return ok(result);
    }

    @ApiOperation(value = "根据用户ID，获取用户对象，一次最多支持（999）个。如需登录即可访问，使用/common/user/get_by_ids接口。")
    @PostMapping("/get_by_ids")
    public BaseResult<List<ReaptvUserAllVO>> getByIds(@Size(min = 1, max = GlobalConstants.BRANCH_MAX_SIZE) @RequestBody List<Long> ids) {
        List<ReaptvUserAllVO> result = userService.getByIds(ids);
        determineReaptvUserAllVO(result);
        return ok(result);
    }

    @PostMapping("/work_for_org")
    @ApiOperation(value = "根据部门ID，获取工作机构在这个部门的人员")
    public BaseResult<List<ReaptvUserEmployeeVO>> workForOrg(@RequestBody UserWorkForOrgDTO dto) {
        List<ReaptvUserEmployeeVO> result = userService.workForOrg(dto);
        determineReaptvUserEmployeeVO(result);
        return ok(result);
    }

    @PostMapping("/get_by_pos")
    @ApiOperation(value = "根据岗位代码获取用户")
    public BaseResult<List<ReaptvUserVO>> getByPos(@RequestBody UserForPositionDTO dto) {
        List<String> codeList = dto.getJobPositionCodeList();
        List<ReaptvUserVO> result = userService.getByJobPositionCode(codeList);
        determineReaptvUserVO(result);
        return ok(result);
    }

    @PostMapping("/belong_to_org")
    @ApiOperation(value = "根据部门ID，获取归属机构在这个部门下的用户id")
    public BaseResult<List<ReaptvUserEmployeeVO>> belongToOrg(@RequestBody UserBelongToOrgDTO dto) {
        List<ReaptvUserEmployeeVO> result = userService.belongToOrg(dto);
        determineReaptvUserEmployeeVO(result);
        return ok(result);
    }

    @PostMapping("/belong_to_org_page")
    @ApiOperation(value = "根据部门ID，获取归属机构在这个部门下的用户id分页")
    public BaseResult<List<ReaptvUserEmployeeVO>> belongToOrgPage(@RequestBody UserBelongToOrgPageDTO dto) {
        IPage<ReaptvUserEmployeeVO> result = userService.belongToOrgPage(dto);
        determineReaptvUserEmployeeVO(result.getRecords());
        return ok(result);
    }

    @GetMapping("/get_user_by_wechat_miniapp_code")
    @ApiOperation(value="根据微信小程序的code，获取绑定的用户账号")
    public BaseResult<String> getUserAccountByWechatMiniappCode(
            @RequestParam(required = true) @ApiParam(value = "微信小程序的client_id") String clientId,
            @RequestParam(required = true) @ApiParam(value = "微信小程序中获取的code") String code){
        ReaptvUserVO user = userService.getUserAccountByWechatMiniappCode(clientId,code);
        if (user == null){
            //如果没有绑定用户，返回空值
            return ok();
        }

        return ok(user.getName());
    }

    @GetMapping("/user_brief_info")
    @ApiOperation(value="根据id获取用户的简要信息")
    public BaseResult<UserBriefInfoVO> getUserBriefInfo(@RequestParam(name = "id", required = true)Long id) {
        UserBriefInfoVO result = userService.getBriefInfoById(id);
        return BaseResult.ok(result);
    }

    private boolean determineWhetherToDisplay() {
        LoginUserDetails user = UserContext.getCurrentUser();
        if (user.getUserType() == 1 || user.getUserType() == 4){
            return false;
        }
        return true;
    }

    private void determineReaptvUserVO(IPage<ReaptvUserVO> result) {

        if (!determineWhetherToDisplay()) {
            return;
        }

        List<ReaptvUserVO> data = result.getRecords();
        Iterator<ReaptvUserVO> iterator = data.iterator();
        while (iterator.hasNext()) {
            ReaptvUserVO next = iterator.next();
            if (next == null || next.getInfoIsShow() == null) {
                iterator.remove();
            }else if(!next.getInfoIsShow()) {
                next.setIdCard(null);
                next.setOrganizationName(null);
                next.setGender(null);
                next.setJobNumber(null);
                next.setEmployeeCode(null);
                next.setWxOpenid(null);
                next.setIdCard(null);
                next.setJobPosition(null);
                next.setPhone(null);
                next.setWorkTerminal(null);
                next.setEmailAddress(null);
                next.setDeleted(null);
            }
        }
    }

    private void determineReaptvUserVO(List<ReaptvUserVO> result) {

        if (!determineWhetherToDisplay()) {
            return;
        }

        Iterator<ReaptvUserVO> iterator = result.iterator();
        while (iterator.hasNext()) {
            ReaptvUserVO next = iterator.next();
            if (next == null || next.getInfoIsShow() == null) {
                iterator.remove();
            }else if(!next.getInfoIsShow()) {
                next.setIdCard(null);
                next.setOrganizationName(null);
                next.setGender(null);
                next.setJobNumber(null);
                next.setEmployeeCode(null);
                next.setWxOpenid(null);
                next.setIdCard(null);
                next.setJobPosition(null);
                next.setPhone(null);
                next.setWorkTerminal(null);
                next.setEmailAddress(null);
                next.setDeleted(null);
            }
        }
    }

    private void determineReaptvUserEmployeeVO(IPage<ReaptvUserEmployeeVO> result) {
        if (!determineWhetherToDisplay()) {
            return;
        }

        List<ReaptvUserEmployeeVO> data = result.getRecords();
        Iterator<ReaptvUserEmployeeVO> iterator = data.iterator();
        while (iterator.hasNext()) {
            ReaptvUserEmployeeVO next = iterator.next();
            if (next == null || next.getInfoIsShow() == null) {
                iterator.remove();
            }else if(!next.getInfoIsShow()) {
                next.setOrganizationName(null);
                next.setJobNumber(null);
                next.setEmployeeCode(null);
                next.setWxOpenid(null);
                next.setWorkTerminal(null);
            }
        }
    }

    private void determineReaptvUserAllVO(List<ReaptvUserAllVO> result) {
        if (!determineWhetherToDisplay()) {
            return;
        }

        Iterator<ReaptvUserAllVO> iterator = result.iterator();
        while (iterator.hasNext()) {
            ReaptvUserAllVO next1 = iterator.next();
            ReaptvEmployeeVO next = next1.getEmployee();
            if (next == null || next.getInfoIsShow() == null) {
                iterator.remove();
            }else if(!next.getInfoIsShow()) {
                next.setOrganizationName(null);
                next.setJobNumber(null);
                next.setEmployeeCode(null);
                next.setWxOpenid(null);
                next.setWorkTerminal(null);
            }
        }
    }

    private void determineReaptvUserEmployeeVO (List<ReaptvUserEmployeeVO> result) {
        if (!determineWhetherToDisplay()) {
            return;
        }

        Iterator<ReaptvUserEmployeeVO> iterator = result.iterator();
        while (iterator.hasNext()) {
            ReaptvUserEmployeeVO next = iterator.next();
            if (next == null || next.getInfoIsShow() == null) {
                iterator.remove();
            }else if(!next.getInfoIsShow()) {
                next.setOrganizationName(null);
                next.setJobNumber(null);
                next.setEmployeeCode(null);
                next.setWxOpenid(null);
                next.setWorkTerminal(null);
            }
        }
    }
}
