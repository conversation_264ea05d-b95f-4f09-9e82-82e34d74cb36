########################################## nacos配置  ###################################
spring:
  cloud:
    nacos:
      discovery:
        server-addr: 192.168.17.174:8848,192.168.17.175:8848,192.168.17.176:8848
        namespace: uat-airport
        username: nacos
        password: Nc@Aps!2024
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        refresh-enabled: true
        file-extension: yml
        namespace: uat-airport
        group: uat
        username: nacos
        password: Nc@Aps!2024
        shared-configs:
          - data-id: application-bash-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
            refresh: true
            group: uat
logging:
  config: classpath:logback.xml
  level:
    com.swcares: info
    org.springframework: warn