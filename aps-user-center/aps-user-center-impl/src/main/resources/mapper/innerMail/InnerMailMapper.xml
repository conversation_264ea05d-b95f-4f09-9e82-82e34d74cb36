<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.innermail.mapper.InnerMailMapper">

    <select id="sentPage" resultType="com.swcares.aps.usercenter.model.innermail.vo.InnerMailSentPageVo" databaseId="oracle">
        SELECT
            inter.*,
            ( SELECT count( rec.id ) FROM msg_interior_receive_info rec WHERE rec.msg_id = inter.id AND rec.status = 1 ) AS successNum,
            ( SELECT count( rec.id ) FROM msg_interior_receive_info rec WHERE rec.msg_id = inter.id AND rec.status = 2 ) AS failNum,
            miri.IS_READ AS isRead,
            CASE WHEN INTER.CREATED_BY = 'system' THEN INTER.CREATED_BY WHEN UE1.NAME IS NULL THEN INTER.CREATED_BY ELSE ue1.name || UE1.JOB_NUMBER END AS createdBy,
            CASE WHEN UE2.NAME IS NULL THEN miri.RECIPIENT ELSE ue2.name || UE2.JOB_NUMBER END AS recipient
        FROM
            msg_interior_depository inter
            LEFT JOIN uc_user uu1 ON uu1.name = inter.created_by
            LEFT JOIN uc_employee ue1 ON ue1.id = uu1.employee_id
            LEFT JOIN msg_interior_receive_info miri ON inter.id = miri.msg_id
            LEFT JOIN uc_user uu2 ON uu2.name = miri.RECIPIENT
            LEFT JOIN uc_employee ue2 ON ue2.id = uu2.employee_id
        WHERE
            inter.deleted = 0
            AND miri.RECEIVE_MODE = '3'
        <if test="dto.systemCode != null and dto.systemCode != ''">
            and inter.system_code = #{dto.systemCode}
        </if>
        <if test="dto.businessType != null and dto.businessType != ''">
            and inter.BUSINESS_TYPE in
            <foreach collection="dto.businessTypeList" close=")" open="(" separator="," item="type">
                #{type}
            </foreach>
        </if>
        <if test="dto.masterId != null and dto.masterId != ''">
            and inter.master_id = #{dto.masterId}
        </if>
        <if test="dto.msgTitle != null and dto.msgTitle != ''">
            and inter.msg_title like #{dto.msgTitle}
        </if>
        <if test="dto.msgContent != null and dto.msgContent != ''">
            and inter.msg_content like #{dto.msgContent}
        </if>
        <if test="dto.createdTimeStart != null">
            and inter.created_time <![CDATA[ >= ]]>  to_date(#{dto.createdTimeStart},'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="dto.createdTimeEnd != null">
            and inter.created_time <![CDATA[ < ]]> (to_date(#{dto.createdTimeEnd},'yyyy-MM-dd hh24:mi:ss') + 1)
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            and (UE1.JOB_NUMBER  like #{dto.createdBy} or ue1.NAME like #{dto.createdBy})
        </if>
        <if test="dto.createUser != null and dto.createUser != ''">
            and uu1.id = #{dto.createUser}
        </if>
        <if test="dto.recipient != null and dto.recipient != ''">
            and (UE2.EMPLOYEE_CODE like #{dto.recipient} or ue2.NAME like #{dto.recipient})
        </if>
        <if test="dto.isRead != null and dto.isRead != ''">
            and miri.IS_READ = #{dto.isRead}
        </if>
        <if test="dto.urgency != null and dto.urgency != ''">
            and inter.urgency = #{dto.urgency}
        </if>
        order by inter.created_time DESC
    </select>

    <select id="receivePage" resultType="com.swcares.components.msg.vo.InteriorReceiveInfoVO" databaseId="oracle">
        <include refid="receiveSql"/>
    </select>
    <select id="receiveList" resultType="com.swcares.components.msg.vo.InteriorReceiveInfoVO">
        <include refid="receiveSql"/>
    </select>

    <sql id="receiveSql">
        SELECT
            rec.id,
            msg.system_code,
            msg.business_type,
            msg.master_id,
            msg.msg_title,
            msg.msg_content,
            msg.target_url,
            msg.h5_target_url,
            msg.urgency,
            rec.status,
            rec.send_time,
            rec.is_read,
            rec.favorites,
            CASE WHEN msg.CREATED_BY = 'system' THEN msg.CREATED_BY WHEN UE2.NAME IS NULL THEN msg.CREATED_BY ELSE ue2.name || UE2.JOB_NUMBER END AS createdBy,
            CASE WHEN UE1.NAME IS NULL THEN rec.RECIPIENT ELSE ue1.name || UE1.JOB_NUMBER END AS recipient,
            msg.send_type
        FROM
            msg_interior_receive_info rec
            LEFT JOIN uc_user uu1 ON rec.RECIPIENT = uu1.name
            LEFT JOIN UC_EMPLOYEE ue1 ON ue1.id = uu1.EMPLOYEE_ID
            LEFT JOIN msg_interior_depository msg ON rec.msg_id = msg.id
            LEFT JOIN uc_user uu2 ON msg.CREATED_BY = uu2.name
            LEFT JOIN UC_EMPLOYEE ue2 ON ue2.id = uu2.EMPLOYEE_ID
        WHERE
            1 = 1
            AND uu1.id = #{userId}
            AND rec.deleted = 0
            AND rec.RECEIVE_MODE = '3'
        <if test="dto.content != null">
            and (msg.created_by like #{dto.content} or msg.msg_content like #{dto.content})
        </if>
        <if test="dto.favorites != null">
            and rec.favorites = #{dto.favorites}
        </if>
        <if test="dto.isRead != null">
            and rec.is_read = #{dto.isRead}
        </if>
        <if test="dto.urgency != null and dto.urgency != ''">
            and msg.urgency = #{dto.urgency}
        </if>
        <if test="dto.businessType != null and dto.businessType != ''">
            and msg.BUSINESS_TYPE in
            <foreach collection="dto.businessTypeList" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.sendStartDate != null">
            and rec.send_time <![CDATA[ >= ]]> to_date(#{dto.sendStartDate},'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="dto.sendEndDate != null">
            and rec.send_time <![CDATA[ < ]]> (to_date(#{dto.sendEndDate},'yyyy-MM-dd hh24:mi:ss') + 1)
        </if>
        <if test="dto.recipient != null and dto.recipient != ''">
            and (UE1.JOB_NUMBER  like #{dto.recipient} or ue1.name like #{dto.recipient})
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            and (UE2.JOB_NUMBER  LIKE #{dto.createdBy} or ue2.name like #{dto.createdBy})
        </if>
        order by rec.is_read ASC, rec.send_time desc
    </sql>

    <select id="getUnreadCountByLevel" resultType="com.swcares.aps.usercenter.model.innermail.vo.InnerMailUnreadCountByLevelVO">
        select
            inter.URGENCY,
            count(inter.id) as unread_count
        from
            msg_interior_depository inter
            inner JOIN msg_interior_receive_info miri ON inter.id = miri.msg_id and miri.is_read = 0
            inner JOIN uc_user uu ON uu.name = miri.RECIPIENT
        where
            uu.id = #{userId}
            and miri.deleted = 0
            and miri.RECEIVE_MODE='3'
            <if test="businessType != null and businessType != ''">
                and inter.BUSINESS_TYPE = #{businessType}
            </if>
        group by URGENCY
    </select>

    <select id="getDistinctBusinessType" resultType="com.swcares.aps.usercenter.model.innermail.vo.InnerMailStaticsByBusinessTypeVO">
        select
        distinct inter.BUSINESS_TYPE
        from
        msg_interior_depository inter
        INNER JOIN msg_interior_receive_info miri ON inter.id = miri.msg_id and  miri.RECEIVE_MODE = '3'
        inner JOIN uc_user uu ON uu.name = miri.RECIPIENT
        where
        uu.id = #{userId}
        and miri.deleted = 0
        <if test="businessType != null and businessType != ''">
            and inter.BUSINESS_TYPE = #{businessType}
        </if>
    </select>

    <select id="getUnreadCountByBusinessType" resultType="com.swcares.aps.usercenter.model.innermail.vo.InnerMailStaticsByBusinessTypeVO">
        select
        inter.BUSINESS_TYPE,
        sum(1-miri.is_read) as unread_count
        from
        msg_interior_depository inter
        INNER JOIN msg_interior_receive_info miri ON inter.id = miri.msg_id and  miri.RECEIVE_MODE = '3'
        inner JOIN uc_user uu ON uu.name = miri.RECIPIENT
        where
        uu.id = #{userId}
        and miri.deleted = 0
        <if test="businessType != null and businessType != ''">
            and inter.BUSINESS_TYPE = #{businessType}
        </if>
        and miri.send_time <![CDATA[ >= ]]> #{startDate}
        group by BUSINESS_TYPE
    </select>

    <select id="getReceiveInfoByBusinessType" resultType="com.swcares.components.msg.vo.InteriorReceiveInfoVO" databaseId="oracle">
        SELECT
        rec.id,
        msg.system_code,
        msg.business_type,
        msg.master_id,
        msg.msg_title,
        msg.msg_content,
        msg.target_url,
        msg.h5_target_url,
        msg.urgency,
        rec.status,
        rec.send_time,
        rec.is_read,
        rec.favorites,
        CASE WHEN msg.CREATED_BY = 'system' THEN msg.CREATED_BY WHEN UE2.NAME IS NULL THEN msg.CREATED_BY ELSE ue2.name || UE2.JOB_NUMBER END AS createdBy,
        CASE WHEN UE1.NAME IS NULL THEN rec.RECIPIENT ELSE ue1.name || UE1.JOB_NUMBER END AS recipient,
        msg.send_type,
        CASE WHEN UE2.INFO_IS_SHOW = 1 THEN UE2.PHONE END AS SENDER_PHONE_NUMBER,
        UE2.PHOTO_RUL AS SENDER_PHOTO
        FROM
        msg_interior_receive_info rec
        LEFT JOIN uc_user uu1 ON rec.RECIPIENT = uu1.name
        LEFT JOIN UC_EMPLOYEE ue1 ON ue1.id = uu1.EMPLOYEE_ID
        LEFT JOIN msg_interior_depository msg ON rec.msg_id = msg.id
        LEFT JOIN uc_user uu2 ON msg.CREATED_BY = uu2.name
        LEFT JOIN UC_EMPLOYEE ue2 ON ue2.id = uu2.EMPLOYEE_ID
        WHERE
        1 = 1
        AND uu1.id = #{userId}
        AND rec.deleted = 0
        AND rec.RECEIVE_MODE = '3'
        <if test="dto.content != null">
            and (msg.created_by like #{dto.content} or msg.msg_content like #{dto.content})
        </if>
        <if test="dto.favorites != null">
            and rec.favorites = #{dto.favorites}
        </if>
        <if test="dto.isRead != null">
            and rec.is_read = #{dto.isRead}
        </if>
        <if test="dto.urgency != null and dto.urgency != ''">
            and msg.urgency = #{dto.urgency}
        </if>
        <if test="dto.businessType != null and dto.businessType != ''">
            and msg.BUSINESS_TYPE in
            <foreach collection="dto.businessTypeList" item="type" separator="," open="(" close=")">
                #{type}
            </foreach>
        </if>
        <if test="dto.sendStartDate != null">
            and rec.send_time <![CDATA[ >= ]]> to_date(#{dto.sendStartDate},'yyyy-MM-dd hh24:mi:ss')
        </if>
        <if test="dto.sendEndDate != null">
            and rec.send_time <![CDATA[ < ]]> (to_date(#{dto.sendEndDate},'yyyy-MM-dd hh24:mi:ss') + 1)
        </if>
        <if test="dto.recipient != null and dto.recipient != ''">
            and (UE1.EMPLOYEE_CODE like #{dto.recipient} or ue1.name like #{dto.recipient})
        </if>
        <if test="dto.createdBy != null and dto.createdBy != ''">
            and (UE2.EMPLOYEE_CODE LIKE #{dto.createdBy} or ue2.name like #{dto.createdBy})
        </if>
        order by msg.business_type ASC, rec.is_read ASC, msg.urgency desc, rec.send_time desc
    </select>
</mapper>
