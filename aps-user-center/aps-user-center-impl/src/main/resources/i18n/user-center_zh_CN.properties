## 编码使用规则：(5-系统类异常、4-业务类异常)
## 1. 第1位数字表示异常类型
## 2. 第2、3位数字表示业务模块
## 3. 第4、5位数字表示异常编码 
## 402-基础框架用户中心异常使用，后两位资源分配(00-09登录、10-19菜单、20-29客户、30-39机构、40-59用户、60-69人员、70-79角色、80-89权限组、90-99通用接口);
40200=验证码错误
40210=当前父级资源下已存在该资源
40211=所选父级资源不可用
40212=终端类型和父级资源不一致
40213=操作类资源必须设置资源权重
40214=子系统编码与父级资源不一致
40215=资源类型不正确
40216=已存在同名子系统
40217=已存在同名编码
40230=机构已存在根节点请检查后再添加
40231=机构或子机构存在人员
40232=机构编码已存在请重新输入
40233=机构不存在
40240=用户名重复
40241=人员已绑定过用户
40242=用户原密码错误
40243=新密码不能与原密码相同
40244=用户不存在
40245=密码必须是数字字母8到15位
40246=弱用户名
40248=当前用户类型权限不够
40249=用户名必须是数字字母下划线，4到32位，不能以数字开头
40250=管理机构树链路已经存在管理员
40251=没有管理(所属)机构

40260=人员不存在
40261=工号初始化参数没有

40270=该角色已存在
40271=您的管理机构权限不足
40272=没设置管理机构，请联系企业管理员
40273=所选角色的归属权限组被禁用，无法启用该角色

40280=该权限组已存在
40281=所选权限组的创建者已被禁用，权限组无法被启用

40220=租户创建异常
40221=租户资源插入异常
40222=租户不存在
40223=租户无此状态值
40224=租户更新异常
40225=租户名已存在
40226=租户公司名已存在
40227=域名已存在
40228=请勿使用弱用户名，请重新输入
40229=传入数据源参数无法正常建立连接

40290=获取钉钉部门人员数据异常：{0}
40291=同步数据失败

###################以下是aps自身的实现######################
30601=未从基础框架获取到登录对象
30602=未从基础框架获取到用户角色信息
30603=数据权限规则设置未发现一级菜单

50001=旅客类别重复