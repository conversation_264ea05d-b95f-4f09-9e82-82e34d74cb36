<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aps-user-center</artifactId>
        <groupId>com.swcares.aps</groupId>
        <version>1.0.1_aps-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>aps-user-center-impl</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
    </properties>
    <dependencies>
        <!-- 用户中心Stater -->
        <dependency>
            <groupId>com.swcares.common</groupId>
            <artifactId>user-center-starter</artifactId>
            <version>${swcares.version}</version>
        </dependency>
        <dependency>
            <groupId>com.oceanbase</groupId>
            <artifactId>oceanbase-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.owasp.encoder</groupId>
            <artifactId>encoder</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.components</groupId>
            <artifactId>system-file-attachment</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-discovery</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.cloud</groupId>
                    <artifactId>spring-cloud-starter-netflix-ribbon</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-loadbalancer</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-bootstrap</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-com</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.baseframe</groupId>
            <artifactId>common-base</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-user-center-model</artifactId>
        </dependency>
        <!--这个依赖千万不能删除-->
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-component-tenant</artifactId>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>coordinate-util</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.swcares.aps</groupId>
            <artifactId>aps-compensation-remote-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.5.0</version>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                    <!--fork:如果没有该项配置,整个devtools不会起作用 -->
                    <fork>true</fork>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>