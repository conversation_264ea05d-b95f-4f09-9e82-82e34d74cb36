package com.swcares.aps.usercenter.remote.api;


import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.captcha.core.model.CaptchaVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * ClassName：UserCenterApi <br>
 * Description： aps-uc的remote-api<br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 * <AUTHOR> <br>
 * Date 2022/8/15 <br>
 * @version v1.0 <br>
 */
@FeignClient(name= "aps-user-center", path= "/api/aps-user-center", contextId = "aps-user-center")
public interface UserCenterApi {
    @GetMapping("/oauth/token?grant_type=self_check&client_id=passenger_password_auth_mode&client_secret=123456")
    Object getTokenBySelfToken(@RequestParam(name="self_token") String self_token, @RequestParam(name="tenantCode") String tenantCode);


    @GetMapping("/captcha/get")
    public BaseResult<Object> getCaptcha(@RequestBody CaptchaVO captchaVO);

    @PostMapping("/captcha/check")
    public BaseResult<Object> checkCaptcha(@RequestBody CaptchaVO captchaVO);
}
