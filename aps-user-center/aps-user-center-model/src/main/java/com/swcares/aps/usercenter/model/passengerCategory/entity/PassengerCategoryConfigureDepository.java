package com.swcares.aps.usercenter.model.passengerCategory.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.swcares.baseframe.common.base.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * ClassName：com.swcares.reaptv.usercenter.model.com.swcares.reaptv.usercenter.model.passengerCategory.entity.PassengerCategoryConfigureDepository <br>;
 * Description：旅客类别实体类 <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 14:56 <br>;
 * @version v1.0 <br>;
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("PASSENGER_CATEGORY_CONFIGURE")
@ApiModel(value="PASSENGER_CATEGORY_CONFIGURE对象", description = "旅客类别配置表")
public class PassengerCategoryConfigureDepository extends BaseEntity {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "服务类型")
    private String type;

    @ApiModelProperty(value = "服务类型字典ID")
    private Long sysDictionaryDataId;

    @ApiModelProperty(value = "旅客类别")
    private String category;

    @ApiModelProperty(value = "旅客类别编码")
    private String code;

    @ApiModelProperty(value = "描述")
    private String remarks;

    @ApiModelProperty(value = "状态（0-停用，1-启用）")
    private String state;

    // alter table PASSENGER_CATEGORY_CONFIGURE ADD air_code VARCHAR2(2);
    // alter table PASSENGER_CATEGORY_CONFIGURE ADD air_name VARCHAR(30);
    // update PASSENGER_CATEGORY_CONFIGURE SET air_code='3U',air_name='四川航空'
    @ApiModelProperty(value = "所属航空公司")
    private String airCode;

    @ApiModelProperty(value = "所属航空公司名称")
    private String airName;
}
