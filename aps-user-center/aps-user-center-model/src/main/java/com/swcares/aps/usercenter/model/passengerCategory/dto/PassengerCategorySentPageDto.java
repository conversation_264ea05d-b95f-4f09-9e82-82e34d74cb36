package com.swcares.aps.usercenter.model.passengerCategory.dto;

import com.swcares.baseframe.common.base.entity.PagedDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * ClassName：com.swcares.reaptv.usercenter.model.com.swcares.reaptv.usercenter.model.passengerCategory.dto.PassengerCategorySentPageDto <br>;
 * Description：旅客类别配置分页dto <br>;
 * Copyright © 2022 xnky.travelsky.net Inc. All rights reserved. <br>;
 * Company：Aviation Cares Of Southwest Chen Du LTD <br>;
 *
 * <AUTHOR> <br>;
 * date 2022/5/24 15:14 <br>;
 * @version v1.0 <br>;
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value="PassengerCategorySentPageDto分页对象", description="旅客类别配置表")
public class PassengerCategorySentPageDto extends PagedDTO {

    @ApiModelProperty(value = "服务类型")
    private String type;

    @ApiModelProperty(value = "服务类型字典ID")
    private Long sysDictionaryDataId;

    @ApiModelProperty(value = "旅客类别")
    private String category;

    @ApiModelProperty(value = "旅客类别编码")
    private String code;

    @ApiModelProperty(value = "状态（0-停用，1-启用）")
    private String state;

    @ApiModelProperty(value = "航司编码")
    private String airCode;

    private List<String> belongAirline;
}
