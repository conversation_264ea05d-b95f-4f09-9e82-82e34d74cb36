package com.swcares.aps.pssn.bff.controller;

import com.swcares.aps.compensation.model.apply.dto.*;
import com.swcares.aps.compensation.model.apply.vo.*;
import com.swcares.aps.compensation.model.privilege.dto.BelongAirlineDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.ApplyApi;
import com.swcares.aps.compensation.remote.api.privilege.AirlineBusinessPrivilegeApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.controller.CompensationOrderInfoController
 * <br>
 * Description：赔偿单信息 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@Slf4j
@RestController
@RequestMapping("/apply")
@Api(tags = "申领单信息接口")
@ApiVersion(value = "申领单bff接口 v1.0")
@RefreshScope
public class ApplyController extends BaseController {
    @Autowired
    private ApplyApi applyApi;
    @Autowired
    private AirlineBusinessPrivilegeApi airlineBusinessPrivilegeApi;
    @Autowired
    private RestTemplate restTemplate;

    @Value("${ocr.id-card.url}")
    private String OCR_ID_CARD_API_URL;

    @Value("${ocr.bank-card.url}")
    private String OCR_BANK_CARD_API_URL;

    // 身份证识别接口转发
    @PostMapping("/recognize-id-card")
    @ApiOperation(value = "身份证识别")
    public BaseResult<Map<String, Object>> recognizeIdCard(@RequestParam("file") MultipartFile file) {
        return postOcrInterface(OCR_ID_CARD_API_URL, file);
    }

    // 银行卡识别接口转发
    @PostMapping("/recognize-bank-card")
    @ApiOperation(value = "银行卡识别")
    public BaseResult<Map<String, Object>> recognizeBankCard(@RequestParam("file") MultipartFile file) {
        return postOcrInterface(OCR_BANK_CARD_API_URL, file);
    }

    private BaseResult<Map<String, Object>> postOcrInterface(String url, MultipartFile file){
        // 创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);

        // 创建请求体
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.add("file", file.getResource());

        // 创建HttpEntity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(body, headers);

        return BaseResult.ok(restTemplate.postForObject(url, requestEntity, Map.class));
    }

    /** 微信支付API版本-商家运营转账V3接口*/
    private final static String PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3 = "WX_PAY_MCH_TRANS_V3";

    /**
     * Title：authPax <br>
     * Description：验证本人领取信息 <br>
     * author：王磊 <br>
     * date：2021/11/25 14:43 <br>
     * @param dto <br>
     * @return <br>
     */
    @PostMapping("/authPax")
    @ApiOperation(value = "验证本人领取信息")
    public BaseResult<String> authPax(@RequestBody @Valid AuthPaxDTO dto) {
        return applyApi.authPax(dto);
    }

    /**
     * Title：sendSMS <br>
     * Description：发送短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum <br>
     * @return <br>
     */
    @GetMapping("/sendSMS")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<String> sendSMS(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        return applyApi.sendSMS(phoneNum);
    }

    /**
     * Title：verificationSMS <br>
     * Description：验证短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum
     * @param authCode <br>
     * @return <br>
     */
    @GetMapping("/verificationSMS")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<String> verificationSMS(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum
            , @ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        return applyApi.verificationSMS(phoneNum, authCode);
    }

    /**
     * Title：findCompensationOrder <br>
     * Description：通过旅客信息查询出赔偿单信息 <br>
     * author：王磊 <br>
     * date：2021/11/26 14:30 <br>
     * @param dto <br>
     * @return <br>
     */
    @PostMapping("/findCompensationOrder")
    @ApiOperation(value = "获取旅客赔偿单详情")
    public BaseResult<CompensationInfoVO> findCompensationOrder(@RequestBody AuthPaxDTO dto) {
        return applyApi.findCompensationOrder(dto);
    }

    /**
     * Title：saveApply <br>
     * Description：保存申领单 <br>
     * author：王磊 <br>
     * date：2021/11/29 11:00 <br>
     * @param dto <br>
     * @return <br>
     */
    @PostMapping("/saveApply")
    @ApiOperation(value = "新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveApply(@RequestBody ApplyOrderDTO dto) {
        return applyApi.saveApply(dto);
    }

    @PostMapping("/queryRecord")
    @ApiOperation(value = "申领记录查询")
    public BaseResult queryRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO) {
        return applyApi.queryRecord(applyQueryRecordDTO);
    }

    @PostMapping("/myRecord")
    @ApiOperation(value = "本人领取查询")
    public BaseResult<List<ReceivingRecordVO>> myRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO) {
        return applyApi.myRecord(applyQueryRecordDTO);
    }

    @PostMapping("/getJsapiSign")
    @ApiOperation(value = "获取JsapiSign")
    public BaseResult<Map<String, String>> getJsapiSign(@RequestBody ApplyJsapiSignDTO dto) {
        return applyApi.getJsapiSign(dto);
    }

    @GetMapping("/getReceiveConfirmInfo")
    @ApiOperation(value = "通过申领单ID获取用户确认收款package")
    public BaseResult getReceiveConfirmInfo(@ApiParam(value = "申领单ID", required = true)String applyId) {
        BaseResult result = applyApi.getReceiveConfirmInfo(applyId);
        log.info("通过申领单ID获取用户确认收款package,applyId:{},{} " , applyId,result);
        return result;
    }

    /**
     * Title：getReceive <br>
     * Description：领取记录查询 <br>
     * author：于琦海 <br>
     * date：2021/12/7 14:00 <br>
     * @param applyGetReceiveDTO ApplyQueryRecordDTO
     * @return PagedResult<List<ReceivingRecordVO>>
     */
    @PostMapping("/receive")
    @ApiOperation(value = "本人领取记录筛选查询")
    public PagedResult<List<ReceivingRecordVO>> getReceive(@RequestBody @Valid ApplyGetReceiveDTO applyGetReceiveDTO) {
        return applyApi.getReceive(applyGetReceiveDTO);
    }

    /**
     * Title：queryDetails <br>
     * Description：查询申领单详情 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param recordId
     * @return BaseResult<ApplyDetailsInfoVO>
     */
    @GetMapping("/queryDetails")
    @ApiOperation(value = "本人详情查询")
    public BaseResult<ApplyDetailsInfoVO> queryDetails(@RequestParam String recordId,@RequestParam String idCard) {
        BaseResult<ApplyDetailsInfoVO> result = applyApi.queryDetails(recordId, idCard);
        if(result.getData() == null || CollectionUtils.isEmpty(result.getData().getCompensateDetailsVOList())){
            return result;
        }
        List<CompensateDetailsVO> compensateDetailsVOList = result.getData().getCompensateDetailsVOList();
        for(CompensateDetailsVO compensateDetailsVO : compensateDetailsVOList){
            if(StringUtils.equals(compensateDetailsVO.getPayTypeApiVersion(), PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){
                compensateDetailsVO.setErrCodeDes(null);
            }
        }
        return result;
    }

    /**
     * Title：replaceRecord <br>
     * Description：代人领取申领单查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param applyQueryRecordDTO ApplyQueryRecordDTO
     * @return PagedResult<List<ReceivingRecordVO>>
     */
    @PostMapping("/replaceRecord")
    @ApiOperation(value = "代人领取查询")
    public PagedResult<List<ReceivingRecordVO>> replaceRecord(@RequestBody @Valid ApplyQueryRecordDTO applyQueryRecordDTO) {
        return applyApi.replaceRecord(applyQueryRecordDTO);
    }

    /**
     * Title：replaceFilterRecord <br>
     * Description：代人领取申领单筛选查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param replaceFilterRecordDTO ReplaceFilterRecordDTO
     * @return PagedResult<List<ReceivingRecordVO>>
     */
    @PostMapping("/replaceFilterRecord")
    @ApiOperation(value = "代人领取筛选查询")
    public PagedResult<List<ReceivingRecordVO>> replaceFilterRecord(@RequestBody @Valid ReplaceFilterRecordDTO replaceFilterRecordDTO) {
        return applyApi.replaceFilterRecord(replaceFilterRecordDTO);
    }

    /**
     * Title：queryReplaceDetails <br>
     * Description：代人领取旅客申领单详情查询 <br>
     * author：于琦海 <br>
     * date：2021/12/10 13:55 <br>
     * @param recordId String
     * @return BaseResult<ReplaceDetailsInfoVO>
     */
    @GetMapping("/queryReplaceDetails")
    @ApiOperation(value = "代人领取详情查询")
    public BaseResult<ReplaceDetailsInfoVO> queryReplaceDetails(@RequestParam String recordId,@RequestParam String idCard) {
        BaseResult<ReplaceDetailsInfoVO> result = applyApi.queryReplaceDetails(recordId, idCard);
        if(result.getData() == null || CollectionUtils.isEmpty(result.getData().getReplaceSubDetailsInfoVOS())){
            return result;
        }
        List<ReplaceSubDetailsInfoVO> replaceSubDetailsInfoVOS = result.getData().getReplaceSubDetailsInfoVOS();
        for(ReplaceSubDetailsInfoVO replaceSubDetailsInfoVO : replaceSubDetailsInfoVOS){
         if(CollectionUtils.isEmpty(replaceSubDetailsInfoVO.getReplaceCompensateDetailsVOS())){continue;}
         for(ReplaceCompensateDetailsVO replaceCompensateDetailsVO : replaceSubDetailsInfoVO.getReplaceCompensateDetailsVOS()){
             if(StringUtils.equals(replaceCompensateDetailsVO.getPayTypeApiVersion(), PAY_TYPE_API_VERSION_WX_PAY_MCH_TRANS_V3)){
                 replaceCompensateDetailsVO.setErrCodeDes(null);
             }
         }
        }
        return result;
    }

    /**
     * @title saveBehalfApply
     * @description 保存申领单
     * <AUTHOR>
     * @date 2021/11/29 11:00
     * @param dto
     * @return BaseResult<Object>
     */
    @PostMapping("/saveBehalfApply")
    @ApiOperation(value = "代领新建航延补偿申领单信息表记录")
    public BaseResult<Object> saveBehalfApply(@RequestBody @Valid ApplyBehalfOrderDTO dto) {
        return applyApi.saveBehalfApply(dto);
    }

    /**
     * Title：authBehalfPax <br>
     * Description：验证代领规则和代领人是否匹配 <br>
     * author：王磊 <br>
     * date：2022/1/6 13:51 <br>
     * @param dto <br>
     * @return BaseResult<Object><br>
     */
    @PostMapping("/authBehalfPax")
    @ApiOperation(value = "验证代领人领取信息")
    public BaseResult<Object> authBehalfPax(@RequestBody @Valid AuthBehalfPaxDTO dto) {
        return applyApi.authBehalfPax(dto);
    }

    /**
     * Title：sendSMSBehalf <br>
     * Description：发送短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum <br>
     * @return BaseResult<Object><br>
     */
    @GetMapping("/sendSMSBehalf")
    @ApiOperation(value = "发送短信验证码")
    public BaseResult<Object> sendSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum) {
        return applyApi.sendSMSBehalf(phoneNum);
    }

    /**
     * Title：verificationSMSBehalf <br>
     * Description：验证短信验证码 <br>
     * author：王磊 <br>
     * date：2021/11/25 16:17 <br>
     * @param phoneNum
     * @param authCode <br>
     * @return BaseResult<Object><br>
     */
    @GetMapping("/verificationSMSBehalf")
    @ApiOperation(value = "验证短信验证码")
    public BaseResult<Object> verificationSMSBehalf(@ApiParam(value = "手机号", required = true) @RequestParam String phoneNum,@ApiParam(value = "验证码", required = true) @RequestParam String authCode) {
        return applyApi.verificationSMSBehalf(phoneNum,authCode);
    }

    /**
     * Title：findFlightInfo <br>
     * Description：通过航班号和航班日期获取航班信息 <br>
     * author：王磊 <br>
     * date：2022/1/10 14:08 <br>
     * @param flightNo
     * @param flightDate <br>
     * @return BaseResult<CompensationFlightInfoVO> <br>
     */
    @GetMapping("/findFlightInfo")
    @ApiOperation(value = "通过航班号和航班日期获取航班信息")
    public BaseResult<CompensationFlightInfoVO> findFlightInfo(@ApiParam(value = "航班号", required = true) @RequestParam String flightNo, @ApiParam(value = "航班日期", required = true) @RequestParam String flightDate) {
        return applyApi.findFlightInfo(flightNo,flightDate);
    }

    /**
     * Title：findCompensationOrderBehalf <br>
     * Description：代领通过条件获取旅客数据 <br>
     * author：王磊 <br>
     * date：2022/1/10 13:47 <br>
     * @param dtos <br>
     * @return BaseResult<List<List<CompensationOrderInfoVO>>> <br>
     */
    @PostMapping("/findCompensationOrderBehalf")
    @ApiOperation(value = "获取旅客赔偿单详情")
    public BaseResult<List<List<CompensationOrderInfoVO>>> findCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos) {
        return applyApi.findCompensationOrderBehalf(dtos);
    }

    /**
     * @title getReplaceRule
     * @description 获取配置的代领规则
     * <AUTHOR>
     * @date 2022/1/13 9:30

     * @return BaseResult<Object>
     */
    @GetMapping("/getReplaceRule")
    @ApiOperation(value = "获取配置的代领规则")
    public  BaseResult<Object> getReplaceRule() {
        return applyApi.getReplaceRule();
    }

    /**
     * Title：authCompensationOrderBehalf <br>
     * Description：通过参数校验旅客数据是否存在异常,如存在异常则返回对应错误的身份证号 <br>
     * author：王磊 <br>
     * date：2022/1/18 9:41 <br>
     * @param dtos <br>
     * @return BaseResult<AuthCompensationOrderVO> <br>
     */
    @PostMapping("/authCompensationOrderBehalf")
    @ApiOperation(value = "过参数校验旅客数据是否存在异常,如存在异常则返回对应错误的身份证号")
    public BaseResult<AuthCompensationOrderVO> authCompensationOrderBehalf(@RequestBody @Valid List<AuthBehalfPaxDTO> dtos){
        return applyApi.authCompensationOrderBehalf(dtos);
    }



    @ApiOperation(value = "机场端获取授权航司二字码")
    @GetMapping("/getBelongAirline/{code}")
    public BaseResult<List<BelongAirlineDTO>> getBelongAirline(@RequestParam("code") String typeCode){
        return airlineBusinessPrivilegeApi.getBelongAirline(typeCode);
    }

    @PostMapping("/getCaptcha")
    @ApiOperation(value = "申领记录查询-获取图形验证码")
    public BaseResult<Object> getCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto) {
        return applyApi.getCaptchaByApplyRecord(dto);
    }
    @PostMapping("/validateCaptcha")
    @ApiOperation(value = "申领记录查询-验证图形验证码")
    public BaseResult<Object> validateCaptchaByApplyRecord(@RequestBody @Valid ApplyRecordCaptchaDTO dto) {
        return applyApi.validateCaptchaByApplyRecord(dto);
    }

    @GetMapping("/getCaptchaConfig")
    @ApiOperation(value = "获取当前租户的短信验证码配置信息")
    public BaseResult<Object> getCaptchaConfigByTenant() {
        return applyApi.getCaptchaConfigByTenant();
    }

    @PostMapping("/getCaptchaByApply")
    @ApiOperation(value = "申领-获取图形验证码")
    public BaseResult<Object> getCaptchaByApply() {
        return applyApi.getCaptchaByApply();
    }

    @PostMapping("/validateCaptchaByApply")
    @ApiOperation(value = "申领-验证图形验证码")
    public BaseResult<Object> validateCaptchaByApply(@RequestBody @Valid ApplyCaptchaDTO dto) {
        return applyApi.validateCaptchaByApply(dto);
    }


}
