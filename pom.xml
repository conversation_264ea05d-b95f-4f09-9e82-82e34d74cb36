<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.swcares.aps</groupId>
	<artifactId>aps</artifactId>
	<packaging>pom</packaging>
	<version>1.0.1_aps-SNAPSHOT</version>

    <modules>
        <!--网关，支持动态路由-->
        <module>aps-gateway</module>
        <module>aps-user-center</module>
        <module>aps-component</module>
        <module>aps-compensation</module>
        <module>aps-workflow</module>
        <module>aps-message-center</module>
        <module>aps-web-bff</module>
        <module>aps-staff-bff</module>
        <module>aps-pssn-bff</module>
        <module>aps-basic-data</module>
        <module>aps-coordinate-api</module>
        <module>aps-ground-service</module>
    </modules>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.5.0</version>
        <relativePath/>
    </parent>

    <name>aps</name>
    <url>http://www.example.com</url>

    <properties>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <nexus.url>maven.sw</nexus.url>
        <nexus.port>80</nexus.port>
        <skipTests>true</skipTests>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <spring.cloud.version>2020.0.4</spring.cloud.version>
        <spring.cloud.alibaba.version>2.2.6.RELEASE</spring.cloud.alibaba.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <jasypt.version>2.1.2</jasypt.version>
        <swcares.version>aps-3.0.5-SNAPSHOT</swcares.version>
        <spring.cloud.starter.gateway.version>3.1.1</spring.cloud.starter.gateway.version>
        <spring.cloude.loadbalancer.version>3.0.3</spring.cloude.loadbalancer.version>
        <easypoi.version>4.1.0</easypoi.version>
        <sentry.version>6.1.0</sentry.version>
        <oracle.jdbc.version>********</oracle.jdbc.version>
        <easyexcel.version>3.2.1</easyexcel.version>
        <coordinate.version>1.0.0-SNAPSHOT</coordinate.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
        </dependency>
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
        </dependency>
    </dependencies>

    <dependencyManagement>
        <dependencies>
            <!-- 引入基础框架 dependencies定义声明-->
            <dependency>
                <groupId>com.swcares.baseframe</groupId>
                <artifactId>common-dependencies</artifactId>
                <version>${swcares.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.github.ulisesbocchio</groupId>
                <artifactId>jasypt-spring-boot-starter</artifactId>
                <version>${jasypt.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-message-center-remote-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!--使用这种方式就不用继承父模块，可以解决单继承的问题。这样就可以继承其他父模块，比如自己创建的父模块
            其实parent的parent就是spring-boot-dependencies
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring.boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>-->
            <!-- spring-cloud -->
            <!-- <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency> -->
            <!-- spring-cloud-alibaba -->
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring.cloud.alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>2.4.3</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-message-center-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-compensation-remote-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-workflow-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-workflow-remote-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- easyexcel 导入导出-->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>easyexcel</artifactId>
                <version>${easyexcel.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-user-center-remote-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-permission</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-tenant</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-dict</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-pay</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-basic-data</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-web</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>${easypoi.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.components</groupId>
                <artifactId>system-file-attachment</artifactId>
                <version>${swcares.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.tools</groupId>
                <artifactId>system-code-generator</artifactId>
                <version>${swcares.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oracle.database.jdbc</groupId>
                <artifactId>ojdbc8</artifactId>
                <version>${oracle.jdbc.version}</version>
            </dependency>

            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-spring-boot-starter</artifactId>
                <version>${sentry.version}</version>
            </dependency>
            <dependency>
                <groupId>io.sentry</groupId>
                <artifactId>sentry-logback</artifactId>
                <version>${sentry.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>coordinate-util</artifactId>
                <version>${coordinate.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>coordinate-center-model</artifactId>
                <version>${coordinate.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-workflow</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-ground-service-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-component-privilege</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-ground-service-remote-api</artifactId>
                <version>${project.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <distributionManagement>
        <repository>
            <id>psi</id> <!-- id值需要保持maven环境配置的settings.xml文件中的 server节点的id一致！ -->
            <name>releases</name>
            <url>http://${nexus.url}:${nexus.port}/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>psi</id>
            <name>snapshot</name>
            <url>http://${nexus.url}:${nexus.port}/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>

</project>
