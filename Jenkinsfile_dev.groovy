pipeline{
    agent any

    parameters {
        string (defaultValue: 'travelsky', description: '打包分支', name: 'branch', trim: true)
        choice(name: 'deployModule', choices: ['all','aps-web','aps-staff','aps-pssn', 'user-center','message-center','gateway','compensation', 'workflow','coordinate'], description: '发布模块')
    }
    stages{
        stage("buildPackage"){
            steps{
                sh '''
                     mvn -e -U -T 4C clean install -Dmaven.test.skip=true -X 
                '''
            }
        }
        stage("doUnit"){
            steps{
                sh '''
                  if [ "${deployModule}" == "aps-web" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit aps-web"
                        # cd ${WORKSPACE}/aps-web-bff
                        # mvn  -T 4C clean test  package
                        
                  fi
                  
                  if [ "${deployModule}" == "aps-staff" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit aps-staff"
                        # cd ${WORKSPACE}/aps-staff-bff
                        # mvn  -T 4C clean test  package
                         
                  fi
                  
                  if [ "${deployModule}" == "aps-pssn" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit aps-pssn"
                        # cd ${WORKSPACE}/aps-pssn-bff
                        # mvn  -T 4C clean test  package
                        
                  fi
                  
                  if [ "${deployModule}" == "user-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit user-center"
                        
                        # cd ${WORKSPACE}/aps-user-center
                        # mvn  -T 4C clean test  package
                        
                  fi
                             
                  if [ "${deployModule}" == "message-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit message-center"
                        # cd ${WORKSPACE}/aps-message-center
                        # mvn  -T 4C clean test  package
                        
                  fi
                  
                  if [ "${deployModule}" == "gateway" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit gateway"
                        # cd ${WORKSPACE}/aps-gateway
                        # mvn  -T 4C clean test  package
                  fi
                  
                  if [ "${deployModule}" == "compensation" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit compensation-impl"
                        # cd ${WORKSPACE}/aps-compensation
                        # mvn  -T 4C clean test  package
                  fi
                  
                  if [ "${deployModule}" == "workflow" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit workflow-impl"
                        # cd ${WORKSPACE}/aps-workflow
                        # mvn  -T 4C clean test  package
                  fi

                  if [ "${deployModule}" == "coordinate" ] || [ "${deployModule}" == "all" ] ;then
                        echo "doUnit aps-coordinate-api"
                        # cd ${WORKSPACE}/aps-coordinate-api
                        # mvn  -T 4C clean test  package
                  fi
				'''
            }

        }

        stage("doSonar"){
            steps{
                sh '''
                  echo "doSonar"
				'''
            }

        }

        stage("uploadPackage"){
            steps{
                sh '''
                 
                  if [ "${deployModule}" == "aps-web" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage aps-web"
                        scp  ${WORKSPACE}/aps-web-bff/target/aps-web-bff*.jar root@192.168.17.231:/opt/app/aps/aps-web-bff.jar
                  fi
                  
                  if [ "${deployModule}" == "aps-staff" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage aps-staff"
                        scp  ${WORKSPACE}/aps-staff-bff/target/aps-staff-bff*.jar root@192.168.17.231:/opt/app/aps/aps-staff-bff.jar
                  fi
                  
                  if [ "${deployModule}" == "aps-pssn" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage aps-pssn"
                        scp  ${WORKSPACE}/aps-pssn-bff/target/aps-pssn-bff*.jar root@192.168.17.231:/opt/app/aps/aps-pssn-bff.jar
                  fi
                                    
                  if [ "${deployModule}" == "gateway" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage gateway"
                        scp  ${WORKSPACE}/aps-gateway/target/aps-gateway*.jar root@192.168.17.231:/opt/app/aps/aps-gateway.jar
                        scp  ${WORKSPACE}/aps-gateway/target/aps-gateway*.jar root@**************:/opt/app/aps/aps-gateway.jar
                        scp  ${WORKSPACE}/aps-gateway/target/aps-gateway*.jar root@**************:/opt/app/aps/aps-gateway.jar
                        
                  fi
                  
                  if [ "${deployModule}" == "user-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage user-center"
                        scp  ${WORKSPACE}/aps-user-center/aps-user-center-impl/target/aps-user-center-impl*.jar root@**************:/opt/app/aps/aps-user-center-impl.jar
                        scp  ${WORKSPACE}/aps-user-center/aps-user-center-impl/target/aps-user-center-impl*.jar root@**************:/opt/app/aps/aps-user-center-impl.jar
                  fi

                  
                  if [ "${deployModule}" == "message-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage user-center"
                        scp  ${WORKSPACE}/aps-message-center/aps-message-center-impl/target/aps-message-center-impl*.jar  root@**************:/opt/app/aps/aps-message-center-impl.jar
                        scp  ${WORKSPACE}/aps-message-center/aps-message-center-impl/target/aps-message-center-impl*.jar  root@**************:/opt/app/aps/aps-message-center-impl.jar
                  fi                  
                  
                  if [ "${deployModule}" == "compensation" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage compensation"
                        scp  ${WORKSPACE}/aps-compensation/aps-compensation-impl/target/aps-compensation-impl*.jar root@**************:/opt/app/aps/aps-compensation-impl.jar
                        scp  ${WORKSPACE}/aps-compensation/aps-compensation-impl/target/aps-compensation-impl*.jar root@**************:/opt/app/aps/aps-compensation-impl.jar
                  fi
                  
                  if [ "${deployModule}" == "workflow" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage workflow"
                        scp  ${WORKSPACE}/aps-workflow/aps-workflow-impl/target/aps-workflow-impl*.jar root@**************:/opt/app/aps/aps-workflow-impl.jar
                        scp  ${WORKSPACE}/aps-workflow/aps-workflow-impl/target/aps-workflow-impl*.jar root@**************:/opt/app/aps/aps-workflow-impl.jar
                  fi
                  
                  if [ "${deployModule}" == "coordinate" ] || [ "${deployModule}" == "all" ] ;then
                        echo "uploadPackage aps-coordinate-api"
                        scp  ${WORKSPACE}/aps-coordinate-api/target/aps-coordinate-api*.jar root@**************:/opt/app/aps/aps-coordinate-api.jar
                        scp  ${WORKSPACE}/aps-coordinate-api/target/aps-coordinate-api*.jar root@**************:/opt/app/aps/aps-coordinate-api.jar
                  fi
                  
                  
				'''
            }
        }

        stage("restartServer"){
            steps{
                sh '''   
                  if [ "${deployModule}" == "aps-web" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer aps-web"
                        ssh root@192.168.17.231 "/opt/app/aps/aps-web.sh restart"
                        sleep  5s
                  fi
                  
                  if [ "${deployModule}" == "aps-staff" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer aps-staff"
                        ssh root@192.168.17.231 "/opt/app/aps/aps-staff.sh restart"
                        sleep  5s
                  fi
                  
                  if [ "${deployModule}" == "aps-pssn" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer aps-pssn"
                        ssh root@192.168.17.231 "/opt/app/aps/aps-pssn.sh restart"
                        sleep  5s
                  fi
                  
                    if [ "${deployModule}" == "gateway" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer gateway"  
                        ssh root@192.168.17.231 "/opt/app/aps/aps-gateway.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-gateway.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-gateway.sh restart"
                        sleep  5s
                  fi
                  
                  if [ "${deployModule}" == "user-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer user-center"
                        ssh root@************** "/opt/app/aps/aps-user-center.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-user-center.sh restart"
                        sleep  5s
                  fi

                  if [ "${deployModule}" == "message-center" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer message-center"
                        ssh root@************** "/opt/app/aps/aps-message-center.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-message-center.sh restart"
                        sleep  5s
                  fi
                
                  if [ "${deployModule}" == "compensation" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer compensation"  
                        ssh root@************** "/opt/app/aps/aps-compensation.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-compensation.sh restart"
                        sleep  5s
                  fi
                  
                  if [ "${deployModule}" == "workflow" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer workflow"  
                        ssh root@************** "/opt/app/aps/aps-workflow.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-workflow.sh restart"
                        sleep  5s
                  fi
                  
                  if [ "${deployModule}" == "coordinate" ] || [ "${deployModule}" == "all" ] ;then
                        echo "restartServer aps-accordinate-api"  
                        ssh root@************** "/opt/app/aps/aps-coordinate-api.sh restart"
                        sleep  5s
                        ssh root@************** "/opt/app/aps/aps-coordinate-api.sh restart"
                        sleep  5s
                  fi
                '''
            }
        }
    }
    post{
        always{
            echo "========always======== "
        }
        success{

            echo "========pipeline executed successfully ======== "
        }
        aborted{

            echo "========pipeline executed aborted ======== "
        }
        failure{
            echo "========pipeline execution failed======== "
        }
    }
}
