package com.swcares.aps.gateway.config;

import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.swcares.aps.gateway.route.NacosRouteDefinitionRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DynamicRouteConfig {

    @Autowired
    private ApplicationEventPublisher publisher;

    @Autowired
    private NacosConfigProperties nacosConfigProperties;

    @Bean
    public NacosRouteDefinitionRepository nacosRouteDefinitionRepository() {
        return new NacosRouteDefinitionRepository(publisher, nacosConfigProperties);
    }
}
