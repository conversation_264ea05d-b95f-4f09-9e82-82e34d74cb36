package com.swcares.aps.gateway.filter;

import com.swcares.aps.gateway.config.TokenPortConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @ClassName：TokenSourceValidateFilter
 * @Description：@TODO
 * @Copyright：© 2024 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： summer
 * @Date： 2025/1/17 13:06
 * @version： v1.0
 */
@Component
@RefreshScope
@Slf4j
public class TokenSourceValidateFilter implements GlobalFilter, Ordered {

    /**
     * 存储端口和域名的映射关系，为了解决某个端的token拿到另外个端使用的问题
     * 数据格式：<web端, http://2222.aaa.cc>, <h5端, http://2222.aaa.cc,http://1111.cccc.dd></>
     */
    @Autowired
    private TokenPortConfig tokenPortConfig;

    @Autowired
    private RedisTemplate<Object, Object> redisTemplate;

    private static final AntPathMatcher MATCHER = new AntPathMatcher();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        //当前请求路径
        String currentPath = exchange.getRequest().getURI().getRawPath();

        String filterPath = tokenPortConfig.getFilterPath();
        if(StringUtils.isNotEmpty(filterPath)){
            List<String> resultList = Arrays.stream(filterPath.split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty()) // 过滤掉空字符串
                    .collect(Collectors.toList());
            log.info("TokenSourceValidateFilter.filter 不需要进行路径重写的地址白名单配置[{}]",filterPath);
            // 遍历resultList，检查currentPath是否包含任何一个指定的路径片段
            for (String pathSegment : resultList) {
                if (currentPath.contains(pathSegment)) {
                    log.info("TokenSourceValidateFilter.filter 不需要进行路径重写,currentPath:[{}]",currentPath);
                    // 如果currentPath包含任何一个指定的路径片段，则不进行路径重写
                    return chain.filter(exchange);
                }
            }
        }

        String authorization = exchange.getRequest().getHeaders().
                getFirst("Authorization");
        authorization = StringUtils.isEmpty(authorization) ? "" : authorization;

        //根据token判断获取当前用户是哪个端登录
        String clientId = getClientId(exchange, authorization.replace("bearer", "").trim());

        // token跨端验证未启用（默认启用） ||  authorization为空  || authorization == bearer
        if(!tokenPortConfig.getEnable() || StringUtils.isEmpty(authorization)
                || authorization.equalsIgnoreCase("bearer") || StringUtils.isEmpty(clientId)){
            // 路径重写
            return chain.filter(exchange.mutate().request(overridePath(exchange, currentPath)).build());
        }

        //1.看当前token有没有被拿到其他地方使用，比对当前请求的Refer和配置文件中是否一致:
        // 后面想了下建议把登录时候的ip以及userAgent,mac地址啥的，多加点，作为登录时环境快照，然后此处做比对
       // String currentReferer = exchange.getRequest().getHeaders().getFirst("Referer");

        //2.看是否跨端使用，用当前请求的servletPath和配置的是否一致，不一致则抛出异常，一致往下传
        String portPath = tokenPortConfig.getPath().get(clientId);
        if(StringUtils.isEmpty(portPath) || !MATCHER.match(portPath, currentPath)){
            return throwTokenException(exchange);
        }

        //3.路径重写
        return chain.filter(exchange.mutate().request(overridePath(exchange, currentPath)).build());
    }

    private ServerHttpRequest overridePath(ServerWebExchange exchange, String currentPath){
        String[] replaceStrArray = currentPath.split("/");
        if(replaceStrArray.length > 3){
            replaceStrArray[3] = "";
        }
        currentPath = String.join("/", replaceStrArray);
        return exchange.getRequest().mutate().path(currentPath).build();
    }

    private String getClientId(ServerWebExchange exchange, String token){
            String userJson = getUser(token);
            if(StringUtils.isEmpty(userJson)){
                return null;
            }
            setTenantId(exchange, userJson);
            String regex = "\"clientId\"\\s*:\\s*\"([^\"]+)\"";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(userJson);

            if (matcher.find()) {
                return matcher.group(1);  // 输出: password_auth_mode
            } else {
                return null;
            }
    }

    private void setTenantId(ServerWebExchange exchange, String userJson){
        //夹带私货，做点事儿，设置下租户id，用于租户信息从域名中迁移出来
        String regex = "\"tenantId\":\\s*(\\d+)";
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(userJson);
        String tenantId = "";
        if (matcher.find()) {
            tenantId = matcher.group(1);  // 输出: password_auth_mode
        }
        //多一次清空是为了安测的时候安全，免得恶意利用，瞎鸡儿乱传认为自己可以改租户id越权，先把你传的给清空了
        exchange.getRequest().mutate().header("tenantId", "");
        exchange.getRequest().mutate().header("tenantId", tenantId);
    }

    private String getUser(String token)  {
        byte[] key = ("oauth2:auth:" + token).getBytes(StandardCharsets.UTF_8);
        RedisConnectionFactory r = redisTemplate.getConnectionFactory();
        byte[] tokeByte = r.getConnection().get(key);
        //整个过滤器只负责跨端检验和referer校验，其他token为null，是否合法等等交给原生的基础框架逻辑实现
        if(null == tokeByte){
            return null;
        }
        return new String(tokeByte, StandardCharsets.UTF_8);
    }

    private static boolean matchPath(AntPathMatcher matcher, String pattern, String url) {
        try {
            URL parsedUrl = new URL(url);
            String path = parsedUrl.getPath();
            return matcher.match(pattern, path);
        } catch (MalformedURLException e) {
            log.error("解析url出错，url【{}】,正则为【{}】", url, pattern);
            return false;
        }
    }

    private Mono<Void> throwTokenException(ServerWebExchange exchange){
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");
        response.setStatusCode(HttpStatus.FORBIDDEN);
        DataBuffer dataBuffer = response.bufferFactory().wrap("Token is invalid".getBytes());
        return response.writeWith(Flux.just(dataBuffer));
    }

    @Override
    public int getOrder() {
        return 1;
    }

    public static void main(String[] args) {
        final AntPathMatcher MATCHER = new AntPathMatcher();

        // URL 路径匹配规则
        String pattern1 = "/web/**";
        String pattern2 = "/lfm-h5/**";

        // 测试用例
        System.out.println(matchPath(MATCHER, pattern1, "http://3u.uat.al.aps.sw/web/compensation/redressRules")); // true
        System.out.println(matchPath(MATCHER, pattern2, "http://3u.uat.al.aps.sw/lfm-h5/21312")); // true

        // 这些应该输出 false
        System.out.println(matchPath(MATCHER, pattern1, "http://3u.uat.al.aps.sw/compensation/redressRules")); // false
        System.out.println(matchPath(MATCHER, pattern2, "http://3u.uat.al.aps.sw/lfm-h5123")); // false
        System.out.println(matchPath(MATCHER, pattern2, "http://3u.uat.al.aps.sw/lfm-23")); // false
        System.out.println(matchPath(MATCHER, pattern2, "http://3u.uat.al.aps.sw")); // false
    }
}
