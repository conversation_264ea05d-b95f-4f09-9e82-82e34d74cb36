package com.swcares.aps.workflow.remote.api.util;

import com.swcares.aps.workflow.dto.BaseActivityDTO;
import com.swcares.baseframe.common.base.BaseResult;

/**
 * @ClassName：WorkflowUtils
 * @Description：一些工具类
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/15 12:42
 * @version： v1.0
 */
public final class WorkflowUtils {

    private final static String AUTOMATIC_TASK_NODE_PRE_KEY="Automatic";

    private final static String SUBMITTER_TASK_NODE_PRE_KEY="submitter";

    private final static String SYNC_PROCESS_TASK_NODE_PRE_KEY="SyncProcess";

    private static final int TASK_HAS_COMPLETE_ERROR_CODE = 31005;

    private static final int TASK_NOT_EXIST_ERROR_CODE = 31004;


    /**
     *
     * @param taskNodeKey
     * @return
     */
    public static boolean isSyncProcessTask(String taskNodeKey){
        return taskNodeKey.indexOf(SYNC_PROCESS_TASK_NODE_PRE_KEY)==0;
    }


    public static boolean isSubmitterTask(String taskNodeKey) {
        return taskNodeKey.indexOf(SUBMITTER_TASK_NODE_PRE_KEY)==0;
    }

    /**
     * 判断是否是自动审核
     * @param activityDTO
     * @return
     */
    public static boolean isAutomaticTask(BaseActivityDTO activityDTO){
        String taskNodeKey = activityDTO.getNodeKey();
        return taskNodeKey.indexOf(AUTOMATIC_TASK_NODE_PRE_KEY)==0;
    }

    /***
     * @title isExecuteTask
     * @description 任务是否被执行
     * <AUTHOR>
     * @date 2022/3/22 10:53
     * @param code
     * @return boolean
     */
    public static boolean isExecutedTask(int code){
        if(TASK_HAS_COMPLETE_ERROR_CODE == code){
            return true;
        }
        return false;
    }

    /**
     * @title taskNotExist
     * @description 任务不存在
     * <AUTHOR>
     * @date 2022/3/22 16:18
     * @param code
     * @return 
     */
    public static boolean taskNotExist(int code){
        if(TASK_NOT_EXIST_ERROR_CODE == code){
            return true;
        }
        return false;
    }


}
