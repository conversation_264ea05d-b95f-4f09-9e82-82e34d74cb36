package com.swcares.aps.workflow.remote.api;


import com.swcares.aps.workflow.dto.*;
import com.swcares.baseframe.common.base.BaseResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;


/**
 * ClassName：FlowableApi <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/1/29 <br>
 * @version v1.0 <br>
 */
@FeignClient(name= "workflow-impl",path = "/workflow-impl/workflow")
public interface WorkflowApi {

    @PostMapping("/startProcess")
    BaseResult<CurrentTaskActivityVO> startProcess(@RequestBody StartProcessParamsDTO params);

    @PostMapping("/sync/process")
    BaseResult<CurrentTaskActivityVO> syncProcess(@RequestBody String params);

    @GetMapping("/getCustomerVariable")
    BaseResult<CustomerDTO> getCustomerVariable(@RequestParam("processInstanceId") String processInstanceId);

    @PostMapping("/completeTask")
    BaseResult<CurrentTaskActivityVO> completeTask(@RequestBody CompleteProcessParamsDTO params);

    @PostMapping("/currentUserTask")
    BaseResult<CurrentTaskActivityVO> currentUserTask(@RequestBody BaseQueryParamDTO paramDTO);

    @PostMapping("/historyTaskAuditActivity")
    BaseResult<HistoryTaskAuditActivityVO> historyTaskAuditActivity(@RequestBody BaseQueryParamDTO paramDTO);

    @GetMapping("/getNextTask")
    BaseResult<CurrentTaskActivityDTO> getNextTask(@RequestParam("taskId") String taskId);

}
