<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>APS登陆页面</title>

    <script src="./jquery-3.6.1.min.js"></script>
    <script src="./jquery.cookie.min.js"></script>
    <script>
        $(function(){

           function getParameterByName(name, url = window.location.href) {
                name = name.replace(/[\[\]]/g, '\\$&');
                var regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)'),
                results = regex.exec(url);
                if (!results) return null;
                if (!results[2]) return '';
                return decodeURIComponent(results[2].replace(/\+/g, ' '));
            }

            function toIndex(){
                $.get("../config/login",function(returnData){
                    console.info(returnData)
                    if(!returnData.loginFlag){ //没有登陆
                        if(returnData.directFlag){ //跳转3方登陆
                           window.location.href=returnData.apsLoginUrl;
                        }else{

                           init();
                        }
                    }else{
                        window.location.href ="../"
                    }
                });
            }



            var token;
            var sys_login_captcha_enable=0;

            function init(){
                $("#loginDiv").show();
                $.get("http://127.0.0.1/api/aps-user-center/login/get_login_cfg",function(returnData){
                    console.info(returnData)
                    if(returnData.data.sys_login_captcha_enable!=1){
                        return;
                    }
                    sys_login_captcha_enable=1;
                    refushCaptcha();
                    $("#captchaTr").show()
                });
            }


            function refushCaptcha(){
                if(sys_login_captcha_enable!=1){
                    return;
                }
                $.ajax({url:"http://127.0.0.1/api/aps-user-center/captcha/get",
                        contentType:"application/json",
                        dataType:"json",
                        type:"post",
                        data:"{\"captchaType\": \"bladePatchca\"}",
                  success:function(returnData){
                    console.info(returnData)
                    token=returnData.data.token;

                    var base64Image="data:image/png;base64,"+returnData.data.originalImageBase64;

                    console.info(base64Image)

                    $("#captchaImg").attr("src",base64Image)
                }});
            }

            function checkRefushCaptcha(){
                var flag=true;
                if(sys_login_captcha_enable!=1){
                    return flag;
                }
                var captcha=$.trim($("#captcha").val());
                $.ajax({url:"http://127.0.0.1/api/aps-user-center/captcha/check",
                            contentType:"application/json",
                            dataType:"json",
                            type:"post",
                            async:false,
                            data:"{\"captchaVerification\": \""+captcha+"\",\"token\": \""+token+"\",\"captchaType\": \"bladePatchca\"}",
                              success:function(returnData){
                                console.info(returnData)
                                if(returnData.code!=200){
                                    alert(returnData.message);
                                    flag=false;
                                }
                            }});

                 return flag;
            }


            function submitForm(){
                 var username=$.trim($("#username").val());
                 var password=$.trim($("#password").val());
                 var captcha=$.trim($("#captcha").val());
                 var url="http://127.0.0.1/api/aps-user-center/oauth/token?captchaVerification="+captcha+"&token="+token+"&captchaType=bladePatchca&username="+username+"&password="+password+"&grant_type=password&type=";

                console.info(url)
                $.ajax({url:url,
                            dataType:"json",
                            type:"get",
                            async:false,
                            beforeSend:function(req){
                                req.setRequestHeader("Authorization","Basic cGFzc3dvcmRfYXV0aF9tb2RlOjEyMzQ1Ng==");
                            },
                              success:function(returnData){
                                console.info(returnData)
                                if(returnData.access_token!=null || returnData.access_token!=""){

                                    storeToken(returnData.access_token);

                                    toIndex();
                                }else{
                                    alert(returnData.message);
                                    refushCaptcha();
                                }
                            }});
            }

            function storeToken(access_token){
                if (window.localStorage) {
                    console.info("store access_token:"+access_token)
                    localStorage.setItem('authorization',access_token);

                    $.cookie('WorkflowAuthorization', access_token, { expires: 1,path:'' });
                    $.cookie('WorkflowAuthorization', access_token, { expires: 1,path:'/' });

                } else {
                    alert('This browser does NOT support');
                }
            }

            $("#submitBtn").click(function(){

                var flag=checkRefushCaptcha();
                if(!flag){
                    refushCaptcha();
                    return;
                }
                submitForm();
            });


            $.ajaxSettings.async = false;

            var pre=getParameterByName("p",window.location.href);
            if(pre == '' || pre == null){pre='Base';}

            var authorizationLocalName=pre+'-authorization';
            var authorizationCode=localStorage.getItem(authorizationLocalName);
            console.info(authorizationCode)

            if(authorizationCode!=null){
               storeToken(authorizationCode)
            }

            toIndex();

        })
    </script>
</head>
<body>
<div style="with:100%;height:100%;display:none;" id="loginDiv">
    <table>
        <tr>
            <td>用户名：</td>
            <td><input type="input" id="username"/></td>
        </tr>
        <tr>
            <td>密码：</td>
            <td><input type="password" id="password"/></td>
        </tr>
        <tr id="captchaTr" style="display:none;">
            <td>验证码：</td>
            <td><input type="input" id="captcha"/>
                <img class="captchaImg" src="" alt="验证码" id="captchaImg">
            </td>
        </tr>
        <tr>
            <td></td>
            <td><input type="button" name="登陆" id="submitBtn" value="登陆">
            </td>
        </tr>
    </table>

</div>

</body>
</html>