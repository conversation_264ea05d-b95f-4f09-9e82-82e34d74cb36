<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper">
<!--    <select id="getNeedSyncWorkflowTask" resultType="com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO">-->
<!--        SELECT-->
<!--        *-->
<!--        FROM-->
<!--        ACT_SYNC_WORKFLOW_TASK-->
<!--        WHERE EXEC_STATUS = '0' OR-->
<!--            (EXEC_STATUS = '3' AND EXEC_TIMES &lt; 5 AND UPDATED_TIME &lt; SYSDATE - NUMTODSINTERVAL(EXEC_TIMES*2, 'MINUTE')) OR-->
<!--            (EXEC_STATUS = '1' AND UPDATED_TIME &lt; SYSDATE - NUMTODSINTERVAL(300, 'MINUTE'))-->
<!--        AND rownum &lt; 20-->
<!--    </select>-->

        <select id="getNeedSyncWorkflowTask" resultType="com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO">
            SELECT
            *
            FROM
            ACT_SYNC_WORKFLOW_TASK
            WHERE EXEC_STATUS = '0' OR
                (EXEC_STATUS = '3' AND EXEC_TIMES &lt; 3000 ) OR
                (EXEC_STATUS = '1' AND UPDATED_TIME &lt; SYSDATE - NUMTODSINTERVAL(300, 'MINUTE'))
            AND rownum &lt; 20
            order by created_time asc,id asc
        </select>

</mapper>
