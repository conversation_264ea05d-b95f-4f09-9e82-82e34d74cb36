package com.swcares.aps.workflow.flowable.service.impl;


import cn.hutool.json.JSONUtil;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.constants.WorkflowErrors;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.common.util.TaskUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.StartProcessParamsDTO;
import com.swcares.aps.workflow.flowable.service.FlowableProcessService;
import com.swcares.aps.workflow.flowable.service.FlowableTaskService;
import com.swcares.aps.workflow.flowable.service.FlowableVariableService;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.history.HistoricProcessInstanceQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * ClassName：FlowableProcessServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableProcessServiceImpl implements FlowableProcessService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RepositoryService repositoryService;

    @Autowired
    private FlowableTaskService flowableTaskService;

    @Autowired
    private FlowableVariableService flowableVariableService;

    @Override
    public ProcessInstance startProcess(StartProcessParamsDTO params) {

        long count = repositoryService.createDeploymentQuery()
                .processDefinitionKey(params.getProcDefKey())
                .count();
        log.info("【aps-workflow-impl】startProcess-启动实例-查询流程定义是否存在,startProcess:{}", JSONUtil.toJsonStr(params));
        if(count<=0){
            throw new BusinessException(WorkflowErrors.PROCESS_DEFINITION_NOT_EXIST,
                    WorkflowErrors.PROCESS_DEFINITION_NOT_EXIST_MES);
        }
        Map<String, Object> vars = ProcessParamsUtil.toMap(params);
        log.info("【aps-workflow-impl】startProcess-启动实例-参数param转换为map（调用了hu-tool:SecureUtil.sha1进行加密）,startProcess:{}", JSONUtil.toJsonStr(params));
        ProcessInstance processInstance = runtimeService
                .startProcessInstanceByKey(params.getProcDefKey(), params.getBusinessKey(), vars);

        return processInstance;
    }


    @Override
    public String completeTask(CompleteProcessParamsDTO params) {
        Task task = taskService.createTaskQuery().taskId(params.getTaskId()).singleResult();
        if(task!=null){
            if(TaskUtil.isSyncProcessTask(task.getTaskDefinitionKey())
                    && (!StringUtils.equals(params.getCanDoSyncProcessTask(),CompleteProcessParamsDTO.CAN_DO_SYNC_PROCESS_TASK_SIGN))){
                throw new BusinessException(WorkflowErrors.PARAM_ERROR,"流程同步节点不能被手动执行");
            }
            flowableTaskService.completeTask(task,params);
            return task.getProcessInstanceId();
        }

        //如果task为空；有可能是客户端重复提交，需要判断
        HistoricTaskInstance historicTaskInstance = historyService.createHistoricTaskInstanceQuery().taskId(params.getTaskId()).singleResult();
        if(historicTaskInstance==null){
            log.error("{}任务不存在",params.getTaskId());
            throw new BusinessException(WorkflowErrors.TASK_NOT_EXIST,WorkflowErrors.TASK_NOT_EXIST_MES);
        }

        //历史任务已经存在,判断是否为客户端重复执行
        String messageSignatureOld = (String)flowableVariableService
                .getProcessVariable(historicTaskInstance.getProcessInstanceId(), ProcessParamsConstants.MESSAGE_SIGNATURE);

        String messageSignatureNew =(String) ProcessParamsUtil.toMap(params, historicTaskInstance.getTaskDefinitionKey())
                .get(ProcessParamsConstants.MESSAGE_SIGNATURE);

        if(!StringUtils.equalsIgnoreCase(messageSignatureOld,messageSignatureNew)){
            log.error("{}任务已经被执行",params.getTaskId());
            throw new BusinessException(WorkflowErrors.TASK_HAS_COMPLETE,WorkflowErrors.TASK_HAS_COMPLETE_MES);
        }

        return historicTaskInstance.getProcessInstanceId();
    }

    @Override
    public HistoricProcessInstance historicProcessInstance(BaseQueryParamDTO params) {
        HistoricProcessInstanceQuery historicProcessInstanceQuery = historyService.createHistoricProcessInstanceQuery();
        log.info("【aps-workflow-impl】currentUserTask-historicProcessInstance-createHistoricProcessInstanceQuery,参数:{}", JSONUtil.toJsonStr(params));
        if(StringUtils.isNotEmpty(params.getBusinessKey())){
            historicProcessInstanceQuery.processInstanceBusinessKey(params.getBusinessKey());
            log.info("【aps-workflow-impl】currentUserTask-historicProcessInstance-processInstanceBusinessKey,参数:{}", JSONUtil.toJsonStr(params));
        }
        if(StringUtils.isNotEmpty(params.getProcessInstanceId())){
            historicProcessInstanceQuery.processInstanceId(params.getProcessInstanceId());
            log.info("【aps-workflow-impl】currentUserTask-historicProcessInstance-processInstanceId,参数:{}", JSONUtil.toJsonStr(params));
        }

        List<HistoricProcessInstance> historicProcessInstances = historicProcessInstanceQuery.list();
        log.info("【aps-workflow-impl】currentUserTask-historicProcessInstance-historicProcessInstanceQuery.list(),参数:{}", JSONUtil.toJsonStr(params));
        if(CollectionUtils.isEmpty(historicProcessInstances)){
            throw new BusinessException(WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST,
                    WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST_MES);
        }
        if(historicProcessInstances.size()>1){
            throw new BusinessException(WorkflowErrors.PROCESS_INSTANCE_EXIST_MULTIPLE,
                    WorkflowErrors.PROCESS_INSTANCE_EXIST_MULTIPLE_MES);
        }

        return historicProcessInstances.get(0);
    }

    @Override
    public HistoricProcessInstance historicProcessInstance(String processInstanceId) {
        HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
                .processInstanceId(processInstanceId)
                .singleResult();

        if(historicProcessInstance==null){
            throw new BusinessException(WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST,
                    WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST_MES);
        }
        return historicProcessInstance;
    }

}
