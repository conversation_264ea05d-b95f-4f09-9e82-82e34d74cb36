package com.swcares.aps.workflow.flowable.service.impl;

import com.swcares.aps.workflow.flowable.service.FlowableFlowElementService;
import lombok.extern.slf4j.Slf4j;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.EndEvent;
import org.flowable.bpmn.model.ExclusiveGateway;
import org.flowable.bpmn.model.FlowElement;
import org.flowable.bpmn.model.FlowNode;
import org.flowable.bpmn.model.SequenceFlow;
import org.flowable.bpmn.model.ServiceTask;
import org.flowable.bpmn.model.SubProcess;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.impl.persistence.entity.ExecutionEntity;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * ClassName：FlowableFlowElementServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/16 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableFlowElementServiceImpl implements FlowableFlowElementService {

    @Autowired
    private TaskService taskService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private RepositoryService repositoryService;
    /**
     * 获取任务节点
     *
     * @param
     * @param taskId 任务id
     */
    public UserTask nextFlowNode(String taskId) {
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if(task==null){
            log.info("【aps-flowable-impl】nextFlowNode,{} task没有找到",taskId);
            return null;
        }
        ExecutionEntity ee = (ExecutionEntity)runtimeService.createExecutionQuery()
                .executionId(task.getExecutionId()).singleResult();
        // 当前审批节点
        String crruentActivityId = ee.getActivityId();
        BpmnModel bpmnModel = repositoryService.getBpmnModel(task.getProcessDefinitionId());
        FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(crruentActivityId);
        // 输出连线
        List<SequenceFlow> outFlows = flowNode.getOutgoingFlows();
        for (SequenceFlow sequenceFlow : outFlows) {
            // 下一个审批节点
            FlowElement targetFlow = sequenceFlow.getTargetFlowElement();
            if (targetFlow instanceof UserTask) {
                if(sequenceFlow.getConditionExpression().contains("AGREE")){
                    return (UserTask) targetFlow;
                }
            }
            // 如果下个审批节点为结束节点，审批节点为网关,业务展示不需要处理这种场景
            //if (targetFlow instanceof EndEvent) {}
        }
        return null;
    }


    /**
     * Title： setExclusiveGateway<br>
     * Description： 如果下一个节点为网关，查下一个用户节点<br>
     * author：傅欣荣 <br>
     * date：2022/2/8 16:16 <br>
     * @param
     * @return
     */
    private void setExclusiveGateway(FlowElement targetFlow) {
        //排他网关，获取连线信息
        List<SequenceFlow> targetFlows = ((ExclusiveGateway) targetFlow).getOutgoingFlows();
        for (SequenceFlow sequenceFlow : targetFlows) {
            //目标节点信息
            FlowElement targetFlowElement = sequenceFlow.getTargetFlowElement();
            if (targetFlowElement instanceof UserTask) {
                // do something
            } else if (targetFlowElement instanceof EndEvent) {
                // do something
            } else if (targetFlowElement instanceof ServiceTask) {
                // do something
            } else if (targetFlowElement instanceof ExclusiveGateway) {
                //递归寻找
            } else if (targetFlowElement instanceof SubProcess) {
                // do something
            }
        }
    }
}
