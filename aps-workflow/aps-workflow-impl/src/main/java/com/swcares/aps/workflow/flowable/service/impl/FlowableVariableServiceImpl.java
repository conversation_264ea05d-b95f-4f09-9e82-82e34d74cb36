package com.swcares.aps.workflow.flowable.service.impl;

import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.constants.WorkflowErrors;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.service.FlowableVariableService;
import com.swcares.baseframe.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.flowable.variable.api.history.HistoricVariableInstanceQuery;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClassName：FlowableVariableServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableVariableServiceImpl implements FlowableVariableService {

    @Autowired
    private HistoryService historyService;

    @Autowired
    private RuntimeService runtimeService;

    @Override
    public Object getProcessVariable(String processInstanceId, String variableName) {
        HistoricVariableInstanceQuery historicVariableInstanceQuery = historyService.createHistoricVariableInstanceQuery()
                .excludeTaskVariables()
                .processInstanceId(processInstanceId)
                .variableName(variableName);
        HistoricVariableInstance historicVariableInstance=historicVariableInstanceQuery.singleResult();

        if(historicVariableInstance!=null){
            return historicVariableInstance.getValue();
        }
        return null;
    }

    @Override
    public Map<String, Object> getTaskLocalVariable(Set<String> taskIds, String variableName) {
        HistoricVariableInstanceQuery historicVariableInstanceQuery = historyService.createHistoricVariableInstanceQuery()
                .taskIds(taskIds)
                .variableName(variableName);
        List<HistoricVariableInstance> list = historicVariableInstanceQuery.list();
        if(CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_MAP;
        }
        Map<String, Object> collect = list.stream()
                .collect(Collectors.toMap(HistoricVariableInstance::getTaskId, HistoricVariableInstance::getValue));
        return collect;
    }

    @Override
    public void syncProcessVariable(SyncWorkflowInfoDTO params) {
        ProcessInstance processInstance = runtimeService
                .createProcessInstanceQuery()
                .processInstanceId(params.getProcessInstanceId())
                .singleResult();

        if(processInstance==null){
            throw new BusinessException(WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST,
                    WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST_MES);
        }
        runtimeService.setVariable(params.getProcessInstanceId(),
                ProcessParamsConstants.SYNC_WORKFLOW_INFO,
                ProcessParamsUtil.formatSyncWorkflowInfo(params));

    }

}
