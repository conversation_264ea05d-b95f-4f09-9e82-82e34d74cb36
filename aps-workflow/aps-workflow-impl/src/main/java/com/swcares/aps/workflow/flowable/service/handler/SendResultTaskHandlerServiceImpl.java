package com.swcares.aps.workflow.flowable.service.handler;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.cpe.coordinate.model.enums.DataCategoryEnum;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadRequestDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.CoordinateUploadResponseDTO;
import com.swcares.aps.cpe.coordinate.model.receiver.dto.WorkflowDataCoordinateDTO;
import com.swcares.aps.cpe.coordinate.util.CoordinateApiUtil;
import com.swcares.aps.workflow.common.config.CoordinateConfig;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.CustomerDTO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.aps.workflow.flowable.service.SyncWorkflowLogService;
import com.swcares.baseframe.utils.lang.DateUtils;
import com.swcares.baseframe.utils.lang.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @ClassName：DefaultTaskHandlerServiceImpl
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2025/4/21 10:23
 * @version： v1.0
 */
@Service
@Slf4j
public class SendResultTaskHandlerServiceImpl implements SyncWorkflowTaskHandlerService {

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Autowired
    private CoordinateConfig coordinateConfig;

    @Autowired
    private FlowableWorkflowService flowableWorkflowService;

    @Autowired
    private SyncWorkflowLogService syncWorkflowLogService;
    @Override
    public boolean support(String taskType) {
        return StringUtils.equals(SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT,taskType);
    }

    @Override
    public void syncSchedule(SyncWorkflowTaskDO taskDO) {
        BaseQueryParamDTO paramDTO = BaseQueryParamDTO.builder().processInstanceId(taskDO.getOwnProcessId()).build();
        String syncWorkflowInfoJson = (String)flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.SYNC_WORKFLOW_INFO);
        log.info("NeedResult推送任务开始执行,从流程中获取syncWorkflowInfoJson:{}", syncWorkflowInfoJson);

        if(StringUtils.isBlank(syncWorkflowInfoJson)){
            if(StringUtils.isBlank(taskDO.getSyncConfig())){
                taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
                syncWorkflowTaskMapper.updateById(taskDO);
                log.info("NeedResult推送任务完成,当前还没有syncWorkflowInfo信息,进入定时任务执行队列等待syncWorkflowInfo到达后再执行");
                return;
            }
            syncWorkflowInfoJson=taskDO.getSyncConfig();
            log.info("NeedResult推送任务开始执行,从task中获取syncWorkflowInfoJson:{}", syncWorkflowInfoJson);
        }
        SyncWorkflowInfoDTO syncWorkflowInfoDTO = ProcessParamsUtil.parseSyncWorkflowInfo(syncWorkflowInfoJson);
        if(!StringUtils.equals(syncWorkflowInfoDTO.getSyncType(),SyncWorkflowTaskDO.TASK_TYPE_NEED_RESULT)){
            if(!SyncWorkflowTaskDO.END_YES.equals(taskDO.getEndFlag())){
                log.info("NeedResult推送任务完成,当前还没有NEED_RESULT类型syncWorkflowInfo信息,进入定时任务执行队列等待syncWorkflowInfo到达后再执行");
                taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
                syncWorkflowTaskMapper.updateById(taskDO);
                return;
            }
        }
        if(StringUtils.isBlank(taskDO.getLastOptionCode())){
            CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.currentUserTask(paramDTO);
            taskDO.setLastOptionCode(currentTaskActivityVO.getPreOptionCode());
            taskDO.setLastComment(currentTaskActivityVO.getPreComment());
            log.info("NeedResult推送任务开始执行,获取optionCode,comment:{},{}", currentTaskActivityVO.getPreOptionCode(),currentTaskActivityVO.getPreComment());
        }
        SyncWorkflowInfoDTO request=new SyncWorkflowInfoDTO();
        try{
            String customerInfo = (String)flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.CUSTOMER_INFO);
            log.info("NeedResult推送任务获取当前实例的关联流程信息:{},当前租户信息:{}",syncWorkflowInfoJson,customerInfo);
            CustomerDTO customerDTO = ProcessParamsUtil.parseCustomerInfo(customerInfo);
            request.setRequestBusinessName(customerDTO.getBusinessName());
            request.setRequestCustomer(customerDTO.getCustomer());
            request.setRequestCustomerCategory(customerDTO.getCustomerCategory());
            request.setRequestProcessInstanceId(taskDO.getOwnProcessId());
            request.setRequestTaskId(taskDO.getOwnTaskId());
            request.setRequestEndFlag(taskDO.getEndFlag());
            request.setProcessInstanceId(syncWorkflowInfoDTO.getRequestProcessInstanceId());
            if(StringUtils.equals(syncWorkflowInfoDTO.getSyncType(),SyncWorkflowTaskDO.TASK_TYPE_NEED_RESULT)){
                request.setTaskId(syncWorkflowInfoDTO.getRequestTaskId());
            }
            request.setRequestLastOptionCode(taskDO.getLastOptionCode());
            request.setRequestLastComment(taskDO.getLastComment());
            request.setSyncType(SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT);

            String workflowData = JSONUtil.toJsonStr(request);
            WorkflowDataCoordinateDTO coordinateDTO = getWorkflowDataCoordinateDTO(customerDTO, syncWorkflowInfoDTO, workflowData);

            //每次发送需要使用不同的requestID， requestID在每一个应用端中应该保证唯一性
            String requestID = taskDO.getOwnProcessId()
                    +taskDO.getOwnTaskId()+
                    DateUtils.formatDate(taskDO.getUpdatedTime(),DateUtils.DEF_PTN_YMD_HMS);

            CoordinateUploadRequestDTO requestDTO = CoordinateApiUtil.getCoordinateUploadRequestDTO(requestID,
                    //本次请求的数据分类，目前支持的数据分类都在DataCategoryEnum中定义
                    DataCategoryEnum.WORKFLOW_DATA.getCode(),
                    coordinateConfig.getAppClientId(),coordinateConfig.getAppSecretKeyVersion(),
                    coordinateConfig.getAppSecretKey(), JSONUtil.toJsonStr(coordinateDTO)
            );

            CoordinateUploadResponseDTO response = CoordinateApiUtil
                    .uploadCoordinateData(coordinateConfig.getCoordinateCenterUrl(), requestDTO);

            taskDO.setSyncProcessId(syncWorkflowInfoDTO.getProcessInstanceId());
            taskDO.setSyncTaskId(syncWorkflowInfoDTO.getTaskId());
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_SUCCESS);
            taskDO.setExecResult(CoordinateApiUtil.getDecryptedData(response, coordinateConfig.getAppSecretKey()));
            taskDO.setUpdatedTime(new Date());
            syncWorkflowTaskMapper.updateById(taskDO);
            syncWorkflowLogService.saveLog("推送SEND_RESULT任务[reqid:"+requestID+"]",workflowData);
        }catch (Exception e){
            log.error("SEND_RESULT同步流程信息出错",e);
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ERROR);
            taskDO.setUpdatedTime(new Date());
            String message="SEND_RESULT同步流程信息出错"+ ExceptionUtil.getExceptionMessage(e);
            if(message.length()>1000){ message=message.substring(0,1000); }
            taskDO.setExecResult(message);
            syncWorkflowTaskMapper.updateById(taskDO);
        }
    }

    @NotNull
    private static WorkflowDataCoordinateDTO getWorkflowDataCoordinateDTO(CustomerDTO customerDTO, SyncWorkflowInfoDTO syncWorkflowInfoDTO, String workflowData) {
        WorkflowDataCoordinateDTO coordinateDTO = new WorkflowDataCoordinateDTO();
        //发送方的租户的代码
        coordinateDTO.setSenderCustomer(customerDTO.getCustomer());
        //发送方的租户的类型
        coordinateDTO.setSenderCustomerCategory(customerDTO.getCustomerCategory());
        //接收方的租户的代码
        coordinateDTO.setReceiverCustomer(syncWorkflowInfoDTO.getRequestCustomer());
        //接收方租户的类型
        coordinateDTO.setReceiverCustomerCategory(syncWorkflowInfoDTO.getRequestCustomerCategory());
        //实际的业务数据，发送方的应用端和接收的应用端要事先约定好格式
        coordinateDTO.setData(workflowData);
        return coordinateDTO;
    }
}
