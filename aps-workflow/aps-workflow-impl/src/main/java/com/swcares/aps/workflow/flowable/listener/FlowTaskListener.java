package com.swcares.aps.workflow.flowable.listener;

import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.dto.CompleteProcessParamsDTO;
import com.swcares.aps.workflow.dto.CurrentTaskActivityVO;
import com.swcares.aps.workflow.dto.SyncWorkflowInfoDTO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.task.service.delegate.DelegateTask;
import org.flowable.task.service.delegate.TaskListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName：FlowTaskListener
 * @Description：@TODO
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2024/6/12 14:12
 * @version： v1.0
 */
@Slf4j
@Component
public class FlowTaskListener implements TaskListener {


    @Autowired
    private FlowableWorkflowService flowableWorkflowService;

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Override
    public void notify(DelegateTask delegateTask) {

        log.info("任务监听器:{}", delegateTask);
        log.info("任务监听器 ProcessDefinitionId:{}", delegateTask.getProcessDefinitionId());
        log.info("任务监听器 getProcessInstanceId:{}", delegateTask.getProcessInstanceId());
        log.info("任务监听器 taskID:{}", delegateTask.getId());

        BaseQueryParamDTO paramDTO = BaseQueryParamDTO.builder().processInstanceId(delegateTask.getProcessInstanceId()).build();
        String syncWorkflowInfoJson = (String)flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.SYNC_WORKFLOW_INFO);

        if(StringUtils.isNotBlank(syncWorkflowInfoJson)){
            log.info("任务监听器 syncWorkflowInfo:{}", syncWorkflowInfoJson);
        }
        
        Object optionCode=flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.OPTION_CODE);
        Object comment = flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.COMMENT);
        CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.currentUserTask(paramDTO);
        log.info("任务监听器 lastOptionCode:{},lastComment:{}", optionCode,comment);

        SyncWorkflowTaskDO taskDO=new SyncWorkflowTaskDO();
        taskDO.setOwnProcessId(delegateTask.getProcessInstanceId());
        taskDO.setLastOptionCode((String)optionCode);
        taskDO.setOwnTaskId(delegateTask.getId());
        taskDO.setLastComment((String)comment);
        taskDO.setOwnBusinessKey(currentTaskActivityVO.getBusiKey());
        taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
        taskDO.setExecTimes(0);
        taskDO.setCreatedTime(new Date());
        taskDO.setUpdatedTime(new Date());

        syncWorkflowTaskMapper.insert(taskDO);
        log.info("插入待通知流程数据,{}",JSONObject.toJSONString(taskDO));

        if(StringUtils.isNotBlank(syncWorkflowInfoJson)){

            SyncWorkflowInfoDTO syncWorkflowInfoDTO = ProcessParamsUtil.parseSyncWorkflowInfo(syncWorkflowInfoJson);
            if(syncWorkflowInfoDTO==null
                    || (!StringUtils.equalsIgnoreCase("1",syncWorkflowInfoDTO.getRequestEndFlag()))){
                return;
            }
            log.info("任务监听器 syncWorkflowInfo:{},同步任务已经结束，直接将结果告诉给当前流程即可", syncWorkflowInfoJson);
            CompleteProcessParamsDTO completeProcessParamsDTO=new CompleteProcessParamsDTO();
            completeProcessParamsDTO.setOptionCode(syncWorkflowInfoDTO.getRequestLastOptionCode());
            completeProcessParamsDTO.setTaskId(delegateTask.getId());
            if(StringUtils.isNotBlank(syncWorkflowInfoDTO.getRequestLastComment())){
                completeProcessParamsDTO.setComment(syncWorkflowInfoDTO.getRequestLastComment());
            }
            completeProcessParamsDTO.setCanDoSyncProcessTask(CompleteProcessParamsDTO.CAN_DO_SYNC_PROCESS_TASK_SIGN);
            flowableWorkflowService.completeTask(completeProcessParamsDTO);
            log.info("任务监听器执行自动审核处理当前节点完成");

        }
    }
}
