package com.swcares.aps.workflow.flowable.service.impl;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.dto.CurrentTaskActivityDTO;
import com.swcares.aps.workflow.dto.HistoryTaskAuditActivityDTO;
import com.swcares.aps.workflow.flowable.service.FlowableActivityService;
import com.swcares.aps.workflow.flowable.service.FlowableFlowElementService;
import com.swcares.aps.workflow.flowable.service.FlowableTaskService;
import com.swcares.aps.workflow.flowable.service.FlowableVariableService;
import com.swcares.aps.workflow.common.util.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricActivityInstance;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.impl.persistence.entity.CommentEntityImpl;
import org.flowable.engine.task.Comment;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * ClassName：FlowableActivityServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/10 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableActivityServiceImpl implements FlowableActivityService {

    @Autowired
    private HistoryService historyService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private FlowableTaskService flowableTaskService;

    @Autowired
    private FlowableVariableService flowableVariableService;

    @Autowired
    private FlowableFlowElementService flowableFlowElementService;


    @Override
    public List<CurrentTaskActivityDTO> getCurrentTaskActivity(HistoricProcessInstance historicProcessInstance) {
        if(StringUtils.isNotEmpty(historicProcessInstance.getEndActivityId())){
            return getEndActivityInfo(historicProcessInstance);
        }else{
            //流程还没有结束,获取下一个用户任务节点
            return getTaskActivityInfo(historicProcessInstance);
        }
    }

    @Override
    public List<HistoryTaskAuditActivityDTO> getHistoryTaskAuditActivity(HistoricProcessInstance historicProcessInstance) {

        String processInstanceId=historicProcessInstance.getId();
        List<HistoricTaskInstance> taskInstances = historyService.createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .orderByTaskCreateTime()
                .asc()
                .list();

        if(CollectionUtils.isEmpty(taskInstances)){
            return Collections.EMPTY_LIST;
        }

        List<Comment> processInstanceComments = taskService.getProcessInstanceComments(processInstanceId);
        Map<String, List<Comment>> processInstanceCommentMp =CollectionUtils.isNotEmpty(processInstanceComments)?
                processInstanceComments.stream().collect(Collectors.groupingBy(Comment::getTaskId)):Collections.EMPTY_MAP;

        List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS=new ArrayList<>(taskInstances.size());

        Set<String> taskIds = taskInstances.stream().map(HistoricTaskInstance::getId).collect(Collectors.toSet());
        Map<String,Object> taskLocalVariableMp=flowableVariableService.getTaskLocalVariable(taskIds,ProcessParamsConstants.EXT_VARS);

        for(HistoricTaskInstance taskInstance:taskInstances){
            HistoryTaskAuditActivityDTO taskAuditActivity=new HistoryTaskAuditActivityDTO();

            taskAuditActivity.setAssignee(taskInstance.getAssignee());
            taskAuditActivity.setTaskId(taskInstance.getId());
            taskAuditActivity.setTaskCreateTime(taskInstance.getCreateTime());
            taskAuditActivity.setTaskEndTime(taskInstance.getEndTime());
            taskAuditActivity.setNodeKey(taskInstance.getTaskDefinitionKey());
            taskAuditActivity.setNodeName(taskInstance.getName());
            List<Comment> comments = processInstanceCommentMp.get(taskInstance.getId());
            if(CollectionUtils.isNotEmpty(comments)){
                CommentEntityImpl comment = (CommentEntityImpl) comments.get(0);
                String fullCommentMessage = comment.getMessage();
                taskAuditActivity.setComment(TaskUtil.getTaskComment(fullCommentMessage));
                taskAuditActivity.setOptionCode(TaskUtil.getTaskOptionCode(fullCommentMessage));
                taskAuditActivity.setTaskStatus(TaskUtil.formatTaskStatus(taskAuditActivity.getOptionCode()));
            }

            if(taskLocalVariableMp.containsKey(taskInstance.getId())){
                JSONObject extVars = JSONUtil.parseObj(taskLocalVariableMp.get(taskInstance.getId()));
                taskAuditActivity.setExtVars(extVars);
            }

            historyTaskAuditActivityDTOS.add(taskAuditActivity);
        }
        return historyTaskAuditActivityDTOS;
    }

    @Override
    public HistoryTaskAuditActivityDTO getPreHistoryTaskAuditActivity(HistoricProcessInstance historicProcessInstance) {
        List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS=this.getHistoryTaskAuditActivity(historicProcessInstance);

        if(StringUtils.isNotEmpty(historicProcessInstance.getEndActivityId())){
            //说明流程已经结束，那么上一个task节点一定是最后一个active
            HistoryTaskAuditActivityDTO auditActivityDTO = historyTaskAuditActivityDTOS.get(historyTaskAuditActivityDTOS.size() - 1);
            return auditActivityDTO;
        }

        if(this.preNodeIsStart(historyTaskAuditActivityDTOS)){
            //说明当前还是第一个节点,那么就还没有上一个TaskActivity
            Object startExtVar = flowableVariableService.getProcessVariable(historicProcessInstance.getId(), ProcessParamsConstants.START_EXT_VARS);
            HistoryTaskAuditActivityDTO auditActivityDTO=new HistoryTaskAuditActivityDTO();
            if(startExtVar!=null){
                auditActivityDTO.setExtVars(JSONUtil.parseObj(startExtVar));
            }

            return auditActivityDTO;
        }
        if(historyTaskAuditActivityDTOS.size()<=1){
            return new HistoryTaskAuditActivityDTO();
        }
        //流程没有结束，那么上一个task一定是倒数第二个active
        HistoryTaskAuditActivityDTO auditActivityDTO = historyTaskAuditActivityDTOS.get(historyTaskAuditActivityDTOS.size() - 2);

        return auditActivityDTO;
    }

    @Override
    public CurrentTaskActivityDTO getNextTask(String taskId) {
        List<String> assignees=new ArrayList<>();
        UserTask userTask = flowableFlowElementService.nextFlowNode(taskId);
        if(userTask != null){
            CurrentTaskActivityDTO activityDTO = new CurrentTaskActivityDTO();
            activityDTO.setNodeName(userTask.getName());
            assignees.add(userTask.getAssignee());
            if(CollectionUtils.isNotEmpty(userTask.getCandidateUsers())){
                assignees.addAll(userTask.getCandidateUsers());
            }
            if(CollectionUtils.isNotEmpty(userTask.getCandidateGroups())){
                assignees.addAll(userTask.getCandidateGroups());
            }
            return activityDTO;
        }
        return null;
    }

    private boolean preNodeIsStart(List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS) {
        for(HistoryTaskAuditActivityDTO auditActivityDTO:historyTaskAuditActivityDTOS){
            //说明有task已经完成，上一个节点肯定不是start节点
            if(auditActivityDTO.getTaskEndTime()!=null){
                return false;
            }
        }
        return true;
    }


    private List<CurrentTaskActivityDTO> getEndActivityInfo(HistoricProcessInstance historicProcessInstance) {

        List<CurrentTaskActivityDTO> currentTaskActivityDTOS =new ArrayList<>(1);
        String endActivityId = historicProcessInstance.getEndActivityId();
        HistoricActivityInstance historicActivityInstance = historyService.createHistoricActivityInstanceQuery()
                .activityId(endActivityId)
                .processInstanceId(historicProcessInstance.getId())
                .singleResult();
        log.info("【aps-workflow-impl】currentUserTask-getCurrentTaskActivity-getEndActivityInfo-createHistoricActivityInstanceQuery,参数:{}", JSONUtil.toJsonStr(historicProcessInstance.getId()));
        CurrentTaskActivityDTO currentTaskActivityDTO = new CurrentTaskActivityDTO();
        currentTaskActivityDTO.setTaskId(null);
        currentTaskActivityDTO.setNodeKey(historicActivityInstance.getActivityId());
        currentTaskActivityDTO.setNodeName(historicActivityInstance.getActivityName());
        currentTaskActivityDTO.setAssignees(null);
        currentTaskActivityDTO.setIsEndActivity(true);

        currentTaskActivityDTOS.add(currentTaskActivityDTO);

        return currentTaskActivityDTOS;
    }

    private List<CurrentTaskActivityDTO> getTaskActivityInfo(HistoricProcessInstance historicProcessInstance) {
        String processInstanceId=historicProcessInstance.getId();
        List<Task> taskList = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        log.info("【aps-workflow-impl】currentUserTask-getCurrentTaskActivity-getTaskActivityInfo-processInstanceId,参数:{}, taskSize:{}", JSONUtil.toJsonStr(historicProcessInstance.getId()), taskList.size());

        List<CurrentTaskActivityDTO> currentTaskActivityDTOS =new ArrayList<>(taskList.size());
        for(Task task:taskList){

            CurrentTaskActivityDTO currentTaskActivityDTO = new CurrentTaskActivityDTO();
            currentTaskActivityDTO.setTaskId(task.getId());
            currentTaskActivityDTO.setNodeKey(task.getTaskDefinitionKey());
            currentTaskActivityDTO.setNodeName(task.getName());
            currentTaskActivityDTO.setAssignees(flowableTaskService.getAuditorPosition(task.getId()));
            currentTaskActivityDTO.setLastAssignee(flowableTaskService.getLastAssignee(task,historicProcessInstance));
            currentTaskActivityDTO.setIsEndActivity(false);

            currentTaskActivityDTOS.add(currentTaskActivityDTO);
        }
        log.info("【aps-workflow-impl】currentUserTask-getCurrentTaskActivity-getTaskActivityInfo-getAuditorPosition || getLastAssignee,参数:{}, taskSize:{}", JSONUtil.toJsonStr(historicProcessInstance.getId()), taskList.size());
        return currentTaskActivityDTOS;
    }

}
