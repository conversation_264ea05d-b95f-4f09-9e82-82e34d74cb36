package com.swcares.aps.workflow.flowable.controller;

import com.swcares.aps.workflow.dto.BaseQueryParamDTO;
import com.swcares.aps.workflow.flowable.service.FlowableProcessService;
import com.swcares.baseframe.common.base.BaseResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * ClassName：FlowableWorkflowTestController <br>
 * Description： flowableWorkflow测试接口相关controller
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/7 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/workflowTest")
@Slf4j
@Api(tags = "工作流测试接口")
public class FlowableWorkflowTestController {

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    private FlowableProcessService flowableProcessService;

    @ApiOperation(value = "getExecutionId")
    @PostMapping("/getExecutionId")
    public BaseResult<Object> getExecutionId(@RequestBody BaseQueryParamDTO params){

        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(params);
        List<Execution> list = runtimeService.createExecutionQuery().processInstanceId(historicProcessInstance.getId())
                .onlyChildExecutions()
                .list();
        String activityId = list.get(0).getActivityId();
        List<ActivityInstance> list1 = runtimeService.createActivityInstanceQuery().activityId(activityId).list();
        return BaseResult.ok(list.get(0).getId());
    }

    @ApiOperation(value = "triggerReceiveTask")
    @GetMapping("/triggerReceiveTask/{id}/{input}")
    public BaseResult<Object> triggerReceiveTask(@PathVariable(name="id") String id,@PathVariable(name="input")String input){

        Map<String,Object> map=new HashMap<>();
        map.put("input",input);
        runtimeService.trigger(id,map);
        return BaseResult.ok("ok");
    }

}
