package com.swcares.aps.workflow.flowable.service.impl;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.constants.WorkflowErrors;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.common.util.TaskUtil;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowErrorLogDO;
import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import com.swcares.aps.workflow.flowable.service.handler.SendResultTaskHandlerServiceImpl;
import com.swcares.aps.workflow.flowable.service.handler.SyncTaskHandlerServiceImpl;
import com.swcares.aps.workflow.flowable.service.FlowableActivityService;
import com.swcares.aps.workflow.flowable.service.FlowableProcessService;
import com.swcares.aps.workflow.flowable.service.FlowableTaskService;
import com.swcares.aps.workflow.flowable.service.FlowableVariableService;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.aps.workflow.flowable.service.SyncWorkflowLogService;
import com.swcares.aps.workflow.remote.api.util.WorkflowUtils;
import com.swcares.baseframe.common.exception.BusinessException;
import com.swcares.baseframe.utils.lang.ExceptionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ClassName：FlowableWorkflowServiceImpl <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/11 <br>
 * @version v1.0 <br>
 */
@Service
@Slf4j
public class FlowableWorkflowServiceImpl implements FlowableWorkflowService {

    @Autowired
    private FlowableProcessService flowableProcessService;

    @Autowired
    private FlowableActivityService flowableActivityService;

    @Autowired
    private FlowableVariableService flowableVariableService;

    @Autowired
    private FlowableTaskService flowableTaskService;

    @Autowired
    private SyncTaskHandlerServiceImpl syncTaskHandlerService;

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Autowired
    private SyncWorkflowLogService syncWorkflowLogService;

    @Autowired
    private SendResultTaskHandlerServiceImpl sendResultTaskHandlerService;

    @Override
    public CurrentTaskActivityVO completeTask(CompleteProcessParamsDTO params) {
        String processInstanceId=flowableProcessService.completeTask(params);
        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(processInstanceId);
        List<CurrentTaskActivityDTO> nextUserTasks= completeCurrentAutomaticTask(params,historicProcessInstance);
        return wrapCurrentTaskActivityVO(historicProcessInstance,nextUserTasks);
    }


    @Override
    public CurrentTaskActivityVO  startProcess(StartProcessParamsDTO params) {
        ProcessInstance processInstance = flowableProcessService.startProcess(params);
        log.info("【aps-workflow-impl】startProcess-启动实例结束,startProcess:{}", JSONUtil.toJsonStr(params));
        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(processInstance.getId());
        log.info("【aps-workflow-impl】startProcess-处理历史实例完成,historicProcessInstance:{}", JSONUtil.toJsonStr(processInstance.getId()));
        List<CurrentTaskActivityDTO> nextUserTasks = flowableActivityService.getCurrentTaskActivity(historicProcessInstance);
        log.info("【aps-workflow-impl】startProcess-获取当前任务完成,getCurrentTaskActivity:{}", JSONUtil.toJsonStr(params));

        if(StartProcessParamsDTO.RESPONSE.equals(params.getProcessFlag())){
            SyncWorkflowTaskDO taskDO = new SyncWorkflowTaskDO();
            taskDO.setOwnProcessId(processInstance.getProcessInstanceId());
            taskDO.setOwnBusinessKey(params.getBusinessKey());
            taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ING);
            taskDO.setExecTimes(0);
            taskDO.setCreatedTime(new Date());
            taskDO.setUpdatedTime(new Date());
            taskDO.setTaskType(SyncWorkflowTaskDO.TASK_TYPE_SYNC);
            syncWorkflowTaskMapper.insert(taskDO);
            syncTaskHandlerService.syncSchedule(taskDO);
        }


        return wrapCurrentTaskActivityVO(historicProcessInstance,nextUserTasks);
    }

    private List<CurrentTaskActivityDTO> completeCurrentAutomaticTask(CompleteProcessParamsDTO params, HistoricProcessInstance historicProcessInstance) {
        List<CurrentTaskActivityDTO> nextUserTasks = flowableActivityService.getCurrentTaskActivity(historicProcessInstance);
        List<String> pushTaskIds = nextUserTasks.stream()
                .filter(t -> TaskUtil.isPushTask(t.getNodeKey()))
                .map(CurrentTaskActivityDTO::getTaskId)
                .collect(Collectors.toList());
        String processInstanceId=historicProcessInstance.getId();
        if(CollectionUtils.isNotEmpty(pushTaskIds)){
            flowableTaskService.completePushTask(params,pushTaskIds);
            historicProcessInstance=flowableProcessService.historicProcessInstance(processInstanceId);
            nextUserTasks = flowableActivityService.getCurrentTaskActivity(historicProcessInstance);
        }
        List<String> automaticTaskIds = nextUserTasks.stream()
                .filter(t -> TaskUtil.isAutomaticTask(t.getNodeKey()))
                .map(CurrentTaskActivityDTO::getTaskId)
                .collect(Collectors.toList());

        //执行自动审核任务
        while(CollectionUtils.isNotEmpty(automaticTaskIds)){

            flowableTaskService.completeAutomaticTask(params, automaticTaskIds);

            historicProcessInstance=flowableProcessService.historicProcessInstance(processInstanceId);
            nextUserTasks = flowableActivityService.getCurrentTaskActivity(historicProcessInstance);

            automaticTaskIds = nextUserTasks.stream()
                    .filter(t -> TaskUtil.isAutomaticTask(t.getNodeKey()))
                    .map(CurrentTaskActivityDTO::getTaskId)
                    .collect(Collectors.toList());
        }
        return nextUserTasks;
    }

    @Override
    public CurrentTaskActivityVO currentUserTask(BaseQueryParamDTO params) {
        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(params);
        log.info("【aps-workflow-impl】currentUserTask-historicProcessInstance,参数:{}", JSONUtil.toJsonStr(params));
        List<CurrentTaskActivityDTO> currentTaskActivityDTOS = flowableActivityService.getCurrentTaskActivity(historicProcessInstance);
        log.info("【aps-workflow-impl】currentUserTask-getCurrentTaskActivity,参数:{}", JSONUtil.toJsonStr(params));
        return wrapCurrentTaskActivityVO(historicProcessInstance,currentTaskActivityDTOS);
    }

    /**
     * 当前还没有考虑会签节点
     * @param historicProcessInstance
     * @param currentTaskActivityDTOS
     * @return
     */
    private CurrentTaskActivityVO wrapCurrentTaskActivityVO(HistoricProcessInstance historicProcessInstance,
                                                            List<CurrentTaskActivityDTO> currentTaskActivityDTOS) {
        CurrentTaskActivityVO currentTaskActivityVO=new CurrentTaskActivityVO();

        currentTaskActivityVO.setCurrentTaskActivityDTOS(currentTaskActivityDTOS);
        currentTaskActivityVO.setBusiKey(historicProcessInstance.getBusinessKey());
        currentTaskActivityVO.setProcessInstanceId(historicProcessInstance.getId());
        currentTaskActivityVO.setModelCode(historicProcessInstance.getProcessDefinitionKey());
        currentTaskActivityVO.setModelName(historicProcessInstance.getProcessDefinitionName());

        HistoryTaskAuditActivityDTO auditActivityDTO=flowableActivityService.getPreHistoryTaskAuditActivity(historicProcessInstance);
        currentTaskActivityVO.setPreTaskExtVars(auditActivityDTO.getExtVars());
        currentTaskActivityVO.setPreOptionCode(auditActivityDTO.getOptionCode());
        currentTaskActivityVO.setPreAssignee(auditActivityDTO.getAssignee());
        currentTaskActivityVO.setPreComment(auditActivityDTO.getComment());

        Object businessData= flowableVariableService.getProcessVariable(historicProcessInstance.getId(), ProcessParamsConstants.BUSINESS_DATA);
        if(businessData!=null){
            currentTaskActivityVO.setBusiData(JSONUtil.parseObj(businessData));
        }

        return currentTaskActivityVO;
    }

    @Override
    public HistoryTaskAuditActivityVO historyTaskAuditActivity(BaseQueryParamDTO params) {
        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(params);
        List<HistoryTaskAuditActivityDTO> historyTaskAuditActivityDTOS=flowableActivityService.getHistoryTaskAuditActivity(historicProcessInstance);
        String processVariable = (String)this.getProcessVariable(params, ProcessParamsConstants.SYNC_WORKFLOW_INFO);
        SyncWorkflowInfoDTO syncWorkflowInfoDTO = ProcessParamsUtil.parseSyncWorkflowInfo(processVariable);
        HistoryTaskAuditActivityVO historyTaskAuditActivityVO=new HistoryTaskAuditActivityVO();
        Object businessData= flowableVariableService
                .getProcessVariable(historicProcessInstance.getId(), ProcessParamsConstants.BUSINESS_DATA);
        if(businessData!=null){
            historyTaskAuditActivityVO.setBusiData(JSONUtil.parseObj(businessData));
        }
        historyTaskAuditActivityVO.setHistoryTaskAuditActivityDTOS(historyTaskAuditActivityDTOS);
        historyTaskAuditActivityVO.setBusiKey(historicProcessInstance.getBusinessKey());
        historyTaskAuditActivityVO.setProcessInstanceId(historicProcessInstance.getId());
        historyTaskAuditActivityVO.setModelCode(historicProcessInstance.getProcessDefinitionKey());
        historyTaskAuditActivityVO.setModelName(historicProcessInstance.getProcessDefinitionName());
        if(syncWorkflowInfoDTO!=null){
            historyTaskAuditActivityVO.setRequestBusinessName(syncWorkflowInfoDTO.getRequestBusinessName());
        }

        return historyTaskAuditActivityVO;
    }

    @Override
    public Object getProcessVariable(BaseQueryParamDTO paramDTO, String variableName) {
        HistoricProcessInstance historicProcessInstance=flowableProcessService.historicProcessInstance(paramDTO);
        return flowableVariableService.getProcessVariable(historicProcessInstance.getId(),variableName);
    }

    @Override
    public CurrentTaskActivityDTO getNextTask(String taskId) {
        return flowableActivityService.getNextTask(taskId);
    }

    @Override
    public void syncProcess(SyncWorkflowInfoDTO params) {
        try{
            syncWorkflowLogService.saveLog("收到syncProcess消息", JSONObject.toJSONString(params));

            try {
                flowableVariableService.syncProcessVariable(params);
            }catch (BusinessException e){
                if(e.getCode()== WorkflowErrors.PROCESS_INSTANCE_NOT_EXIST){
                    syncProcessEndSendResult(params);
                    return;
                }
            }

            if(StringUtils.equals(SyncWorkflowTaskDO.TASK_TYPE_SYNC,params.getSyncType())){
                log.info("syncProcess接收到同步类型为SYNC数据,第一次通信成功");
                return;
            }
            if(StringUtils.equals(SyncWorkflowTaskDO.TASK_TYPE_NEED_RESULT,params.getSyncType())){
                log.info("syncProcess接收到同步类型为NEED_RESULT数据");
                return;
            }

            List<Task> taskList = flowableTaskService.getCurrentProcessTask(params.getProcessInstanceId());
            if(CollectionUtils.isEmpty(taskList)){
                log.error("syncProcess 没有执行流程同步,{},流程已经没有待审核得节点",params.getProcessInstanceId());
                return;
            }

            List<Task> tasks = taskList
                    .stream()
                    .filter(t-> WorkflowUtils.isSyncProcessTask(t.getTaskDefinitionKey()))
                    .collect(Collectors.toList());

            if(StringUtils.isNotEmpty(params.getTaskId()) && CollectionUtils.isNotEmpty(tasks)){
                tasks = tasks
                        .stream()
                        .filter(t -> params.getTaskId().equals(t.getId()))
                        .collect(Collectors.toList());

            }
            if(CollectionUtils.isEmpty(tasks)){
                log.error("syncProcess 没有执行流程同步,{},流程没有同步节点",params.getProcessInstanceId());
                return;
            }

            for(Task task:tasks){
                CompleteProcessParamsDTO completeProcessParamsDTO=new CompleteProcessParamsDTO();
                completeProcessParamsDTO.setOptionCode(params.getRequestLastOptionCode());
                completeProcessParamsDTO.setTaskId(task.getId());
                if(StringUtils.isNotBlank(params.getRequestLastComment())){
                    completeProcessParamsDTO.setComment(params.getRequestLastComment());
                }
                completeProcessParamsDTO.setCanDoSyncProcessTask(CompleteProcessParamsDTO.CAN_DO_SYNC_PROCESS_TASK_SIGN);
                completeTask(completeProcessParamsDTO);
            }
        }catch (Exception e){
            saveErrorLogToDB(params, e);
            throw e;
        }
    }

    private void syncProcessEndSendResult(SyncWorkflowInfoDTO params) {
        if(StringUtils.equals(params.getSyncType(),SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT)){return;}
        String processInstanceId = params.getProcessInstanceId();
        BaseQueryParamDTO queryParamDTO = BaseQueryParamDTO.builder().processInstanceId(processInstanceId).build();
        CurrentTaskActivityVO currentTaskActivityVO = currentUserTask(queryParamDTO);
        if(!currentTaskActivityVO.getCurrentTaskActivityDTOS().get(0).getIsEndActivity()){return;}
        log.info("syncProcess进入到syncProcessEndSendResult流程:{}",JSONUtil.toJsonStr(params));
        SyncWorkflowTaskDO taskDO=new SyncWorkflowTaskDO();
        taskDO.setOwnProcessId(processInstanceId);
        taskDO.setLastOptionCode(currentTaskActivityVO.getPreOptionCode());
        taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_ING);
        taskDO.setOwnBusinessKey(currentTaskActivityVO.getBusiKey());
        taskDO.setLastComment(currentTaskActivityVO.getPreComment());
        taskDO.setEndFlag(SyncWorkflowTaskDO.END_YES);
        taskDO.setExecTimes(0);
        taskDO.setCreatedTime(new Date());
        taskDO.setUpdatedTime(new Date());
        taskDO.setSyncConfig(JSONUtil.toJsonStr(params));
        taskDO.setTaskType(SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT);
        syncWorkflowTaskMapper.insert(taskDO);
        sendResultTaskHandlerService.syncSchedule(taskDO);
    }

    public void saveErrorLogToDB(SyncWorkflowInfoDTO params, Exception e) {
        SyncWorkflowErrorLogDO syncWorkflowErrorLogDO=new SyncWorkflowErrorLogDO();
        syncWorkflowErrorLogDO.setCreatedTime(new Date());
        syncWorkflowErrorLogDO.setRequestParam("处理syncProcess异常:"+JSONObject.toJSONString(params));
        String exceptionMessage = ExceptionUtil.getExceptionMessage(e);
        if(StringUtils.length(exceptionMessage)>1000){
            exceptionMessage = exceptionMessage.substring(0, 1000);
        }
        syncWorkflowErrorLogDO.setErrorLog(exceptionMessage);
        syncWorkflowLogService.saveLog(syncWorkflowErrorLogDO);
    }

}
