package com.swcares.aps.workflow.flowable.listener;

import com.swcares.aps.workflow.flowable.entity.SyncWorkflowTaskDO;
import com.swcares.aps.workflow.flowable.mapper.SyncWorkflowTaskMapper;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * @ClassName：ExampleExecutionListenerOne
 * @Description：事件监听；start事件->整个流程开始时调用该类；end事件taskactivity结束时调用
 * @Copyright：© 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @author： chenmd
 * @Date： 2022/3/17 17:47
 * @version： v1.0
 */
@Slf4j
@Component
public class FlowEndExecutionListener implements ExecutionListener {

    @Autowired
    private SyncWorkflowTaskMapper syncWorkflowTaskMapper;

    @Override
    public void notify(DelegateExecution delegateExecution) {

        SyncWorkflowTaskDO taskDO=new SyncWorkflowTaskDO();
        taskDO.setOwnProcessId(delegateExecution.getProcessInstanceId());
        taskDO.setExecStatus(SyncWorkflowTaskDO.STATUS_WAITE);
        taskDO.setOwnBusinessKey(delegateExecution.getProcessInstanceBusinessKey());
        taskDO.setEndFlag(SyncWorkflowTaskDO.END_YES);
        taskDO.setExecTimes(-100);
        taskDO.setCreatedTime(new Date());
        taskDO.setUpdatedTime(new Date());

        taskDO.setTaskType(SyncWorkflowTaskDO.TASK_TYPE_SEND_RESULT);
        syncWorkflowTaskMapper.insert(taskDO);

    }
}
