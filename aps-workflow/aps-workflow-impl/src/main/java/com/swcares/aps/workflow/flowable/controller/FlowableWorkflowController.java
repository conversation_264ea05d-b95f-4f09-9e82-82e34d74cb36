package com.swcares.aps.workflow.flowable.controller;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.swcares.aps.workflow.common.constants.ProcessParamsConstants;
import com.swcares.aps.workflow.common.constants.WorkflowErrors;
import com.swcares.aps.workflow.common.util.ProcessParamsUtil;
import com.swcares.aps.workflow.common.util.TaskUtil;
import com.swcares.aps.workflow.dto.*;
import com.swcares.aps.workflow.enums.AuditStatusEnum;
import com.swcares.aps.workflow.flowable.service.FlowableWorkflowService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.exception.BusinessException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;


/**
 * ClassName：FlowableProcessController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/2/8 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/workflow")
@Slf4j
@Api(tags = "工作流接口")
public class FlowableWorkflowController {

    @Autowired
    private FlowableWorkflowService flowableWorkflowService;


    @ApiOperation(value = "启动流程接口")
    @PostMapping("/startProcess")
    public BaseResult<CurrentTaskActivityVO> startProcess(@RequestBody StartProcessParamsDTO params){

        log.info("【aps-workflow-impl】startProcess,begin:{}", JSONUtil.toJsonStr(params));
        CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.startProcess(params);
        log.info("【aps-workflow-impl】startProcess,end:{},{}", JSONUtil.toJsonStr(params),JSONUtil.toJsonStr(currentTaskActivityVO));

        return BaseResult.ok(currentTaskActivityVO);

    }

    @ApiOperation(value = "完成用户任务")
    @PostMapping("/completeTask")
    public BaseResult<CurrentTaskActivityVO> completeTask(@RequestBody CompleteProcessParamsDTO params){

        log.info("【aps-workflow-impl】completeTask,begin:{}", JSONUtil.toJsonStr(params));
        AuditStatusEnum statusEnum = AuditStatusEnum.build(params.getOptionCode(), false);
        if(null==statusEnum){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR,"不合法的optionCode");
        }
        CurrentTaskActivityVO currentTaskActivityVO  = flowableWorkflowService.completeTask(params);
        log.info("【aps-workflow-impl】completeTask,end:{},{}", JSONUtil.toJsonStr(params),JSONUtil.toJsonStr(currentTaskActivityVO));

        return BaseResult.ok(currentTaskActivityVO);

    }

    @ApiOperation(value = "获取当前待处理任务")
    @PostMapping("/currentUserTask")
    public BaseResult<CurrentTaskActivityVO> currentUserTask(@RequestBody BaseQueryParamDTO paramDTO){

        log.info("【aps-workflow-impl】currentUserTask,begin:{}", JSONUtil.toJsonStr(paramDTO));
        if(StringUtils.isEmpty(paramDTO.getBusinessKey()) &&
                StringUtils.isEmpty(paramDTO.getProcessInstanceId())){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR);
        }

        CurrentTaskActivityVO currentTaskActivityVO = flowableWorkflowService.currentUserTask(paramDTO);
        log.info("【aps-workflow-impl】currentUserTask,end:{},{}", JSONUtil.toJsonStr(paramDTO), JSONUtil.toJsonStr(currentTaskActivityVO));

        return BaseResult.ok(currentTaskActivityVO);
    }

    @ApiOperation(value = "获取最近一次用户任务审核操作")
    @PostMapping("/lastTaskOptionCode")
    public BaseResult<String> lastTaskOptionCode(@RequestBody BaseQueryParamDTO paramDTO){

        log.info("【aps-workflow-impl】lastTaskOptionCode,begin:{}", JSONUtil.toJsonStr(paramDTO));
        if(StringUtils.isEmpty(paramDTO.getBusinessKey()) &&
                StringUtils.isEmpty(paramDTO.getProcessInstanceId())){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR);
        }
        Object variable=flowableWorkflowService.getProcessVariable(paramDTO, ProcessParamsConstants.OPTION_CODE);
        log.info("【aps-workflow-impl】lastTaskOptionCode,end:{},{}", JSONUtil.toJsonStr(paramDTO),JSONUtil.toJsonStr(variable));

        if(variable!=null) {
            return BaseResult.ok((String) variable);
        }
        return BaseResult.ok("");
    }

    @ApiOperation(value = "获取用户任务审核操作历史")
    @PostMapping("/historyTaskAuditActivity")
    public BaseResult<HistoryTaskAuditActivityVO> historyTaskAuditActivity(@RequestBody BaseQueryParamDTO paramDTO){

        log.info("【aps-workflow-impl】historyTaskAuditActivity,begin:{}", JSONUtil.toJsonStr(paramDTO));
        if(StringUtils.isEmpty(paramDTO.getBusinessKey()) &&
                StringUtils.isEmpty(paramDTO.getProcessInstanceId())){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR);
        }

        HistoryTaskAuditActivityVO historyTaskAuditActivityVO=flowableWorkflowService.historyTaskAuditActivity(paramDTO);
        log.info("【aps-workflow-impl】historyTaskAuditActivity,end:{},{}", JSONUtil.toJsonStr(paramDTO), JSONUtil.toJsonStr(historyTaskAuditActivityVO));

        //过滤掉 nodeKey 以 "push" 开头的数据（push节点是向对方发送审核结果）
        List<HistoryTaskAuditActivityDTO> filteredItems = historyTaskAuditActivityVO.getHistoryTaskAuditActivityDTOS().stream()
                .filter(item -> !TaskUtil.isPushTask(item.getNodeKey())) // 过滤条件
                .collect(Collectors.toList());
        historyTaskAuditActivityVO.setHistoryTaskAuditActivityDTOS(filteredItems);

        return BaseResult.ok(historyTaskAuditActivityVO);
    }


    @ApiOperation(value = "获取当前节点的下一个节点接口")
    @GetMapping("/getNextTask")
    public BaseResult<CurrentTaskActivityDTO> getNextTask(@RequestParam(value="taskId",required=true)String taskId){

        log.info("【aps-workflow-impl】getNextTask,begin:{}", taskId);
        if(StringUtils.isEmpty(taskId)){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR);
        }
        CurrentTaskActivityDTO nextTask = flowableWorkflowService.getNextTask(taskId);
        log.info("【aps-workflow-impl】getNextTask,end:{},{}", taskId,JSONUtil.toJsonStr(nextTask));

        return  BaseResult.ok(nextTask);

    }

    @ApiOperation(value = "获取流程参数配置")
    @GetMapping("/getProcessVariable")
    public BaseResult<Object> getProcessVariable(String processInstanceId,String variableName){

        BaseQueryParamDTO queryParamDTO= BaseQueryParamDTO.builder().processInstanceId(processInstanceId).build();
        log.info("【aps-workflow-impl】getProcessVariable,begin,processInstanceId:{},variableName:{}",processInstanceId, variableName);
        //TODO 根据协同中心要求改接口
        Object processVariable = flowableWorkflowService.getProcessVariable(queryParamDTO, variableName);
        log.info("【aps-workflow-impl】getProcessVariable,end:{}", variableName);

        return BaseResult.ok(processVariable);

    }



    @ApiOperation(value = "获取流程实例变量信息")
    @GetMapping("/getCustomerVariable")
    public BaseResult<CustomerDTO> getCustomerVariable(String processInstanceId){

        BaseQueryParamDTO queryParamDTO= BaseQueryParamDTO.builder().processInstanceId(processInstanceId).build();
        log.info("【aps-workflow-impl】getCustomerVariable,begin,processInstanceId:{}",processInstanceId);
        //TODO 根据协同中心要求改接口
        String customerInfo = (String)flowableWorkflowService.getProcessVariable(queryParamDTO, ProcessParamsConstants.CUSTOMER_INFO);
        log.info("【aps-workflow-impl】getCustomerVariable,end,processInstanceId:{},customerInfo:{}",processInstanceId,customerInfo);
        if(StringUtils.isBlank(customerInfo)){
            return BaseResult.ok();
        }
        return BaseResult.ok(ProcessParamsUtil.parseCustomerInfo(customerInfo));

    }

    @ApiOperation(value = "新同步流程流程接口")
    @PostMapping("/sync/process")
    public BaseResult<CurrentTaskActivityVO> syncProcess(@RequestBody String request){
        log.info("【aps-workflow-impl】syncProcess,begin:{}", request);
        SyncWorkflowInfoDTO params= JSONObject.parseObject(request,SyncWorkflowInfoDTO.class);

        if(StringUtils.isBlank(params.getProcessInstanceId())
                || StringUtils.isBlank(params.getRequestProcessInstanceId())
                || StringUtils.isBlank(params.getRequestBusinessName())){
            throw new BusinessException(WorkflowErrors.PARAM_ERROR,"请求参数错误");
        }
        //TODO 根据协同中心要求改接口
        flowableWorkflowService.syncProcess(params);
        log.info("【aps-workflow-impl】syncProcess,end:{}", JSONUtil.toJsonStr(params));

        return BaseResult.ok();

    }


}