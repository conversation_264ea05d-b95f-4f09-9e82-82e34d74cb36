<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>aps</artifactId>
        <groupId>com.swcares.aps</groupId>
        <version>1.0.1_aps-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>aps-workflow</artifactId>
    <packaging>pom</packaging>

    <modules>
        <module>aps-workflow-impl</module>
        <module>aps-workflow-model</module>
        <module>aps-workflow-remote-api</module>
    </modules>
    
    <properties>
        <flowable.version>6.7.2</flowable.version>
        <h2.version>1.4.200</h2.version>
        <lombok.version>1.18.14</lombok.version>
        <liquibase.version>4.3.5</liquibase.version>
        <spingfox.version>3.0.0</spingfox.version>
        <mysql.version>8.0.25</mysql.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.swcares.aps</groupId>
                <artifactId>aps-workflow-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.oceanbase</groupId>
                <artifactId>oceanbase-client</artifactId>
                <version>2.4.3</version>
            </dependency>
            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-idm</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-modeler</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-task</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <dependency>
                <groupId>org.flowable</groupId>
                <artifactId>flowable-spring-boot-starter-ui-admin</artifactId>
                <version>${flowable.version}</version>
            </dependency>

            <!-- H2 -->
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>

            <!-- MySql -->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.version}</version>
            </dependency>

            <dependency>
                <groupId>org.liquibase</groupId>
                <artifactId>liquibase-core</artifactId>
                <version>${liquibase.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>javax.xml.bind</groupId>
                        <artifactId>jaxb-api</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>
            
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
                <scope>provided</scope>
            </dependency>

            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-boot-starter</artifactId>
                <version>${spingfox.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>