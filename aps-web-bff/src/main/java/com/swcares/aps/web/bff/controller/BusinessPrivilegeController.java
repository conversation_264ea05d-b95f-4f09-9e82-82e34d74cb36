package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeRequestDTO;
import com.swcares.aps.compensation.model.privilege.dto.SearchBusinessPrivilegeDTO;
import com.swcares.aps.compensation.remote.api.privilege.BusinessPrivilegeApi;
import com.swcares.aps.compensation.model.privilege.dto.BusinessPrivilegeDTO;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR> <PERSON> Yi
 * @Classname BusinessPrivilegeController
 * @Description @TODO
 * @Copyright: © 2021 xnky.travelsky.net Inc. All rights reserved.
 * @Company：Aviation Cares Of Southwest Chen Du LTD
 * @Date 2024/6/12 08:41
 * @Version 1.0
 */
@RestController
@RequestMapping("/businessPrivilege")
@Api(tags = "业务授权接口")
@ApiVersion(value = "业务授权 v1.0")
@Valid
public class BusinessPrivilegeController {
    @Autowired
    BusinessPrivilegeApi businessPrivilegeApi;

    @ApiOperation(value = "新建对机场的业务授权")
    @PostMapping("/create")
    public BaseResult<Boolean> createAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto)
    {
        return businessPrivilegeApi.createAirportBusinessPrivilege(dto);
    }

    @ApiOperation(value = "修改对机场的业务授权")
    @PostMapping("/update")
    public BaseResult<Boolean> updateAirportBusinessPrivilege(@RequestBody BusinessPrivilegeRequestDTO dto)
    {
        return businessPrivilegeApi.updateAirportBusinessPrivilege(dto);
    }

    @ApiOperation(value="分页查询业务授权列表")
    @PostMapping("/list")
    public PagedResult<List<BusinessPrivilegeDTO>> list(@RequestBody SearchBusinessPrivilegeDTO searchCriteria)
    {
        return businessPrivilegeApi.list(searchCriteria);
    }

    @ApiOperation(value="查询指定ID的业务授权详情")
    @GetMapping("/detail")
    public BaseResult<BusinessPrivilegeDTO> getDetail(@RequestParam(value = "id") Long id)
    {
        return businessPrivilegeApi.getDetail(id);
    }


    @ApiOperation(value="查询对指定的机场的业务授权详情")
    @GetMapping("/detailByAirportCode")
    public BaseResult<BusinessPrivilegeDTO> getDetailByAirportCode(@RequestParam(value = "airportCode") String airportCode)
    {
        return businessPrivilegeApi.getDetailByAirportCode(airportCode);
    }

    @ApiOperation(value="启用")
    @PostMapping("/enable")
    public BaseResult<Boolean> enableBusinessPrivileges(@RequestBody List<Long> ids)
    {
        return businessPrivilegeApi.enableBusinessPrivileges(ids);
    }

    @ApiOperation(value="停用")
    @PostMapping("/disable")
    public BaseResult<Boolean> disableBusinessPrivileges(@RequestBody List<Long> ids)
    {
        return businessPrivilegeApi.disableBusinessPrivileges(ids);
    }
}
