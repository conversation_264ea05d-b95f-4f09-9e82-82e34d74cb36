package com.swcares.aps.web.bff.service.impl;

import cn.hutool.json.JSONUtil;
import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.service.ReplaceRejectReasonService;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.security.UserContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description：代领人审核拒绝原由<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月12日  <br>
 * @version v1.0 <br>
 */
@Slf4j
@Service
public class ReplaceRejectReasonServiceImpl implements ReplaceRejectReasonService {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @Override
    public BaseResult<Map<String, RuleManageDTO>> pages() {
        return compensationInfoApi.replaceRejectReasonPage();
    }

    @Override
    public BaseResult<List<ReplaceRejectReasonDTO>> getAllRejectReason(String airCode) {
        BaseResult<List<ReplaceRejectReasonDTO>> result = compensationInfoApi.getAllReplaceRejectReason();
        log.info("【aps-component-bff】ReplaceRejectReason,getAllRejectReason,result:{}", JSONUtil.toJsonStr(result));
        if (result.getData() != null && result.getData().size() > 0) {
            result.setData(result.getData().stream().filter(dto -> dto.getAirCode().equals(airCode)).collect(Collectors.toList()));
        }
        return result;
    }

    @Override
    public BaseResult<Object> delete(ReplaceConfigDeleteDTO deleteDTO) {
        deleteDTO.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        BaseResult<Object> result = compensationInfoApi.deleteReplaceRejectReason(deleteDTO);
        log.info("【aps-component-bff】ReplaceRejectReason,delete,result:{}", JSONUtil.toJsonStr(deleteDTO), JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public BaseResult<Object> update(ReplaceRejectReasonDTO dto) {
        dto.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        BaseResult<Object> result = compensationInfoApi.updateReplaceRejectReason(dto);
        log.info("【aps-component-bff】ReplaceRejectReason,update,result:{}", JSONUtil.toJsonStr(dto), JSONUtil.toJsonStr(result));
        return result;
    }

    @Override
    public BaseResult<Object> create(ReplaceRejectReasonDTO dto) {
        dto.setUpdatedBy(String.valueOf(UserContext.getUserId()));
        dto.setCreatedBy(String.valueOf(UserContext.getUserId()));
        BaseResult<Object> result = compensationInfoApi.saveReplaceRejectReason(dto);
        log.info("【aps-component-bff】ReplaceRejectReason,create,result:{}", JSONUtil.toJsonStr(dto), JSONUtil.toJsonStr(result));
        return result;
    }
}
