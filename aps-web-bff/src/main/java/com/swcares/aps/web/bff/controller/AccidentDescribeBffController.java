package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDTO;
import com.swcares.aps.compensation.model.dataconfig.dto.CompensationConfigDeleteDTO;
import com.swcares.aps.compensation.model.dataconfig.vo.CompensationConfigVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


@RestController
@RequestMapping("/baggage/accidentDescribeBff")
@Api(tags = "异常行李事故说明维护接口")
@ApiVersion(value = "异常行李事故说明维护接口 v1.0")
public class AccidentDescribeBffController extends BaseController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;


    @GetMapping("/getAllAccidentDescribeSelect")
    @ApiOperation(value = "获取所有事故说明")
    public BaseResult<List<CompensationConfigVO>> getAllAccidentDescribeSelect(String airCode) {
        return compensationInfoApi.getAllAccidentDescribeSelect(airCode);
    }

    @PostMapping("/delete")
    @ApiOperation(value = "刪除事故说明记录")
    public BaseResult<Object> delete(@RequestBody CompensationConfigDeleteDTO dto) {
        return compensationInfoApi.deleteAccidentDescribe(dto);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改事故说明记录")
    public BaseResult<Object>  update(@RequestBody CompensationConfigDTO dto) {
        return compensationInfoApi.updateAccidentDescribe(dto);
    }

    @PostMapping("/create")
    @ApiOperation(value = "添加事故说明记录")
    public BaseResult<Object>  create(@RequestBody CompensationConfigDTO dto) {
        return compensationInfoApi.createAccidentDescribe(dto);
    }
}
