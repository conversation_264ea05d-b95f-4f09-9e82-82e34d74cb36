package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonPageDTO;
import com.swcares.aps.web.bff.service.ReplaceRejectReasonService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/compensation/replace/rejectReason")
@Api(tags = "代领人领取审核拒绝原由接口")
@ApiVersion(value = "代领设置 v1.0")
public class ReplaceRejectReasonController extends BaseController {

    @Autowired
    private ReplaceRejectReasonService replaceRejectReasonService;

    @PostMapping("/pages")
    @ApiOperation(value = "条件分页查询审核拒绝原由记录")
    public BaseResult<Map<String, RuleManageDTO>> pages() {
        return replaceRejectReasonService.pages();
    }

    @GetMapping("/getAllRejectReason")
    @ApiOperation(value = "获取所有审核拒绝原由记录")
    public BaseResult<List<ReplaceRejectReasonDTO>> getAllRejectReason() {
        return replaceRejectReasonService.getAllRejectReason();
    }

    @PostMapping("/delete")
    @ApiOperation(value = "刪除拒绝原由记录")
    public BaseResult<Object> delete(@RequestBody ReplaceConfigDeleteDTO dto) {
        return replaceRejectReasonService.delete(dto);
    }

    @PostMapping("/update")
    @ApiOperation(value = "修改拒绝原由记录")
    public BaseResult<Object>  update(@RequestBody ReplaceRejectReasonDTO dto) {
       return replaceRejectReasonService.update(dto);
    }

    @PostMapping("/create")
    @ApiOperation(value = "添加拒绝原由记录")
    public BaseResult<Object>  create(@RequestBody ReplaceRejectReasonDTO dto) {
        return replaceRejectReasonService.create(dto);

    }
}
