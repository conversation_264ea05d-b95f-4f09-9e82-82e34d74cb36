package com.swcares.aps.web.bff.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.swcares.aps.basic.data.businessimpl.model.dto.CompensationPaxSearchDTO;
import com.swcares.aps.basic.data.remoteapi.model.dto.PassengerQueryDTO;
import com.swcares.aps.basic.data.remoteapi.model.vo.PassengerBasicInfoVO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationCashReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.CompensationLuggageReportDTO;
import com.swcares.aps.compensation.model.compensation.dto.LuggageBusinessReviewDTO;
import com.swcares.aps.compensation.model.compensation.vo.CashBusinessCostsDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.CashPayDetailVO;
import com.swcares.aps.compensation.model.compensation.vo.LuggageBusinessCostsDetailVO;
import com.swcares.aps.compensation.model.complaint.vo.CompensationCountByPassengerInfoVo;
import com.swcares.aps.compensation.model.complaint.vo.ComplaintPassengerListVo;
import com.swcares.aps.compensation.model.irregularflight.dto.*;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationChoicePaxVO;
import com.swcares.aps.compensation.model.irregularflight.vo.CompensationOrderInfoExamineVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxCompensationCountVO;
import com.swcares.aps.compensation.model.irregularflight.vo.PaxReceiveRecordVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.compensation.remote.api.irregularflight.ComplaintAccidentApi;
import com.swcares.aps.web.bff.service.CompensationOrderWebService;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * ClassName：com.swcares.com/swcares/aps/compensating/mode/irregularflight.controller.CompensationOrderInfoController <br>
 * Description：赔偿单信息 前端控制器 <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * Date 2021-10-27 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/compensation")
@Api(tags = "赔偿单信息接口")
@ApiVersion(value = "不正常航班赔付 v1.0")
public class CompensationOrderInfoController extends BaseController {

    @Autowired
    CompensationInfoApi compensationInfoApi;

    @Autowired
    ComplaintAccidentApi complaintAccidentApi;

    @Autowired
    CompensationOrderWebService compensationOrderService;


    @PostMapping("/save")
    @ApiOperation(value = "新建赔偿单信息记录")
    public BaseResult<Object> save(@Validated @RequestBody CompensationSyntheticalSaveDTO dto){
        return compensationInfoApi.save(dto);
    }

    @DeleteMapping("/delete")
    @ApiOperation(value = "通过ID删除赔偿单信息记录")
    public BaseResult<Object> delete(@RequestBody @ApiParam(value = "主键id", required = true) CompensationOrderIdDTO dto) {
        return compensationInfoApi.orderDelete(dto.getId());
    }


    @GetMapping("/find")
    @ApiOperation(value = "通过ID查询赔偿单详情信息")
    public BaseResult<Object> findDetails(@ApiParam(value = "赔偿单主键id", required = true) Long id,@ApiParam(value = "事故单id", required = true) Long accidentId,@ApiParam(value = "赔偿单号",required=true)String orderNo,@ApiParam(value = "事故单类型",required=true)String accidentType) {
        return ok(compensationOrderService.getCompensationDetailsVo(id,accidentId,orderNo,accidentType));
    }

    @PutMapping("/takeEffect")
    @ApiOperation(value = "通过ID进行赔偿发放操作")
    public BaseResult<Object> takeEffect(@ApiParam(value = "赔偿单主键id", required = true)@RequestBody FlightAccidentIdDTO idDTO){
        return compensationInfoApi.takeEffect(idDTO.getId());
    }

    @PutMapping("/close")
    @ApiOperation(value = "通过ID进行赔偿关闭操作")
    public BaseResult<Object> close(@ApiParam(value = "赔偿单主键id", required = true)@RequestBody FlightAccidentIdDTO idDTO){
        return compensationInfoApi.close(idDTO.getId());
    }

    @GetMapping("/findChoicePax")
    @ApiOperation(value = "通过赔偿单ID、及筛选项，查询已选旅客列表")
    public BaseResult<List<CompensationChoicePaxVO>> findChoicePax(CompensationPaxFrozenDTO compensationPaxFrozenDTO) {
        return compensationInfoApi.findChoicePax(compensationPaxFrozenDTO);
    }

    @GetMapping("/findSelectedPax")
    @ApiOperation(value = "通过赔偿单ID查询已选旅客列表")
    public BaseResult<Object> findSelectedPax(@ApiParam(value = "赔偿单主键id", required = true)@RequestBody FlightAccidentIdDTO idDTO) {
        return ok(compensationInfoApi.findSelectedPax(idDTO.getId()));
    }

    @GetMapping("/page")
    @ApiOperation(value = "条件分页查询赔偿单信息记录")
    public PagedResult<List<CompensationOrderInfoExamineVO>> page(CompensationOrderInfoPagedDTO dto) {
        return compensationInfoApi.pages(dto);
    }
/*
    @GetMapping("/pages")
    @ApiOperation(value = "条件分页查询赔偿单(带审核)信息记录")
    public PagedResult<List<CompensationOrderInfoExamineVO>> pages(CompensationOrderInfoPagedDTO dto) {
        return compensationInfoApi.pages(dto);
    }*/

    @GetMapping("/findPax")
    @ApiOperation(value = "旅客查询接口")
    public BaseResult<List<PassengerBasicInfoVO>> findPax(CompensationPaxSearchDTO dto) {
        PassengerQueryDTO queryDTO = new PassengerQueryDTO();
        BeanUtils.copyProperties(dto, queryDTO);
        BaseResult<List<PassengerBasicInfoVO>> passengers = compensationInfoApi.getPassengers(queryDTO);
        if (passengers.getData() == null || passengers.getData().size() == 0) {
            return passengers;
        }
        BaseResult<List<CompensationCountByPassengerInfoVo>> frequencyResult = complaintAccidentApi.getFrequency(queryDTO);
        if (frequencyResult.getCode() != 200) {
            return passengers;
        }
        if (frequencyResult.getData() != null) {
            List<CompensationCountByPassengerInfoVo> data = frequencyResult.getData();
            Map<String, String> compensationCountMap = data.stream()
                    .collect(Collectors.toMap(CompensationCountByPassengerInfoVo::getTicketNumber, CompensationCountByPassengerInfoVo::getFrequency));
            passengers.getData().forEach(passengerBasicInfoVO -> {
                if (compensationCountMap != null && compensationCountMap.size() > 0) {
                    passengerBasicInfoVO.setFrequency(Integer.valueOf(compensationCountMap.get(passengerBasicInfoVO.getTktNo()) != null ? compensationCountMap.get(passengerBasicInfoVO.getTktNo()) : "0"));
                } else {
                    passengerBasicInfoVO.setFrequency(0);
                }
            });
        }
        return ok(passengerInfoSort(passengers.getData()));
    }

    @GetMapping("/findPaxOrderInfo")
    @ApiOperation(value = "通过通过paxName、idNo、paxId、flightNo、flightDate查询【旅客赔偿记录】")
    public BaseResult<List<PaxCompensationCountVO>> getPaxOrderInfo(PaxOrderInfoQueryDTO dto) {
        return compensationInfoApi.getPaxOrderInfo(dto);
    }

    @PutMapping("/freezeOrderPax")
    @ApiOperation(value = "冻结||激活旅客")
    public BaseResult<Object> freezeOrderPax(@RequestBody FreezeOrderPaxDTO freezeOrderPaxDTO) {
        return compensationInfoApi.freezeOrderPax(freezeOrderPaxDTO);
    }

    @GetMapping("/findOrderEditEcho")
    @ApiOperation(value = "通过ID查询赔偿单编辑回显信息")
    public BaseResult<Map<String, Object>> getOrderEditEchoById(@ApiParam(value = "赔偿单主键id", required = true) Long id,@ApiParam(value = "事故单id", required = true) Long accidentId) {
        return ok(compensationOrderService.getOrderEditEchoDetails(id,accidentId));
    }

    @GetMapping("/findPaxReceiveRecord")
    @ApiOperation(value = "通过旅客orderId，PaxID-查领取记录-固定数据")
    public BaseResult<List<PaxReceiveRecordVO>> findPaxReceiveRecord(@ApiParam(value = "orderId", required = true) Long orderId,@ApiParam(value = "paxId", required = true) String paxId) {
        return compensationInfoApi.findPaxReceiveRecord(orderId,paxId);
    }

    @GetMapping("/findSameTypeOrders")
    @ApiOperation(value = "通过航班日期，航班号查询同类型赔偿单")
    public BaseResult<Object> getSameTypeOrders(@ApiParam(value = "航班日期", required = true) String flightDate,@ApiParam(value = "航班号", required = true)String flightNo,@ApiParam(value = "事故单类型", required = true)String accidentType,@ApiParam(value = "事故单Id")Long orderId) {
        return ok(compensationInfoApi.getSameTypeOrders(flightDate, flightNo, accidentType,orderId));
    }

    @PostMapping("/getCashBusinessCostsDetail")
    @ApiOperation(value = "查询现金业务明细详情")
    public PagedResult<List<CashBusinessCostsDetailVO>> getCashBusinessCostsDetail(@RequestBody CompensationCashReportDTO dto){
        return compensationInfoApi.getCashBusinessCostsDetail(dto);
    }

    @PostMapping("/getCashPayDetail")
    @ApiOperation(value = "查询现金支付明细报表")
    public PagedResult<List<CashPayDetailVO>> getCashPayDetail(@RequestBody CompensationCashReportDTO dto){
        return compensationInfoApi.getCashPayDetail(dto);
    }

    @PostMapping("/cashBusinessCosts/export")
    @ApiOperation(value = "现金业务明细表导出")
    public void CashBusinessCostsExport(@RequestBody CompensationCashReportDTO dto, HttpServletResponse response){
        try {
            //获取数据源
            PagedResult<List<CashBusinessCostsDetailVO>> cashBusinessCostsDetailResult = compensationInfoApi.getCashBusinessCostsDetailReport(dto);
            List<CashBusinessCostsDetailVO> businessCostsDetailRecords = cashBusinessCostsDetailResult.getData();
            //获取当前年月日 时分
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm业务成本明细表");
            String tableName = LocalDateTime.now().format(dtf);
            ExportParams params = new ExportParams("现金业务明细报表","业务成本明细表", ExcelType.XSSF);
            //将参数传入ExcelExportUtil类,获取WorkBook对象
            Workbook workbook = ExcelExportUtil.exportExcel(params, CashBusinessCostsDetailVO.class, businessCostsDetailRecords);
            //设置返回流的格式，避免乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            //使用URLEncoder.encode防止中文乱码
            String fileName = URLEncoder.encode(tableName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            //写入返回流
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @PostMapping("/cashPay/export")
    @ApiOperation(value = "现金支付明细报表导出")
    public void CashPayExport(@RequestBody CompensationCashReportDTO dto, HttpServletResponse response){
        try {
            //获取数据源
            PagedResult<List<CashPayDetailVO>> cashPayDetailResult = compensationInfoApi.getCashPayDetailReport(dto);
            List<CashPayDetailVO> cashPayDetailRecords = cashPayDetailResult.getData();
            //获取当前年月日 时分
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm财务明细表");
            String tableName = LocalDateTime.now().format(dtf);
            ExportParams params = new ExportParams("财务明细表","财务明细表", ExcelType.XSSF);
            //将参数传入ExcelExportUtil类,获取WorkBook对象
            Workbook workbook = ExcelExportUtil.exportExcel(params, CashPayDetailVO.class, cashPayDetailRecords);
            //设置返回流的格式，避免乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            //使用URLEncoder.encode防止中文乱码
            String fileName = URLEncoder.encode(tableName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            //写入返回流
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private List<PassengerBasicInfoVO> passengerInfoSort(List<PassengerBasicInfoVO> result) {
        if (result.size() == 0) {
            return result;
        }
        Map<String, List<PassengerBasicInfoVO>> collect = result.stream().collect(Collectors.groupingBy(PassengerBasicInfoVO::getIdNo));
        List<PassengerBasicInfoVO> head = new ArrayList<>(result.size());
        List<PassengerBasicInfoVO> tail = new ArrayList<>();
        collect.forEach((k, v) -> {
            if (v.size() > 1) {
                v.forEach(head::add);
            } else {
                tail.add(v.get(0));
            }
        });
        head.sort(Comparator.comparing(PassengerBasicInfoVO::getIdNo).thenComparing(PassengerBasicInfoVO::getTktDate));
        tail.sort(Comparator.comparing(PassengerBasicInfoVO::getFrequency).reversed().thenComparing(PassengerBasicInfoVO::getTktDate));
        head.addAll(tail);
        return head;
    }


    @PostMapping("/luggageBusinessReview")
    @ApiOperation(value = "复核操作")
    public BaseResult<Object> luggageBusinessReview(@RequestBody LuggageBusinessReviewDTO dto) {
        return compensationInfoApi.luggageBusinessReview(dto);
    }


    @PostMapping("/getLuggageBusinessCostsDetail")
    @ApiOperation(value = "查询异常行李业务明细详情")
    public PagedResult<List<LuggageBusinessCostsDetailVO>> getLuggageBusinessCostsDetail(@RequestBody CompensationLuggageReportDTO dto){
        return compensationInfoApi.getLuggageBusinessCostsDetail(dto);
    }

    @PostMapping("/luggageBusiness/getLuggageBusinessCostsDetailExport")
    @ApiOperation(value = "异常行李业务明细表导出")
    public void getLuggageBusinessCostsDetailExport(@RequestBody CompensationLuggageReportDTO dto, HttpServletResponse response){
        try {
            //获取数据源
            PagedResult<List<LuggageBusinessCostsDetailVO>> luggageBusinessDetailResult = compensationInfoApi.getLuggageBusinessCostsDetailExport(dto);
            List<LuggageBusinessCostsDetailVO> businessCostsDetailRecords = luggageBusinessDetailResult.getData();
            //获取当前年月日 时分
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm业务成本明细表");
            String tableName = LocalDateTime.now().format(dtf);
            ExportParams params = new ExportParams("异常行李业务明细表","异常行李业务明细表", ExcelType.XSSF);
            //将参数传入ExcelExportUtil类,获取WorkBook对象
            Workbook workbook = ExcelExportUtil.exportExcel(params, LuggageBusinessCostsDetailVO.class, businessCostsDetailRecords);
            //设置返回流的格式，避免乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            //使用URLEncoder.encode防止中文乱码
            String fileName = URLEncoder.encode(tableName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            //写入返回流
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}
