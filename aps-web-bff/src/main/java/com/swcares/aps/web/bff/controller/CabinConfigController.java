package com.swcares.aps.web.bff.controller;

import com.swcares.aps.compensation.model.dataconfig.vo.CabinConfigVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.security.LoginUserDetails;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/compensation/cabin/")
@Api(tags = "赔付系统舱位配置")
@ApiVersion(value = "赔付系统业务配置 v1.0")
public class CabinConfigController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @PostMapping("/list")
    @ApiOperation(value = "加载当前用户所在航司的仓位信息")
    public BaseResult<CabinConfigVO> loadCabinByAirline(){
        return compensationInfoApi.loadCabinByAirline();
    }

    @PostMapping("/create")
    @ApiOperation(value = "创建或者修改仓位信息")
    public BaseResult<Object> createCabinConfig(String businessCabin, String economyCabin) {
        return compensationInfoApi.createCabinConfig(businessCabin, economyCabin);
    }


}