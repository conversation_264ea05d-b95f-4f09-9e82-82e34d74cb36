package com.swcares.aps.web.bff.service;

import com.swcares.aps.compensation.model.dataconfig.dto.RuleManageDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceConfigDeleteDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonDTO;
import com.swcares.aps.compensation.model.replace.dto.ReplaceRejectReasonPageDTO;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;

import java.util.List;
import java.util.Map;

/**
 * ClassName：com.swcares.aps.compensation.bff.web.service.impl <br>
 * Description：代领人审核拒绝原由<br>
 * Copyright  2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest Chen Du LTD  <br>
 *
 * <AUTHOR> <br>
 * date 2022年 01月12日 <br>
 * @version v1.0 <br>
 */
public interface ReplaceRejectReasonService {

    /**
     * Title：pages <br>
     * Description：分页获取当前代人领取拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022/1/12 <br>
     * @param dto <br>
     * @return  <br>
     */
    BaseResult<Map<String, RuleManageDTO>> pages();

    /**
     * Title：getAllRejectReason <br>
     * Description：获取当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022/1/12 <br>
     * @param
     * @return  <br>
     */
    BaseResult<List<ReplaceRejectReasonDTO>>  getAllRejectReason(String airCode);

    /**
     * Title：delete <br>
     * Description：删除当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022/1/12 <br>
     * @param deleteDTO
     * @return <br>
     */
    BaseResult<Object> delete(ReplaceConfigDeleteDTO deleteDTO);

    /**
     * Title：update <br>
     * Description：修改当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022/1/12 <br>
     * @param dto
     * @return <br>
     */
    BaseResult<Object> update(ReplaceRejectReasonDTO dto);

    /**
     * Title：create <br>
     * Description：创建当前代人领取所有拒绝原由 <br>
     * author：陈明东 <br>
     * date：2022/1/12 <br>
     * @param dto
     * @return <br>
     */
    BaseResult<Object> create(ReplaceRejectReasonDTO dto);
}
