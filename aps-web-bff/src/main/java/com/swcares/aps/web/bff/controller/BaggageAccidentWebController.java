package com.swcares.aps.web.bff.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.swcares.aps.compensation.model.baggage.accident.dto.BaggageAccidentInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.dto.CompensationExpressInfoDTO;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindBaggageDTO;
import com.swcares.aps.compensation.model.baggage.accident.dto.FindBaggageDetailDTO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.web.bff.enums.AccidentStatusEnum;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * ClassName：BaggageAccidentController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest 唐康  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/web")
@Api(tags = "异常行李事故单相关接口")
@ApiVersion(value = "异常行李事故单 v1.0")
public class BaggageAccidentWebController extends BaseController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @GetMapping("/findTerminal")
    @ApiOperation(value = "获取服务航站")
    public BaseResult<Object> getTerminal() {
        return compensationInfoApi.getTerminal();
    }

    @PostMapping("/findBaggageAccidentList")
    @ApiOperation(value = "箱包事故单信息列表查询")
    public PagedResult<List<FindBaggageVO>> findBaggageAccidentList(@RequestBody FindBaggageDTO dto){
        dto.setCreatedBy(String.valueOf(UserContext.getUserId()));
        return compensationInfoApi.findBaggageAccidentList(dto);
    }

    @PostMapping("/baggageAccidentDetailInfo")
    @ApiOperation(value = "箱包事故单详情信息")
    public BaseResult<BaggageDetailFinalVO> baggageAccidentDetailInfo(@RequestBody FindBaggageDetailDTO dto){
        return compensationInfoApi.baggageAccidentDetailInfo(dto);
    }

    //-----------------------快递相关----------------
    @PostMapping("/accident/baggage/express/save")
    @ApiOperation(value = "新建异常行李快递信息")
    public BaseResult<Object> saveExpress(@Validated @RequestBody CompensationExpressInfoDTO dto){
        return compensationInfoApi.saveExpress(dto);
    }

    @DeleteMapping("/accident/baggage/express/remove")
    @ApiOperation(value = "根据id删除一条快递信息")
    public BaseResult<Object> removeExpress(String expressId){
        return compensationInfoApi.removeExpress(expressId);
    }

    //-----------------------事故单相关----------------
    @PostMapping("/accident/baggage/save")
    @ApiOperation(value = "新建异常行李事故单")
    public BaseResult<Object> save(@Validated @RequestBody BaggageAccidentInfoDTO dto){
        return compensationInfoApi.baggageSave(dto);
    }

    @GetMapping("/accident/baggage/edit")
    @ApiOperation(value = "编辑异常行李事故单回显")
    public BaseResult<Object> edit(@Validated @RequestParam String accidentId) {
        return compensationInfoApi.edit(accidentId);
    }

    @GetMapping("/accident/baggage/toLost")
    @ApiOperation(value = "少收转丢失")
    public BaseResult<Object> toLost(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toLost(accidentId);
    }

    @GetMapping("/accident/baggage/toMatch")
    @ApiOperation(value = "匹配多/少收")
    public BaseResult<Object> toMatch(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toMatch(accidentId);
    }

    @GetMapping("/accident/baggage/saveMatch")
    @ApiOperation(value = "匹配绑定多/少收")
    public BaseResult<Object> saveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "须绑定的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.saveMatch(accidentId, accidentNo);
    }

    @GetMapping("/accident/baggage/relieveMatch")
    @ApiOperation(value = "解除匹配绑定多/少收")
    public BaseResult<Object> relieveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "解除匹配的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.relieveMatch(accidentId, accidentNo);
    }

    @DeleteMapping("/accident/baggage/delete/{id}")
    @ApiOperation(value = "通过id删除异常行李单")
    public BaseResult<Object> deleteById(@PathVariable @ApiParam(value = "主键id",required = true)Long id){
        return compensationInfoApi.deleteById(id);
    }

    @GetMapping("/accident/baggage/submit")
    @ApiOperation(value = "提交异常行李事故单(状态转换)")
    public BaseResult<Object> submit(@ApiParam(value = "事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TODO.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toVoidStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转作废")
    public BaseResult<Object> toVoidStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TO_VOID.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toCloseStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转结案")
    public BaseResult<Object> toCloseStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.CASE_CLOSED.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }
    @PostMapping("/getLuggageCompensationReport")
    @ApiOperation(value = "箱包补偿报表")
    PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReportDetail(@RequestBody LuggageCompensatePageDTO dto){
        return compensationInfoApi.getLuggageCompensationReportDetail(dto);
    }

    @GetMapping("/getLuggageManagementList")
    @ApiOperation(value = "获取箱包信息")
    BaseResult<List<LuggageInfoVO>> getLuggageManagementList(@RequestParam(value = "brand" ,required = false) @ApiParam(value = "箱包品牌")List<String> brand){
        return compensationInfoApi.getLuggageManagementList(brand);
    }

    @PostMapping("/luggageCompensationReport/export")
    @ApiOperation(value = "箱包补偿报表导出")
    public void LuggageCompensationExport(@RequestBody LuggageCompensatePageDTO dto, HttpServletResponse response){
        try {
            //获取数据源
            PagedResult<List<LuggageCompensateDetailVO>> luggageCompensationReportResult = compensationInfoApi.getLuggageCompensationReport(dto);
            List<LuggageCompensateDetailVO> luggageCompensateDetailVOS = luggageCompensationReportResult.getData();
            //获取当前年月日 时分
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm箱包明细报表");
            String tableName = LocalDateTime.now().format(dtf);
            ExportParams params = new ExportParams("箱包明细报表","箱包明细报表", ExcelType.XSSF);
            //将参数传入ExcelExportUtil类,获取WorkBook对象
            Workbook workbook = ExcelExportUtil.exportExcel(params, LuggageCompensateDetailVO.class, luggageCompensateDetailVOS);
            //设置返回流的格式，避免乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            //使用URLEncoder.encode防止中文乱码
            String fileName = URLEncoder.encode(tableName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            //写入返回流
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
