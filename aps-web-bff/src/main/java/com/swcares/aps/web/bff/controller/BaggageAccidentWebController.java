package com.swcares.aps.web.bff.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.swcares.aps.compensation.model.baggage.accident.dto.*;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageAccidentDropdownVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageDetailFinalVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportDetailVO;
import com.swcares.aps.compensation.model.baggage.accident.vo.BaggageTransportListVO;
import com.swcares.aps.compensation.model.baggage.luggage.dto.LuggageCompensatePageDTO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.FindBaggageVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageCompensateDetailVO;
import com.swcares.aps.compensation.model.baggage.luggage.vo.LuggageInfoVO;
import com.swcares.aps.compensation.remote.api.irregularflight.CompensationInfoApi;
import com.swcares.aps.component.dict.mapper.RegionMapper;
import com.swcares.aps.component.dict.model.entity.Region;
import com.swcares.aps.web.bff.enums.AccidentStatusEnum;
import com.swcares.baseframe.common.annotation.ApiVersion;
import com.swcares.baseframe.common.base.BaseResult;
import com.swcares.baseframe.common.base.PagedResult;
import com.swcares.baseframe.common.core.controller.BaseController;
import com.swcares.baseframe.common.security.UserContext;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * ClassName：BaggageAccidentController <br>
 * Description： <br>
 * Copyright © 2021 xnky.travelsky.net Inc. All rights reserved. <br>
 * Company：Aviation Cares Of Southwest 唐康  <br>
 *
 * <AUTHOR> <br>
 * Date 2022/3/3 <br>
 * @version v1.0 <br>
 */
@RestController
@RequestMapping("/web")
@Api(tags = "异常行李事故单相关接口")
@ApiVersion(value = "异常行李事故单 v1.0")
public class BaggageAccidentWebController extends BaseController {

    @Autowired
    private CompensationInfoApi compensationInfoApi;

    @GetMapping("/findTerminal")
    @ApiOperation(value = "获取服务航站")
    public BaseResult<Object> getTerminal() {
        return compensationInfoApi.getTerminal();
    }

    @PostMapping("/findBaggageAccidentList")
    @ApiOperation(value = "箱包事故单信息列表查询")
    public PagedResult<List<FindBaggageVO>> findBaggageAccidentList(@RequestBody FindBaggageDTO dto){
        dto.setCreatedBy(String.valueOf(UserContext.getUserId()));
        return compensationInfoApi.findBaggageAccidentList(dto);
    }

    @PostMapping("/baggageAccidentDetailInfo")
    @ApiOperation(value = "箱包事故单详情信息")
    public BaseResult<BaggageDetailFinalVO> baggageAccidentDetailInfo(@RequestBody FindBaggageDetailDTO dto){
        return compensationInfoApi.baggageAccidentDetailInfo(dto);
    }

    //-----------------------快递相关----------------
    @PostMapping("/accident/baggage/express/save")
    @ApiOperation(value = "新建异常行李快递信息")
    public BaseResult<Object> saveExpress(@Validated @RequestBody CompensationExpressInfoDTO dto){
        return compensationInfoApi.saveExpress(dto);
    }

    @DeleteMapping("/accident/baggage/express/remove")
    @ApiOperation(value = "根据id删除一条快递信息")
    public BaseResult<Object> removeExpress(String expressId){
        return compensationInfoApi.removeExpress(expressId);
    }

    //-----------------------事故单相关----------------
    @PostMapping("/accident/baggage/save")
    @ApiOperation(value = "新建异常行李事故单")
    public BaseResult<Object> save(@Validated @RequestBody BaggageAccidentInfoDTO dto){
        return compensationInfoApi.baggageSave(dto);
    }

    @GetMapping("/accident/baggage/edit")
    @ApiOperation(value = "编辑异常行李事故单回显")
    public BaseResult<Object> edit(@Validated @RequestParam String accidentId) {
        return compensationInfoApi.edit(accidentId);
    }

    @GetMapping("/accident/baggage/toLost")
    @ApiOperation(value = "少收转丢失")
    public BaseResult<Object> toLost(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toLost(accidentId);
    }

    @GetMapping("/accident/baggage/toMatch")
    @ApiOperation(value = "匹配多/少收")
    public BaseResult<Object> toMatch(@ApiParam(value = "异常行李事故单id",required = true)String accidentId){
        return compensationInfoApi.toMatch(accidentId);
    }

    @GetMapping("/accident/baggage/saveMatch")
    @ApiOperation(value = "匹配绑定多/少收")
    public BaseResult<Object> saveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "须绑定的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.saveMatch(accidentId, accidentNo);
    }

    @GetMapping("/accident/baggage/relieveMatch")
    @ApiOperation(value = "解除匹配绑定多/少收")
    public BaseResult<Object> relieveMatch(@ApiParam(value = "当前行李事故单id",required = true)String accidentId,@ApiParam(value = "解除匹配的行李事故单号",required = true)String accidentNo){
        return compensationInfoApi.relieveMatch(accidentId, accidentNo);
    }

    @DeleteMapping("/accident/baggage/delete/{id}")
    @ApiOperation(value = "通过id删除异常行李单")
    public BaseResult<Object> deleteById(@PathVariable @ApiParam(value = "主键id",required = true)Long id){
        return compensationInfoApi.deleteById(id);
    }

    @GetMapping("/accident/baggage/submit")
    @ApiOperation(value = "提交异常行李事故单(状态转换)")
    public BaseResult<Object> submit(@ApiParam(value = "事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TODO.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toVoidStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转作废")
    public BaseResult<Object> toVoidStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.TO_VOID.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }

    @GetMapping("/accident/baggage/toCloseStatus")
    @ApiOperation(value = "异常行李事故单待处理状态转结案")
    public BaseResult<Object> toCloseStatus(@ApiParam(value = "当前行李事故单id",required = true)String accidentId){
        String targetStatus= AccidentStatusEnum.CASE_CLOSED.getValue();
        return compensationInfoApi.changeStatus(accidentId, targetStatus);
    }
    @PostMapping("/getLuggageCompensationReport")
    @ApiOperation(value = "箱包补偿报表")
    PagedResult<List<LuggageCompensateDetailVO>> getLuggageCompensationReportDetail(@RequestBody LuggageCompensatePageDTO dto){
        return compensationInfoApi.getLuggageCompensationReportDetail(dto);
    }

    @GetMapping("/getLuggageManagementList")
    @ApiOperation(value = "获取箱包信息")
    BaseResult<List<LuggageInfoVO>> getLuggageManagementList(@RequestParam(value = "brand" ,required = false) @ApiParam(value = "箱包品牌")List<String> brand){
        return compensationInfoApi.getLuggageManagementList(brand);
    }

    @PostMapping("/luggageCompensationReport/export")
    @ApiOperation(value = "箱包补偿报表导出")
    public void LuggageCompensationExport(@RequestBody LuggageCompensatePageDTO dto, HttpServletResponse response){
        try {
            //获取数据源
            PagedResult<List<LuggageCompensateDetailVO>> luggageCompensationReportResult = compensationInfoApi.getLuggageCompensationReport(dto);
            List<LuggageCompensateDetailVO> luggageCompensateDetailVOS = luggageCompensationReportResult.getData();
            //获取当前年月日 时分
            DateTimeFormatter dtf = DateTimeFormatter.ofPattern("yyyyMMddHHmm箱包明细报表");
            String tableName = LocalDateTime.now().format(dtf);
            ExportParams params = new ExportParams("箱包明细报表","箱包明细报表", ExcelType.XSSF);
            //将参数传入ExcelExportUtil类,获取WorkBook对象
            Workbook workbook = ExcelExportUtil.exportExcel(params, LuggageCompensateDetailVO.class, luggageCompensateDetailVOS);
            //设置返回流的格式，避免乱码
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");
            //使用URLEncoder.encode防止中文乱码
            String fileName = URLEncoder.encode(tableName, "UTF-8").replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            //写入返回流
            workbook.write(response.getOutputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @PostMapping("/transport/list")
    @ApiOperation(value = "查询异常行李运输单列表")
    public PagedResult<List<BaggageTransportListVO>> queryBaggageTransportList(@RequestBody @Valid BaggageTransportQueryDTO dto) {
        return compensationInfoApi.queryBaggageTransportList(dto);
    }

    @GetMapping("/transport/detail/{transportId}")
    @ApiOperation(value = "查询运输单详情")
    public BaseResult<BaggageTransportDetailVO> getTransportDetail(@PathVariable(value = "transportId") Long transportId) {
        return compensationInfoApi.getTransportDetail(transportId);
    }

    @PostMapping("/transport/save")
    @ApiOperation(value = "修改运输单信息")
    public BaseResult<Object> saveTransportInfo(@Validated @RequestBody BaggageTransportInfoDTO dto){
        return compensationInfoApi.saveTransportInfo(dto);
    }

    @PostMapping("/transport/review")
    @ApiOperation(value = "复核运输单")
    public BaseResult<Boolean> reviewTransport(@Valid @RequestBody BaggageTransportReviewDTO dto) {
        return compensationInfoApi.reviewTransport(dto);
    }
    @PostMapping("/undeliveredDropdown")
    @ApiOperation(value = "获取未交付的异常行李事故单下拉列表")
    public BaseResult<List<BaggageAccidentDropdownVO>> getUndeliveredAccidentDropdown(@RequestBody UndeliveredAccidentDTO dto) {
        return compensationInfoApi.getUndeliveredAccidentDropdown(dto);
    }

    @Autowired
    private RegionMapper regionMapper;

    /**
     * 根据pid和deep查询区域数据
     * @param pid 父级ID (必填)
     * @param deep 层级深度 (必填)
     * @return 符合条件的区域列表
     */
    @GetMapping("/region/query")
    public BaseResult<List<Region>> getRegionsByPidAndDeep(
            @RequestParam Long pid,
            @RequestParam Integer deep) {
        LambdaQueryWrapper<Region> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Region::getPid, pid)
                .eq(Region::getDeep, deep)
                .orderByAsc(Region::getId);
        return ok(regionMapper.selectList(queryWrapper));
    }

    // ----------------------- 运输单工作流相关接口 -----------------------

    @PostMapping("/transport/submit/{transportId}")
    @ApiOperation(value = "提交运输单到工作流")
    public BaseResult<Object> submitTransport(@PathVariable(value = "transportId") Long transportId) {
        return compensationInfoApi.submitTransport(transportId);
    }

    @PostMapping("/transport/audit")
    @ApiOperation(value = "审核运输单")
    public BaseResult<Object> auditTransport(@Valid @RequestBody BaggageTransportAuditDTO dto) {
        return compensationInfoApi.auditTransport(dto);
    }

    @GetMapping("/transport/reviewers/{transportId}")
    @ApiOperation(value = "查询运输单可选审核人")
    public BaseResult<Object> getTransportReviewers(@PathVariable(value = "transportId") Long transportId,
            @ApiParam(value = "任务ID，可选") @RequestParam(required = false) String taskId) {
        return compensationInfoApi.getTransportReviewers(transportId, taskId);
    }

    @PostMapping("/transport/reviewers/save")
    @ApiOperation(value = "保存运输单审核人")
    public BaseResult<Object> saveTransportReviewers(@Valid @RequestBody BaggageTransportReviewerSaveDTO dto) {
        return compensationInfoApi.saveTransportReviewers(dto);
    }
}
