<?xml version="1.0" encoding="UTF-8"?>
<!-- 每隔20S扫描1次配置文件，如果配置文件被修改自动加载 -->
<configuration scan="true" scanPeriod="60 seconds">
	<!-- Windows操作系统 -->
	<if condition='property("os.name").contains("Windows")'>
    	<then>
      		<property name="LOG_FILE_ROOT" value="C://opt/applog/aps-logs" />
      		
      		<!-- 控制台输出 -->
			<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
				<encoder>
					<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
				</encoder>
			</appender>
    	</then>
  	</if>
  	<!-- Linux操作系统 -->
  	<if condition='property("os.name").contains("Linux")'>
    	<then>
      		<property name="LOG_FILE_ROOT" value="/opt/applog/aps-logs" />
      		
      		<!-- 控制台输出 -->
			<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
				<filter class="ch.qos.logback.classic.filter.LevelFilter">
					<level>ERROR</level>
					<onMatch>ACCEPT</onMatch>
					<onMismatch>DENY</onMismatch>
				</filter>
				<encoder>
					<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
				</encoder>
			</appender>
    	</then>
  	</if>
	<!-- MAC操作系统 -->
	<if condition='property("os.name").toUpperCase().contains("MAC")'>
		<then>
			<property name="LOG_FILE_ROOT" value="${user.home}/WorkSpace/log/applog/aps-logs"/>

			<!-- 控制台输出 -->
			<appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
				<encoder>
					<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
				</encoder>
			</appender>
		</then>
	</if>
	
	
	<!-- 时间滚动输出 level为 debug 日志 -->
	<appender name="DEBUG" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_ROOT}/aps-web-bff-debug.log</file>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>DEBUG</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_FILE_ROOT}/aps-web-bff-debug-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy	class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--debug级别 最多保留7天log-->
		    <maxHistory>7</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- 时间滚动输出 level为 INFO 日志 -->
	<appender name="INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_ROOT}/aps-web-bff-info.log</file>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>INFO</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_FILE_ROOT}/aps-web-bff-info-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy	class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--info级别 最多保留90天log-->
		    <maxHistory>90</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
		</encoder>
	</appender>
	
		<!-- 时间滚动输出 level为 INFO 日志 -->
	<appender name="WARN" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_ROOT}/aps-web-bff-warn.log</file>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>WARN</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${LOG_FILE_ROOT}/aps-web-bff-warn-%d{yyyy-MM-dd}.%i.log.gz</fileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy	class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--warn级别 最多保留7天log-->
		    <maxHistory>7</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- 时间滚动输出 level为 ERROR 日志 -->
	<appender name="ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<file>${LOG_FILE_ROOT}/aps-web-bff-error.log</file>
		<filter class="ch.qos.logback.classic.filter.LevelFilter">
			<level>ERROR</level>
			<onMatch>ACCEPT</onMatch>
			<onMismatch>DENY</onMismatch>
		</filter>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<FileNamePattern>${LOG_FILE_ROOT}/aps-web-bff-error-%d{yyyy-MM-dd}.%i.log.gz</FileNamePattern>
			<timeBasedFileNamingAndTriggeringPolicy	class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
				<maxFileSize>100MB</maxFileSize>
			</timeBasedFileNamingAndTriggeringPolicy>
			<!--error级别 最多保留40天log-->
		    <maxHistory>40</maxHistory>
		</rollingPolicy>
		<encoder>
			<pattern>%date{yyyy-MM-dd HH:mm:ss.SSS}[%level][%thread][%logger.java:%line] - %msg%n</pattern>
		</encoder>
	</appender>
	
	<!-- root Logger -->
	<root level="INFO">
		<appender-ref ref="STDOUT" />
		<appender-ref ref="DEBUG"  />
		<appender-ref ref="INFO"   />
		<appender-ref ref="WARN"   />
		<appender-ref ref="ERROR"  />
	</root>
</configuration>